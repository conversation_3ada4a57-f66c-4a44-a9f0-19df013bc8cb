ARG RUN_BASEIMAGE=133139256227.dkr.ecr.ap-southeast-1.amazonaws.com/mgt-global-tech-multiplierbaseimage-ecr:amazoncorretto__17-al2023__latest_v2

FROM public.ecr.aws/docker/library/gradle:8.1.1-jdk17 AS build
ARG CODEARTIFACT_AUTH_TOKEN
ARG GRPC_CRT
ARG GRPC_CRT_KEY
ARG ENV_VAR
ENV ENV_VAR=$ENV_VAR

COPY --chown=gradle:gradle . /home/<USER>/src
WORKDIR /home/<USER>/src
ENV CODEARTIFACT_AUTH_TOKEN ${CODEARTIFACT_AUTH_TOKEN}

RUN git config --global --add safe.directory /home/<USER>/src
RUN mkdir -p /home/<USER>/src/app/src/main/resources/certificates
RUN echo "${GRPC_CRT}" >> /home/<USER>/src/app/src/main/resources/certificates/server.crt
RUN echo "${GRPC_CRT_KEY}" >> /home/<USER>/src/app/src/main/resources/certificates/server.key
RUN chown -R gradle:gradle /home/<USER>/src/app/src/main/resources
RUN gradle build --no-daemon -x test

FROM ${RUN_BASEIMAGE}
ARG GRPC_CRT
ARG ENV_VAR
ENV ENV_VAR=$ENV_VAR
ARG APM_AGENT_VERSION
ENV APM_AGENT_VERSION=${APM_AGENT_VERSION}

EXPOSE 8080 9090
WORKDIR /app

RUN echo "${GRPC_CRT}" >> server.crt
RUN $JAVA_HOME/bin/keytool -import -trustcacerts -alias core-service -file server.crt -keystore $JAVA_HOME/lib/security/cacerts --storepass changeit -noprompt

COPY --from=build /home/<USER>/src/app/build/libs/*.jar /app/application.jar

RUN curl -Lo elastic-apm-agent.jar "https://search.maven.org/remotecontent?filepath=co/elastic/apm/elastic-apm-agent/${APM_AGENT_VERSION}/elastic-apm-agent-${APM_AGENT_VERSION}.jar"
COPY ./entrypoint.sh .
RUN chmod +x entrypoint.sh
ENTRYPOINT ["./entrypoint.sh"]

