syntax = "proto3";

package com.multiplier.integration.schema;


message GetIntegrationFromExternalCompanyIdAndPlatformNameRequest {
    string platformCompanyId = 1;
    string platformName = 2;
}

message GetIntegrationFromExternalCompanyIdAndPlatformNameResponse {
    int64 companyId = 1;
}

service CustomerIntegrationService {
    rpc getIntegrationFromExternalCompanyIdAndPlatformName(GetIntegrationFromExternalCompanyIdAndPlatformNameRequest) returns (GetIntegrationFromExternalCompanyIdAndPlatformNameResponse) {}
}