name: Deploy to Production [Rollback]
on:
  workflow_dispatch:
    tags:
      - '**'

##
# ECR_REPOSITORY: prd-app-tech-customerintegrationservice-ecr
# container-name: customerIntegrationService
# service: customerIntegrationService

jobs:
 deploy:
    # only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: production
      service_name: customerIntegrationService
      ecr_repository: prd-app-tech-customerintegrationservice-ecr
      allow_rollback: true
