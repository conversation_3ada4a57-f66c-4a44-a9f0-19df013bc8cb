name: AWS

on:
  workflow_call:
    outputs:
      CODEARTIFACT_AUTH_TOKEN:
        value: ${{ jobs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}

permissions:
  id-token: write
  contents: read

jobs:
  aws:
    name: AWS CodeArtifact Token
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}:role/prd-apse1-github-workflow-iamrole
          aws-region: ap-southeast-1
          role-duration-seconds: 3600

      - name: Fetch AWS CodeArtifact Token
        run: echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain multiplier-artifacts --domain-owner ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }} --query authorizationToken --output text)" >> $GITHUB_ENV
    outputs:
      CODEARTIFACT_AUTH_TOKEN: ${{ env.CODEARTIFACT_AUTH_TOKEN }}
