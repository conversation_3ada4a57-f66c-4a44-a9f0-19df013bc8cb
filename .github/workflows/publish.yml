name: Publish to CodeArtifact

on:
  release:
    types: [ published ]

run-name: Publish to CodeArtifact [${{ github.ref }}]

jobs:
  aws:
    uses: ./.github/workflows/aws.yml
    secrets: inherit

  deploy:
    if: startsWith(github.ref, 'refs/tags/')
    name: Publish
    runs-on: ubuntu-latest
    needs: [ aws ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: zulu
          java-version: 17

      - name: Publish
        uses: gradle/gradle-build-action@v2
        with:
          arguments: publish -x check --info