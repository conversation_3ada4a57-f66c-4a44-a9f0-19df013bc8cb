name: PR build and Sonar analysis
on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]
permissions:
  id-token: write
  contents: read
jobs:
  build:
    name: PR & Sonar Build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          fetch-depth: 0  # Shallow clones should be disabled for a better relevancy of analysis
      - name: Set up JDK 17
        uses: actions/setup-java@v1
        with:
          java-version: 17

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}:role/prd-apse1-github-workflow-iamrole
          aws-region: ap-southeast-1
          role-duration-seconds: 3600

      - name: Fetch AWS CodeArtifact Token
        run: echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain multiplier-artifacts --domain-owner ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }} --query authorizationToken --output text)" >> $GITHUB_ENV

      - name: Cache SonarCloud packages
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle') }}
          restore-keys: ${{ runner.os }}-gradle

      - name: Build and analyze
        if: ${{ github.event_name == 'pull_request' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}  # Needed to get PR information, if any
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: ./gradlew build jacocoTestReport sonarqube --info -Dorg.gradle.jvmargs=-Xmx3584m

      #integraion tests removed since initially new services does not have jpaIntTest implemented
      #jpaIntTest will be enable once implemented
      - name: Build, run integration test and analyze
        if: ${{ github.event_name == 'push' && github.ref_name == 'main' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: ./gradlew build jacocoTestReport sonarqube --info -Dorg.gradle.jvmargs=-Xmx3584m
