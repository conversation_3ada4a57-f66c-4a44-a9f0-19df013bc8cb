name: Deploy to release
on:
  workflow_dispatch:
    tags:
      - '**'

##
# ECR_REPOSITORY: rel-app-tech-customerintegrationservice-ecr
# container-name: customerIntegrationService
# service: customerIntegrationService

jobs:
  deploy:
    # only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: release
      service_name: customerIntegrationService
      ecr_repository: rel-app-tech-customerintegrationservice-ecr
