{"employeeId": "profile.id", "firstName": "profile.firstName", "lastName": "profile.lastName", "email": "contactInfo.personalEmails[0]", "gender": "profile.gender", "position": "orgStructure.designation", "startOn": "profile.startDate", "endOn": "profile.terminationDate", "countryState": "locations.workAddress.state", "phoneNumber": "contactInfo.phones[0].number", "workEmail": "profile.workEmail", "contract.status": "profile.employmentStatus", "term": "profile.terminationDate", "address.city": "locations.presentAddress.city", "address.country": "locations.presentAddress.country", "address.line1": "locations.presentAddress.addressLine1", "address.line2": "locations.presentAddress.addressLine2", "address.postalCode": "locations.presentAddress.zipCode", "address.state": "locations.presentAddress.state", "address.zipcode": "locations.presentAddress.zipCode", "albanianIdentityCard": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "beneficiaryName": "dependents[0].first<PERSON>ame", "beneficiaryRelationship": "dependents[0].relation", "beneficiaryNationalId": "customFields.fields.beneficiaryNationalId", "bpjsNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "burgerServiceNummer": "employeeIdentificationData[type=OTHERS].identificationNumber", "citizenCard": "employeeIdentificationData[type=OTHERS].identificationNumber", "cityOfWork": "locations.workAddress.city", "commercialRegistrationNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "corCode": "customFields.fields.corCode", "dateOfBirth": "profile.birthDate", "disabilityDegree": "customFields.fields.disabilityDegree", "eiNit": "employeeIdentificationData[type=OTHERS].identificationNumber", "employeeCprNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "ethnicity": "customFields.fields.ethnicity", "fatherHusbandName": "dependents[relation=FATHER].firstName", "fathersName": "dependents[relation=FATHER].firstName", "fiscalCode": "employeeIdentificationData[type=OTHERS].identificationNumber", "gibraltarIdentityCard": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "hdmfNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "healthInsuranceNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "idCardGeorgia": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "identificationNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "identificationNumberForForeigner": "employeeIdentificationData[type=PASSPORT].identificationNumber", "identityProof": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "idIssueDate": "customFields.fields.idIssueDate", "idIssuePlace": "customFields.fields.idIssuePlace", "idIssuingBody": "customFields.fields.idIssuingBody", "immigrationStatus": "customFields.fields.immigrationStatus", "incomeTaxNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "individualIdentificationNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "inssNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "internalPassportOfRussia": "employeeIdentificationData[type=PASSPORT].identificationNumber", "isssNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "khmerIdentityCard": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "longTermVisa": "customFields.fields.longTermVisa", "maritalStatus": "profile.maritalStatus", "nationalHealthFundBranch": "customFields.fields.nationalHealthFundBranch", "nationalId": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalIdCard": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalIdCardNo": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalIdentificationNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalIdentityCard": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalIdIssuedDate": "customFields.fields.nationalIdIssuedDate", "nationalIdNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalInsuranceId": "employeeIdentificationData[type=OTHERS].identificationNumber", "nationalInsuranceNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "nationality": "locations.presentAddress.country", "NationalRegisterNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nationalTaxRegistryNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "nid": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "nitNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "npwpCardNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "nssfNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "nuitNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "numericPersonal": "employeeIdentificationData[type=OTHERS].identificationNumber", "numericPersonalIssueDate": "customFields.fields.numericPersonalIssueDate", "nupNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "panNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "passportIssuedDate": "customFields.fields.passportIssuedDate", "passportNo": "employeeIdentificationData[type=PASSPORT].identificationNumber", "passportNoOrDrivingLicense": "employeeIdentificationData[type=PASSPORT].identificationNumber", "passportNumber": "employeeIdentificationData[type=PASSPORT].identificationNumber", "pensionFundNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "pensionsAffiliateNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "performanceBonusMaxLimitPercentage": "customFields.fields.performanceBonusMaxLimitPercentage", "personalCard": "employeeIdentificationData[type=OTHERS].identificationNumber", "personalIdentificationNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "peselNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "philHealthNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "placeOfBirth": "customFields.fields.placeOfBirth", "race": "customFields.fields.race", "religion": "customFields.fields.religion", "residenceCard": "customFields.fields.residenceCard", "residentPermit": "employeeIdentificationData[type=OTHERS].identificationNumber", "residentPermitNo": "employeeIdentificationData[type=OTHERS].identificationNumber", "residentRegistrationNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "socialInsuranceNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "socialSecurityNumber": "employeeIdentificationData[type=NATIONAL_ID].identificationNumber", "taxCode": "customFields.fields.taxCode", "taxIdentification": "employeeIdentificationData[type=OTHERS].identificationNumber", "taxIdentificationNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "taxIdNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "taxOfficeBranch": "customFields.fields.taxOfficeBranch", "taxpayerID": "employeeIdentificationData[type=OTHERS].identificationNumber", "taxPayerIdentificationNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "taxpayerIdNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "tinNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "uanNumberExists": "employeeIdentificationData[type=OTHERS].identificationNumber", "uanNumberValue": "employeeIdentificationData[type=OTHERS].identificationNumber", "uniformCivilNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "uniqueMasterCitizenNumber": "employeeIdentificationData[type=OTHERS].identificationNumber", "workLocation": "locations.workAddress.city", "workPassNumber": "customFields.fields.workPassNumber", "workPassType": "customFields.fields.workPassType", "workPermit": "employeeIdentificationData[type=OTHERS].identificationNumber", "workPermitNo": "customFields.fields.workPermitNo", "workVisa": "customFields.fields.workVisa", "workVisaNo": "customFields.fields.workVisaNo", "payrollFrequency": "compensation.fixed[0].frequency", "rateFrequency": "compensation.fixed[0].payPeriod", "basePay": "compensation.fixed[0].amount"}