<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<style>
  @font-face {
    font-family: 'Helvetica';
    font-style: normal;
    font-weight: 400;
    mso-font-alt: 'Verdana';

  }

  * {
    font-family: 'Helvetica', Verdana;
  }
</style>
<html dir="ltr" lang="en">

  <head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
    <meta name="x-apple-disable-message-reformatting" />
  </head>

  <body style="color:rgb(14,5,0);background-color:rgb(244,243,243);border-radius:4px">
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="height:2.5rem">
      <tbody>
        <tr>
          <td></td>
        </tr>
      </tbody>
    </table>
    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background-color:rgb(255,255,255);max-width:600px;box-shadow:0 0 #0000, 0 0 #0000, 0 4px 6px -1px rgb(0,0,0,0.1), 0 2px 4px -2px rgb(0,0,0,0.1)">
      <tbody>
        <tr>
          <td>
            <style>
              @font-face {
                font-family: 'Helvetica';
                font-style: normal;
                font-weight: 400;
                mso-font-alt: 'Verdana';

              }

              * {
                font-family: 'Helvetica', Verdana;
              }
            </style>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background-color:rgb(247,105,24);border-top-left-radius:4px;border-top-right-radius:4px;padding-top:1rem;padding-bottom:1rem">
              <tbody style="width:100%">
                <tr style="width:100%">
                  <td align="center" data-id="__react-email-column">
                    <style>
                      @font-face {
                        font-family: 'Helvetica';
                        font-style: normal;
                        font-weight: 400;
                        mso-font-alt: 'Verdana';

                      }

                      * {
                        font-family: 'Helvetica', Verdana;
                      }
                    </style><img alt="Multiplier" src="https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/multiplier-logo-inverse-v2.png" style="display:block;outline:none;border:none;text-decoration:none;width:8rem" />
                  </td>
                </tr>
              </tbody>
            </table>
            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding-left:2rem;padding-right:2rem;padding-top:2rem;padding-bottom:2rem">
              <tbody>
                <tr>
                  <td>
                    <style>
                      @font-face {
                        font-family: 'Helvetica';
                        font-style: normal;
                        font-weight: 400;
                        mso-font-alt: 'Verdana';

                      }

                      * {
                        font-family: 'Helvetica', Verdana;
                      }
                    </style>
                    <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background-color:rgb(255,255,255);margin-bottom:1.5rem">
                      <tbody>
                        <tr>
                          <td>
                          <td data-id="__react-email-column">
                            <table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation">
                              <tbody style="width:100%">
                                <tr style="width:100%">
                                  <div th:style="${#ctx.containsVariable(&#x27;showCompanyLogo&#x27;) 
      ? showCompanyLogo 
      : !#ctx.containsVariable(&#x27;companyLogoLink&#x27;) 
      ? false
      : companyLogoLink != null
      ? #strings.startsWith(companyLogoLink, &#x27;http&#x27;) 
      : false} ? &#x27;display: block;&#x27; : &#x27;display: none;&#x27;"><img th:src="${companyLogoLink}" th:alt="${companyName}" alt="Love Bonito Logo" src="/static/multiplier/logo/love-bonito-logo.png" style="display:block;outline:none;border:none;text-decoration:none;height:2rem;width:auto;margin-right:1rem;border-radius:0.5rem"/></div></tr></tbody></table></td></td></tr></tbody></table><p style="font-size:14px;line-height:24px;margin:16px 0">Hi <span th:text="${adminName}">$adminName</span>,</p><p style="font-size:14px;line-height:24px;margin:16px 0">This is to inform you that your<!-- --> <span th:text="${platformName}">$platformName</span> gp sync with id <span th:text="${syncId}">$syncId</span> is stopped after being in progress for too long.</p><p style="font-size:14px;line-height:24px;margin:16px 0">If you have any questions, please feel free to &#x27;Reply to all&#x27; to this email.</p><p style="font-size:14px;line-height:24px;margin:16px 0">Sincerely,<br/>Multiplier Team</p></td></tr></tbody></table><style>
    @font-face {
      font-family: 'Helvetica';
      font-style: normal;
      font-weight: 400;
      mso-font-alt: 'Verdana';
      
    }

    * {
      font-family: 'Helvetica', Verdana;
    }
  </style><table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="background-color:rgb(247,105,24);padding-left:2rem;padding-right:2rem;border-bottom-right-radius:4px;border-bottom-left-radius:4px"><tbody><tr><td><p style="font-size:0.875rem;line-height:1.25rem;margin:16px 0;color:rgb(255,255,255);padding-top:1rem"><span th:text="${contractType == &#x27;CONTRACTOR&#x27;
      ? &quot;You are receiving this email because you made an account or we invited you to join the Multiplier platform. For any support, please reach out to &quot; 
      : &quot;You are receiving this email because you made an account or your employer has invited you to join the Multiplier platform. For any support, please reach out to &quot;}">You are receiving this email because you made an account or your employer has invited you to join the Multiplier platform. For any support, please reach out to<!-- --> </span><a href="mailto:<EMAIL>" style="color:rgb(255,255,255);text-decoration:none;text-decoration-line:underline" target="_blank"><EMAIL></a></p><table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="padding-bottom:1.5rem"><tbody><tr><td><td data-id="__react-email-column"><style>
    @font-face {
      font-family: 'Helvetica';
      font-style: normal;
      font-weight: 400;
      mso-font-alt: 'Verdana';
      
    }

    * {
      font-family: 'Helvetica', Verdana;
    }
  </style><img alt="Multiplier" src="https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/multiplier-logo-inverse-v2.png" style="display:block;outline:none;border:none;text-decoration:none;width:6rem"/></td><td data-id="__react-email-column" style="width:66.666667%"><td data-id="__react-email-column"><a href="https://www.facebook.com/UseMultiplier" style="color:rgb(247,105,24);text-decoration:none;font-weight:600" target="_blank"><img alt="Multiplier Facebook page" src="https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/facebook_logo-v2.png" style="display:block;outline:none;border:none;text-decoration:none;width:1.5rem;height:1.5rem;margin:0.5rem"/></a></td><td data-id="__react-email-column"><a href="https://www.linkedin.com/company/multipliertechnologies/" style="color:inherit;text-decoration:none" target="_blank"><img alt="Multiplier LinkedIn page" src="https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/linkedin_logo-v2.png" style="display:block;outline:none;border:none;text-decoration:none;width:1.5rem;height:1.5rem;margin:0.5rem"/></a></td><td data-id="__react-email-column"><a href="https://x.com/usemultiplier" style="color:rgb(247,105,24);text-decoration:none;font-weight:600" target="_blank"><img alt="Multiplier X account" src="https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/x_logo.png" style="display:block;outline:none;border:none;text-decoration:none;width:1.5rem;height:1.5rem;margin:0.5rem"/></a></td><td data-id="__react-email-column"><a href="https://www.instagram.com/usemultiplier" style="color:rgb(247,105,24);text-decoration:none;font-weight:600" target="_blank"><img alt="Multiplier Instagram account" src="https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/instagram_logo.png" style="display:block;outline:none;border:none;text-decoration:none;width:1.5rem;height:1.5rem;margin:0.5rem"/></a></td></td></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><table align="center" width="100%" border="0" cellPadding="0" cellSpacing="0" role="presentation" style="height:2.5rem"><tbody><tr><td></td></tr></tbody></table></body></html>