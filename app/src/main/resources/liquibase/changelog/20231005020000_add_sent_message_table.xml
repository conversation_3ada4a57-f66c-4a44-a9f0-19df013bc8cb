<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231005010000_3" author="petar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="sent_message" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create sent_message table</comment>

        <createTable tableName="sent_message" schemaName="customer_integration">
            <column name="integration_id" type="VARCHAR(255)" />
            <column name="sync_id" type="VARCHAR(255)" />
            <column name="data" type="JSONB"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
