<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet author="ryanhuynh" id="20231121190953-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="platform_employee_data" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create internal employee data table</comment>

        <createTable schemaName="customer_integration" tableName="platform_employee_data">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="platform_employee_data_pk"/>
            </column>
            <column name="integration_id" type="BIGINT"/>
            <column name="employee_id" type="VARCHAR(255)"/>
            <column name="employee_data" type="JSONB"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
