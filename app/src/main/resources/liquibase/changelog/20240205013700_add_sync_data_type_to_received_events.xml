<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240205013700-1" author="ryan" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="received_events" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="received_events" columnName="sync_data_type" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add sync_data_type column to received_events table</comment>

        <addColumn tableName="received_events" schemaName="customer_integration">
            <column name="sync_data_type" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>