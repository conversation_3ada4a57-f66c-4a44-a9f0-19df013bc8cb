<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231220100000-1" author="ryan" dbms="postgresql">
        <preConditions>
            <tableExists tableName="platform_employee_data" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="platform_employee_data" columnName="origin" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add origin column to platform_employee_data table</comment>

        <addColumn tableName="platform_employee_data" schemaName="customer_integration">
            <column name="origin" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>