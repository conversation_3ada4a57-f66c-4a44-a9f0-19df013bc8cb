<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="**************-1" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as column exists">
            <not>
                <columnExists
                        columnName="external_account"
                        tableName="accounting_line_item_mapping_aud"
                        schemaName="customer_integration"
                />
            </not>

        </preConditions>
        <comment>Adding external account column to audit table</comment>
        <addColumn tableName="accounting_line_item_mapping_aud" schemaName="customer_integration">
            <column name="external_account"
                    type="JSONB">
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>
