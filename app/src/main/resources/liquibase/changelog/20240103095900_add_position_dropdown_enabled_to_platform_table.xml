<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240103095900-1" author="beeyen" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="platform" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="platform" columnName="is_position_dropdown_enabled" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add is_position_dropdown_enabled column to platform table</comment>

        <addColumn tableName="platform" schemaName="customer_integration">
            <column name="is_position_dropdown_enabled" type="BOOLEAN" defaultValueBoolean="true"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>