<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240724000000-2" author="petar" dbms="postgresql">

        <createTable tableName="timeoff_events" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="timeoff_events_pkey" />
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="timeoff_type" type="VARCHAR(255)" />
            <column name="external_id" type="VARCHAR(255)" />
            <column name="internal_id" type="VARCHAR(255)" />
            <column name="integration_id" type="VARCHAR(255)" />
            <column name="timeoff_data" type="jsonb" />
            <column name="should_process" type="BOOLEAN"/>
            <column name="processed" type="BOOLEAN"/>

        </createTable>

    </changeSet>
</databaseChangeLog>
