<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240111110100-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <tableExists tableName="platform_employee_data" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="platform_employee_data" columnName="is_deleted" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add origin column to platform_employee_data table</comment>

        <addColumn tableName="platform_employee_data" schemaName="customer_integration">
            <column name="is_deleted" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>