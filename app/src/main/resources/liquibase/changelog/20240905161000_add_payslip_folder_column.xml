<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240905161000-1" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="document_folders" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create document folders table</comment>

        <createTable tableName="document_folders" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="document_folders_pkey" />
            </column>
            <column name="folder_id" type="VARCHAR(255)" />
            <column name="folder_label" type="VARCHAR(255)" />
            <column name="folder_type" type="VARCHAR(255)" />
            <column name="is_active" type="BOOLEAN" />
            <column name="integration_id" type="BIGINT" />
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

    </changeSet>

    <changeSet id="20240905161000-2" author="beeyen" dbms="postgresql">
        <sql>
            ALTER TABLE "customer_integration"."document_folders" ADD CONSTRAINT chk_document_folders_type_enum CHECK (folder_type IN ('PAYSLIP'));
        </sql>
    </changeSet>

    <changeSet id="20240905161000-3" author="beeyen" dbms="postgresql">
        <addForeignKeyConstraint
            baseTableName="document_folders"
            baseColumnNames="integration_id"
            referencedTableName="company_integration"
            referencedColumnNames="id"
            constraintName="fk_document_folders"
            baseTableSchemaName="customer_integration"
            referencedTableSchemaName="customer_integration"
            onDelete="CASCADE"
        />
    </changeSet>
</databaseChangeLog>
