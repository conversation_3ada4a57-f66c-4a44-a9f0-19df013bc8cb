<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250204190000-1" author="minh" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sqlCheck expectedResult="bigint">
                    SELECT data_type
                    FROM information_schema.columns
                    WHERE table_schema = 'customer_integration' AND table_name = 'timeoff_events'
                    AND column_name = 'integration_id';
                </sqlCheck>
            </not>
        </preConditions>
        <modifyDataType
            schemaName="customer_integration"
            tableName="timeoff_events"
            columnName="integration_id"
            newDataType="bigint"
        />
    </changeSet>


</databaseChangeLog>
