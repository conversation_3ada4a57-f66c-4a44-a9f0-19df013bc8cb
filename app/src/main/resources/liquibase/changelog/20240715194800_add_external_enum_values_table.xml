<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240715194800-1" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="external_platform_values" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create legal entity mapping table</comment>

        <createTable tableName="external_platform_values" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="external_platform_values_pkey" />
            </column>
            <column name="field_id" type="VARCHAR(255)" />
            <column name="mapped_key" type="VARCHAR(255)" />
            <column name="integration_id" type="BIGINT" />
            <column name="values" type="_VARCHAR(255)" />
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

    </changeSet>

    <changeSet id="20240715194800-2" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="received_events" columnName="is_entity_enabled" schemaName="customer_integration"/>
                <columnExists tableName="received_events" columnName="entity_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns is_entity_enabled and entity_id to received_events table</comment>

        <addColumn tableName="received_events" schemaName="customer_integration">
            <column name="is_entity_enabled" type="BOOLEAN" defaultValue="true">
                <constraints nullable="false" />
            </column>
            <column name="entity_id" type="BIGINT"/>
        </addColumn>

        <update tableName="received_events" schemaName="customer_integration">
            <column name="is_entity_enabled" valueBoolean="true"/>
            <where>is_entity_enabled IS NULL</where>
        </update>
    </changeSet>

</databaseChangeLog>
