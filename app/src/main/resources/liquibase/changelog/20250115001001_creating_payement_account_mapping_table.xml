<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="**************-1" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as column exists">
            <not>
                <tableExists
                        tableName="accounting_external_payment_account_mapping"
                        schemaName="customer_integration"
                />
            </not>
        </preConditions>
        <comment>Adding accounting external payment account mapping table.</comment>
        <createTable schemaName="customer_integration" tableName="accounting_external_payment_account_mapping">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="external_payment_account" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-2" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as column exists">
            <not>
                <tableExists
                        tableName="accounting_external_payment_account_mapping-aud"
                        schemaName="customer_integration"
                />
            </not>
        </preConditions>
        <comment>Adding accounting external payment account mapping audit table.</comment>
        <createTable schemaName="customer_integration" tableName="accounting_external_payment_account_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_external_payment_account_mapping_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_external_payment_account_mapping_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="external_payment_account" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-3" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as column exists">
            <not>
                <sequenceExists
                        sequenceName="accounting_external_payment_account_mapping_seq"
                        schemaName="customer_integration"
                />
            </not>
        </preConditions>
        <comment>Adding sequence for accounting_external_payment_account_mapping_seq.</comment>
        <createSequence
                sequenceName="accounting_external_payment_account_mapping_seq"
                schemaName="customer_integration"
                cacheSize="1"
                cycle="false"
                dataType="BIGINT"
                incrementBy="1"
                maxValue="9223372036854775807"
                minValue="1"
                startValue="1"
        />
    </changeSet>

    <changeSet id="**************-4" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped Associating accounting_external_payment_account_mapping_seq with id">
            <sequenceExists schemaName="customer_integration" sequenceName="accounting_external_payment_account_mapping_seq"/>
            <tableExists schemaName="customer_integration" tableName="accounting_external_payment_account_mapping" />
        </preConditions>
        <comment>Associate accounting_external_payment_account_mapping_seq with id </comment>
        <sql>
            ALTER TABLE customer_integration.accounting_external_payment_account_mapping
                ALTER COLUMN id SET DEFAULT nextval('customer_integration.accounting_external_payment_account_mapping_seq');
        </sql>
    </changeSet>


</databaseChangeLog>
