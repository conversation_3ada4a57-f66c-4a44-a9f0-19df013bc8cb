<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250103009000-1" author="Ganesh Jadhav" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists sequenceName="revinfo_seq"/>
            </not>
        </preConditions>
        <comment>Creating hibernate sequence</comment>
        <createSequence
                sequenceName="revinfo_seq"
                schemaName="public"
                maxValue="9223372036854775807"
                startValue="1"
                incrementBy="50"/>
    </changeSet>

    <changeSet author="Ganesh Jadhav" id="20250103009000-2" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="public" tableName="revinfo"/>
            </not>
        </preConditions>
        <createTable schemaName="public" tableName="revinfo">
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="revinfo_pkey"/>
            </column>
            <column name="revtstmp" type="BIGINT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
