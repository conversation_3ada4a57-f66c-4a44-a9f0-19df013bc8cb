<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231005010000" author="petar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="received_events" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create received_events table</comment>

        <createTable tableName="received_events" schemaName="customer_integration">
            <column name="event_id" type="VARCHAR(255)">
                <constraints primaryKey="true" primaryKeyName="received_events_pkey" nullable="false"/>
            </column>
            <column name="sync_id" type="VARCHAR(255)" />
            <column name="integration_id" type="VARCHAR(255)" />
            <column name="event_type" type="VARCHAR(255)" />
            <column name="identifier_value" type="VARCHAR(255)" />
            <column name="received_time" type="DATE" />
            <column name="data" type="JSONB"/>
            <column name="confirmed_by_user" type="BOOLEAN" />
            <column name="processed" type="BOOLEAN" />
        </createTable>
    </changeSet>
</databaseChangeLog>