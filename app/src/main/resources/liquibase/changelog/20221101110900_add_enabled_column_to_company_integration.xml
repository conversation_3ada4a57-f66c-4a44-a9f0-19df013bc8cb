<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="datnguyen" id="20221101110900-1"  dbms="postgresql">
        <preConditions onFail="MARK_RAN" onFailMessage="Skip change because of already applied...">
            <not>
                <columnExists columnName="enabled"
                              schemaName="customer_integration"
                              tableName="company_integration"/>
            </not>
        </preConditions>
        <comment>Add enabled column into company_integration table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="enabled" type="boolean" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>

    <changeSet author="datnguyen" id="20221101110900-2"  dbms="postgresql">
        <comment>Update old record in company_integration to enabled true </comment>
        <sql dbms="postgresql">
            UPDATE "customer_integration"."company_integration" SET enabled = true;
        </sql>
    </changeSet>

</databaseChangeLog>