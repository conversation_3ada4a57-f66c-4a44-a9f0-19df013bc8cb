<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240820132600-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="external_platform_values" columnName="is_deleted" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns is_deleted to external_platform_values table</comment>

        <addColumn tableName="external_platform_values" schemaName="customer_integration">
            <column name="is_deleted" type="boolean" defaultValueBoolean="false"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
