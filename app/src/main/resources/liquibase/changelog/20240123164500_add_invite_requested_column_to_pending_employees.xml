<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240123164500-1" author="ryan" dbms="postgresql">
        <preConditions onFail="MARK_RAN" onFailMessage="Skip change because of already applied...">
            <not>
                <columnExists columnName="invite_requested"
                              schemaName="customer_integration"
                              tableName="pending_employee"/>
            </not>
        </preConditions>
        <comment>Add invite_requested columns to pending_employee table</comment>
        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="invite_requested" type="boolean" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>