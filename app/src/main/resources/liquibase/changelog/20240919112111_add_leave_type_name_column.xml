<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240919112111-1" author="ryan" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="leave_types_mapping" columnName="internal_type_name" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns internal_type_name to leave_types_mapping</comment>

        <addColumn tableName="leave_types_mapping" schemaName="customer_integration">
            <column name="internal_type_name" type="VARCHAR(255)"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
