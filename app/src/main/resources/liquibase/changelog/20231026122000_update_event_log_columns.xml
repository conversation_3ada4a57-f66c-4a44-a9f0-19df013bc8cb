<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231026122000-1" author="ashish" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="event_log" schemaName="customer_integration"/>
            <columnExists tableName="event_log" columnName="error_message" schemaName="customer_integration"/>
            <columnExists tableName="event_log" columnName="event_payload" schemaName="customer_integration"/>
        </preConditions>
        <comment>Alter error_message from JSONB to TEXT</comment>

        <modifyDataType tableName="event_log" columnName="error_message" newDataType="TEXT" schemaName="customer_integration"/>
        <modifyDataType tableName="event_log" columnName="event_payload" newDataType="TEXT" schemaName="customer_integration"/>
    </changeSet>
</databaseChangeLog>