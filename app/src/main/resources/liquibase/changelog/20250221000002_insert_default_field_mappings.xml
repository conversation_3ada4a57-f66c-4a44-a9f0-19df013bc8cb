<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="**************-1" author="HuyNguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>inserts default field mappings</comment>
        <sql>
            insert into customer_integration.field_mapping_configuration(key, value,
                                                                         type, is_deleted,
                                                                         created_on, updated_on,
                                                                         created_by, updated_by,
                                                                         enum_mappings, platform_id)
            select 'bank.accountNumber',
                   'bankAccounts[0].accountNumber',
                   'DEFAULT',
                   false,
                   now(),
                   now(),
                   null,
                   null,
                   null,
                   null
            where not exists(select id
                             from customer_integration.field_mapping_configuration
                             where key = 'bank.accountNumber'
                               and type = 'DEFAULT'
                               and platform_id is null);

            insert into customer_integration.field_mapping_configuration(key, value,
                                                                         type, is_deleted,
                                                                         created_on, updated_on,
                                                                         created_by, updated_by,
                                                                         enum_mappings, platform_id)
            select 'bank.bankName',
                   'bankAccounts[0].bankName',
                   'DEFAULT',
                   false,
                   now(),
                   now(),
                   null,
                   null,
                   null,
                   null
            where not exists(select id
                             from customer_integration.field_mapping_configuration
                             where key = 'bank.bankName'
                               and type = 'DEFAULT'
                               and platform_id is null);

            insert into customer_integration.field_mapping_configuration(key, value,
                                                                         type, is_deleted,
                                                                         created_on, updated_on,
                                                                         created_by, updated_by,
                                                                         enum_mappings, platform_id)
            select 'bank.localBankCode',
                   'bankAccounts[0].routingInfo[type=BANK_IDENTIFICATION_CODE].number',
                   'DEFAULT',
                   false,
                   now(),
                   now(),
                   null,
                   null,
                   null,
                   null
            where not exists(select id
                             from customer_integration.field_mapping_configuration
                             where key = 'bank.localBankCode'
                               and type = 'DEFAULT'
                               and platform_id is null);

            insert into customer_integration.field_mapping_configuration(key, value,
                                                                         type, is_deleted,
                                                                         created_on, updated_on,
                                                                         created_by, updated_by,
                                                                         enum_mappings, platform_id)
            select 'bank.swiftCode',
                   'bankAccounts[0].routingInfo[type=SWIFT_CODE].number',
                   'DEFAULT',
                   false,
                   now(),
                   now(),
                   null,
                   null,
                   null,
                   null
            where not exists(select id
                             from customer_integration.field_mapping_configuration
                             where key = 'bank.swiftCode'
                               and type = 'DEFAULT'
                               and platform_id is null);

            insert into customer_integration.field_mapping_configuration(key, value,
                                                                         type, is_deleted,
                                                                         created_on, updated_on,
                                                                         created_by, updated_by,
                                                                         enum_mappings, platform_id)
            select 'bank.routingNumber',
                   'bankAccounts[0].routingInfo[type=ROUTING_NUMBER].number',
                   'DEFAULT',
                   false,
                   now(),
                   now(),
                   null,
                   null,
                   null,
                   null
            where not exists(select id
                             from customer_integration.field_mapping_configuration
                             where key = 'bank.routingNumber'
                               and type = 'DEFAULT'
                               and platform_id is null);
        </sql>
    </changeSet>

    <changeSet id="**************-2" author="HuyNguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>update enum value default mapping</comment>
        <sql>
            update customer_integration.field_mapping_configuration
            set enum_mappings = '{
              "DAILY": "DAILY",
              "ANNUAL": "ANNUALLY",
              "HOURLY": "HOURLY",
              "WEEKLY": "WEEKLY",
              "MONTHLY": "MONTHLY",
              "BI_WEEKLY": "BI_WEEKLY",
              "QUARTERLY": "QUARTERLY",
              "SEMI_MONTHLY": "SEMIMONTHLY"
            }'
            where platform_id is null
              and key = 'rateFrequency';

            update customer_integration.field_mapping_configuration
            set enum_mappings = '{
              "DAILY": "DAILY",
              "ANNUAL": "ANNUALLY",
              "HOURLY": "HOURLY",
              "WEEKLY": "WEEKLY",
              "MONTHLY": "MONTHLY",
              "BI_WEEKLY": "BIWEEKLY",
              "QUARTERLY": "QUARTERLY",
              "SEMI_MONTHLY": "SEMIMONTHLY"
            }'
            where platform_id is null
              and key = 'payrollFrequency';
        </sql>
    </changeSet>
</databaseChangeLog>
