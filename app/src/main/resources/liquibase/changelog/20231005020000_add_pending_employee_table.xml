<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231005010000_2" author="petar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="pending_employee" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create pending_employee table</comment>

        <createTable tableName="pending_employee" schemaName="customer_integration">
            <column name="integration_id" type="VARCHAR(255)" />
            <column name="sync_id" type="VARCHAR(255)" />
            <column name="identifier" type="VARCHAR(255)" />
            <column name="data" type="JSONB"/>
            <column name="pending_import" type="BOOLEAN" />
            <column name="success" type="BOOLEAN" />
            <column name="pending_invite" type="BOOLEAN" />
        </createTable>
    </changeSet>
</databaseChangeLog>
