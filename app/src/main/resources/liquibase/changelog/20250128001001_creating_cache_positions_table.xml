<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250128001001-1" author="jezdimir.loncar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped as table exists">
            <not>
                <tableExists
                        tableName="cache_positions"
                        schemaName="customer_integration"
                />
            </not>
        </preConditions>

        <comment>Creating cache_positions table</comment>

        <createTable schemaName="customer_integration" tableName="cache_positions">
            <column name="company_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_cache_position"/>
            </column>
            <column name="platform_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_cache_position"/>
            </column>
            <column name="data" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>

        </createTable>
    </changeSet>
</databaseChangeLog>