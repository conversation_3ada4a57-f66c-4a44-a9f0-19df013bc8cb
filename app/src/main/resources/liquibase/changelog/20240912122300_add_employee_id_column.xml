<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240912122300-1" author="ryan" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="timeoff_events" columnName="employee_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns employee_id to timeoff_events</comment>

        <addColumn tableName="timeoff_events" schemaName="customer_integration">
            <column name="employee_id" type="VARCHAR(255)"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
