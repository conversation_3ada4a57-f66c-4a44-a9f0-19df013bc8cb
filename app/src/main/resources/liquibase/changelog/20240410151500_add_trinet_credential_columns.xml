<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="**************-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="company_integration" columnName="account_name" schemaName="customer_integration"/>
                <columnExists tableName="company_integration" columnName="external_company_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns account_name and external_company_id to company_integration table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="account_name" type="VARCHAR(255)"/>
            <column name="external_company_id" type="VARCHAR(255)"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
