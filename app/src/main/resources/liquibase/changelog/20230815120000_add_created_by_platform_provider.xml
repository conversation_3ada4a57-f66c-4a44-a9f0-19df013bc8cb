<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20230815120000-1" author="ashish" dbms="postgresql">
        <preConditions>
            <tableExists tableName="provider_platform" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="provider_platform" columnName="created_by" schemaName="customer_integration"/>
            </not>
            <not>
                <columnExists tableName="provider_platform" columnName="updated_by" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <addColumn tableName="provider_platform" schemaName="customer_integration">
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>