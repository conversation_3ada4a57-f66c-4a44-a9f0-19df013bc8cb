<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240916140000-1" author="minh" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="event_log_archive" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create Event Log Archive table</comment>

        <createTable tableName="event_log_archive" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="event_log_archive_pkey" />
            </column>
            <column name="event_id" type="VARCHAR(255)" />
            <column name="event_type" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="event_payload" type="TEXT"/>
            <column name="status" type="VARCHAR(30)"/>
            <column name="retries_done" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="retries_left" type="INT" defaultValue="3" >
                <constraints nullable="false"/>
            </column>
            <column name="error_message" type="TEXT"/>
            <column name="last_attempt" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="next_attempt" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="sync_id" type="VARCHAR(255)" />
            <column name="company_id" type="BIGINT"/>
            <column name="contract_id" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet id="20240916140000-2" author="minh" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="received_events_archive" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create received_events_archive table</comment>

        <createTable tableName="received_events_archive" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="event_id" type="VARCHAR(255)">
                <constraints primaryKey="true" primaryKeyName="received_events_archive_pkey" nullable="false"/>
            </column>
            <column name="sync_id" type="VARCHAR(255)" />
            <column name="integration_id" type="VARCHAR(255)" />
            <column name="event_type" type="VARCHAR(255)" />
            <column name="identifier_value" type="VARCHAR(255)" />
            <column name="received_time" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="data" type="JSONB"/>
            <column name="confirmed_by_user" type="BOOLEAN" />
            <column name="processed" type="BOOLEAN" />
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="sync_data_type" type="VARCHAR(255)"/>
            <column name="errors" type="TEXT"/>
            <column name="is_entity_enabled" type="BOOLEAN" defaultValue="true">
                <constraints nullable="false" />
            </column>
            <column name="entity_id" type="BIGINT"/>
            <column name="entity_country" type="varchar(10)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>