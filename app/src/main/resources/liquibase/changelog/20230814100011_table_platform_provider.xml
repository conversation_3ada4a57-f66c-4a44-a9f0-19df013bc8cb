<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20230814100011-1" author="ashish" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="provider_platform" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create provider platform junction table</comment>

        <createTable tableName="provider_platform" schemaName="customer_integration">
            <column name="platform_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="provider_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="status" type="VARCHAR(30)">
                <constraints nullable="false" />
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>

        <addForeignKeyConstraint baseColumnNames="provider_id"
                                 baseTableName="provider_platform" baseTableSchemaName="customer_integration"
                                 constraintName="fk_provider_id" deferrable="false" initiallyDeferred="false"
                                 onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id"
                                 referencedTableName="provider" referencedTableSchemaName="customer_integration"
                                 validate="true"/>

        <addForeignKeyConstraint baseColumnNames="platform_id"
                                 baseTableName="provider_platform" baseTableSchemaName="customer_integration"
                                 constraintName="fk_platform_id" deferrable="false" initiallyDeferred="false"
                                 onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id"
                                 referencedTableName="platform" referencedTableSchemaName="customer_integration"
                                 validate="true"/>

        <createIndex indexName="idx_provider_platform_status" tableName="provider_platform" schemaName="customer_integration" unique="true">
            <column name="provider_id" />
            <column name="platform_id" />
            <column name="status" />
        </createIndex>
    </changeSet>

</databaseChangeLog>