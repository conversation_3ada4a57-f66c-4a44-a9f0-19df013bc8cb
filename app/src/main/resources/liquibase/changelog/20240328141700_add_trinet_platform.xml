<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="20240328141700-1" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting TriNet into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'HRIS' and name = 'TriNet'
            </sqlCheck>
        </preConditions>
        <comment>Insert TriNet platform</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (18, 'HRIS', 'TriNet');
        </sql>
    </changeSet>

    <changeSet id="20240328141700-2" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [provider]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider
                WHERE name = 'TRINET'
            </sqlCheck>
        </preConditions>
        <comment>Insert TRINET provider</comment>
        <sql>
            INSERT INTO "customer_integration"."provider" ("id", "name")
            VALUES (3, 'TRINET');
        </sql>
    </changeSet>

</databaseChangeLog>