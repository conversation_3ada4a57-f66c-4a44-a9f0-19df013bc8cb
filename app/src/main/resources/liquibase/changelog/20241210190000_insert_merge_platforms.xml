<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="**************-1" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'ACCOUNTING' and name = 'Quickbooks'
            </sqlCheck>
        </preConditions>
        <comment>Insert ACCOUNTING platform Quickbooks</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (20, 'ACCOUNTING', 'Quickbooks');
        </sql>
    </changeSet>

    <changeSet id="**************-2" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [customer_integration]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider_platform
                WHERE platform_id = 20 and provider_id = 1
            </sqlCheck>
        </preConditions>
        <comment>Insert Quickbooks integration platform</comment>
        <sql>
            INSERT INTO customer_integration.provider_platform
                (platform_id, provider_id, status, created_on)
            VALUES
                (20, 1, 'ACTIVE', now());
        </sql>
    </changeSet>

    <changeSet id="**************-3" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'ACCOUNTING' and name = 'Xero'
            </sqlCheck>
        </preConditions>
        <comment>Insert ACCOUNTING platform Quickbooks</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (21, 'ACCOUNTING', 'Xero');
        </sql>
    </changeSet>

    <changeSet id="**************-4" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [customer_integration]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider_platform
                WHERE platform_id = 21 and provider_id = 1
            </sqlCheck>
        </preConditions>
        <comment>Insert Xero integration platform</comment>
        <sql>
            INSERT INTO customer_integration.provider_platform
            (platform_id, provider_id, status, created_on)
            VALUES
            (21, 1, 'ACTIVE', now());
        </sql>
    </changeSet>

</databaseChangeLog>