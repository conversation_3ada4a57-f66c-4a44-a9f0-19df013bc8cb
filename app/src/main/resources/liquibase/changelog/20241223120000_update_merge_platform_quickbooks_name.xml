<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="**************-1" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped updating table [platform]. Already updated...">
            <sqlCheck expectedResult="1">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE id = 20 and category = 'ACCOUNTING' and name = 'Quickbooks';
            </sqlCheck>
        </preConditions>
        <comment>Update ACCOUNTING platform Quickbooks name</comment>
        <sql>
            UPDATE "customer_integration"."platform"
            SET name = 'Quickbooks Online'
            WHERE id = 20 and category = 'ACCOUNTING' and name = 'Quickbooks';
        </sql>
    </changeSet>
</databaseChangeLog>