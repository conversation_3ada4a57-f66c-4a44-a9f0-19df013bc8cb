<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231005020000_1" author="petar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="sync" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create sync table</comment>

        <createTable tableName="sync" schemaName="customer_integration">
            <column name="sync_id" type="VARCHAR(255)">
                <constraints primaryKey="true" primaryKeyName="sync_pkey" nullable="false"/>
            </column>
            <column name="integration_id" type="VARCHAR(255)" />
            <column name="client_sync_id" type="VARCHAR(255)" />
            <column name="start_time" type="VARCHAR(255)" />
            <column name="end_time" type="VARCHAR(255)" />
            <column name="in_progress" type="BOOLEAN" />
            <column name="started_automatically" type="BOOLEAN" />
        </createTable>
    </changeSet>
</databaseChangeLog>
