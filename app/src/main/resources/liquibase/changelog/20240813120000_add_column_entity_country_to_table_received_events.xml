<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240813120000-1" author="minh" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="received_events" columnName="entity_country" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add column entity_country to received_events table</comment>

        <addColumn tableName="received_events" schemaName="customer_integration">
            <column name="entity_country" type="varchar(10)"/>
        </addColumn>
    </changeSet>

<!--    <changeSet id="20240813120000-2" author="minh" dbms="postgresql">-->
<!--        <comment>Drop column entity_id from received_events table</comment>-->
<!--        <dropColumn tableName="received_events" schemaName="customer_integration" columnName="entity_id"/>-->
<!--    </changeSet>-->

    <changeSet id="20240813120000-3" author="minh" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="legal_entity_mapping" columnName="entity_country" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add column entity_country to legal_entity_mapping table</comment>

        <addColumn tableName="legal_entity_mapping" schemaName="customer_integration">
            <column name="entity_country" type="varchar(10)"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>
