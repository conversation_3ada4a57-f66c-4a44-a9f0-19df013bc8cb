<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="20250306190000-1" author="Vidya" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'HRIS' and name = 'ADP WorkForceNow'
            </sqlCheck>
        </preConditions>
        <comment>Insert ADP WorkForceNow platform</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (22, 'HRIS', 'ADP WorkForceNow');
        </sql>
    </changeSet>

    <changeSet id="20250306190000-2" author="Vidya" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider_platform
                WHERE platform_id = 22 and provider_id = 2
            </sqlCheck>
        </preConditions>
        <comment>Insert ADP WorkForceNow integration platform</comment>
        <sql>
            INSERT INTO customer_integration.provider_platform
            (platform_id, provider_id, status, created_on)
            VALUES
            (22, 2, 'ACTIVE', now());
        </sql>
    </changeSet>
</databaseChangeLog>

