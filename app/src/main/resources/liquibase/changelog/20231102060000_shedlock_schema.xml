<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231026122000-1" author="ashish" dbms="postgresql">
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped, the index already exists">
            <not>
                <tableExists schemaName="customer_integration" tableName="shedlock"/>
            </not>
        </preConditions>
        <comment>Adding shedlock table</comment>
        <createTable schemaName="customer_integration" tableName="shedlock">
            <column name="name" type="VARCHAR(64)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="shedlock_name_pk"/>
            </column>
            <column name="lock_until" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="locked_at" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="locked_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>