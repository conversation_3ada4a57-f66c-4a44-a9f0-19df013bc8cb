<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="**************-1" author="nate" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>

        <comment>Create customer integration schema</comment>

        <sql dbms="postgresql, h2" endDelimiter=";">
            CREATE SCHEMA IF NOT EXISTS customer_integration
        </sql>
    </changeSet>
    <changeSet id="**************-2" author="nate" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="platform" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create platform table</comment>

        <createTable tableName="platform" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="platform_pkey" />
            </column>
            <column name="category" type="VARCHAR(30)" />
            <column name="name" type="VARCHAR(255)" />

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet id="**************-3" author="nate" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="provider" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create provider table</comment>

        <createTable tableName="provider" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="provider_pkey" />
            </column>
            <column name="name" type="VARCHAR(255)" />

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet id="**************-4" author="nate" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="company_integration" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create company integration table</comment>

        <createTable tableName="company_integration" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="company_integration_pkey" />
            </column>
            <column name="company_id" type="BIGINT" />
            <column name="provider_id" type="BIGINT" />
            <column name="platform_id" type="BIGINT" />
            <column name="account_token" type="VARCHAR(255)" />

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>
    <changeSet id="**************-5" author="nate" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="platform_contract_integration" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create platform contract integration table</comment>

        <createTable tableName="platform_contract_integration" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="platform_contract_integration_pkey" />
            </column>
            <column name="contract_id" type="BIGINT" />
            <column name="provider_id" type="BIGINT" />
            <column name="platform_id" type="BIGINT" />
            <column name="platform_employee_id" type="VARCHAR(255)" />
            <column name="remote_id" type="VARCHAR(255)" />

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

</databaseChangeLog>