<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240422151500-1" author="ashish" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="manual_sync" columnName="dismissed_on" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns dismissed_on to manual_sync table</comment>

        <addColumn tableName="manual_sync" schemaName="customer_integration">
            <column name="dismissed_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
