<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240822120800-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="platform" columnName="is_special_enum" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns is_special_enum to platform table</comment>

        <addColumn tableName="platform" schemaName="customer_integration">
            <column name="is_special_enum" type="boolean" defaultValueBoolean="false"/>
        </addColumn>

    </changeSet>

    <changeSet id="20240822120800-2" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>Update Hibob is_special_enum column</comment>
        <sql>
            UPDATE customer_integration.platform
            SET is_special_enum = true
            WHERE id = 3;
        </sql>
    </changeSet>
</databaseChangeLog>
