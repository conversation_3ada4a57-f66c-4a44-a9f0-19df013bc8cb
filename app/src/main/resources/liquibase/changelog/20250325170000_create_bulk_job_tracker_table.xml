<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="long" id="20250325170000-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="bulk_job_tracker" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create bulk job tracker table</comment>

        <createTable schemaName="customer_integration" tableName="bulk_job_tracker">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="bulk_job_tracker_pk"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false" unique="true" uniqueConstraintName="bulk_job_tracker_job_id_uk"/>
            </column>
            <column name="job_status" type="VARCHAR(50)">
                <constraints nullable="false" />
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="entity_id" type="BIGINT" >
                <constraints nullable="false" />
            </column>
            <column name="group_name" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="module_names" type="JSONB">
                <constraints nullable="false" />
            </column>
            <column name="original_file_uri" type="TEXT"/>
            <column name="original_uri_type" type="VARCHAR(50)"/>
            <column name="report_file_uri" type="TEXT"/>
            <column name="report_uri_type" type="VARCHAR(50)"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="long" id="20250325170000-2" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="bulk_job_tracker" schemaName="customer_integration_aud" />
            </not>
        </preConditions>
        <comment>Create bulk job tracker audit table</comment>

        <createTable schemaName="customer_integration" tableName="bulk_job_tracker_aud">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="bulk_job_tracker_aud_pk"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="bulk_job_tracker_aud_pk"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="job_status" type="VARCHAR(50)">
                <constraints nullable="false" />
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="entity_id" type="BIGINT" >
                <constraints nullable="false" />
            </column>
            <column name="group_name" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="module_names" type="JSONB">
                <constraints nullable="false" />
            </column>
            <column name="original_file_uri" type="TEXT"/>
            <column name="original_uri_type" type="VARCHAR(50)"/>
            <column name="report_file_uri" type="TEXT"/>
            <column name="report_uri_type" type="VARCHAR(50)"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
