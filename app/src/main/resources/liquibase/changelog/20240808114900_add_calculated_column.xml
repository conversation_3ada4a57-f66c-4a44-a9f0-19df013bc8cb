<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240808114900-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="fields_mapping" columnName="is_calculated" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns is_calculated to fields_mapping table</comment>

        <addColumn tableName="fields_mapping" schemaName="customer_integration">
            <column name="is_calculated" type="boolean" defaultValueBoolean="false"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
