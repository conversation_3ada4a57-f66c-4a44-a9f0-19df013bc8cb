<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="20240205012800-1" author="ryan" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="entities_integration" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create entities_integration table</comment>

        <createTable tableName="entities_integration" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entities_integration_pkey" />
            </column>
            <column name="entity_type" type="VARCHAR(255)" />
            <column name="internal_id" type="BIGINT" />
            <column name="external_id" type="VARCHAR(255)" />
            <column name="contract_id" type="BIGINT" />
            <column name="company_integration_id" type="BIGINT"/>

            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </createTable>
    </changeSet>

</databaseChangeLog>