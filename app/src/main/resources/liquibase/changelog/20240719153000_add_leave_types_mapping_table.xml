<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240719153000-1" author="ryan" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="leave_types_mapping" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create leave types mapping table</comment>

        <createTable tableName="leave_types_mapping" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="leave_types_mapping_pkey" />
            </column>
            <column name="company_id" type="BIGINT" />
            <column name="internal_type_id" type="VARCHAR(255)" />
            <column name="external_type_id" type="VARCHAR(255)" />
            <column name="integration_id" type="BIGINT" />
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
