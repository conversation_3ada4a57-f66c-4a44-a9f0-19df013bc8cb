<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250428095000-1" author="long" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="sftp_access_request" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create sftp_access_request table</comment>

        <createTable tableName="sftp_access_request" schemaName="customer_integration">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true" primaryKeyName="sftp_access_request_pk" nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bulk_module" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="main_sftp_directory" type="VARCHAR(255)">
                <constraints nullable="true" unique="true" uniqueConstraintName="main_sftp_directory_uk"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addUniqueConstraint
            columnNames="company_id, entity_id, bulk_module"
            tableName="sftp_access_request"
            schemaName="customer_integration"
            constraintName="sftp_access_request_uk"/>
    </changeSet>

    <changeSet id="20250428095000-2" author="long" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="sftp_access_request_aud" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create sftp_access_request audit table</comment>

        <createTable tableName="sftp_access_request_aud" schemaName="customer_integration">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="sftp_access_request_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="sftp_access_request_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="bulk_module" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="main_sftp_directory" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="created_on" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_on" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="updated_by" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
