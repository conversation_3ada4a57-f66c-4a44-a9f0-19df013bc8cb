<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20230818121000-1" author="ashish" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [provider]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider
                WHERE name = 'KNIT'
            </sqlCheck>
        </preConditions>
        <comment>Insert KNIT provider</comment>
        <sql>
            INSERT INTO "customer_integration"."provider" ("id", "name")
            VALUES (2, 'KNIT');
        </sql>
    </changeSet>
</databaseChangeLog>