<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240604201500-1" author="ashish" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="event_log" columnName="company_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add column company_id to event_log table</comment>

        <addColumn tableName="event_log" schemaName="customer_integration">
            <column name="company_id" type="BIGINT"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
