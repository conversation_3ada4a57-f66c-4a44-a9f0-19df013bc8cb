<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240211000000-1" author="petar" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="entities_integration" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="entities_integration" columnName="report_id" schemaName="customer_integration"/>
            </not>
            <not>
                <columnExists tableName="entities_integration" columnName="integration_id" schemaName="customer_integration"/>
            </not>
            <not>
                <columnExists tableName="entities_integration" columnName="paid" schemaName="customer_integration"/>
            </not>
            <not>
                <columnExists tableName="entities_integration" columnName="processed" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add report_id, integration_id and processed to entities_integration table</comment>

        <addColumn tableName="entities_integration" schemaName="customer_integration">
            <column name="report_id" type="VARCHAR(255)"/>
        </addColumn>
        <addColumn tableName="entities_integration" schemaName="customer_integration">
            <column name="integration_id" type="VARCHAR(255)"/>
        </addColumn>
        <addColumn tableName="entities_integration" schemaName="customer_integration">
            <column name="paid" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
        <addColumn tableName="entities_integration" schemaName="customer_integration">
            <column name="processed" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>