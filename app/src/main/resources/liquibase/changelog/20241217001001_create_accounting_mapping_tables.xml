<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">


    <changeSet id="**************-1" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_legal_entity_mapping"/>
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_legal_entity_mapping</comment>
        <createTable schemaName="customer_integration" tableName="accounting_legal_entity_mapping">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="entity_id" type="BIGINT">
            </column>
            <column name="external_entity" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-2" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_legal_entity_mapping_aud"/>
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_legal_entity_mapping_aud</comment>
        <createTable schemaName="customer_integration" tableName="accounting_legal_entity_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_legal_entity_mapping_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_legal_entity_mapping_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="entity_id" type="BIGINT">
            </column>
            <column name="external_entity" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>


    <changeSet id="**************-3" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists schemaName="customer_integration" sequenceName="accounting_legal_entity_mapping_seq"/>
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_legal_entity_mapping_seq</comment>
        <createSequence schemaName="customer_integration"
                        sequenceName="accounting_legal_entity_mapping_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"
        />
    </changeSet>


    <changeSet id="**************-4" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_department_mapping" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_department_mapping</comment>
        <createTable schemaName="customer_integration" tableName="accounting_department_mapping">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="department_id" type="BIGINT">
            </column>
            <column name="external_department" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-5" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_department_mapping_aud" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_department_mapping_aud</comment>
        <createTable schemaName="customer_integration" tableName="accounting_department_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_department_mapping_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_department_mapping_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="department_id" type="BIGINT">
            </column>
            <column name="external_department" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-6" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists schemaName="customer_integration" sequenceName="accounting_department_mapping_seq" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_department_mapping_seq</comment>
        <createSequence schemaName="customer_integration"
                        sequenceName="accounting_department_mapping_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"
        />
    </changeSet>

    <changeSet id="**************-7" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_external_multiplier_vendor_mapping" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_external_multiplier_vendor_mapping</comment>
        <createTable schemaName="customer_integration" tableName="accounting_external_multiplier_vendor_mapping">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="external_vendor" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-8" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists
                        schemaName="customer_integration"
                        tableName="accounting_external_multiplier_vendor_mapping_aud" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_external_multiplier_vendor_mapping_aud</comment>
        <createTable schemaName="customer_integration" tableName="accounting_external_multiplier_vendor_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_external_multiplier_vendor_mapping_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_external_multiplier_vendor_mapping_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
            </column>
            <column name="external_vendor" type="JSONB">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-9" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists schemaName="customer_integration" sequenceName="accounting_external_multiplier_vendor_mapping_seq" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_external_multiplier_vendor_mapping_seq</comment>
        <createSequence schemaName="customer_integration"
                        sequenceName="accounting_external_multiplier_vendor_mapping_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"
        />
    </changeSet>

    <changeSet id="**************-10" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_line_item_mapping" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_line_item_mapping</comment>
        <createTable schemaName="customer_integration" tableName="accounting_line_item_mapping">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="line_item_type" type="VARCHAR">
            </column>
            <column name="external_account" type="JSONB">
            </column>
            <column name="mapped_parent_id" type="BIGINT">
            </column>
            <column name="mapped_parent_type" type="VARCHAR">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-11" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="accounting_line_item_mapping_aud" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_department_mapping_aud</comment>
        <createTable schemaName="customer_integration" tableName="accounting_line_item_mapping_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_line_item_mapping_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="accounting_line_item_mapping_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="line_item_type" type="VARCHAR">
            </column>
            <column name="mapped_parent_id" type="VARCHAR">
            </column>
            <column name="mapped_parent_type" type="VARCHAR">
            </column>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="**************-12" author="himanshu" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists schemaName="customer_integration" sequenceName="accounting_line_item_mapping_seq" />
            </not>
        </preConditions>
        <comment>Creation of customer_integration.accounting_line_item_mapping_seq</comment>
        <createSequence schemaName="customer_integration"
                        sequenceName="accounting_line_item_mapping_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"
        />
    </changeSet>

</databaseChangeLog>
