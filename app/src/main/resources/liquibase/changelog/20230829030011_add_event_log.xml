<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20230829030011-1" author="ashish" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="event_log" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create Event Log table</comment>

        <createTable tableName="event_log" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="event_log_pkey" />
            </column>
            <column name="event_id" type="VARCHAR(255)" />
            <column name="event_type" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>
            <column name="event_payload" type="JSONB"/>
            <column name="status" type="VARCHAR(30)"/>
            <column name="retries_done" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="retries_left" type="INT" defaultValue="3" >
                <constraints nullable="false"/>
            </column>
            <column name="error_message" type="JSONB"/>
            <column name="last_attempt" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="next_attempt" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
        <createIndex indexName="idx_event_log_status_next_attempt" tableName="event_log" schemaName="customer_integration">
            <column name="status"/>
            <column name="next_attempt"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>