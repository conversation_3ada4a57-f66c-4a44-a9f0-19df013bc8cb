<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20221027141100-1" author="datnguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [provider]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider
                WHERE name = 'MERGE_DEV'
            </sqlCheck>
        </preConditions>
        <comment>Insert MERGE_DEV provider</comment>
        <sql>
            INSERT INTO "customer_integration"."provider" ("id", "name")
            VALUES (1, 'MERGE_DEV');
        </sql>
    </changeSet>

    <changeSet id="20221027141100-2" author="datnguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'HRIS' and name = 'BambooHR'
            </sqlCheck>
        </preConditions>
        <comment>Insert HRIS platform</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (1, 'HRIS', 'BambooHR');
        </sql>
    </changeSet>
</databaseChangeLog>