<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231201000000-1" author="petar" dbms="postgresql">

        <dropColumn tableName="sync" schemaName="customer_integration">
            <column name="started_automatically"/>
        </dropColumn>

        <dropColumn tableName="company_integration" schemaName="customer_integration">
            <column name="two_way_sync_enabled"/>
        </dropColumn>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="outgoing_sync_enabled" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="incoming_sync_enabled" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="last_outgoing_sync_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="last_incoming_sync_time" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
