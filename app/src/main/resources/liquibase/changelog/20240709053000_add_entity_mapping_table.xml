<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240709053000-1" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="legal_entity_mapping" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create legal entity mapping table</comment>

        <createTable tableName="legal_entity_mapping" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="legal_entity_mapping_pkey" />
            </column>
            <column name="entity_id" type="BIGINT" />
            <column name="entity_name" type="VARCHAR(255)" />
            <column name="status" type="VARCHAR(255)" />
            <column name="is_enabled" type="BOOLEAN" />
            <column name="company_id" type="BIGINT" />
            <column name="integration_id" type="BIGINT" />
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

    </changeSet>

    <changeSet id="20240709053000-2" author="beeyen" dbms="postgresql">
        <sql>
            ALTER TABLE "customer_integration"."legal_entity_mapping" ADD CONSTRAINT chk_legal_entity_mapping_status_enum CHECK (status IN ('FULLY_MAPPED', 'PARTIALLY_MAPPED', 'UNMAPPED'));
        </sql>
    </changeSet>
</databaseChangeLog>
