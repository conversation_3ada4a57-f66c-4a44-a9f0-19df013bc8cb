<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240328022000-1" author="ashish" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="event_log" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="event_log" columnName="sync_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add sync_id column to event_log table</comment>

        <addColumn tableName="event_log" schemaName="customer_integration">
            <column name="sync_id" type="VARCHAR(255)" />
        </addColumn>
    </changeSet>
</databaseChangeLog>