<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">


    <changeSet id="20241229120000-1" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="company_payable_external_system_transaction"/>
            </not>
        </preConditions>
        <comment>Creation of customer_integration.company_payable_external_system_transaction</comment>
        <createTable schemaName="customer_integration" tableName="company_payable_external_system_transaction">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_payable_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="JSONB"/>
            <column name="response_payload" type="JSONB"/>
            <column name="request_payload" type="JSONB"/>
            <column name="financial_transaction_type" type="VARCHAR(255)"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="20241229120000-2" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="customer_integration" tableName="company_payable_external_system_transaction_aud"/>
            </not>
        </preConditions>
        <comment>Creation of customer_integration.company_payable_external_system_transaction_aud</comment>
        <createTable schemaName="customer_integration" tableName="company_payable_external_system_transaction_aud">
            <column name="id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="company_payable_external_system_transaction_aud_pkey"/>
            </column>
            <column name="rev" type="BIGINT">
                <constraints primaryKey="true" nullable="false" primaryKeyName="company_payable_external_system_transaction_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="integration_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_payable_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="JSONB"/>
            <column name="response_payload" type="JSONB"/>
            <column name="request_payload" type="JSONB"/>
            <column name="financial_transaction_type" type="VARCHAR(255)"/>
            <column name="created_by" type="BIGINT">
            </column>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="updated_by" type="BIGINT">
            </column>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>


    <changeSet id="20241229120000-3" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN">
            <not>
                <sequenceExists schemaName="customer_integration" sequenceName="company_payable_external_system_transaction_seq"/>
            </not>
        </preConditions>
        <comment>Creation of customer_integration.company_payable_external_system_transaction_seq</comment>
        <createSequence schemaName="customer_integration"
                        sequenceName="company_payable_external_system_transaction_seq"
                        cacheSize="1"
                        cycle="false"
                        dataType="BIGINT"
                        incrementBy="1"
                        maxValue="9223372036854775807"
                        minValue="1"
                        startValue="1"
        />
    </changeSet>
</databaseChangeLog>
