<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240328011300-1" author="beeyen"  dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <columnExists schemaName="customer_integration" tableName="entities_integration" columnName="report_id" />
        </preConditions>
        <comment>Drop column report_id in entities_integration table</comment>
        <dropColumn schemaName="customer_integration" tableName="entities_integration" columnName="report_id" />
    </changeSet>

</databaseChangeLog>