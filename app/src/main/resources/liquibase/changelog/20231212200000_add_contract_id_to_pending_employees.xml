<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231212200000-1" author="petar" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="pending_employee" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="pending_employee" columnName="contract_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add contract_id column to event_log table</comment>

        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="contract_id" type="BIGINT"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>