<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet id="20241016190000-1" author="HuyNguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'HRIS' and name = 'Keka'
            </sqlCheck>
        </preConditions>
        <comment>Insert HRIS platform Keka HR</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (19, 'HRIS', 'Keka HR');
        </sql>
    </changeSet>

    <changeSet id="20241016190000-2" author="HuyNguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.provider_platform
                WHERE platform_id = 19 and provider_id = 2
            </sqlCheck>
        </preConditions>
        <comment>Insert Keka HR integration platform</comment>
        <sql>
            INSERT INTO customer_integration.provider_platform
                (platform_id, provider_id, status, created_on)
            VALUES
                (19, 2, 'ACTIVE', now());
        </sql>
    </changeSet>

</databaseChangeLog>