<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240827122300-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="field_mapping_configuration" columnName="enum_mappings" schemaName="customer_integration"/>
            </not>
            <not>
                <columnExists tableName="field_mapping_configuration" columnName="platform_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns enum_mappings and platform_id to field_mapping_configuration table</comment>

        <addColumn tableName="field_mapping_configuration" schemaName="customer_integration">
            <column name="enum_mappings" type="jsonb"/>
            <column name="platform_id" type="BIGINT" />
        </addColumn>

    </changeSet>
</databaseChangeLog>
