<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240822030600-1" author="beeyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="field_mapping_configuration" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create legal entity mapping table</comment>

        <createTable tableName="field_mapping_configuration" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="field_mapping_configuration_pkey" />
            </column>
            <column name="key" type="VARCHAR(255)" />
            <column name="value" type="VARCHAR(255)" />
            <column name="type" type="VARCHAR(255)" />
            <column name="is_deleted" type="boolean" defaultValueBoolean="false"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>

    </changeSet>
</databaseChangeLog>
