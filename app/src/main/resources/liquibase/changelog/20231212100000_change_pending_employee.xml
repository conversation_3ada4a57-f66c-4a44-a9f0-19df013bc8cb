<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231212100000-2" author="petar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <and>
                <tableExists tableName="pending_employee" schemaName="customer_integration" />
                <columnExists tableName="pending_employee" columnName="data" schemaName="customer_integration" />
                      </and>
        </preConditions>
        <comment>Add identifier_value column to pending_employee table</comment>
        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="identifier_value" type="VARCHAR(255)" />
        </addColumn>
        <dropColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="data"  />
        </dropColumn>
        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="first_name" type="VARCHAR(255)" />
        </addColumn>
        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="last_name" type="VARCHAR(255)" />
        </addColumn>
    </changeSet>
</databaseChangeLog>
