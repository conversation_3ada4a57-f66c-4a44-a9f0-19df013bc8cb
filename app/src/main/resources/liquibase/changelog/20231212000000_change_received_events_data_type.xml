<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231212000000-1" author="petar" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <tableExists tableName="received_events" schemaName="customer_integration" />
            <columnExists tableName="received_events" columnName="received_time" schemaName="customer_integration" />
        </preConditions>
        <comment>Modify received_time column type to TIMESTAMP WITHOUT TIME ZONE</comment>

        <modifyDataType
                tableName="received_events"
                schemaName="customer_integration"
                columnName="received_time"
                newDataType="TIMESTAMP WITHOUT TIME ZONE"/>

    </changeSet>
</databaseChangeLog>
