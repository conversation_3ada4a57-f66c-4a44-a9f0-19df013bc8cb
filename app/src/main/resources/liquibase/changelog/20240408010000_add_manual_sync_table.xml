<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240408010000-1" author="ashish" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="manual_sync" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Create manual sync table</comment>

        <createTable tableName="manual_sync" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="manual_sync_pkey" />
            </column>
            <column name="sync_id" type="VARCHAR(255)" />
            <column name="integration_id" type="BIGINT" />
            <column name="status" type="VARCHAR(255)" />
            <column name="type" type="VARCHAR(255)" />
            <column name="started_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="completed_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
