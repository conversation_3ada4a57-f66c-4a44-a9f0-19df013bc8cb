<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20221103170900-1" author="daniel nguyen" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped inserting into table [platform]. Already exists...">
            <sqlCheck expectedResult="0">
                SELECT COUNT(*)
                FROM customer_integration.platform
                WHERE category = 'HRIS' and name = 'Personio'
            </sqlCheck>
        </preConditions>
        <comment>Insert HRIS platform</comment>
        <sql>
            INSERT INTO "customer_integration"."platform" ("id", "category", "name")
            VALUES (2, 'HRIS', 'Personio');
        </sql>
    </changeSet>

</databaseChangeLog>