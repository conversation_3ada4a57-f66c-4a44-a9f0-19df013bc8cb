<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="**************-1" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped Associating accounting_line_item_mapping_seq with id">
                <sequenceExists schemaName="customer_integration" sequenceName="accounting_line_item_mapping_seq"/>
                <tableExists schemaName="customer_integration" tableName="accounting_line_item_mapping" />
        </preConditions>
        <comment>Associate accounting_line_item_mapping_seq with id </comment>
        <sql>
            ALTER TABLE customer_integration.accounting_line_item_mapping
            ALTER COLUMN id SET DEFAULT nextval('customer_integration.accounting_line_item_mapping_seq');
        </sql>
    </changeSet>

    <changeSet id="**************-2" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped Associating accounting_legal_entity_mapping_seq with id">
            <sequenceExists schemaName="customer_integration" sequenceName="accounting_legal_entity_mapping_seq"/>
            <tableExists schemaName="customer_integration" tableName="accounting_legal_entity_mapping" />
        </preConditions>
        <comment>Associate accounting_legal_entity_mapping_seq with id </comment>
        <sql>
            ALTER TABLE customer_integration.accounting_legal_entity_mapping
            ALTER COLUMN id SET DEFAULT nextval('customer_integration.accounting_legal_entity_mapping_seq');
        </sql>
    </changeSet>

    <changeSet id="**************-3" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped Associating accounting_department_mapping_seq with id">
            <sequenceExists schemaName="customer_integration" sequenceName="accounting_department_mapping_seq"/>
            <tableExists schemaName="customer_integration" tableName="accounting_department_mapping" />
        </preConditions>
        <comment>Associate accounting_department_mapping_seq with id </comment>
        <sql>
            ALTER TABLE customer_integration.accounting_department_mapping
            ALTER COLUMN id SET DEFAULT nextval('customer_integration.accounting_department_mapping_seq');
        </sql>
    </changeSet>

    <changeSet id="**************-4" author="Ganesh Jadhav" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions onFail="MARK_RAN" onFailMessage="Skipped Associating accounting_external_multiplier_vendor_mapping_seq with id">
            <sequenceExists schemaName="customer_integration" sequenceName="accounting_external_multiplier_vendor_mapping_seq"/>
            <tableExists schemaName="customer_integration" tableName="accounting_external_multiplier_vendor_mapping" />
        </preConditions>
        <comment>Associate accounting_external_multiplier_vendor_mapping_seq with id </comment>
        <sql>
            ALTER TABLE customer_integration.accounting_external_multiplier_vendor_mapping
            ALTER COLUMN id SET DEFAULT nextval('customer_integration.accounting_external_multiplier_vendor_mapping_seq');
        </sql>
    </changeSet>
</databaseChangeLog>
