<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet author="minh" id="20250102150000-1" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <preConditions>
            <not>
                <tableExists tableName="platform_timeoff_integration" schemaName="customer_integration" />
            </not>
        </preConditions>
        <comment>Create platform timeoff integration table</comment>

        <createTable schemaName="customer_integration" tableName="platform_timeoff_integration">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="platform_timeoff_integration_pk"/>
            </column>
            <column name="integration_id" type="BIGINT"/>
            <column name="contract_id" type="BIGINT"/>
            <column name="employee_id" type="VARCHAR(255)"/>
            <column name="internal_timeoff_id" type="BIGINT"/>
            <column name="external_timeoff_id" type="VARCHAR(255)"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="created_by" type="BIGINT"/>
            <column name="updated_by" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet id="20250102150000-2" author="minh" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="platform_contract_integration" columnName="integration_id" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns integration_id to platform_contract_integration</comment>

        <addColumn tableName="platform_contract_integration" schemaName="customer_integration">
            <column name="integration_id" type="BIGINT"/>
        </addColumn>

    </changeSet>

</databaseChangeLog>
