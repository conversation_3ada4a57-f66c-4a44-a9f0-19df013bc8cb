<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240104100000-1" author="ryan" dbms="postgresql">
        <comment>Add ID columns to tables that do not have them yey</comment>
        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" unique="true"/>
            </column>
        </addColumn>
        <addColumn tableName="received_events" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" unique="true"/>
            </column>
        </addColumn>
        <addColumn tableName="sync" schemaName="customer_integration">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints nullable="false" unique="true"/>
            </column>
        </addColumn>
    </changeSet>
</databaseChangeLog>