<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231215200000-1" author="petar" dbms="postgresql">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="company_integration" schemaName="customer_integration"/>
            <not>
                <columnExists tableName="company_integration" columnName="import_in_progress" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add contract_id column to event_log table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="import_in_progress" type="BOOLEAN" defaultValueBoolean="false"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>