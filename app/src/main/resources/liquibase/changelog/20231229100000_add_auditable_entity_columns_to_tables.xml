<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20231229100000-1" author="ryan" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="pending_employee" columnName="created_by" schemaName="customer_integration"/>
                <columnExists tableName="pending_employee" columnName="created_on" schemaName="customer_integration"/>
                <columnExists tableName="pending_employee" columnName="updated_by" schemaName="customer_integration"/>
                <columnExists tableName="pending_employee" columnName="updated_on" schemaName="customer_integration"/>

                <columnExists tableName="received_events" columnName="created_by" schemaName="customer_integration"/>
                <columnExists tableName="received_events" columnName="created_on" schemaName="customer_integration"/>
                <columnExists tableName="received_events" columnName="updated_by" schemaName="customer_integration"/>
                <columnExists tableName="received_events" columnName="updated_on" schemaName="customer_integration"/>

                <columnExists tableName="sync" columnName="created_by" schemaName="customer_integration"/>
                <columnExists tableName="sync" columnName="created_on" schemaName="customer_integration"/>
                <columnExists tableName="sync" columnName="updated_by" schemaName="customer_integration"/>
                <columnExists tableName="sync" columnName="updated_on" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns for support AuditableBaseEntity to tables that do not have them yet</comment>

        <addColumn tableName="pending_employee" schemaName="customer_integration">
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>

        <addColumn tableName="received_events" schemaName="customer_integration">
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>

        <addColumn tableName="sync" schemaName="customer_integration">
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>