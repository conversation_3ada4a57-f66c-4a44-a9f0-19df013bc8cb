<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20240805191400-1" author="beeyen" dbms="postgresql">
        <preConditions>
            <not>
                <columnExists tableName="company_integration" columnName="initial_sync_started" schemaName="customer_integration"/>
            </not>
        </preConditions>
        <comment>Add columns initial_sync_started to company_integration table</comment>

        <addColumn tableName="company_integration" schemaName="customer_integration">
            <column name="initial_sync_started" type="boolean" defaultValueBoolean="false"/>
        </addColumn>

    </changeSet>
</databaseChangeLog>
