spring:
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: true

platform:
  base-url: https://dev-customer.frontend.acc.staging.usemultiplier.com
  kafka:
    auto-startup: true
    group-id: customer-integration-stg-1
    bootstrap-servers: b-2.kafka-cluster-sta.1unmw8.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-cluster-sta.1unmw8.c2.kafka.ap-southeast-1.amazonaws.com:9092
  merge-dev:
    api-key: TBD
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}

scheduler:
  cron:
    member-update: "0 */1 * * * *"

feign:
  hystrix:
    enabled: false
  client:
    config:
      docgen-service:
        url: ${PLATFORM_DOCGEN_PUBLICBASEURL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: BASIC
        decode404: false