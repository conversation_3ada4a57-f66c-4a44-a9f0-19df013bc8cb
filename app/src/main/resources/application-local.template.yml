spring:
  main:
    banner-mode: OFF
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **********************************************
    username: postgres
    password: password
    hikari:
      poolName: Hikari
      auto-commit: false
  jpa:
    show-sql: false
    open-in-view: true
    properties:
      hibernate.jdbc.time_zone: UTC

server:
  port: 8182

merge-dev:
  enabled: true

external:
  core-service:
    host: core-service.staging.usemultiplier.local
    port: 8081

platform:
  base-url: https://stage-app.usemultiplier.com
  kafka:
    auto-startup: false
    group-id: customer-integration-local
    bootstrap-servers: localhost:9092
  pigeon-service:
    kafka:
      bootstrap-servers: localhost:9092
  userservice:
    system.notification.email: <EMAIL>
  merge-dev:
    api-key: TBD
  knit:
    api-key: TBD
    api-url: https://api.getknit.dev
  maximum-retry: 3
  trinet:
    api-url: https://apiqe1.trinet.com

scheduler:
  receivedUpdateEventsNotificationScheduler:
    enabled: false
  receivedDeleteEventsScheduler:
    enabled: false
  verifyIntegrationCredentialScheduler:
    enabled: false
  timeOffEventsScheduler:
    enabled: false
  eventProcessingScheduler:
    enabled: false
  manualSyncScheduler:
    enabled: false
  receivedCreateEventsScheduler:
    enabled: false
  archiveProcessedEventsScheduler:
    enabled: false

  cron:
    member-update: "0 */1 * * * *"

cloud:
  aws:
    s3:
      enabled: true
      bucket: stg-apse1-sftp-multipliers3
    region:
      static: ap-southeast-1

kafka:
  group-id: customer-integration-local
  bootstrap-servers: b-2.stgapptechappkafk.r4k9xq.c5.kafka.ap-southeast-1.amazonaws.com:9092,b-1.stgapptechappkafk.r4k9xq.c5.kafka.ap-southeast-1.amazonaws.com:9092

grpc:
  client:
    authority-service:
      address: dns:///user-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    bulk-upload-service:
      address: dns:///bulk-upload-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    core-service:
      address: dns:///core-service.stage.local.usemultiplier.com:9090
      negotiationType: TLS
      enableKeepAlive: false
    member-service:
      address: dns:///member-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
      enableKeepAlive: false
    pigeon-service:
      address: dns:///pigeon-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-service:
      address: dns:///contract-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    expense-service:
      address: dns:///expense-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    timeoff-service:
      address: dns:///timeoff-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    country-service:
      address: dns:///country-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-offboarding-service:
      address: dns:///contract-offboarding-service.staging.usemultiplier.internal:9090
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    contract-onboarding-service:
      address: dns:///contract-onboarding-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    pay-se:
      address: dns:///pay-se-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
    field-mapping-service:
      address: dns:///field-mapping-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
      enableKeepAlive: true
      keepAliveWithoutCalls: true
  server:
    port: 9090
    security:
      enabled: false
pigeon:
  client:
    kafka:
      bootstrap-servers: localhost:9092

jwt:
  public-key: MIGbMBAGByqGSM49AgEGBSuBBAAjA4GGAAQAmzU6eIvgeuPL9Rd/NHjqyatae8pg4AeJxMo/judIMA3n4MAxPbI6/2VboB+0sdzG+Bc13AHXNExqEFBcyMdKV8oB+yoR4uTc/qrDeTZhoDvETBLVHwfRbX4ecTA2+I0DD5huI/CAcbKdrIiLqs4h+A4wi50CJ+D+I6TBkB3jTGJsRrY=

integration:
  webhook:
    sftp:
      api-key: abc123
