platform:
  base-url: https://app.usemultiplier.com
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}
  kafka:
    auto-startup: true
    group-id: customer-integration-prod-1
    bootstrap-servers: b-3.kafka-cluster-pro.8h6lkf.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-2.kafka-cluster-pro.8h6lkf.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-cluster-pro.8h6lkf.c2.kafka.ap-southeast-1.amazonaws.com:9092
  pigeon-service:
    kafka:
      bootstrap-servers: ${PLATFORM_PIGEON_KAFKA_BOOTSTRAPSERVERS}
      topic: topic.internal.v1.send.notification.request
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
      max-block-ms: 3200
      retry-count: 3
      retry-backoff-ms: 1000
  trinet:
    api-url: https://api.trinet.com

scheduler:
  cron:
    member-update: "0 0 */6 * * *"

feign:
  hystrix:
    enabled: false
  client:
    config:
      docgen-service:
        url: ${PLATFORM_DOCGEN_PUBLICBASEURL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: BASIC
        decode404: false

ops:
  support-email: <EMAIL>
