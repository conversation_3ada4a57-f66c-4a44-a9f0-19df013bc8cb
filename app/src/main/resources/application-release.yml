platform:
  base-url: https://release-app.usemultiplier.com
  docgen:
    baseurl: ${PLATFORM_DOCGEN_BASEURL}
    public-baseurl: ${PLATFORM_DOCGEN_PUBLICBASEURL}
  kafka:
    auto-startup: true
    group-id: customer-integration-release-1
    bootstrap-servers: b-2.kafka-cluster-rel.2cwaen.c2.kafka.ap-southeast-1.amazonaws.com:9092,b-1.kafka-cluster-rel.2cwaen.c2.kafka.ap-southeast-1.amazonaws.com:9092

scheduler:
  cron:
    member-update: "0 */1 * * * *"

feign:
  hystrix:
    enabled: false
  client:
    config:
      docgen-service:
        url: ${PLATFORM_DOCGEN_PUBLICBASEURL}
        connectTimeout: 120000
        readTimeout: 120000
        loggerLevel: BASIC
        decode404: false