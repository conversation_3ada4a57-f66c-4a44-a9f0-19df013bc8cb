package com.multiplier.integration.service

import com.multiplier.common.transport.auth.AccessDeniedResult
import com.multiplier.common.transport.auth.MplAccessDeniedException
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.attribute.CompanyAccessService
import com.multiplier.common.transport.user.attribute.EntityAccessService
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import graphql.schema.GraphQLObjectType
import mu.KotlinLogging
import org.springframework.stereotype.Service

/**
 * Service responsible for authorization checks in the application.
 * Provides methods to validate user access to entities and companies.
 */
@Service
class AuthorizationService(
    private val currentUser: CurrentUser,
    private val entityAccessService: EntityAccessService,
    private val companyAccessService: CompanyAccessService
) {
    private val log = KotlinLogging.logger {}

    /**
     * Checks if the current user has access to the specified entity.
     * Throws MplAccessDeniedException if access is denied.
     *
     * @param dfe The GraphQL data fetching environment
     * @param entityId The ID of the entity to check access for
     * @throws MplAccessDeniedException if the user does not have access to the entity
     */
    fun checkEntityAccessOrThrow(dfe: DgsDataFetchingEnvironment, entityId: Long) {
        if (!entityAccessService.hasEntityAccess(dfe, entityId)) {
            log.warn { "User ${currentUser.context?.id} does not have access to entity $entityId" }
            throw MplAccessDeniedException(
                AccessDeniedResult(
                    getResourceName(dfe), currentUser.context!!, emptyList()
                )
            )
        }
    }

    /**
     * Checks if the current user has access to the specified company.
     * Throws MplAccessDeniedException if access is denied.
     *
     * @param dfe The GraphQL data fetching environment
     * @param companyId The ID of the company to check access for
     * @throws MplAccessDeniedException if the user does not have access to the company
     */
    fun checkCompanyAccessOrThrow(dfe: DgsDataFetchingEnvironment, companyId: Long) {
        if (!companyAccessService.hasCompanyAccess(dfe, companyId)) {
            log.warn { "User ${currentUser.context?.id} does not have access to company $companyId" }
            throw MplAccessDeniedException(
                AccessDeniedResult(
                    getResourceName(dfe), currentUser.context!!, emptyList()
                )
            )
        }
    }

    /**
     * Gets the resource name from the data fetching environment.
     * This is used in the access denied exception to provide context about which GraphQL field was being accessed.
     *
     * @param dfe The GraphQL data fetching environment
     * @return The name of the resource being accessed. E.g. Mutation.createSftpAccessRequest
     */
    private fun getResourceName(dfe: DgsDataFetchingEnvironment): String {
        val graphQLType = (dfe.parentType as? GraphQLObjectType)?.name ?: ""
        // This will return full query or mutation. E.g. Mutation.contractUpdateTimeOffEntitlements
        return graphQLType + "." + dfe.fieldDefinition.name
    }
}
