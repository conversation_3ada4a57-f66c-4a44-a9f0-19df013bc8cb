package com.multiplier.integration.service.exception

import com.multiplier.common.exception.MplSystemException

/**
 * TODO: We need to classifiy which usecase is business exception and which one is system exception
 */
class IntegrationNotFoundException(message: String = CustomerErrorCode.INTEGRATION_NOT_FOUND.message) :
    MplSystemException(
        CustomerErrorCode.INTEGRATION_NOT_FOUND,
        message
    )
