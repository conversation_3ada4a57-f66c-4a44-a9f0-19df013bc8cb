package com.multiplier.integration.service

import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PendingEmployeeRepository
import com.multiplier.integration.types.Employee
import com.multiplier.integration.utils.validateIntegrationCompanyMatch
import mu.KotlinLogging
import org.springframework.stereotype.Service
import kotlin.random.Random

@Service
class PendingEmployeeService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val pendingEmployeeRepository: PendingEmployeeRepository
) {

    private val log = KotlinLogging.logger {}

    fun getEmployeesToSendInviteTo(integrationId : Long, currentUserCompanyId: Long?, isOpsUser: Boolean = false) : List<Employee> {

        var integration = companyIntegrationRepository.findById(integrationId).get()
        validateIntegrationCompanyMatch(currentUserCompanyId, integration.companyId, isOpsUser)

        var matchingEmployees = pendingEmployeeRepository.findByIntegrationIdAndInviteRequested(integration.accountToken, false)
        val result: MutableList<Employee> = mutableListOf()

        for (matchingEmployee in matchingEmployees) {
            val employee = Employee(Random.nextLong(), matchingEmployee.identifier,
            matchingEmployee.firstName, matchingEmployee.lastName)
            result.add(employee)
        }

        return result
    }
}