package com.multiplier.integration.service.fieldmappings

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.FieldMappingConfigurationRepository
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.model.FieldMappingConfigurationType
import com.multiplier.integration.repository.model.FieldType
import com.multiplier.integration.repository.model.JpaFieldsMapping
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InboundDataExtractionService.BaseExtractor.Companion.extractDataFromPathAsString
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha3CountryCode
import mu.KotlinLogging
import org.springframework.stereotype.Component
import utils.StringUtils.Companion.replaceAllNonAlphanumericCharacters

private val log = KotlinLogging.logger {}

@Component
class EmployeeDataFieldMapper(
    private val fieldsMappingRepository: FieldsMappingRepository,
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val featureFlagService: FeatureFlagService,
    private val fieldMappingConfigurationRepository: FieldMappingConfigurationRepository,
    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter,
) {
    fun mapEmployeeData(
        eventData: Map<String, Any>,
        legalEntityId: Long,
        onboardingType: OnboardingType,
        platformId: Long,
        integrationId: Long,
        companyId: Long,
    ): Map<String, String> {
        val ffAttributes = mapOf(
            "company" to companyId,
            "platform" to platformId
        )

        val mappings = if (featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, ffAttributes)) {
            if (featureFlagService.isOn(FeatureFlag.FIELD_MAPPING_SERVICE_ENABLED, ffAttributes)) {
                // Use the new field mapping service
                mapEmployeeDataUsingNewFieldMappingService(legalEntityId, eventData, companyId, integrationId)
            }
            else
            // Use the existing customer mappings
            mapEmployeeDataUsingCustomerMappings(legalEntityId, eventData, integrationId)
        } else {
            // Use the default mappings
            mapEmployeeDataUsingDefaultMappings(eventData, platformId)
        }

        val overrides = getOverrides(mappings, onboardingType, legalEntityId)

        return mappings + overrides
    }

    private fun mapEmployeeDataUsingNewFieldMappingService(
        legalEntityId: Long,
        eventData: Map<String, Any>,
        companyId: Long,
        integrationId: Long
    ): Map<String, String> {
        log.info("Using field mapping service for companyId=$companyId, integrationId=$integrationId")

        try {
            // Get field mapping profiles for the company
            val profiles = fieldMappingServiceAdapter.listProfiles(companyId)

            // Find the profile for this integration
            val profile = profiles.profilesList.firstOrNull { profile ->
                profile.configMap.fieldsMap["entityId"]?.stringValue == legalEntityId.toString() &&
                        profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
            } ?: throw IllegalStateException("No field mapping profile found for companyId=$companyId")

            // Execute the field mapping
            val result = fieldMappingServiceAdapter.executeMapping(profile.id, eventData)

            return result.transformedData.fieldsMap.mapValues {
                it.value.allFields.values.firstOrNull()?.toString().orEmpty()
            }
        } catch (e: Exception) {
            log.error(e) { "Error using new field mapping service: ${e.message}" }
            throw IllegalStateException(e.message)
        }
    }

    private fun mapEmployeeDataUsingCustomerMappings(
        legalEntityId: Long,
        eventData: Map<String, Any>,
        integrationId: Long
    ): Map<String, String> {
        checkLegalEntityMapping(legalEntityId, integrationId)

        val fieldsMapping = fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(
            legalEntityId, integrationId
        )

        return mapEmployeeData(eventData, fieldsMapping)
    }

    private fun mapEmployeeDataUsingDefaultMappings(
        eventData: Map<String, Any>,
        platformId: Long
    ): Map<String, String> {
        val defaultMappings = fieldMappingConfigurationRepository.getMappingsByTypeWithFallback(
            FieldMappingConfigurationType.DEFAULT,
            platformId
        )
        val calculatedMappings = fieldMappingConfigurationRepository.getMappingsByTypeWithFallback(
            FieldMappingConfigurationType.CALCULATED,
            platformId
        )
        val mappings = calculatedMappings + defaultMappings

        return mappings.associate { field ->
            val originField = field.key
            val enumMappingJson: Map<String, String>? =
                field.enumMappings?.let { DataMapper.objectMapper.readValue<Map<String, String>?>(it) }
            val mappedData = processMappedField(
                eventData,
                originField,
                field.value,
                enumMappingJson,
                !field.enumMappings.isNullOrBlank(),
                field.type == FieldMappingConfigurationType.CALCULATED
            )
            originField to mappedData.ifBlank { "" }
        }
    }

    private fun checkLegalEntityMapping(
        legalEntityId: Long,
        integrationId: Long
    ) {
        val legalEntityMapping = legalEntityMappingRepository.findByIntegrationIdAndEntityId(
            integrationId, legalEntityId
        ).orElseThrow { RuntimeException("Not found legal entity mapping") }

        if (!legalEntityMapping.isEnabled) {
            throw RuntimeException("Legal entity field mapping for entity $legalEntityId not enabled for GP sync")
        }
    }

    private fun getOverrides(eventData: Map<String, String>, onboardingType: OnboardingType, legalEntityId: Long): Map<String, String> {
        val replaceEmailWithWorkEmail = if (eventData["email"].isNullOrEmpty() && !eventData["workEmail"].isNullOrEmpty())
            eventData + mapOf(
                "email" to (eventData["workEmail"] ?: ""),
                "workEmail" to "",
            )
        else emptyMap()

        val legalEntityIdForHris = if (onboardingType == OnboardingType.HRIS_PROFILE_DATA)
            mapOf("legalEntityId" to legalEntityId.toString())
        else emptyMap()

        return replaceEmailWithWorkEmail + legalEntityIdForHris
    }

    private fun mapEmployeeData(
        eventData: Map<String, Any>,
        fieldsMapping: List<JpaFieldsMapping>,
    ): Map<String, String> {
        return fieldsMapping
            .filter { !it.originField.isNullOrBlank() }
            .associate { field ->
                val originField = field.originField!!
                val enumMappings = field.children
                    .filter { !it.mappedField.isNullOrBlank() }
                    .associate { it.mappedField!!.uppercase() to it.originField }

                val mappedData = field.mappedField?.let { mappedField ->
                    processMappedField(
                        eventData,
                        originField,
                        mappedField,
                        enumMappings,
                        field.type == FieldType.ENUM,
                        field.isCalculated
                    )
                }.orEmpty()

                originField to mappedData.ifBlank { "" }
            }
    }

    private fun processMappedField(
        eventData: Map<String, Any>,
        originField: String,
        mappedField: String,
        enumMappings: Map<String, String?>?,
        isEnum: Boolean,
        isCalculated: Boolean
    ): String {
        var originData = extractDataFromPathAsString(eventData, mappedField) ?: return ""
        originData = formatSpecialCases(originData, mappedField, isEnum)

        return when {
            isCalculated -> handleCalculatedFieldCount(originData)
            originField == "term" -> mapTermField(originData)
            originField == "uanNumberExists" -> mapUanNumberExistsField(originData)
            isEnum -> enumMappings?.get(originData.uppercase()) ?: originData
            else -> originData
        }
    }

    private fun mapTermField(originData: String?): String {
        return if (originData.isNullOrBlank()) {
            ContractOuterClass.ContractTerm.PERMANENT.name
        } else {
            ContractOuterClass.ContractTerm.FIXED.name
        }
    }

    private fun formatSpecialCases(input: String, path: String, enum: Boolean = false): String {
        try {
            if (path.contains("employeeIdentificationData")) {
                return replaceAllNonAlphanumericCharacters(input, " ")
            }
            if (path.contains("country") && !enum) {
                return mapToAlpha3CountryCode(input)
            }
            if (isValidDate(input)) {
                return input.substring(0, 10) // Extract and return only the date part
            }
        } catch (e: Exception) {
            log.error("handleFormatData exception: ", e)
        }
        return input
    }

    private fun handleCalculatedFieldCount(data: String?): String {
        return try {
            val node: JsonNode = DataMapper.objectMapper.readTree(data)
            if (node.isArray) {
                val dataList: List<Any> = DataMapper.objectMapper.readValue(data!!)
                dataList.size.toString()
            } else {
                "0"
            }
        } catch (e: Exception) {
            "0"
        }
    }

    private fun mapUanNumberExistsField(originData: String?): String {
        return if (originData.isNullOrBlank()) "false" else "true"
    }

    private fun isValidDate(dateStr: String): Boolean {
        val regex = Regex("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$")
        return regex.matches(dateStr)
    }
}