package com.multiplier.integration.service

import com.multiplier.integration.Constants.UnsupportedFetchingDocumentCategoriesPlatforms
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaDocumentFolder
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationNotFoundException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.DocumentFolder
import com.multiplier.integration.types.DocumentFolderDetail
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.UpsertDocumentFolderInput
import com.multiplier.integration.utils.validateIntegrationCompanyMatch
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class DocumentFoldersService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val documentFoldersRepository: DocumentFoldersRepository,
    private val knitAdapter: KnitAdapter,
    private val dataMapper: DataMapper,
    ) {
    private val log = KotlinLogging.logger {}
    fun getDocumentFolders(companyId: Long?, integrationId: Long, type: DocumentFolderType, isOpsUser: Boolean = false): DocumentFolder {
        val matchingIntegration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationNotFoundException("Integration not found for integrationId=$integrationId") }
        validateIntegrationCompanyMatch(companyId, matchingIntegration.companyId, isOpsUser)

        log.info("Fetching saved document folder by integrationId $integrationId")
        val saveFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(integrationId = integrationId, folderType = type)
        val mappedSaveFolder = if (saveFolder != null) dataMapper.map(saveFolder.folderId, saveFolder.folderLabel) else null

        log.info("Fetching all platform document folders ${matchingIntegration.platform.name}")
        val documentFolders = getKnitDocumentFolders(matchingIntegration)

        return DocumentFolder.newBuilder()
            .savedFolder(mappedSaveFolder)
            .folders(documentFolders)
            .build()
    }

    fun upsertDocumentFolder(companyId: Long?, input: UpsertDocumentFolderInput, isOpsUser: Boolean = false): DocumentFolder {
        val integrationId = input.integrationId
        val matchingIntegration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationNotFoundException("Integration not found for integrationId=$integrationId") }
        validateIntegrationCompanyMatch(companyId, matchingIntegration.companyId, isOpsUser)

        val folders = getKnitDocumentFolders(matchingIntegration)
        val updateFolderId = input.folderId
        val targetFolder = folders?.firstOrNull {
            it.id == updateFolderId
        } ?: throw BadRequestException("Not found folderId $updateFolderId from existed folders")

        log.info("Fetching saved folder by integration id $integrationId")
        val savedFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(integrationId, input.type)
        val updatedSaveFolder = savedFolder?.apply {
            folderId = targetFolder.id
            folderLabel = targetFolder.label
        }
            ?: JpaDocumentFolder(
                folderType = input.type,
                folderId = targetFolder.id,
                folderLabel = targetFolder.label,
                companyIntegration = matchingIntegration
            )
        documentFoldersRepository.save(updatedSaveFolder)

        return DocumentFolder.newBuilder()
            .savedFolder(dataMapper.map(updatedSaveFolder.folderId, updatedSaveFolder.folderLabel))
            .folders(folders)
            .build()
    }

    private fun getKnitDocumentFolders(matchingIntegration: JpaCompanyIntegration): List<DocumentFolderDetail>? {
        val companyId = matchingIntegration.companyId
        val platformId = matchingIntegration.platform.id ?: return null
        return runBlocking {
            val folders = if (matchingIntegration.platform.name in UnsupportedFetchingDocumentCategoriesPlatforms) {
                val employeeDirectory = knitAdapter.getEmployeeDirectory(companyId, platformId)
                val firstEmployeeId = employeeDirectory.employees?.firstOrNull()?.id ?: return@runBlocking emptyList()
                val response = knitAdapter.getDocumentCategories(companyId, platformId, firstEmployeeId)
                response.categories?.map { dataMapper.map(it.id!!, it.name!!) }
            } else {
                val response = knitAdapter.getDocCategories(companyId, platformId)
                response.data?.categories?.map { dataMapper.map(it.id, it.name) }
            }
            folders
        }
    }
}

