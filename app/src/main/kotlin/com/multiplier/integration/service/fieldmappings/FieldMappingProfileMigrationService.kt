package com.multiplier.integration.service.fieldmappings

import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.common.jobs.annotations.Job
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.fieldmapping.grpc.schema.Rule
import com.multiplier.fieldmapping.grpc.schema.TransformationType
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.model.FieldType
import com.multiplier.integration.repository.model.JpaFieldsMapping
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.types.TaskResponse
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.Objects

@Component
class FieldMappingProfileMigrationService(
    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter,
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val fieldsMappingRepository: FieldsMappingRepository,
) {
    private val log = KotlinLogging.logger {}

    @Transactional
    @Job("Migrate field mappings to Profile rules")
    fun migrateFieldMappingsToProfilesByCompanyIdAndEntityIdAndIntegrationId(
        request: MigrationRequest
    ): TaskResponse {
        val entityId = request.entityId
        val companyId = request.companyId
        val integrationId = request.integrationId
        log.info("Starting FieldMapping to Profile Migration with companyId: ${companyId}, entityId: ${entityId} and integrationId: $integrationId.")
        try {
            val legalEntity =
                legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId).orElseThrow {
                    EntityNotFoundException(
                        "Not found legal entity mapping for entityId $entityId"
                    )
                }
            migrateEntityMapping(legalEntity)
            return TaskResponse(true, "Successfully migrated field mappings")
        }catch (e: Exception){
            log.error("Error in migration process: ${e.message}", e)
            return TaskResponse(false, "Failed to migrate field mappings: ${e.message}")
        }
    }

    @Transactional
    @Job("Migrate all field mappings to Profile rules")
    fun migrateFieldMappingsToProfiles(): TaskResponse {
        log.info("Starting FieldMappingProfileMigration.")

        try {
            legalEntityMappingRepository.findAll().forEach { legalEntity ->
                try {
                    migrateEntityMapping(legalEntity)
                } catch (e: Exception) {
                    log.error("Error migrating entity ${legalEntity.entityId}: ${e.message}", e)
                }
            }
        } catch (e: Exception) {
            log.error("Error in migration process: ${e.message}", e)
        }
        return TaskResponse(true, "Successfully retried received event")
    }

     fun migrateEntityMapping(legalEntity: JpaLegalEntityMapping) {
        val entityId = legalEntity.entityId
        val companyId = legalEntity.companyId
        val integrationId = legalEntity.integrationId
        log.info("Migrating field mappings for entityId: $entityId, integrationId: $integrationId")

        val profiles = fieldMappingServiceAdapter.listProfiles(companyId)
        val profile = profiles.profilesList.firstOrNull { profile ->
            profile.configMap.fieldsMap["entityId"]?.stringValue == entityId.toString() &&
                    profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
        } ?: createNewProfile(companyId, entityId, integrationId, legalEntity)

        if (profile.rulesCount == 0) {
            val fieldMappings = fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(entityId, integrationId)
            val ruleList = createRulesFromFieldMappings(fieldMappings)
            if (ruleList.isNotEmpty())
            fieldMappingServiceAdapter.updateProfile(profile.toBuilder().addAllRules(ruleList).build())
        }
    }

    private fun createNewProfile(
        companyId: Long,
        entityId: Long,
        integrationId: Long,
        legalEntity: JpaLegalEntityMapping,
    ): Profile {
        log.info("Creating new profile for entityId=$entityId and integrationId=$integrationId")

        val configMap = Struct.newBuilder().putAllFields(
            mapOf(
                "entityId" to Value.newBuilder().setStringValue(entityId.toString()).build(),
                "integrationId" to Value.newBuilder().setStringValue(integrationId.toString()).build(),
            )
        ).build()

        val profileRequest = Profile.newBuilder()
            .setName(legalEntity.entityName)
            .setDescription("Profile for ${legalEntity.entityName}")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setConfigMap(configMap)
            .build()

        return fieldMappingServiceAdapter.createProfile(profileRequest).profile
    }

    fun createRulesFromFieldMappings(fieldMappings: List<JpaFieldsMapping>): List<Rule> {
        if (fieldMappings.isEmpty()) return emptyList()

        fun isUniqueMapping(it: JpaFieldsMapping): Boolean =
            it.originField != null && it.mappedField != null &&
                    fieldMappings.none { mapping ->
                        mapping.originField == it.originField &&
                                mapping.parent == it.parent &&
                                (it.id ?: Long.MAX_VALUE) > (mapping.id ?: Long.MAX_VALUE)
                    }

        fun isCountryField(mapping: JpaFieldsMapping): Boolean =
            mapping.mappedField?.contains("country", ignoreCase = true) ?: false

        return fieldMappings.filter(::isUniqueMapping).map {
            val transformationType = when {
                it.type == FieldType.ENUM && !isCountryField(it) -> TransformationType.VALUE_TRANSFORMATION
                it.isCalculated -> TransformationType.FUNCTIONAL_TRANSFORMATION
                it.originField == "uanNumberExists" -> TransformationType.CONDITIONAL_MAPPING
                isCountryField(it) -> TransformationType.FUNCTIONAL_TRANSFORMATION
                else -> TransformationType.DIRECT_MAPPING
            }
            val transformationConfig = when {
                it.type == FieldType.ENUM && !isCountryField(it)-> Struct.newBuilder().putFields(
                    "mappings", Value.newBuilder().setStructValue(
                        Struct.newBuilder().putAllFields(
                            it.children.filter { child -> !child.mappedField.isNullOrBlank() }
                                .associate { child ->
                                    child.mappedField to Value.newBuilder()
                                        .setStringValue(child.originField).build()
                                }
                        )
                    ).build()
                ).build()
                it.isCalculated -> Struct.newBuilder().putFields(
                    "function", Value.newBuilder().setStringValue("COUNT").build()
                ).build()
                it.originField == "uanNumberExists" -> Struct.newBuilder()
                    .putFields(
                        "conditions",
                        Value.newBuilder().setListValue(
                            com.google.protobuf.ListValue.newBuilder().addValues(
                                Value.newBuilder().setStructValue(
                                    Struct.newBuilder()
                                        .putFields("operator", Value.newBuilder().setStringValue("exists").build())
                                        .putFields("result", Value.newBuilder().setStringValue("true").build())
                                        .build()
                                ).build()
                            ).build()
                        ).build()
                    )
                    .putFields("default", Value.newBuilder().setStringValue("false").build())
                    .build()
                isCountryField(it) -> Struct.newBuilder().putFields(
                    "function", Value.newBuilder().setStringValue("COUNTRY_ALPHA_3_CODE").build()
                ).build()
                else -> Struct.getDefaultInstance()
            }
            Rule.newBuilder()
                .setSourceField(it.mappedField)
                .setSourceLabel(it.mappedFieldLabel?:it.mappedField)
                .setTargetField(it.originField)
                .setTargetLabel(it.originFieldLabel?:it.originField)
                .setIsRequired(it.isRequired)
                .setTransformationType(transformationType)
                .setTransformationConfig(transformationConfig)
                .build()
        }
    }
}

data class MigrationRequest(
    val companyId: Long,
    val entityId: Long,
    val integrationId: Long
)