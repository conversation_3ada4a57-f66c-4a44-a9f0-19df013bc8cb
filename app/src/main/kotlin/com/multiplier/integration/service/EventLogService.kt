package com.multiplier.integration.service

import com.multiplier.common.exception.toSystemException
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.EntityNotFoundException
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Service
class EventLogService(
        private val eventLogRepository: EventLogRepository
) {
    private val log = KotlinLogging.logger {}

    fun findEventLogByEventId(eventId: String): JpaEventLog {
        return eventLogRepository.findByEventId(eventId).orElseThrow{
            EntityNotFoundException("Event Log with event id=$eventId not found")
        }
    }

    fun fetchToBeProcessedEvents(): List<JpaEventLog> {
        val now = LocalDateTime.now()
        val statuses = listOf(EventStatus.TO_BE_PROCESSED, EventStatus.TO_BE_PROCESSED_AGAIN)
        val events = eventLogRepository.findByStatusInAndNextAttemptBeforeOrdered(statuses, now)
        log.info("Found ${events.size} events to be processed")
        return events
    }

    fun fetchMemberUpdateEvents(): List<JpaEventLog> {
        val now = LocalDateTime.now()
        val statuses = listOf(EventStatus.TO_BE_PROCESSED, EventStatus.TO_BE_PROCESSED_AGAIN)
        val eventTypes = listOf(
            EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
            EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
            EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
            EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
        )
        val events = eventLogRepository.findByStatusInAndEventTypeInAndNextAttemptBeforeOrdered(statuses, eventTypes, now)
        if (events.isEmpty()) {
            log.info("No member update events found to be processed.")
        } else {
            log.info("Found ${events.size} member update events to be processed.")
        }
        return events
    }


    fun processEvent(event: JpaEventLog) {
        log.info("Processing event with eventId=${event.eventId}, current status: ${event.status}, retries left: ${event.retriesLeft}, retries done: ${event.retriesDone}")
        updateEventLog(
            eventLog = event,
            status = EventStatus.PROCESSING,
            retriesLeft = event.retriesLeft - 1,
            retriesDone = event.retriesDone + 1
        )
    }

    fun markEventSuccessful(event: JpaEventLog) {
        log.info("Marking event SUCCESS with eventId=${event.eventId}.")
        updateEventLog(
            eventLog = event,
            status = EventStatus.SUCCESS
        )
    }

    fun handleNonRetryableEvent(event: JpaEventLog, errorMessage: String?) {
        log.info("Updating NonRetryable event with eventId=${event.eventId}.")
        updateEventLog(
            eventLog = event,
            status = EventStatus.IGNORED,
            errorMessage = errorMessage
        )
    }

    fun handleRetryableEvent(event: JpaEventLog, errorMessage: String?) {
        log.info("Updating Retryable event with eventId=${event.eventId}.")
        val newStatus = if (event.retriesLeft <= 0) {
            EventStatus.FAILED
        } else {
            EventStatus.TO_BE_PROCESSED_AGAIN
        }
        log.info("Updating status of event with eventId=${event.eventId} to $newStatus")
        updateEventLog(
            eventLog = event,
            status = newStatus,
            errorMessage = errorMessage
        )
    }

    fun handleInvalidProcessingDateEvent(event: JpaEventLog, contract: ContractOuterClass.Contract) {
        log.info("Updating next attempt date to contract start date")
        val nextAttemptDate = LocalDate.of(contract.startOn.year, contract.startOn.month, contract.startOn.day)
        event.nextAttempt = nextAttemptDate.atStartOfDay()
        event.syncId = null // This makes sure that the sync report won't get affected by this event
        return try {
            eventLogRepository.save(event)
            log.info("Successfully updated and saved EventLog with ID=${event.id}.")
        } catch (e: Exception) {
            log.error("Error updating and saving EventLog with event ID=$event.", e)
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException("Error updating and saving EventLog with event ID=$event.", e)
        }
    }


    fun createEventLog(
        type: EventType,
        eventId: String = UUID.randomUUID().toString(),
        status: EventStatus = EventStatus.TO_BE_PROCESSED,
        payload: String,
        retries: Int = 3,
        errorMessage: String? = null,
        contractId: Long,
        syncId: String? = null
    ): JpaEventLog {
        val eventLog = JpaEventLog(
                eventType = type,
                eventId = eventId,
                eventPayload = payload,
                status = status,
                retriesLeft = retries,
                retriesDone = 0,
                nextAttempt = LocalDateTime.now(),
                errorMessage = errorMessage,
                contractId = contractId,
                syncId = syncId
        )

        return try {
            eventLogRepository.save(eventLog)
            log.info("Successfully created and saved EventLog with ID=${eventLog.id}.")
            eventLog
        } catch (e: Exception) {
            log.error("Error creating and saving EventLog with event ID=$eventId.", e)
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException("Error creating and saving EventLog with event ID=$eventId.", e)
        }
    }


    fun createPayableEventLog(
        type: EventType,
        eventId: String = UUID.randomUUID().toString(),
        status: EventStatus = EventStatus.TO_BE_PROCESSED,
        payload: String,
        retries: Int = 3,
        errorMessage: String? = null,
        syncId: String? = null
    ): JpaEventLog {
        val eventLog = JpaEventLog(
            eventType = type,
            eventId = eventId,
            eventPayload = payload,
            status = status,
            retriesLeft = retries,
            retriesDone = 0,
            nextAttempt = LocalDateTime.now().plusSeconds(60),
            errorMessage = errorMessage,
            contractId = -1,
            syncId = syncId
        )

        return try {
            eventLogRepository.save(eventLog)
            log.info("Successfully created and saved Payable EventLog with ID=${eventLog.id}.")
            eventLog
        } catch (e: Exception) {
            log.error("Error creating and saving Payable EventLog with event ID=$eventId.", e)
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException("Error creating and saving Payable EventLog with event ID=$eventId.", e)
        }
    }

    fun updateEventLog(
        eventLog: JpaEventLog,
        status: EventStatus? = null,
        retriesLeft: Int? = null,
        retriesDone: Int? = null,
        errorMessage: String? = null,
        nextAttemptToBeMadeInSeconds: Long = 60,
    ): JpaEventLog {
        // Update properties if the respective arguments are provided (not null)
        status?.let { eventLog.status = it }
        retriesLeft?.let { eventLog.retriesLeft = it }
        retriesDone?.let { eventLog.retriesDone = it }
        errorMessage?.let { eventLog.errorMessage = it }

        // Always update the nextAttempt when the event log is updated
        if (status != EventStatus.SUCCESS) {
            if (retriesDone != null) {
                eventLog.nextAttempt = LocalDateTime.now().plusSeconds(nextAttemptToBeMadeInSeconds*retriesDone)
            } else {
                eventLog.nextAttempt = LocalDateTime.now().plusSeconds(nextAttemptToBeMadeInSeconds)
            }
        }
        eventLog.lastAttempt = LocalDateTime.now()

        if (eventLog.retriesLeft <= 0) {
            eventLog.nextAttempt = null
            if (status != EventStatus.SUCCESS) {
                eventLog.status = EventStatus.FAILED
            }
        }

        return try {
            eventLogRepository.save(eventLog)
            log.info("Successfully updated and saved EventLog with ID=${eventLog.id}.")
            eventLog
        } catch (e: Exception) {
            log.error("Error updating and saving EventLog with event ID=${eventLog.eventId}.", e)
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException("Error updating and saving EventLog with event ID=${eventLog.eventId}.", e)
        }
    }



    fun save(event: JpaEventLog) {
        try {
            eventLogRepository.save(event)
            log.info("Successfully saved EventLog with ID=${event.id}.")
        } catch (e: Exception) {
            log.error("Error saving EventLog with ID=${event.id}.", e)
        }
    }
}
