package com.multiplier.integration.service.grpc

import com.multiplier.integration.schema.CustomerIntegration.GetIntegrationFromExternalCompanyIdAndPlatformNameRequest
import com.multiplier.integration.schema.CustomerIntegration.GetIntegrationFromExternalCompanyIdAndPlatformNameResponse
import com.multiplier.integration.schema.CustomerIntegrationServiceGrpcKt
import com.multiplier.integration.service.CustomerIntegrationService
import io.grpc.Status
import kotlinx.coroutines.Dispatchers
import mu.KotlinLogging
import net.devh.boot.grpc.server.service.GrpcService

private val log = KotlinLogging.logger {}

@GrpcService
class CustomerIntegrationGrpcService(
    private val customerIntegrationService: CustomerIntegrationService,
) : CustomerIntegrationServiceGrpcKt.CustomerIntegrationServiceCoroutineImplBase(
    coroutineContext = Dispatchers.Unconfined
) {
    override suspend fun getIntegrationFromExternalCompanyIdAndPlatformName(request: GetIntegrationFromExternalCompanyIdAndPlatformNameRequest): GetIntegrationFromExternalCompanyIdAndPlatformNameResponse {
        log.info("getIntegrationFromExternalCompanyIdAndPlatformName request: ${request.platformCompanyId} - ${request.platformName}")
        try {
            val customerIntegration =
                customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                    request.platformCompanyId,
                    request.platformName
                )
            return GetIntegrationFromExternalCompanyIdAndPlatformNameResponse.newBuilder()
                .setCompanyId(customerIntegration.companyId)
                .build()
        } catch (e: Exception) {
            log.error("GetIntegrationFromExternalCompanyIdAndPlatformName exception", e)
            throw GrpcExceptionWrapper(Status.INTERNAL, e)
        }
    }
}