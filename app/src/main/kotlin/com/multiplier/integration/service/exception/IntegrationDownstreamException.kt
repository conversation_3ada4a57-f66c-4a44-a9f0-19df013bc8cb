package com.multiplier.integration.service.exception

import com.multiplier.common.exception.MplSystemException
import mu.KotlinLogging

class IntegrationDownstreamException(message: String) :
    MplSystemException(CustomerErrorCode.DOWNSTREAM_ERROR, message)

object DownstreamServiceUtils {

    private val log = KotlinLogging.logger {}

    fun <T> execute(block: () -> T): T {
        return try {
            block()
        } catch (e: Exception) {
            log.warn("Downstream service error: " + e.message, e)
            throw IntegrationDownstreamException(e.message ?: "Downstream service error")
        }
    }
}