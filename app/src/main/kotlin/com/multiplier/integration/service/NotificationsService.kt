package com.multiplier.integration.service

import com.multiplier.contract.offboarding.email.MemberEmails
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.country.schema.Country
import com.multiplier.integration.adapter.api.BulkUploadServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.CountryServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.ValidationSummaryChunk
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest
import com.multiplier.integration.service.email.AdminEmailTemplateGenerator
import com.multiplier.integration.service.email.EmailService
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.NotificationType
import com.multiplier.integration.utils.FTPDirectoryUtil
import com.multiplier.integration.utils.toCapitalize
import com.multiplier.pigeonservice.dto.Attachment
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import mu.KotlinLogging
import org.apache.logging.log4j.util.Strings
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.util.StringUtils
import utils.StringUtils.Companion.maskEmail
import utils.getCompanyCountryCode
import utils.getCompanyUserEmail
import utils.getFullName
import utils.getPrimaryOrDefaultEmailOrThrow
import java.time.LocalDate
import com.multiplier.company.schema.grpc.CompanyOuterClass as NewCompanyOuterClass

@Service
class NotificationsService(

    private val emailService: EmailService,
    private val memberEmailBuilder: MemberEmails,
    private val adminEmailTemplateGenerator: AdminEmailTemplateGenerator,
    private val memberService: MemberServiceAdapter,
    private val countryService: CountryServiceAdapter,
    private val docgenServiceAdapter: DocgenServiceAdapter,
    private val contractService: ContractServiceAdapter,
    private val newCompanyServiceAdapter: NewCompanyServiceAdapter,
    private val bulkJobTrackerService: BulkJobTrackerService,
    private val bulkUploadServiceAdapter: BulkUploadServiceAdapter,
) {

    @Value("\${platform.userservice.system.notification.email}")
    private lateinit var systemNotificationEmail: String

    @Value("\${ops.support-email}")
    private lateinit var opsSupportEmail: String

    private val log = KotlinLogging.logger {}

    @Value("\${platform.base-url}")
    private lateinit var baseUrl: String

    fun sendMemberToUpdateBasicDetailEmailNotification(contract: Contract, platformName: String) {
        log.info("Invoking sendMemberToUpdateBasicDetailEmailNotification for contractId: ${contract.id}")

        val email = getMemberUpdateDetailsEmail(contract, platformName)
        emailService.sendEmail(email)

        log.info("Successfully invoked sendMemberToUpdateBasicDetailEmailNotification for contractId: ${contract.id}")
    }

    fun sendMemberToUpdateBankDetailEmailNotification(contract: Contract, platformName: String) {
        log.info("Invoking sendMemberToUpdateBankDetailEmailNotification for contractId: ${contract.id}")

        val email = getMemberUpdateBankDetailsEmail(contract, platformName)
        emailService.sendEmail(email)

        log.info("Successfully invoked sendMemberToUpdateBankDetailEmailNotification for contractId: ${contract.id}")
    }

    @Async
    fun sendAdminEmployeesImportCompleted(companyId: Long, platformName: String) {
        log.info("Invoking sendAdminEmployeesImportCompleted for companyId: $companyId")

        val emails = getImportCompletedEmail(companyId, platformName)
        if (emails.isEmpty()) {
            log.info("No emails to send for companyId: $companyId")
            return
        }
        emails.forEach { email ->
           this.trySendEmail(email, email.to)
        }
        log.info("Successfully completed sendAdminEmployeesImportCompleted for companyId: $companyId")
    }

    @Async
    fun sendAdminIntegrationCredentialExpired(companyId: Long, platformName: String) {
        log.info("Invoking sendAdminIntegrationCredentialExpired for companyId: $companyId")

        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)

        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo.toString(),
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName
                )
                val emailBody =
                    adminEmailTemplateGenerator.getEmailTemplateForIntegrationCredentialExpired(email, platformName)
                        .apply { putAllContentVariables(templateParams) }
                        .build()
                this.trySendEmail(emailBody, email)
            }
        }
        log.info("Successfully completed sendAdminIntegrationCredentialExpired for companyId: $companyId")
    }

    @Async
    fun sendAdminIntegrationConnected(companyId: Long, platformName: String) {
        log.info("Invoking sendAdminIntegrationConnected for companyId: $companyId")
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)

        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo.toString(),
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName
                )
                val emailBody =
                    adminEmailTemplateGenerator.getEmailTemplateForIntegrationConnected(email, platformName)
                        .apply { putAllContentVariables(templateParams) }
                        .build()
                this.trySendEmail(emailBody, email)
            }
        }
        log.info("Successfully completed sendAdminIntegrationConnected for companyId: $companyId")
    }


    @Async
    fun sendAdminIntegrationDisconnected(companyId: Long, platformName: String, companyUserId: Long, platformId: Long) {
        log.info("Invoking sendAdminIntegrationDisconnected for companyId: $companyId")
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)
        val integrationUrl = "$baseUrl/company/integrations/$platformId"

        val initiatingAdminName = companyAdmins.usersList.firstOrNull { it.userId == companyUserId.toString() }?.let { it.getFullName() }
            ?: throw IntegrationInternalServerException("Company user not found for companyId: $companyId and userId: $companyUserId")

        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo.toString(),
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName,
                    "initiatingAdminName" to initiatingAdminName,
                    "link" to integrationUrl
                )
                val emailBody =
                    adminEmailTemplateGenerator.getEmailTemplateForIntegrationDisconnected(email, platformName)
                        .apply { putAllContentVariables(templateParams) }
                        .build()
                this.trySendEmail(emailBody, email)
            }
        }
        log.info("Successfully completed sendAdminIntegrationDisconnected for companyId: $companyId")
    }

    @Async
    fun sendAdminOutgoingSyncDisabled(companyId: Long, platformName: String, platformId: Long?) {
        log.info("Invoking sendAdminOutgoingSyncDisabled for companyId: $companyId, platformName: $platformName")
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)
        val integrationUrl = "$baseUrl/company/integrations/$platformId"

        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo.toString(),
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName,
                    "link" to integrationUrl
                )
                val emailBody =
                    adminEmailTemplateGenerator.getEmailTemplateForOutgoingSyncDisabled(email, platformName)
                        .apply { putAllContentVariables(templateParams) }
                        .build()
                this.trySendEmail(emailBody, email)
            }
        }
        log.info("Successfully completed sendAdminOutgoingSyncDisabled for companyId: $companyId")
    }

    @Async
    fun sendAdminAutoSyncFailed(companyId: Long, platformName: String, userName: String, errors: String, syncId: String?) {
        if (syncId != null) {
            log.info("Not invoking sendAdminAutoSyncFailed for manual sync or first time sync: $syncId")
            return
        }
        log.info("Invoking sendAdminAutoSyncFailed for companyId: $companyId, platformName: $platformName")
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)

        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo.toString(),
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName,
                    "userName" to userName,
                    "errors" to errors
                )
                val emailBody =
                    adminEmailTemplateGenerator.getEmailTemplateForAutoSyncFailed(email, platformName)
                        .apply { putAllContentVariables(templateParams) }
                        .build()
                this.trySendEmail(emailBody, email)
            }
        }
        log.info("Successfully completed sendAdminOutgoingSyncDisabled for companyId: $companyId")
    }

    private fun getMemberUpdateDetailsEmail(contract: Contract, platformName: String): EmailNotificationBody {
        val company = getCompanyDetails(contract.companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val member = getMemberDetails(contract)
        val builder =
            memberEmailBuilder.getEmailToUpdateBasicDetail(getMemberEmailByContractId(contract.id))
        val currentDate = LocalDate.now()
        val templateParams =
            mapOf(
                "companyLogoLink" to companyLogoLink,
                "showCompanyLogo" to showCompanyLogo.toString(),
                "companyName" to company.name,
                "country" to company.country,
                "employeeFullName" to member.name,
                "date" to LocalDate.of(currentDate.getYear(), currentDate.getMonth(), currentDate.lengthOfMonth()).toString(),
                "platformName" to platformName,
                "link" to baseUrl
            )
        builder.putAllContentVariables(templateParams)

        return builder.build()
    }

    private fun getMemberUpdateBankDetailsEmail(contract: Contract, platformName: String): EmailNotificationBody {
        val company = getCompanyDetails(contract.companyId!!)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val member = getMemberDetails(contract)
        val builder =
            memberEmailBuilder.getEmailToUpdateBankDetail(getMemberEmailByContractId(contract.id))
        val currentDate = LocalDate.now()
        val templateParams =
            mapOf(
                "companyLogoLink" to companyLogoLink,
                "showCompanyLogo" to showCompanyLogo.toString(),
                "companyName" to company.name,
                "country" to company.country,
                "employeeFullName" to member.name,
                "date" to LocalDate.of(currentDate.getYear(), currentDate.getMonth(), currentDate.lengthOfMonth()).toString(),
                "platformName" to platformName,
                "link" to baseUrl
            )
        builder.putAllContentVariables(templateParams)

        return builder.build()
    }

    private fun getImportCompletedEmail(companyId: Long, platformName: String): List<EmailNotificationBody> {
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink).toString()
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)

        return companyAdmins.usersList.mapNotNull { companyAdmin ->
            companyAdmin.getCompanyUserEmail()?.let { email ->
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo,
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName,
                    "link" to baseUrl
                )
                adminEmailTemplateGenerator.getEmailTemplateForEmployeeImportCompletion(email, platformName)
                    .apply { putAllContentVariables(templateParams) }
                    .build()
            }
        }
    }

    private fun getMemberDetails(contract: Contract): MemberDetails {
        val member = memberService.getMember(contract.memberId)
        val email = member.getPrimaryOrDefaultEmailOrThrow()
        val name = "${member.firstName} ${member.lastName}"
        return MemberDetails(name, email)
    }

    private fun getCompanyDetails(companyId: Long): CompanyDetails {
        val company = newCompanyServiceAdapter.getCompanyById(companyId)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)
        var companyAdminName = "UNKNOWN"
        var companyAdminEmail = "UNKNOWN"

        if (companyAdmins.usersCount > 0) {
            val companyAdmin = companyAdmins.getUsers(0)
            companyAdminName = "${companyAdmin.firstName} ${companyAdmin.lastName}"
            companyAdminEmail = companyAdmin.getPrimaryOrDefaultEmailOrThrow()
        }

        val countryCode = Country.GrpcCountryCode.valueOf(company.getCompanyCountryCode()!!)
        val companyCountry = countryService.getCountryNameByCode(countryCode)

        return CompanyDetails(
            company.displayName,
            companyCountry,
            companyAdminName,
            companyAdminEmail,
            company.companyLogoId.value)
    }

    private fun fetchLogoLink(logoId: Long?): String? {
        if (logoId == null || logoId == 0L || logoId == -1L) {
            return Strings.EMPTY
        }
        try {
            val document = docgenServiceAdapter.getDocument(logoId)

            return document?.downloadURL()?.toString()
        } catch (e: Exception) {
            e.printStackTrace()
            return Strings.EMPTY
        }
    }

    private fun trySendEmail(
        emailBody: EmailNotificationBody,
        email: String
    ) {
        try {
            emailService.sendEmail(emailBody)
            log.info("Email sent to ${maskEmail(email)}")
        } catch (e: Exception) {
            log.error("Error sending email to ${maskEmail(email)}: ${e.message}")
        }
    }

    @Async
    fun sendingResultEmail(companyAdmins: NewCompanyOuterClass.CompanyUsers, attachments: List<Attachment>, templateType: NotificationType? = NotificationType.CompanyInvitation, subject: String? = "Result file", templateParams: MutableMap<String, String>? = null) {
        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val emailBuilder = EmailNotificationBody.newBuilder()
                emailBuilder.setSubject(subject)
                emailBuilder.setFrom(systemNotificationEmail)
                emailBuilder.setTo(email)
                emailBuilder.setTemplateType(templateType.toString())
                if (!templateParams.isNullOrEmpty()) {
                    templateParams["employeeFullName"] = companyAdmin.getFullName()
                    emailBuilder.putAllContentVariables(templateParams)
                }
                emailService.sendEmailWithAttachment(emailBuilder.build(), attachments)
            }
        }
    }

    private fun getMemberEmailByContractId(id: Long): String {
        return contractService.getContractMemberEmailsByContractIds(setOf(id)).getValue(id)
    }

    @Async
    fun sendAdminGPSyncClosure(syncId: String, companyId: Long, platformName: String) {
        log.info("Invoking sendAdminStaleGPSyncDeactivated for companyId: $companyId, syncId: $syncId")
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val showCompanyLogo = StringUtils.hasText(companyLogoLink)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)

        companyAdmins.usersList.forEach { companyAdmin ->
            val email = companyAdmin.getCompanyUserEmail()
            if (email != null) {
                val templateParams = mapOf(
                    "companyLogoLink" to companyLogoLink,
                    "showCompanyLogo" to showCompanyLogo.toString(),
                    "companyName" to company.name,
                    "country" to company.country,
                    "adminName" to companyAdmin.getFullName(),
                    "platformName" to platformName,
                    "syncId" to syncId,
                )
                val emailBody =
                    adminEmailTemplateGenerator.getEmailTemplateForStaleGPSyncDeactivated(email, platformName)
                        .apply { putAllContentVariables(templateParams) }
                        .build()
                try {
                    emailService.sendEmail(emailBody)
                    log.info("sendAdminStaleGPSyncDeactivated Email sent to ${maskEmail(email)}")
                } catch (e: Exception) {
                    log.error("Error sendAdminStaleGPSyncDeactivated email to ${maskEmail(email)}: ${e.message}")
                }
            }
        }
        log.info("Successfully completed sendAdminStaleGPSyncDeactivated for companyId: $companyId")
    }

    fun notifySFTPUpsertSuccess(bulkJobTrackerId: Long) {
        log.info { "[notifySFTPUpsertSuccess] notify admins for SFTP data creation success with bulkJobTrackerId=$bulkJobTrackerId" }
        notifySFTP(
            bulkJobTrackerId = bulkJobTrackerId,
            expectedStatus = BulkJobStatus.UPSERT_SUCCESSFUL,
        )
        log.info { "[notifySFTPUpsertSuccess] success to notify admins for SFTP data creation success with bulkJobTrackerId=$bulkJobTrackerId" }
    }

    fun notifySFTPValidationFailure(bulkJobTrackerId: Long) {
        log.info { "[notifySFTPValidationFailure] notify admins for SFTP validation failure with bulkJobTrackerId=$bulkJobTrackerId" }
        notifySFTP(
            bulkJobTrackerId = bulkJobTrackerId,
            expectedStatus = BulkJobStatus.VALIDATION_FAILED,
        )
        log.info { "[notifySFTPValidationFailure] success to notify admins for SFTP validation failure with bulkJobTrackerId=$bulkJobTrackerId" }
    }

    private fun notifySFTP(
        bulkJobTrackerId: Long,
        expectedStatus: BulkJobStatus,
    ) {
        val bulkJobTracker = bulkJobTrackerService.findByIdOrThrow(bulkJobTrackerId)
        if (bulkJobTracker.jobStatus != expectedStatus) {
            log.info { "[notifySFTP] bulkJobTracker jobStatus=${bulkJobTracker.jobStatus}, skipping notification" }
            return
        }

        val bulkJob = bulkUploadServiceAdapter.getJob(bulkJobTracker.jobId)
        val validationChunkByName = bulkJob.validationSummary.validationSummaryChunks.associateBy { it.name }
        val companyData = fetchCompanyData(bulkJobTracker.companyId)
        val fileName = FTPDirectoryUtil.getFileName(bulkJobTracker.originalFileURI!!)

        bulkJobTracker.moduleNames.forEach { moduleName ->
            sendSFTPNotificationEmail(
                moduleName = moduleName,
                fileName = fileName,
                validationChunks = validationChunkByName,
                companyData = companyData,
                jobStatus = expectedStatus,
                reportURI = if (expectedStatus == BulkJobStatus.VALIDATION_FAILED) bulkJobTracker.reportFileURI!! else null
            )
        }
        log.info { "[notifySFTP] Success to notify admins for jobStatus=$expectedStatus with bulkJobTrackerId=$bulkJobTrackerId" }
    }

    private fun sendSFTPNotificationEmail(
        moduleName: String,
        fileName: String,
        validationChunks: Map<String, ValidationSummaryChunk>,
        companyData: CompanyData,
        jobStatus: BulkJobStatus,
        reportURI: String? = null
    ) {
        val chunkName = getChunkName(moduleName)
        val chunk = validationChunks[chunkName]
        if (chunk == null) {
            log.warn { "Could not find validation summary chunk for module $moduleName" }
            return
        }

        val emails = createSFTPEmailBodies(
            companyData = companyData,
            moduleName = moduleName,
            fileName = fileName,
            jobStatus = jobStatus,
            entriesCount = if (jobStatus == BulkJobStatus.UPSERT_SUCCESSFUL) chunk.identifiedRowCount else null,
            reportURI = reportURI
        )
        emails.forEach { trySendEmail(emailBody = it, email = it.to) }
    }

    private fun createSFTPEmailBodies(
        companyData: CompanyData,
        moduleName: String,
        fileName: String,
        jobStatus: BulkJobStatus,
        entriesCount: Int? = null,
        reportURI: String? = null
    ): List<EmailNotificationBody> {
        return companyData.companyAdmins.usersList.mapNotNull { admin ->
            createSFTPEmailForAdmin(
                admin = admin,
                companyData = companyData,
                moduleName = moduleName,
                fileName = fileName,
                jobStatus = jobStatus,
                entriesCount = entriesCount,
                reportURI = reportURI
            )
        }
    }

    private fun createSFTPEmailForAdmin(
        admin: NewCompanyOuterClass.CompanyUser,
        companyData: CompanyData,
        moduleName: String,
        fileName: String,
        jobStatus: BulkJobStatus,
        entriesCount: Int? = null,
        reportURI: String? = null
    ): EmailNotificationBody? {
        val email = admin.getCompanyUserEmail() ?: return null
        val featureName = getFeatureName(moduleName)

        val templateParams = mutableMapOf(
            "companyName" to companyData.name,
            "companyCountry" to companyData.country,
            "companyLogoLink" to companyData.companyLogoLink,
            "adminName" to admin.getFullName(),
            "bulkModule" to featureName,
            "fileName" to fileName
        )

        if (entriesCount != null) {
            templateParams["entriesCount"] = entriesCount.toString()
        }

        if (reportURI != null) {
            templateParams["reportURI"] = reportURI
        }

        val emailBuilder = when (jobStatus) {
            BulkJobStatus.UPSERT_SUCCESSFUL -> adminEmailTemplateGenerator.getEmailTemplateForSFTPIntegrationUpsertSuccess(
                adminEmail = email,
                entriesCount = entriesCount!!,
                fileName = fileName,
                feature = featureName
            )
            BulkJobStatus.VALIDATION_FAILED -> adminEmailTemplateGenerator.getEmailTemplateForSFTPIntegrationValidationFailure(
                adminEmail = email,
                fileName = fileName,
                feature = featureName
            )
            else -> {
                log.warn { "[createSFTPEmailForAdmin] jobStatus $jobStatus is not supported" }
                return null
            }
        }

        return emailBuilder
            .apply { putAllContentVariables(templateParams) }
            .build()
    }

    private fun fetchCompanyData(companyId: Long): CompanyData {
        val company = getCompanyDetails(companyId)
        val companyLogoLink = fetchLogoLink(company.companyLogoId)
        val companyAdmins = newCompanyServiceAdapter.getCompanyAdmins(companyId)
        return CompanyData(company.name, company.country, companyLogoLink, companyAdmins)
    }

    private fun getFeatureName(moduleName: String): String {
        return when(moduleName) {
            BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name -> "timesheet"
            else -> moduleName
        }
    }

    private fun getChunkName(moduleName: String): String {
        return when (moduleName) {
            BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name -> "EOR_TIMESHEET"
            else -> moduleName
        }
    }

    /**
     * Sends an email notification to the operations team about a new SFTP access request.
     *
     * @param request The SFTP access request that was created
     */
    fun sendSFTPAccessRequestNotificationToOps(request: JpaSFTPAccessRequest) {
        log.info { "[sendSftpAccessRequestNotificationToOps] Sending notification to ops team for SFTP access request ID ${request.id}" }

        // Get company name for the request
        val company = getCompanyDetails(request.companyId)

        val emailTemplate = adminEmailTemplateGenerator.getEmailTemplateForSftpAccessRequest(
            opsEmail = opsSupportEmail
        )

        // Add content variables for the email template
        val templateParams = mapOf(
            "requestId" to request.id.toString(),
            "companyId" to request.companyId.toString(),
            "companyName" to company.name.toCapitalize(),
            "entityId" to request.entityId.toString(),
            "bulkModule" to request.bulkModule.name,
        )

        emailTemplate.putAllContentVariables(templateParams)
        trySendEmail(emailTemplate.build(), opsSupportEmail)

        log.info { "[sendSftpAccessRequestNotificationToOps] Successfully sent notification to ops team for SFTP access request ID ${request.id}" }
    }
}

private data class CompanyDetails(
    val name: String,
    val country: String,
    val adminName: String,
    val adminEmail: String,
    val companyLogoId: Long? = null
)

private data class MemberDetails(
    val name: String,
    val email: String,
)

private data class CompanyData(
    val name: String,
    val country: String,
    val companyLogoLink: String?,
    val companyAdmins: NewCompanyOuterClass.CompanyUsers
)
