package com.multiplier.integration.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.opencsv.CSVReader
import mu.KotlinLogging
import java.io.InputStream
import java.io.InputStreamReader
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

private val log = KotlinLogging.logger {}

private const val EMPLOYEE_EMAIL = "email"
private const val DATE = "date"
private const val WORK_START_TIME = "workStartTime"
private const val WORK_END_TIME = "workEndTime"

object CsvTimeSheetFileExtractor : FileExtractor {

    data class HeaderIndices(
        val userEmail: Int,
        val startTime: Int,
        val endTime: Int,
    )

    override fun extractEntries(inputStream: InputStream): List<Map<String, String>> {
        log.info { "[extractEntries] extract entries from csv file" }
        val allRows = CSVReader(InputStreamReader(inputStream)).use { it.readAll() }

        if (allRows.isEmpty()) {
            throw CustomerErrorCode.INVALID_FILE_FORMAT.toBusinessException(
                message = "CSV file is empty"
            )
        }

        val indices = validateHeadersAndGetIndices(allRows.first())

        // Process data rows (skip header row)
        val entries = allRows.drop(1).mapNotNull { processRow(it, indices) }
        if (entries.isEmpty()) {
            throw CustomerErrorCode.INVALID_FILE_FORMAT.toBusinessException(
                message = "CSV file has no valid data rows"
            )
        }

        log.info { "[extractEntries] extract entries from csv file with total entries: ${entries.size}" }
        return entries
    }

    private fun validateHeadersAndGetIndices(headerRow: Array<String>): HeaderIndices {
        val requiredHeaders = mapOf(
            EMPLOYEE_EMAIL to listOf("user_email"),
            WORK_START_TIME to listOf("start_time"),
            WORK_END_TIME to listOf("end_time")
        )

        val columnIndices = mutableMapOf<String, Int>()

        // Find the index of each required column
        for ((key, possibleNames) in requiredHeaders) {
            var foundIndex = -1
            for (i in headerRow.indices) {
                val headerValue = headerRow[i].trim()
                if (possibleNames.any { it.equals(headerValue, ignoreCase = true) }) {
                    foundIndex = i
                    break
                }
            }

            if (foundIndex == -1) {
                throw CustomerErrorCode.INVALID_FILE_FORMAT.toBusinessException(
                    message = "Required column not found: ${possibleNames.first()}. Available columns: ${headerRow.joinToString(", ")}"
                )
            }

            columnIndices[key] = foundIndex
        }

        return HeaderIndices(
            userEmail = columnIndices[EMPLOYEE_EMAIL]!!,
            startTime = columnIndices[WORK_START_TIME]!!,
            endTime = columnIndices[WORK_END_TIME]!!
        )
    }

    private fun processRow(row: Array<String>, indices: HeaderIndices): Map<String, String>? {
        // Skip empty rows
        if (row.isEmpty() || row.all { it.isBlank() }) {
            return null
        }

        val userEmail = safeGetColumn(row, indices.userEmail).trim()
        val startTimeStr = safeGetColumn(row, indices.startTime).trim()
        val endTimeStr = safeGetColumn(row, indices.endTime).trim()

        // Skip rows with empty employee email (only required field)
        if (userEmail.isBlank()) {
            return null
        }

        return mapOf(
            EMPLOYEE_EMAIL to userEmail,
            DATE to extractDateFromIsoDateTime(startTimeStr),
            WORK_START_TIME to extractTimeFromIsoDateTime(startTimeStr),
            WORK_END_TIME to extractTimeFromIsoDateTime(endTimeStr),
        )
    }

    private fun safeGetColumn(row: Array<String>, columnIndex: Int): String {
        return try {
            if (columnIndex < row.size) row[columnIndex] else ""
        } catch (_: IndexOutOfBoundsException) {
            ""
        }
    }

    private fun extractDateFromIsoDateTime(dateTimeStr: String): String {
        return if (dateTimeStr.isBlank()) {
            ""
        } else {
            try {
                val offsetDateTime = OffsetDateTime.parse(dateTimeStr)
                offsetDateTime.toLocalDate().format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
            } catch (e: DateTimeParseException) {
                throw CustomerErrorCode.INVALID_FILE_FORMAT.toBusinessException(
                    message = "Invalid datetime format: '$dateTimeStr'. Expected ISO 8601 format (e.g., 2025-06-02T14:00:30-04:00)",
                    cause = e,
                )
            }
        }
    }

    private fun extractTimeFromIsoDateTime(dateTimeStr: String): String {
        return if (dateTimeStr.isBlank()) {
            ""
        } else {
            try {
                val offsetDateTime = OffsetDateTime.parse(dateTimeStr)
                offsetDateTime.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm"))
            } catch (e: DateTimeParseException) {
                throw CustomerErrorCode.INVALID_FILE_FORMAT.toBusinessException(
                    message = "Invalid datetime format: '$dateTimeStr'. Expected ISO 8601 format (e.g., 2025-06-02T14:00:30-04:00)",
                    cause = e,
                )
            }
        }
    }
}