package com.multiplier.integration.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.repository.BulkJobTrackerRepository
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaBulkJobTracker
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.service.exception.CustomerErrorCode
import org.springframework.stereotype.Service

data class BulkJobTrackerDTO(
    val id: Long,
    val jobStatus: BulkJobStatus,
    val originalFileURI: String,
    val reportFileURI: String? = null,
    val reportURIType: URIType? = null,
)

@Service
class BulkJobTrackerService(
    private val bulkJobTrackerRepository: BulkJobTrackerRepository,
) {

    fun findByIdOrThrow(id: Long): JpaBulkJobTracker {
        return bulkJobTrackerRepository.findById(id)
            .orElseThrow { CustomerErrorCode.ENTITY_NOT_FOUND.toBusinessException(
                message = "Bulk Job Tracker not found for id: $id",
            ) }
    }

    fun findByJobIdOrThrow(jobId: Long): JpaBulkJobTracker {
        return bulkJobTrackerRepository.findByJobId(jobId)
            ?: throw CustomerErrorCode.ENTITY_NOT_FOUND.toBusinessException(
                message = "Bulk Job Tracker not found for jobId: $jobId",
            )
    }

    fun createBulkJobTracker(input: JpaBulkJobTracker): JpaBulkJobTracker {
        validateForCreateOrThrow(input)
        return bulkJobTrackerRepository.save(input)
    }

    private fun validateForCreateOrThrow(input: JpaBulkJobTracker) {
        bulkJobTrackerRepository.findByJobId(input.jobId)
            ?.let {
                throw CustomerErrorCode.INTERNAL_ERROR.toSystemException(
                    message = "Bulk Job Tracker already exists for jobId: ${input.jobId}",
                )
            }
    }

    fun updateBulkJobTracker(input: BulkJobTrackerDTO): JpaBulkJobTracker {
        val entity = findByIdOrThrow(input.id)
        entity.jobStatus = input.jobStatus
        entity.originalFileURI = input.originalFileURI
        entity.reportFileURI = input.reportFileURI
        entity.reportURIType = input.reportURIType
        return bulkJobTrackerRepository.save(entity)
    }
}