package com.multiplier.integration.service.fieldmappings

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.contract.onboarding.schema.copy
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData.Group
import mu.KotlinLogging
import java.time.LocalDate
import java.time.format.DateTimeFormatter

data class GroupedEmployeeData(
    val employeeData: Map<String, String>,
    val compensationData: List<Map<String, String>> = emptyList(),
) {

    enum class Group {
        EMPLOYMENT_DATA,
        COMPENSATION_DATA
    }

    val groups
        get() = compensationData + employeeData

    val all
        get() = groups.flatMap { it.entries }.associate { it.key to it.value }

}

private val log = KotlinLogging.logger {}

object CompensationSchemaMapper {

    fun mapToBulkOnboardDataSpec(data: List<BulkOnboardDataSpec>): List<BulkOnboardDataSpec> {
        val nonCompensationData = data.filter { it.group != Group.COMPENSATION_DATA.name }

        val componentSpecs: List<BulkOnboardDataSpec> = data
            .find { it.key == "COMPONENT_NAME" }
            .specForEachComponent()

        if (componentSpecs.isEmpty()) {
            return nonCompensationData
        }

        val billingFrequency = data.copyCompensationAttribute("BILLING_FREQUENCY")
        val currency = data.copyCompensationAttribute("CURRENCY")
        val paySchedule = compensationAttribute()

        return nonCompensationData + componentSpecs + listOfNotNull(billingFrequency, paySchedule, currency)
    }

    fun groupEmployeeData(
        input: Map<String, String>,
        startDate: LocalDate,
        payScheduleName: String,
    ): GroupedEmployeeData {
        val components = input.entries.filter { it.key.startsWith("COMP_ITEM:") && it.value.isNotEmpty() }
        val attributes = input.entries.filter { it.key.startsWith("COMP_ATTR:") }
        val employeeData = input.entries.filter { !it.key.startsWith("COMP_") }.associate { it.key to it.value }
        val employeeId = input["employeeId"]
        val compensationAttributes = attributes.map { it.key.removePrefix("COMP_ATTR:") to it.value.trim() }

        log.info("Compensation components: {}, compensation attributes: {}, employeeId: {}", components, attributes, employeeId)

        if (components.isEmpty() || compensationAttributes.isEmpty() || employeeId == null) {
            log.warn("Components, attributes or employee id is missing. Not grouping compensation data.")
            return GroupedEmployeeData(
                employeeData = employeeData,
                compensationData = emptyList()
            )
        }

        val compensationData = components.map {
            mapOf(
                "employeeId" to employeeId,
                "group" to Group.COMPENSATION_DATA.name,
                "COMPONENT_NAME" to it.key.removePrefix("COMP_ITEM:").toCompensationComponentName(),
                "BILLING_RATE" to it.value.trim(),
                "BILLING_RATE_TYPE" to "Value",
                "IS_INSTALLMENT" to "No",
                "START_DATE" to startDate.format(DateTimeFormatter.ISO_DATE),
                "PAY_SCHEDULE_NAME" to payScheduleName,
            ) + compensationAttributes
        }

        return GroupedEmployeeData(
            employeeData = employeeData + ("group" to Group.EMPLOYMENT_DATA.name),
            compensationData = compensationData,
        )
    }

    private fun compensationAttribute(): BulkOnboardDataSpec? =
        BulkOnboardDataSpec.newBuilder()
            .setKey("COMP_ATTR:PAY_SCHEDULE")
            .setLabel("Pay Schedule")
            .setGroup(Group.COMPENSATION_DATA.name)
            .setRequired(true)
            .build()

    private fun BulkOnboardDataSpec?.specForEachComponent(): List<BulkOnboardDataSpec> {
        if (this == null) {
            return emptyList()
        }

        return this.valuesList.map {
            BulkOnboardDataSpec.newBuilder()
                .setKey("COMP_ITEM:${it.uppercase().replace(" ", "_")}")
                .setLabel(it)
                .setGroup(this.group)
                .setRequired(false)
                .build()
        }
    }

    private fun String.toCompensationComponentName(): String {
        return lowercase()
            .replace("_", " ")
            .split(" ")
            .joinToString(" ") { it.replaceFirstChar { char -> char.uppercaseChar() } }
    }

    private fun List<BulkOnboardDataSpec>.copyCompensationAttribute(compSchemaKey: String) =
        this.find { it.key == compSchemaKey }
            ?.let { it.copy { key = "COMP_ATTR:$compSchemaKey" } }
}
