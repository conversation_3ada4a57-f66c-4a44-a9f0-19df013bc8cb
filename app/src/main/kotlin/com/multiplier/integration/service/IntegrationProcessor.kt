package com.multiplier.integration.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.service.exception.CustomerErrorCode

data class IntegrationInput(
    val type: URIType,
    val uri: String,
    val companyId: Long,
    val module: BulkModule,
    val entityId: Long,
)

enum class BulkModule {
    TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
}

interface IntegrationProcessor {
    fun getIntegrationDataGroupName(input: IntegrationInput): String {
        return when {
            input.module == BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA -> "EOR_TIMESHEET"
            else -> throw CustomerErrorCode.BAD_REQUEST.toBusinessException(
                message = "No integration data group name supported for the module ${input.module}"
            )
        }
    }

    fun startIntegration(input: IntegrationInput)
    fun handleValidationFailure(bulkJobTrackerId: Long)
    fun handleValidationSuccess(bulkJobTrackerId: Long)
    fun handleDataCreationSuccess(bulkJobTrackerId: Long)
}
