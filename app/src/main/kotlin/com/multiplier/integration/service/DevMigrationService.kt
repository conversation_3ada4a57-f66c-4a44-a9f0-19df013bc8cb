package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.types.PlatformCategory
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class DevMigrationService(
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
) {
    private val log = KotlinLogging.logger {}

    fun migrateIntegrationIdForContractIntegrationTable(batchSize: Int) = runBlocking {
        val contractIds = platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull()

        contractIds.chunked(batchSize).map { batch ->
            processBatch(batch.toSet())
        }
    }

    private fun processBatch(batch: Set<Long>) {
        log.info { "Processing batch: $batch" }
        val contracts = contractServiceAdapter.getContractsByContractIds(batch)
        val companyIds = contracts.map { it.companyId }.toSet()
        log.info {  "Found company ids: $companyIds" }

        val integrations = companyIntegrationRepository.findByCompanyIdIn(companyIds)
            .filter { it.enabled && it.platform.category == PlatformCategory.HRIS }
        log.info { "Found integrations: ${integrations.size}" }
        val contractIdToIntegrationId = contracts.associate { contract ->
            val integration = integrations.find { it.companyId == contract.companyId }
            contract.id to integration?.id
        }
        log.info { "Found contract id to integration id map: $contractIdToIntegrationId" }

        val contractIntegrations =
            platformContractIntegrationRepository.findAllByContractIdIn(batch)
        log.info { "Found contract integrations: ${contractIntegrations.size}" }
        val updatedContractIntegrations = mutableListOf<JpaPlatformContractIntegration>()
        for (contractIntegration in contractIntegrations) {
            val integrationId = contractIdToIntegrationId[contractIntegration.contractId]
            if (integrationId != null) {
                contractIntegration.integrationId = integrationId
                updatedContractIntegrations.add(contractIntegration)
            }
        }


        platformContractIntegrationRepository.saveAll(updatedContractIntegrations)
        log.info { "Processed batch: $batch" }
    }
}