package com.multiplier.integration.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.service.exception.CustomerErrorCode
import org.apache.poi.openxml4j.exceptions.InvalidFormatException
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.InputStream

object ExcelProcessor {

    fun createXSSFWorkbookOrThrow(input: InputStream): Workbook {
        return try {
            XSSFWorkbook(input)
        } catch (e: InvalidFormatException) {
            throw CustomerErrorCode.INVALID_FILE_FORMAT.toBusinessException(
                message = "Invalid file format. Please verify uploaded file is a valid XLSX file.",
                cause = e,
            )
        } catch (e: Exception) {
            throw CustomerErrorCode.FILE_PARSE_ERROR.toSystemException(
                message = "Failed to parse uploaded file due to an unexpected error",
                cause = e,
            )
        }
    }
}