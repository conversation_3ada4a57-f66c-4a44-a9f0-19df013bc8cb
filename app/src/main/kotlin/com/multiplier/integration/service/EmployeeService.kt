package com.multiplier.integration.service

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.Constants.EmployeeOrigin
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.knit.ContactInfo
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.adapter.model.knit.Profile
import com.multiplier.integration.platforms.PlatformStrategy
import com.multiplier.integration.platforms.actions.CreateEmployeeEmployeeAction
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.platforms.actions.EmployeeAction
import com.multiplier.integration.platforms.actions.TerminateEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateAddressEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateBasicDetailsEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateCompensationEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateContactDetailsEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateContractDocumentEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateEmployeeEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateEmploymentActiveEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateFactsheetEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateInsuranceOnboardingKitEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateSalaryReviewDocumentEmployeeAction
import com.multiplier.integration.platforms.actions.UploadPayslipDocumentEmployeeAction
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import com.multiplier.integration.repository.model.PlatformData
import com.multiplier.integration.repository.model.toPlatformData
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.utils.asListOfType
import com.multiplier.member.schema.Address
import com.multiplier.member.schema.Member
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.core.env.Environment
import org.springframework.stereotype.Service
import java.time.LocalDate
import kotlin.reflect.KClass
import com.multiplier.integration.adapter.model.EmployeeData as LegacyEmployeeData

@Service
class EmployeeService(
    private val platformRepository: PlatformRepository,
    private val platformStrategies: MutableList<out PlatformStrategy>,
    private val customerIntegrationService: CustomerIntegrationService,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val environment: Environment,
    ) {

    private val log = KotlinLogging.logger {}

    fun createEmployee(
            companyId: Long,
            employeeData: LegacyEmployeeData?,
            member: Member,
            contract: Contract,
            eventLog: JpaEventLog? = null
    ) {

        val isNotLocal = !environment.matchesProfiles("local")

        val firstName = employeeData?.firstName

        // We want the employees created for testing to be picked up only in local profile
        if (firstName != null && firstName.contains(TEST_EMPLOYEE_NAME_PREFIX) && isNotLocal) {
            log.info("Aborting processing for employee with id " + employeeData.id + " as the employee has been created for testing")
            return
        }

        val (strategy, integration) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        // Check if profile is sync or not
        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email
        val cacheEmployee = platformEmployeeDataRepository.findByIntegrationIdAndWorkEmailOrPersonalEmail(integrationId = integration.id, workEmail = primaryEmail, email = primaryEmail)
        if (cacheEmployee.isNotEmpty()) {
            throw IntegrationIllegalStateException("This employee profile is already existed")
        }
        if(strategy is CreateEmployeeEmployeeAction) {
            runBlocking {
                val response = strategy.createEmployee(companyId, employeeData, member, contract, eventLog)
                val employeeId = response?.createdEmployeeId
                if (employeeId != null) {
                    contractServiceAdapter.updateContractEmployeeId(contract.id, employeeId)
                } else {
                    log.warn("No employeeId found in response from createEmployee")
                }
                insertNewlyCreatedEmployeeToCache(response!!)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support CreateEmployeeEmployeeAction")
        }
    }

    fun createEmployee(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ) {

        val (strategy, integration) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        // Check if profile is sync or not

        val cacheEmployee = platformEmployeeDataRepository.findByIntegrationIdAndWorkEmailOrPersonalEmail(integrationId = integration.id, workEmail = primaryEmail, email = primaryEmail)
        if (cacheEmployee.isNotEmpty()) {
            throw IllegalStateException("This employee profile is already existed")
        }
        if(strategy is CreateEmployeeEmployeeAction) {
            runBlocking {
                val response = strategy.createEmployeeWithoutIntegrationData(companyId, firstName, lastName, primaryEmail, position, workEmail)
                val employeeId = response?.createdEmployeeId
                if (employeeId == null) {
                    throw RuntimeException("Employee not successfully created")
                }
            }
        } else {
            throw IllegalStateException("The strategy for companyId=$companyId does not support CreateEmployeeEmployeeAction")
        }
    }


    fun insertNewlyCreatedEmployeeToCache(response: CreateEmployeePlatformResponse) {
        log.info("Insert new employee data to cache for employeeId=${response.createdEmployeeId} and companyIntegrationId=${response.companyIntegrationId}")
        try {
            val originalRequest = response.originalRequest!!
            val newEmployeeData = EmployeeData(
                profile = Profile(
                    firstName = originalRequest.firstName,
                    lastName = originalRequest.lastName,
                    workEmail = originalRequest.workEmail,
                    id = response.createdEmployeeId,
                ),
                contactInfo = ContactInfo(
                    personalEmails = originalRequest.personalEmails,
                    phones = emptyList()
                ),
                employeeDetailData = response.employeeDetailData
            )
            val newPlatformEmployeeData = JpaPlatformEmployeeData(
                employeeId = response.createdEmployeeId,
                employeeData = DataMapper.objectMapper.writeValueAsString(newEmployeeData),
                integrationId = response.companyIntegrationId,
                origin = EmployeeOrigin.INTERNAL.name,
                isDeleted = false
            )
            platformEmployeeDataRepository.save(newPlatformEmployeeData)
        } catch (e: Exception) {
            log.error("Error while inserting new employee data to cache for employeeId=${response.createdEmployeeId} and companyIntegrationId=${response.companyIntegrationId}", e)
        }
    }

    fun updateEmployee(
        companyId: Long,
        employeeData: LegacyEmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is UpdateEmployeeEmployeeAction) {
            runBlocking {
                strategy.updateEmployee(companyId, employeeData, member, contract, eventLog)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support CreateEmployeeEmployeeAction")
        }
    }

    fun updateEmployeeCompensation(
            companyId: Long,
            contractId: Long,
            compensationDetails: CompensationData,
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)

        if(strategy is UpdateCompensationEmployeeAction) {
            runBlocking {
                strategy.updateEmployeeCompensation(companyId, contractId, compensationDetails)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support UpdateCompensationEmployeeAction")
        }
    }

    fun markEmployeeAsActive(
        companyId: Long,
        contractId: Long,
    ) =
        doByPlatformStrategy(UpdateEmploymentActiveEmployeeAction::class) {
            it.markEmployeeAsActive(companyId, contractId)
        }

    fun markEmployeeAsInactive(
        companyId: Long,
        contractId: Long,
    ) =
        doByPlatformStrategy(UpdateEmploymentActiveEmployeeAction::class) {
            it.markEmployeeAsInactive(companyId, contractId)
        }

    fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails,
    ) =
        doByPlatformStrategy(UpdateBasicDetailsEmployeeAction::class) {
            it.updateEmployeeBasicDetails(companyId, contractId, details)
        }

    fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails,
    ) =
        doByPlatformStrategy(UpdateContactDetailsEmployeeAction::class) {
            it.updateEmployeeContactDetails(companyId, contractId, contactDetails)
        }

    fun updateEmployeeAddress(
        companyId: Long,
        contractId: Long,
        address: Address,
    ) =
        doByPlatformStrategy(UpdateAddressEmployeeAction::class) {
            it.updateEmployeeAddress(companyId, contractId, address)
        }

    fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is UpdateInsuranceOnboardingKitEmployeeAction) {
            runBlocking {
                strategy.updateOnboardingKitDocument(companyId, contractId, document)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support updateOnboardingKitDocument")
        }
    }

    fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is UpdateFactsheetEmployeeAction) {
            runBlocking {
                strategy.updateFactsheetDocument(companyId, contractId, document)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support updateOnboardingKitDocument")
        }
    }

    fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is UpdateContractDocumentEmployeeAction) {
            runBlocking {
                strategy.updateContractDocument(companyId, contractId, document)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support updateOnboardingKitDocument")
        }
    }

    fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is UpdateSalaryReviewDocumentEmployeeAction) {
            runBlocking {
                strategy.updateSalaryReviewDocument(companyId, contractId, document)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support updateSalaryReviewDocument")
        }
    }

    fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is UploadPayslipDocumentEmployeeAction) {
            runBlocking {
                strategy.uploadPayslipDocument(companyId, contractId, document)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support updateSalaryReviewDocument")
        }
    }

    fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog
    ) {
        val (strategy, integration) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        if(strategy is TerminateEmployeeAction) {
            runBlocking {
                strategy.terminateEmployee(companyId, contractId, terminationDate, terminationReason, eventLog, integration.id)
            }
        } else {
            throw IntegrationIllegalStateException("The strategy for companyId=$companyId does not support terminateEmployee")
        }
    }

    private inline fun <reified T : EmployeeAction> doByPlatformStrategy(
        commandType: KClass<T>,
        crossinline action: suspend (executor: T) -> Unit,
    ) = runBlocking {
        log.info {
            "[EmployeeService] Running $commandType for ${platformStrategies.size} platforms"
        }
        val availableStrategies = platformStrategies.filter { commandType.isInstance(it) }
        log.info {
            "[EmployeeService] Running $commandType on ${availableStrategies.size} platforms"
        }

        val supportedPlatforms =
            getPlatformByName(availableStrategies.map { it.platformName }.toSet()).map { it.name }
        log.info { "[EmployeeService] Supported platforms: $supportedPlatforms" }

        val validStrategies =
            availableStrategies
                .filter { supportedPlatforms.contains(it.platformName) }
                .asListOfType<T>()
        validStrategies?.forEach { log.info { "[EmployeeService] Valid strategy: $it" } }

        validStrategies
            ?.map {
                CoroutineScope(Dispatchers.Default).async {
                    val result = runCatching { action(it) }
                    if (result.isFailure) {
                        val itAsPlatformStrategy = it as PlatformStrategy
                        log.error(
                            "Failed running ${commandType.simpleName} for platform ${itAsPlatformStrategy.platformName}: ${result.exceptionOrNull()}")
                    }
                }
            }
            ?.awaitAll()
    }

    fun checkIfEmployeeAlreadyExistedInCache(contract: ContractOuterClass.Contract): Boolean {
        val companyId = contract.companyId
        val integration = companyIntegrationRepository.findByCompanyIdIn(setOf(companyId)).firstOrNull() // assuming that a company only has 1 active integrations

        val employeeContractIntegration = platformContractIntegrationRepository.findByContractId(contract.id).firstOrNull()
            ?: return false

        if (integration == null) return false

        val employeeData = platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
            integration.id!!,
            employeeContractIntegration.platformEmployeeId
        ).firstOrNull()

        return employeeData != null
    }

    private fun getPlatformByName(names: Set<String>?): List<PlatformData> {
        if (names != null) {
            return platformRepository.findByNameIn(names).mapNotNull { it.toPlatformData() }
        }
        return platformRepository.findAll().mapNotNull { it.toPlatformData() }
    }

    fun getEmployeeDataOrigin(contractId: Long): EmployeeOrigin? {
        val jpaContractIntegration = platformContractIntegrationRepository.findByContractId(contractId).firstOrNull()
            ?: return null
        val origin = platformEmployeeDataRepository.findByEmployeeId(jpaContractIntegration.platformEmployeeId).firstOrNull()?.origin ?: return null
        return EmployeeOrigin.valueOf(origin)
    }

    fun isTriNetPlatform(companyId: Long): Boolean {
        val (strategy, _) = customerIntegrationService.getPlatformStrategyByCompanyId(companyId)
        return strategy?.platformName === "TriNet"
    }
}
