package com.multiplier.integration.service.exception

import com.multiplier.common.exception.ErrorCode

enum class CustomerErrorCode(override val message: String) : ErrorCode {
    BAD_REQUEST("Bad request"),
    INTEGRATION_NOT_FOUND("Integration not found"),
    ENTITY_ALREADY_EXISTS("Entity already exists"),
    ENTITY_NOT_FOUND("Entity not found"),
    ILLEGAL_STATE_FOR_TRINET("Illegal state for Trinet"),
    INVALID_CONTRACT_PROCESSING_DATE("Invalid contract processing date"),
    INTERNAL_API_ERROR("Integration not found"),
    KNIT_INTEGRATION_ERROR("Knit integration error"),
    INTERNAL_ERROR("Internal server error"),
    INVALID_REQUEST("Invalid request"),
    ILLEGAL_STATE("Illegal state"),
    ILLEGAL_ARGUMENT("Illegal argument"),
    EXTERNAL_GRPC_ERROR("External gRPC error"),
    DOWNSTREAM_ERROR("Downstream server error"),
    INVALID_FILE_FORMAT("Invalid file format"),
    FILE_PARSE_ERROR("Error parsing file"),
    FILE_EXTRACTOR_NOT_SUPPORTED("File extractor not supported"),
}