package com.multiplier.integration.service.email

import com.multiplier.pigeonservice.PigeonNotificationClient
import com.multiplier.pigeonservice.dto.Attachment
import com.multiplier.pigeonservice.dto.PigeonEmailNotificationDTO
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class EmailService(
    private val pigeonNotificationClient: PigeonNotificationClient,
) {
    private val log = KotlinLogging.logger {}

    fun sendEmail(email: EmailNotificationBody) {
        try {
            log.info("Sending email notification")

            val pigeonNotification = PigeonEmailNotificationDTO(email, HashMap())
            pigeonNotificationClient.send(pigeonNotification)

            log.info("Email notification sent")
        } catch (e: Exception) {
            log.error("Error sending email notification", e)
        }
    }

    fun sendEmailWithAttachment(email: EmailNotificationBody, attachments: List<Attachment>) {
        try {
            log.info("Sending email notification")

            val pigeonNotification = PigeonEmailNotificationDTO(email, HashMap(), attachments)
            pigeonNotificationClient.send(pigeonNotification)

            log.info("Email notification sent")
        } catch (e: Exception) {
            log.error("Error sending email notification", e)
        }
    }
}
