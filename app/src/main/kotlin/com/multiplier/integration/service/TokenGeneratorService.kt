package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.DefaultTriNetAPIAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MergeDevAdapter
import com.multiplier.integration.adapter.api.resources.financial.*
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderPlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.repository.type.ProviderPlatformStatus
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.KnitIntegrationException
import com.multiplier.integration.types.*
import com.multiplier.integration.utils.mapPlatformIdToKnitAppId
import com.multiplier.integration.utils.mapPlatformIdToMergeDevAppId
import com.multiplier.integration.utils.validateIntegrationCompanyMatch
import jakarta.transaction.Transactional
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.stereotype.Component

@Component
class TokenGeneratorService(
    private val knitAdapter: KnitAdapter,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val providerRepository: ProviderRepository,
    private val platformRepository: PlatformRepository,
    private val providerPlatformRepository: ProviderPlatformRepository,
    private val syncService: SyncService,
    private val triNetAPIAdapter: DefaultTriNetAPIAdapter,
    private val notificationsService: NotificationsService,
    private val customerIntegrationService: CustomerIntegrationService,
    private val mergeDevAdapter: MergeDevAdapter,
    private val environment: Environment,
    ) {

    private val log = KotlinLogging.logger {}

    @Value("\${platform.merge-dev.enabled:false}") private var isMergeDevEnabled: Boolean = false

    fun getAuthTokenFromKnit(companyId: String,
                             companyName: String,
                             userEmail: String,
                             userName: String,
                             platformId: String,
                             currentUserCompanyId: Long?,
                             clearErrors: Boolean = true,
                             isOpsUser: Boolean = false
    ): KnitAuthResponse {
        validateIntegrationCompanyMatch(currentUserCompanyId, companyId.toLong(), isOpsUser)
        if (companyId.isBlank() ||
                companyName.isBlank() ||
                userEmail.isBlank() ||
                userName.isBlank() ||
                platformId.isBlank()) {
            log.warn { "One or more input arguments are null or blank. companyId=$companyId, " +
                    "companyName=$companyName, platformId=$platformId" }
            throw IntegrationIllegalArgumentException("One or more input arguments are null or blank.")
        }

        val platform = platformRepository.findById(platformId.toLong())
                .orElseThrow { IntegrationIllegalArgumentException("Platform not found for given platformId: $platformId") }

        if(platform?.name.isNullOrBlank()) {
            log.warn { "Platform name is null or blank for given platformId: $platformId" }
            throw IntegrationIllegalArgumentException("Platform name is null or blank for given platformId: $platformId")
        }

        val knitAppId = mapPlatformIdToKnitAppId(platform.name);

        if(knitAppId.isBlank()) {
            log.warn { "Knit appId is blank for given platformId: $platformId" }
            throw IntegrationIllegalArgumentException("Knit appId is blank for given platformId: $platformId")
        }

        val authToken = runBlocking {
            knitAdapter.requestAuthToken(
                    companyId,
                    companyName,
                    userEmail,
                    userName,
                    knitAppId,
                    clearErrors,
                    category = platform.category.name
                )
        }

        if (authToken == null) {
            log.error { "Failed to retrieve authToken from Knit for companyId=$companyId" }
            throw KnitIntegrationException("Failed to retrieve authToken from Knit.")
        }

        log.info { "Successfully retrieved authToken from Knit for companyId=$companyId" }
        return authToken
    }

    fun createCompanyPlatformIntegration(companyId: Long, platformId: Long, token: String, isOpsUser: Boolean = false) {
        log.info("Attempting to create platform integration for companyId: $companyId, platformId: $platformId")

        if (token.isNullOrBlank()) {
            log.warn("token or accountToken is blank")
            throw IntegrationIllegalArgumentException("token must not be blank")
        }
        val platform = platformRepository.findById(platformId)
            .orElseThrow { IntegrationIllegalArgumentException("Platform not found for given platformId: $platformId") }

        // Check for existing enabled integrations for the company
        val existingIntegrations = companyIntegrationRepository.findEnabledIntegrationByCompanyIdAndPlaformCategory(companyId, true, category = platform.category)
        if (existingIntegrations?.isNotEmpty() == true) {
            log.warn("Enabled integration already exists for companyId: $companyId")
            throw IntegrationIllegalArgumentException("Only one active integration allowed.")
        }

        val platformProvider = providerPlatformRepository.findByPlatformIdAndStatus(platformId, ProviderPlatformStatus.ACTIVE)
                .firstOrNull()
                ?: throw IntegrationIllegalArgumentException("No active providers found for platformId: $platformId")

        val providerId = platformProvider.provider.id
                ?: throw IntegrationIllegalStateException("Provider ID should not be null")

        val providerOptional = providerRepository.findById(providerId)

        if (!providerOptional.isPresent) {
            log.warn("Provider not found for given providerId: ${platformProvider.provider.id}")
            throw IntegrationIllegalArgumentException("Provider not found for given providerId: ${platformProvider.provider.id}")
        }
        val provider = providerOptional.get()


        var integration = companyIntegrationRepository.findIntegration(companyId, provider, platform)

        if (integration != null && integration.enabled) {
            log.error("Integration already exists for the given company, provider, and platform.")
            throw IntegrationIllegalArgumentException("Integration already exists for the given company, provider, and platform.")
        } else if (integration != null && !integration.enabled) {
            integration.enabled = true
            integration.accountToken = token
            log.info("Updated existing integration for companyId: $companyId, platformId: $platformId")
        } else {
            integration = JpaCompanyIntegration(
                    companyId = companyId,
                    provider = provider,
                    platform = platform,
                    accountToken = token,
                    enabled = true,
                    incomingSyncEnabled =  false,
                    outgoingSyncEnabled =  false,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null
            )
            log.info("Saving new integration for companyId: $companyId, platformId: $platformId")
        }
        val updatedIntegration = companyIntegrationRepository.save(integration)
        handleKnitIntegration(updatedIntegration.id, platform.category, updatedIntegration.companyId, isOpsUser)
    }

    @Transactional
    fun getTriNetCredential(companyId: Long?, externalCompanyId: String?, companyUserName: String?, companyUserPassword: String?, isOpsUser: Boolean = false): TrinetCredentialResult {
        if (companyId == null || externalCompanyId.isNullOrBlank() || companyUserName.isNullOrBlank() || companyUserPassword.isNullOrBlank()) {
            log.error("Not externalCompanyId: $externalCompanyId or companyUserName: $companyUserName or companyUserPassword: $companyUserPassword can be empty")
            return TrinetCredentialResult.newBuilder()
                .success(false)
                .message("Not externalCompanyId or companyUserName or companyUserPassword can be empty")
                .build()
        }
        // Get TriNet access token to verify input credential
        triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)
            ?: return TrinetCredentialResult.newBuilder()
                .success(false)
                .message("Invalid TriNet credential")
                .build()
        val platform = platformRepository.findFirstByCategoryAndName(category = PlatformCategory.HRIS, name = "TriNet") ?: return TrinetCredentialResult.newBuilder()
            .success(false)
            .message("Not found platform TriNet")
            .build()
        val provider = providerRepository.findFirstByName(ProviderName.TRINET) ?: return TrinetCredentialResult.newBuilder()
            .success(false)
            .message("Not found  provider ${ProviderName.TRINET}")
            .build()

        val isProd = environment.matchesProfiles("prod")
        val existingIntegration = companyIntegrationRepository.findByProviderIdAndPlatformIdAndExternalCompanyId(provider.id!!, platform.id!!, externalCompanyId)
        if (!existingIntegration.isNullOrEmpty() && isProd) {
            return TrinetCredentialResult.newBuilder()
                .success(false)
                .message("The TriNet company you are trying to use is already connected to another Multiplier account")
                .build()
        }

        val integration =
            companyIntegrationRepository.findIntegration(companyId, provider, platform)
                ?: JpaCompanyIntegration(
                    companyId = companyId,
                    provider = provider,
                    platform = platform,
                    enabled = true,
                    lastIncomingSyncTime = null,
                    lastOutgoingSyncTime = null,
                    lastOutgoingSyncTimeToggleOnTime = null,
                    lastOutgoingSyncTimeToggleOffTime = null,
                    accountToken = companyUserPassword
                )

        // Update + activate with new AccountToken in case we had the integration inactive
        integration.accountToken = companyUserPassword
        integration.accountName = companyUserName
        integration.externalCompanyId = externalCompanyId

        // CU-86cvf60rz : add a dummy call to getLocation to verify that the companyId is correct (case-sensitive)
        triNetAPIAdapter.getLocationByName(integration, "") ?: return TrinetCredentialResult.newBuilder()
            .success(false)
            .message("Invalid company ID")
            .build()

        integration.enabled = true
        val savedIntegration = companyIntegrationRepository.save(integration)



        val outgoingSyncUpdateResp = customerIntegrationService.changeSyncState(savedIntegration.id!!, syncType=SyncType.OUTGOING, integration.enabled, integration.companyId, isOpsUser)
        if (!outgoingSyncUpdateResp.success) {
            return TrinetCredentialResult.newBuilder()
                .success(false)
                .message(outgoingSyncUpdateResp.message)
                .build()
        }
        notificationsService.sendAdminIntegrationConnected(companyId, platform.name)

        return TrinetCredentialResult.newBuilder()
            .success(true)
            .message("Successfully")
            .build()
    }

    private fun handleKnitIntegration(integrationId: Long?, category: PlatformCategory, companyId: Long?, isOpsUser: Boolean = false): Boolean {
        if (category == PlatformCategory.EXPENSES) {
            if (integrationId == null  || companyId == null) {
                throw IntegrationIllegalArgumentException("Not found integration or current user company id to trigger incoming sync")
            }
            syncService.startManualSync(integrationId, companyId, PlatformCategory.EXPENSES, isOpsUser)
        }
        return true
    }

    fun getLinkTokenFromMergeDev(
        companyId: Long,
        companyName: String,
        companyEmail: String,
        platformId: Long
    ): MergeDevLinkTokenResponse {
        if (companyName.isBlank() ||
            companyEmail.isBlank()
        ) {
            log.warn { "One or more input arguments are null or blank. companyId=$companyId, " +
                    "companyName=$companyName, companyEmail=$companyEmail, platformId=$platformId"
            }
            throw IntegrationIllegalArgumentException("One or more input arguments are null or blank.")
        }
        val platform = platformRepository.findById(platformId)
            .orElseThrow { IntegrationIllegalArgumentException("Platform not found for given platformId: $platformId") }

        if(platform?.name.isNullOrBlank()) {
            log.warn { "Platform name is null or blank for given platformId: $platformId" }
            throw IntegrationIllegalArgumentException("Platform name is null or blank for given platformId: $platformId")
        }

        val linkTokenResponse = mergeDevAdapter.createLinkToken(
           MergeDevLinkTokenRequest(
                    endUserEmailAddress = companyEmail,
                    endUserOrganizationName = companyName,
                    endUserOriginId = companyId.toString(),
                    appId = mapPlatformIdToMergeDevAppId(platform.name)
           )
        )
        val mergeDevLinkTokenMessage = linkTokenResponse.message as? MergeDevLinkTokenMessage

        if(mergeDevLinkTokenMessage == null || mergeDevLinkTokenMessage.linkToken.isNullOrBlank() || !linkTokenResponse.success) {
            log.error { "MergeDevLinkTokenMessage is null for given platformId: $platformId and companyId: $companyId" }
            return MergeDevLinkTokenErrorResponse.Builder()
                .error(
                    MergeDevError.Builder()
                    .msg( linkTokenResponse.error?.errorMessage?: "")
                    .build()
                )
                .build()

        }

        return MergeDevLinkTokenSuccessResponse.Builder()
            .token(
                MergeDevLinkToken.Builder()
                    .token(mergeDevLinkTokenMessage.linkToken)
                    .build()
            )
            .build()
    }

    fun createOrUpdateMergeDevToken(companyId: Long, platformId: Long, publicToken: String, opsUser: Boolean) {
        if (publicToken.isBlank()) {
            log.warn("public token is blank")
            throw IntegrationIllegalArgumentException("public token must not be blank")
        }

        val mergeResponse = mergeDevAdapter.retrieveAccountToken(
            MergeDevAccountTokenRetrieveRequest(
                    publicToken = publicToken
            )
        )

        val mergeDevAccountTokenRetrieveMessage = mergeResponse.message as? MergeDevAccountTokenRetrieveMessage
        log.info("mergeDevAccountTokenRetrieve function call done")

        if (!mergeResponse.success || mergeDevAccountTokenRetrieveMessage != null && mergeDevAccountTokenRetrieveMessage.accountToken.isBlank()) {
          throw BadRequestException(mergeResponse.error?.errorMessage ?: "Failed to retrieve account token")
        }

        createCompanyPlatformIntegration(
            companyId,
            platformId,
            mergeDevAccountTokenRetrieveMessage?.accountToken ?: "",
            opsUser
        )
    }
}
