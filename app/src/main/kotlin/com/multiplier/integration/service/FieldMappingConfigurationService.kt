package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.integration.repository.FieldMappingConfigurationRepository
import com.multiplier.integration.repository.model.FieldMappingConfigurationType
import com.multiplier.integration.repository.model.JpaFieldMappingConfiguration
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.FieldMappingConfiguration
import com.multiplier.integration.types.FieldMappingConfigurationInput
import com.multiplier.integration.types.TaskResponse
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class FieldMappingConfigurationService(
    private val fieldMappingConfigurationRepository: FieldMappingConfigurationRepository,
    private val dataMapper: DataMapper,
) {

    private val log = KotlinLogging.logger {}
    private val objectMapper = jacksonObjectMapper()


    fun getFieldMappingConfiguration(type: String, platformId: Long? = null): List<FieldMappingConfiguration> {
        val fieldMappingConfigs =
            fieldMappingConfigurationRepository.findByTypeAndIsDeletedFalse(type = FieldMappingConfigurationType.valueOf(type), platformId)
        return fieldMappingConfigs.map {
            dataMapper.map(it)
        }
    }

    fun upsertFieldMappingConfiguration(input: List<FieldMappingConfigurationInput>, platformId: Long? = null): List<FieldMappingConfiguration> {
        val keys = input.map { it.key }
        val existingConfigs = fieldMappingConfigurationRepository.findByKeyInAndIsDeletedFalse(keys, platformId)
            .associateBy { it.key }

        val configurationsToSave = input.map { configInput ->
            val updatedEnumMappings = configInput.enumMappings?.associate { it.key to it.value }
            val updatedEnumMappingsString = updatedEnumMappings?.let { objectMapper.writeValueAsString(it) } ?: null
            existingConfigs[configInput.key]?.apply {
                configInput.value?.let { value = it }
                updatedEnumMappingsString?.let { enumMappings = it }
                configInput.type?.let { type = FieldMappingConfigurationType.valueOf(it) }
            } ?: JpaFieldMappingConfiguration(
                key = configInput.key,
                value = configInput.value,
                type = configInput.type?.let {
                    FieldMappingConfigurationType.valueOf(it)
                } ?: FieldMappingConfigurationType.DEFAULT,
                enumMappings = updatedEnumMappingsString,
                platformId = platformId
            )
        }
        fieldMappingConfigurationRepository.saveAll(configurationsToSave)

        val updateConfigs = fieldMappingConfigurationRepository.findByKeyInAndIsDeletedFalse(keys, platformId)
        return updateConfigs.map {
            dataMapper.map(it)
        }
    }

    fun deleteFieldMappingConfigs(keys: List<String>, platformId: Long? = null): TaskResponse {
        val configs = fieldMappingConfigurationRepository.findByKeyInAndIsDeletedFalse(keys, platformId)

        val updatedConfigs = configs.map {
            it.apply {
                it.isDeleted = true
            }
        }
        fieldMappingConfigurationRepository.saveAll(updatedConfigs)

        return TaskResponse(true, "Delete field mapping configs successfully")
    }
}