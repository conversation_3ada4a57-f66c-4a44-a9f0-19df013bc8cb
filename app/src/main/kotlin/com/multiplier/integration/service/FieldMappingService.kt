package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.api.PaymentServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.AddCustomFieldMappingRequest
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.Field
import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.adapter.api.resources.knit.FieldValues
import com.multiplier.integration.adapter.api.resources.knit.GetFieldValuesResponse
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.core.model.ExternalField
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.ExternalPlatformValuesRepository
import com.multiplier.integration.repository.FieldMappingConfigurationRepository
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository
import com.multiplier.integration.repository.model.FieldMappingConfigurationType
import com.multiplier.integration.repository.model.FieldType
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaExternalPlatformValues
import com.multiplier.integration.repository.model.JpaFieldsMapping
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.model.JpaReceivedEventArchive
import com.multiplier.integration.repository.model.LegalMappingStatus
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.model.MaritalStatus
import com.multiplier.integration.types.Company
import com.multiplier.integration.types.FieldMapping
import com.multiplier.integration.types.FieldMappingV2
import com.multiplier.integration.types.Gender
import com.multiplier.integration.types.IntegrationEntityMappingStatusOutput
import com.multiplier.integration.types.IntegrationFieldsMappingOutput
import com.multiplier.integration.types.IntegrationFieldsMappingOutputV2
import com.multiplier.integration.types.SaveIntegrationFieldsMappingInput
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.types.UnmappedField
import com.multiplier.integration.utils.formatExternalKey
import com.multiplier.integration.utils.mapPlatformIdToKnitAppId
import com.multiplier.integration.utils.populateKeyFromLabel
import com.multiplier.integration.utils.populateLabelFromKey
import com.multiplier.integration.utils.replaceAllExceptLast
import com.multiplier.payse.schema.common.AccountType
import com.multiplier.payse.schema.common.PaymentDirection
import com.multiplier.payse.schema.common.PaymentPartner
import com.multiplier.payse.schema.common.TransferType
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPartner
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirement
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirements
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementsRequest
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.LocalDateTime
import java.util.*

@Service
class FieldMappingService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val knitAdapter: KnitAdapter,
    private val receivedEventRepository: ReceivedEventRepository,
    private val newCompanyServiceAdapter: NewCompanyServiceAdapter,
    private val fieldsMappingRepository: FieldsMappingRepository,
    private val contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter,
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val externalPlatformValuesRepository: ExternalPlatformValuesRepository,
    private val paymentServiceAdapter: PaymentServiceAdapter,
    private val fieldMappingConfigurationRepository: FieldMappingConfigurationRepository,
    private val receivedEventsArchiveRepository: ReceivedEventsArchiveRepository,
    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter,
) {

    private val log = KotlinLogging.logger {}
    private val objectMapper = jacksonObjectMapper()
    private val dataMapper = DataMapper()

    fun getIntegrationFieldsMapping(
        entityId: Long,
        integrationId: Long,
        isFetchingLatest: Boolean = true,
    ): IntegrationFieldsMappingOutput {
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
        val companyId = integration.companyId
        log.info("Fetching legal entity and company integration")
        val legalEntities = newCompanyServiceAdapter.getLegalEntities(companyId)
        val matchedLegalEntity = legalEntities.find { it.id == entityId }
            ?: throw EntityNotFoundException("Not found legal entity with entityId=$entityId")
        val existingFieldMappings =
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(entityId, integrationId)
        val legalEntityMapping =
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId).orElseThrow {
                EntityNotFoundException(
                    "Not found legal entity mapping for entityId $entityId"
                )
            }
        val dataSpecs = getOnboardDataSpecs(matchedLegalEntity, companyId)
        val knitFields = getKnitFields(integrationId, companyId, integration)
        log.info("Check if data specs not existed in mapping table then update them. $dataSpecs")
        val externalPlatformValues = getExternalEnumValues(integration, knitFields)
        val thirdPartyFields = formatKnitFields(knitFields)
        // handle some platforms not expose some enum fields
        val updatedExternalPlatformValues = getFixedEnumValues(externalPlatformValues, integrationId, thirdPartyFields)
        val defaultMappings = getDefaultMappings(thirdPartyFields, updatedExternalPlatformValues, matchedLegalEntity)
        val calculatedFields = getCalculatedFields()

        val processedFieldMappings = processFieldMappings(
            existingFieldMappings,
            dataSpecs,
            defaultMappings,
            entityId,
            integration,
            calculatedFields
        )
        if (processedFieldMappings.isNotEmpty()) {
            fieldsMappingRepository.saveAll(processedFieldMappings)
        }
        deleteOutdatedFieldMappings(existingFieldMappings, processedFieldMappings)

        val fieldsMappingOutput = formatFieldMappingsData(processedFieldMappings)
        val unmappedFieldsOutput = formatUnmappedFields(updatedExternalPlatformValues, thirdPartyFields, integration)
        return dataMapper.map(
            integrationId,
            unmappedFieldsOutput,
            fieldsMappingOutput,
            matchedLegalEntity,
            legalEntityMapping,
            companyId
        )
    }

    fun formatUnmappedFields(
        updatedExternalPlatformValues: List<JpaExternalPlatformValues>,
        thirdPartyFields: List<UnmappedField>,
        integration: JpaCompanyIntegration,
    ): List<UnmappedField> {
        val externalKeyToValuesMap = updatedExternalPlatformValues.associate {
            it.fieldId to it.values
        }
        val unmappedFields = thirdPartyFields.map {
            val updatedKey = if (!it.isMappedByThirdParty) it.key.populateKeyFromLabel(it.label) else it.key
            val childrenMapping = externalKeyToValuesMap[it.fieldId]?.map { child ->
                val mappedValue = objectMapper.readValue(child, FieldValues::class.java)
                val childKey =
                    if (it.isCustomField && integration.platform.isSpecialEnum) mappedValue.id else mappedValue.label
                dataMapper.map(
                    key = childKey,
                    label = mappedValue.label,
                    isMappedByThirdParty = it.isMappedByThirdParty,
                    fieldId = null,
                    type = FieldType.STRING.name,
                    fieldFromApp = null,
                    isCustomField = it.isCustomField
                ).build()
            }
            dataMapper.map(
                key = updatedKey,
                label = it.label.populateLabelFromKey(it.key?.replace("customFields.fields.", "")),
                isMappedByThirdParty = it.isMappedByThirdParty,
                fieldId = it.fieldId,
                type = it.type,
                fieldFromApp = it.fieldFromApp,
                isCustomField = it.isCustomField
            ).apply {
                if (childrenMapping != null) {
                    children(childrenMapping)
                }
            }.build()
        }
        return handleExternalFieldWithSubFields(unmappedFields)
    }

    private fun processFieldMappings(
        fieldsMappings: List<JpaFieldsMapping>,
        dataSpecs: List<BulkOnboardDataSpec>,
        defaultMappings: Map<String, ExternalField>,
        entityId: Long,
        integration: JpaCompanyIntegration,
        calculatedFields: Map<String, String>,
    ): List<JpaFieldsMapping> {
        val uniqueJpaFieldsMapping = deleteDuplicatedFieldMappings(fieldsMappings)
        val newDataSpecs = dataSpecs.map { dataSpec ->
            log.info("Found new data spec need to be mapped ${dataSpec.key}")
            val existingMapping = uniqueJpaFieldsMapping.find { it.originField == dataSpec.key }
            val defaultMapping = defaultMappings[dataSpec.key]
            val children = if (dataSpec.valuesCount > 0) {
                val mappedValues = defaultMapping?.values?.map {
                    objectMapper.readValue(it, FieldValues::class.java)
                } ?: emptyList()
                dataSpec.valuesList.mapNotNull { value ->
                    val mappedValue = mappedValues.find { v -> v.label?.uppercase() == value.uppercase() }
                    val existingChildMapping = existingMapping?.children?.find { it.originField == value }
                    JpaFieldsMapping(
                        id = existingChildMapping?.id,
                        companyId = integration.companyId,
                        entityId = entityId,
                        integrationId = integration.id!!,
                        originField = value,
                        originFieldLabel = value,
                        type = FieldType.STRING,
                        mappedField = existingChildMapping?.mappedField
                            ?: if (defaultMapping?.isCustomField == true && integration.platform.isSpecialEnum) mappedValue?.id else mappedValue?.label,
                        mappedFieldLabel = existingChildMapping?.mappedFieldLabel ?: mappedValue?.label,
                        isRequired = false,
                        isActive = true,
                        parent = null
                    )
                }
            } else emptyList()
            JpaFieldsMapping(
                id = existingMapping?.id,
                companyId = integration.companyId,
                entityId = entityId,
                integrationId = integration.id!!,
                originField = dataSpec.key,
                originFieldLabel = dataSpec.label,
                type = if (dataSpec.valuesCount > 0) FieldType.ENUM else FieldType.STRING,
                mappedField = existingMapping?.mappedField
                    ?: calculatedFields[dataSpec.key]
                    ?: defaultMapping?.mappedKey,
                mappedFieldLabel = existingMapping?.mappedFieldLabel
                    ?: defaultMapping?.label.populateLabelFromKey(
                        defaultMapping?.mappedKey?.replace(
                            "customFields.fields.",
                            ""
                        )
                    ),
                isRequired = dataSpec.required,
                isActive = true,
                isCalculated = dataSpec.key in calculatedFields.keys,
                children = children
            ).also { newDataSpec ->
                children.forEach { it.parent = newDataSpec }
            }
        }
        return newDataSpecs
    }

    private fun deleteDuplicatedFieldMappings(fieldsMapping: List<JpaFieldsMapping>): List<JpaFieldsMapping> {
        val toBeDeleted = fieldsMapping.filter {
            fieldsMapping.any { mapping ->
                mapping.originField == it.originField
                        && Objects.equals(mapping.parent, it.parent)
                        && it.id!! > mapping.id!!
            }
        }
        val toBeRetained = fieldsMapping.filter { toBeDeleted.none { mapping -> mapping.id == it.id } }
        fieldsMappingRepository.deleteAll(toBeDeleted)
        return toBeRetained;
    }

    private fun deleteOutdatedFieldMappings(
        existedFieldsMapping: List<JpaFieldsMapping>,
        newFieldsMapping: List<JpaFieldsMapping>
    ) {
        val outdatedMapping = existedFieldsMapping.filter {
            newFieldsMapping.none { mapping -> it.id == mapping.id }
        }
        if (outdatedMapping.isNotEmpty()) {
            fieldsMappingRepository.deleteAll(outdatedMapping)
        }
    }

    fun getKnitFields(
        integrationId: Long,
        companyId: Long,
        integration: JpaCompanyIntegration,
    ): List<FieldData> {
        log.info("Fetching data specs from KNIT for integrationId $integrationId")
        val platformKeys = runBlocking {
            knitAdapter.getAllFields(
                companyId,
                integration.platform.id!!,
                mapPlatformIdToKnitAppId(integration.platform.name)
            )
        }
        val externalFields = buildList {
            platformKeys.data?.run {
                default?.let { addAll(it) }
                listOfNotNull(mapped, unmapped)
                    .flatten()
                    .onEach { it.isCustomField = true }
                    .let { addAll(it) }
            }
        }
        return externalFields
    }

    private fun getOnboardDataSpecs(
        matchedLegalEntity: CompanyOuterClass.LegalEntity,
        companyId: Long,
    ): List<BulkOnboardDataSpec> {
        log.info("Fetching data specs from onboarding service for legal entity ${matchedLegalEntity.legalName}")
        val getOnboardDataSpecsRequest = BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = matchedLegalEntity.id,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.valueOf("COUNTRY_CODE_${matchedLegalEntity.address.country}"),
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = emptyMap())
        )
        return contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(getOnboardDataSpecsRequest)
    }

    private fun handleExternalFieldWithSubFields(externalFields: List<UnmappedField>): List<UnmappedField> {
        // handle for compensation first, need to update for general case
        val typeField = externalFields.find { it.key == "compensation.variable[0].type" }
        val planIdField = externalFields.find { it.key == "compensation.variable[0].planId" }
        return externalFields.map {
            if (it.key in listOf(
                    "compensation.variable[0].amount",
                    "compensation.variable[0].payPeriod",
                    "compensation.variable[0].frequency"
                )
            ) {
                it.apply {
                    subFields = listOf(typeField, planIdField)
                }
            }
            it
        }
    }

    private fun formatFieldMappingsData(fieldMappings: List<JpaFieldsMapping>): List<FieldMapping> {
        return fieldMappings.map {
            val res = dataMapper.map(it)
            if (it.type == FieldType.ENUM) {
                res.children(it.children.map { ch ->
                    dataMapper.map(ch)
                        .build()
                })
            }
            res.build()
        }
    }

    fun formatKnitFields(externalFields: List<FieldData>?): List<UnmappedField> {
        if (externalFields.isNullOrEmpty()) {
            return emptyList()
        }
        return externalFields.map {
            val isMapped = it.mappedKey != null
            val formatWithCustomFieldKey = formatExternalKey(it, isMapped)
            dataMapper.map(
                formatWithCustomFieldKey,
                it.label,
                isMapped,
                it.fieldId,
                it.dataType,
                it.fieldFromApp,
                it.isCustomField ?: false
            ).build()
        }
    }

    fun saveIntegrationFieldsMapping(input: SaveIntegrationFieldsMappingInput): TaskResponse {
        val integration = companyIntegrationRepository.findById(input.integrationId).orElseThrow {
            EntityNotFoundException(
                "Not found company integration with integrationId=${input.integrationId}"
            )
        }
        val companyId = integration.companyId
        log.info("Fetching legal entity and company integration")
        val legalEntities = newCompanyServiceAdapter.getLegalEntities(companyId)
        legalEntities.find { it.id == input.entityId }
            ?: throw EntityNotFoundException("Not found legal entity with entityId=${input.entityId}")
        val customMappingRequests = mutableListOf<AddCustomFieldMappingRequest>()
        val newFieldMappings = input.mapping.map {
            if (!it.isMappedByThirdParty) {
                customMappingRequests.add(
                    AddCustomFieldMappingRequest(
                        fieldId = it.mappedFieldId,
                        fieldFromApp = it.mappedFieldFromApp,
                        mappedKey = it.mappedField.replace("customFields.fields.", ""),
                        dataType = it.type
                    )
                )
            }
            val subKey = if (!it.mappedSubFields.isNullOrEmpty()) {
                val subFieldStr = it.mappedSubFields.map { f -> "${f.key}=${f.value}" }
                subFieldStr.joinToString(",")
            } else "0"
            val formattedKey = replaceAllExceptLast(it.mappedField, "0", subKey)

            val data = JpaFieldsMapping(
                companyId = companyId,
                entityId = input.entityId,
                integrationId = input.integrationId,
                originField = it.originField,
                originFieldLabel = it.originFieldLabel,
                type = FieldType.valueOf(it.type),
                mappedField = formattedKey,
                mappedFieldLabel = it.mappedFieldLabel,
            )
            val children = (if (it.type == FieldType.ENUM.name && it.children.isNotEmpty()) it.children.map { ch ->
                JpaFieldsMapping(
                    companyId = companyId,
                    entityId = input.entityId,
                    integrationId = input.integrationId,
                    originField = ch.originField,
                    originFieldLabel = ch.originFieldLabel,
                    type = FieldType.STRING,
                    mappedField = ch.mappedField,
                    mappedFieldLabel = ch.mappedFieldLabel,
                )
            } else emptyList()).toMutableList()
            data.children = children
            data
        }
        saveOrUpdateFieldMappings(newFieldMappings, input.entityId, input.integrationId)
        customMappingRequests.forEach { customMappingRequest ->
            runBlocking {
                knitAdapter.addCustomFieldMapping(companyId, integration.platform.id!!, customMappingRequest)
            }
        }

        return TaskResponse(true, "Successfully updated mappings")
    }

    @Transactional
    private fun saveOrUpdateFieldMappings(
        mappings: List<JpaFieldsMapping>,
        entityId: Long,
        integrationId: Long,
    ): List<JpaFieldsMapping> {
        val updatedFieldMappings = mappings.map { mapping ->
            // Check if the mapping already exists
            var existingMapping = fieldsMappingRepository.findByEntityIdAndIntegrationIdAndOriginFieldAndIsActive(
                mapping.entityId,
                mapping.integrationId,
                mapping.originField ?: ""
            )

            if (existingMapping != null) {
                // Update existing mapping
                existingMapping.apply {
                    mappedField = mapping.mappedField
                    mappedFieldLabel = mapping.mappedFieldLabel
                    isRequired = mapping.isRequired
                    isActive = mapping.isActive
                    // Update existing children and add new children
                    val existingChildIds = existingMapping.children.map { it.originField }.toSet()
                    val updatedChildIds = mutableListOf<String>()
                    val updatedChildren = mapping.children.map { child ->
                        if (!child.originField.isNullOrEmpty() && child.originField in existingChildIds && child.originField !in updatedChildIds) {
                            // Update existing child
                            val existingChild = existingMapping.children.first { it.originField == child.originField }
                            existingChild.apply {
                                mappedField = child.mappedField
                                mappedFieldLabel = child.mappedFieldLabel
                                isRequired = child.isRequired
                                isActive = child.isActive
                            }
                            updatedChildIds.add(child.originField)
                            existingChild
                        } else {
                            // Add new child
                            child.apply { parent = existingMapping }
                            child
                        }
                    }
                    // Clear and add updated children
                    children = updatedChildren
                }
                fieldsMappingRepository.save(existingMapping)
            } else {
                // Save new mapping
                mapping.children.forEach { it.parent = mapping }
                fieldsMappingRepository.save(mapping)
            }
        }
        // Update legal entity mapping status if needed
        val currentFieldMappingsCount = fieldsMappingRepository.getFieldMappingCounts(entityId, integrationId)
        val resultArray = if (currentFieldMappingsCount.isNotEmpty()) currentFieldMappingsCount[0] else emptyArray()
        val totalRequiredRecords = resultArray.getOrElse(0) { 0 }
        val totalRequiredMappedFieldNull = resultArray.getOrElse(1) { 0 }
        log.info("Number of required fields: $totalRequiredRecords, number of required fields not mapped: $totalRequiredMappedFieldNull")
        // If there are no mandatory fields needed for onboarding, just mark mapping status to be FULLY_MAPPED
        if (totalRequiredMappedFieldNull != totalRequiredRecords || totalRequiredRecords == 0L) {
            val legalEntityMapping =
                legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId).orElseThrow {
                    EntityNotFoundException("Not found legal entity mapping for entityId $entityId")
                }
            val status =
                if (totalRequiredMappedFieldNull == 0L) LegalMappingStatus.FULLY_MAPPED else LegalMappingStatus.PARTIALLY_MAPPED
            log.info("Legal entity mapping status need to be updated from ${legalEntityMapping.status} to $status")
            if (legalEntityMapping.status != status) {
                legalEntityMapping.status = status
                legalEntityMappingRepository.save(legalEntityMapping)
            }
        }

        return updatedFieldMappings
    }

    fun getIntegrationLegalEntityMappings(integrationId: Long): List<IntegrationEntityMappingStatusOutput> {
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
        // Fetch existed entity mappings
        var entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        val entityMappingIds = entityMappings.map { it.entityId }.toMutableSet()
        log.info("Fetching legal entities for company ${integration.companyId}")
        val legalEntities = newCompanyServiceAdapter.getLegalEntities(integration.companyId)
        val newLegalEntities = mutableListOf<JpaLegalEntityMapping>()
        val entityIdToLegalEntity = mutableMapOf<Long, CompanyOuterClass.LegalEntity>()
        for (legalEntity in legalEntities) {
            if (legalEntity.id !in entityMappingIds) {
                log.info("Found new legal entity need to be added ${legalEntity.legalName}")
                newLegalEntities.add(
                    JpaLegalEntityMapping(
                        entityId = legalEntity.id,
                        entityName = legalEntity.legalName,
                        companyId = integration.companyId,
                        status = LegalMappingStatus.UNMAPPED,
                        isEnabled = false,
                        integrationId = integrationId,
                        entityCountry = legalEntity.address.country
                    )
                )
            }
            entityIdToLegalEntity[legalEntity.id] = legalEntity
        }
        if (newLegalEntities.isNotEmpty()) {
            legalEntityMappingRepository.saveAll(newLegalEntities)
            entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        }
        return entityMappings.map {
            val legalEntityObj = entityIdToLegalEntity[it.entityId]
            val legalEntityMapped = dataMapper.map(legalEntityObj)
            IntegrationEntityMappingStatusOutput.newBuilder()
                .entityMappingId(it.id)
                .integrationId(integrationId)
                .isEnabled(it.isEnabled)
                .legalEntity(legalEntityMapped)
                .entityMappingStatus(com.multiplier.integration.types.LegalMappingStatus.valueOf(it.status.name))
                .company(
                    Company.newBuilder()
                        .id(it.companyId)
                        .build()
                )
                .build()
        }
    }

    @Transactional
    fun saveIntegrationEntityMappingStatus(entityMappingId: Long, enableDataSync: Boolean = true): TaskResponse {
        val legalEntityMapping = legalEntityMappingRepository.findById(entityMappingId).orElseThrow {
            EntityNotFoundException(
                "Not found legal entity mapping with id = $entityMappingId"
            )
        }
        legalEntityMapping.isEnabled = enableDataSync

        if (!enableDataSync) {
            legalEntityMappingRepository.save(legalEntityMapping)
            return TaskResponse.newBuilder()
                .success(true)
                .message("Successfully updated status")
                .build()
        }

        if (legalEntityMapping.status != LegalMappingStatus.FULLY_MAPPED) {
            return TaskResponse.newBuilder()
                .success(false)
                .message("Need to map all fields to enable sync for this legal entity")
                .build()
        }

        legalEntityMappingRepository.save(legalEntityMapping)
        // Update old events failed for not existed legal entity mapping to be processed again
        val integration = companyIntegrationRepository.findById(legalEntityMapping.integrationId)
            .orElseThrow { IllegalStateException("Integration not found for integrationId=${legalEntityMapping.integrationId}") }
        if (integration.enabled && integration.incomingSyncEnabled) {
            val receivedEvents =
                receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                    integration.accountToken,
                    legalEntityMapping.entityCountry,
                    isEntityEnabled = false,
                    processed = true
                )
            val updatedReceivedEvents = mutableListOf<JpaReceivedEvent>()
            val archivedReceivedEvents =
                receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                    integration.accountToken,
                    legalEntityMapping.entityCountry,
                    isEntityEnabled = false,
                    processed = true
                )
            updatedReceivedEvents.addAll(receivedEvents.map {
                it.isEntityEnabled = true
                it.processed = false
                it
            })
            if (archivedReceivedEvents.isNotEmpty()) {
                updatedReceivedEvents.addAll(restoreArchivedReceivedEventsForEntityRetry(archivedReceivedEvents))
                receivedEventsArchiveRepository.deleteAll(archivedReceivedEvents)
            }
            if (updatedReceivedEvents.isNotEmpty()) {
                receivedEventRepository.saveAll(updatedReceivedEvents)
            }
        }
        return TaskResponse.newBuilder()
            .success(true)
            .message("Successfully updated status")
            .build()
    }

    private fun getDepartmentFieldValues(
        integration: JpaCompanyIntegration,
        fieldId: String
    ): GetFieldValuesResponse {
        log.info("Department field $fieldId returned empty values, trying departments.list endpoint")
        try {
            val deptResp = knitAdapter.getDepartmentsList(
                integration.companyId,
                integration.platform.id!!,
                mapPlatformIdToKnitAppId(integration.platform.name)
            )
            // Only use departments response if it was successful and has data
            if (deptResp.success && !deptResp.data?.departments.isNullOrEmpty()) {
                val fieldValues = deptResp.data.departments.map { dept ->
                    FieldValues(
                        id = dept.id,
                        label = dept.name
                    )
                }
                return GetFieldValuesResponse(
                    success = true,
                    data = Field(fields = fieldValues),
                    responseCode = deptResp.responseCode
                )
            } else {
                log.warn("Departments list endpoint returned empty response")
                return GetFieldValuesResponse(
                    success = false,
                    error = ErrorResponse(msg = "No departments found or error occurred"),
                    responseCode = deptResp.responseCode
                )
            }
        } catch (e: Exception) {
            log.error("Error calling departments list endpoint", e)
            return GetFieldValuesResponse(
                success = false,
                error = ErrorResponse(msg = e.message ?: "Unknown error"),
                responseCode = null
            )
        }
    }

    fun getExternalEnumValues(
        integration: JpaCompanyIntegration,
        externalFields: List<FieldData>?,
    ): List<JpaExternalPlatformValues>? {
        val cachedExternalValues = externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integration.id!!)
        val cacheEnumMapping = cachedExternalValues.associateBy { it.fieldId }
        val enumExternalFields =
            externalFields?.filter { it.fieldId != null && (it.dataType == FieldType.ENUM.name || it.fieldId.contains("department")) }

        val fetchEnumValues = enumExternalFields?.filter {
            !cacheEnumMapping.containsKey(it.fieldId) || (cacheEnumMapping.containsKey(it.fieldId) && Duration.between(
                cacheEnumMapping[it.fieldId]?.updatedOn,
                LocalDateTime.now()
            ).toDays() >= 1)
        }?.map {
            Pair(it.fieldId!!, it.mappedKey)
        }
        if (fetchEnumValues.isNullOrEmpty()) {
            return cachedExternalValues
        }
        // Update caching for enum values
        val resultMap = runBlocking {
            fetchEnumValues.map { (fieldId, mappedKey) ->
                async {
                    // First try the regular field.values endpoint
                    var resp = knitAdapter.getFieldValues(
                        integration.companyId,
                        integration.platform.id!!,
                        mapPlatformIdToKnitAppId(integration.platform.name),
                        fieldId
                    )
                    
                    // If it's a department field and the response is empty/null, try the departments.list endpoint
                    if (fieldId.contains("department") && (resp.data?.fields.isNullOrEmpty())) {
                        val deptResp = getDepartmentFieldValues(integration, fieldId)
                        // Only use the new response if it's successful and has data
                        if (deptResp.success && !deptResp.data?.fields.isNullOrEmpty()) {
                            resp = deptResp
                        } else {
                            log.info("Falling back to original field values response for department field $fieldId")
                        }
                    }
                    
                    if (resp.success) fieldId to (resp to mappedKey) else null
                }
            }.awaitAll()
                .filterNotNull()
                .toMap()
        }
        if (resultMap.isEmpty()) {
            return cachedExternalValues
        }
        val updatedExternalPlatformValues = resultMap.map { (fieldId, result) ->
            val updatedValues = result.first.data?.fields?.map { objectMapper.writeValueAsString(it) }
            if (cacheEnumMapping.containsKey(fieldId)) {
                cacheEnumMapping[fieldId]?.let {
                    it.values = updatedValues
                    it.updatedOn = LocalDateTime.now()
                    it
                }
            } else {
                JpaExternalPlatformValues(
                    fieldId = fieldId,
                    integrationId = integration.id!!,
                    mappedKey = result.second,
                    values = updatedValues
                )
            }
        }
        externalPlatformValuesRepository.saveAll(updatedExternalPlatformValues)
        return externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integration.id!!)
    }

    fun getFixedEnumValues(
        externalPlatformValues: List<JpaExternalPlatformValues>?,
        integrationId: Long,
        thirdPartyFields: List<UnmappedField>,
    ): List<JpaExternalPlatformValues> {
        val fixedEnumMappings = mapOf(
            "profile.gender" to enumValues<Gender>(),
            "profile.maritalStatus" to enumValues<MaritalStatus>()
        )
        val dynamicEnumKeys = externalPlatformValues?.map { it.mappedKey }?.toSet() ?: emptySet()
        val mappedKeyToFieldId = thirdPartyFields.filter { !it.key.isNullOrBlank() }.associate { it.key to it.fieldId }

        val updatedEnumValues = fixedEnumMappings
            .filterKeys { it !in dynamicEnumKeys }
            .map { (key, enumValues) ->
                JpaExternalPlatformValues(
                    mappedKey = key,
                    integrationId = integrationId,
                    fieldId = mappedKeyToFieldId[key] ?: key,
                    values = enumValues.map { v ->
                        objectMapper.writeValueAsString(
                            mapOf(
                                "id" to v.name,
                                "label" to v.name
                            )
                        )
                    }
                )
            }

        return (externalPlatformValues ?: emptyList()) + updatedEnumValues
    }

    fun getCalculatedFields(): Map<String, String> {
        return try {
            val mappingConfigurations =
                fieldMappingConfigurationRepository.findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type = FieldMappingConfigurationType.CALCULATED)
            mappingConfigurations.associate { it.key to it.value }
        } catch (e: Exception) {
            log.error("getCalculatedFields exception: ", e)
            emptyMap()
        }
    }

    fun getDefaultMappings(
        defaultFields: List<UnmappedField>?,
        externalFieldValues: List<JpaExternalPlatformValues>?,
        legalEntity: CompanyOuterClass.LegalEntity,
    ): Map<String, ExternalField> {
        try {
            val regex = "\\[[^\\]]*\\]".toRegex()
            val mappingConfigurations =
                fieldMappingConfigurationRepository.findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type = FieldMappingConfigurationType.DEFAULT)
            val defaultMappings: Map<String, String> = mappingConfigurations.associate { it.key to it.value }
            val externalKeyToFieldMap = defaultFields?.associate {
                it.key to it
            }
            val externalKeyToValuesMap = externalFieldValues?.associate {
                it.mappedKey to it
            }
            val bankMappings = getBankDetails(legalEntity.currencyCode, legalEntity.address.country)
            val combinedMapping = defaultMappings + bankMappings
            val result = mutableMapOf<String, ExternalField>()
            combinedMapping.forEach { (key, value) ->
                val formattedValue = regex.replace(value, "[]")
                val externalValueObj = externalKeyToValuesMap?.getOrDefault(formattedValue, null)
                val externalField = externalKeyToFieldMap?.getOrDefault(value, null)
                result[key] = ExternalField(
                    mappedKey = value,
                    label = externalField?.label,
                    values = externalValueObj?.values ?: emptyList(),
                    fieldId = externalValueObj?.fieldId,
                    isCustomField = externalField?.isCustomField
                )
            }
            return result
        } catch (e: Exception) {
            log.error("getDefaultMappings exception: ", e)
            return emptyMap()
        }
    }

    private fun getBankDetails(currency: String, country: String?): Map<String, String> {
        log.info("Creating IntegrationPaymentAccountRequirementsRequest for employee")

        val request = IntegrationPaymentAccountRequirementsRequest.newBuilder()
            .setAccountType(AccountType.PERSONAL)
            .setTransferType(TransferType.FIAT)
            .setSourceCurrency(currency)
            .setTargetCurrency(currency)
            .setCountryCode(country.orEmpty())
            .setPaymentDirection(PaymentDirection.PAY_OUT)
            .addPaymentPartners(PaymentPartner.HSBC)
            .setIntegrationPartner(IntegrationPartner.KNIT)
            .build()

        log.info("Request created: $request")

        var paymentRequirements: IntegrationPaymentAccountRequirements? = null

        try {
            log.info("Fetching payment requirements for request: $request")
            paymentRequirements = paymentServiceAdapter.getIntegrationPaymentAccountRequirements(request)
            log.info("Payment requirements fetched successfully for employee")
        } catch (e: Exception) {
            log.error("Error getting payment requirements for employee", e)
        }

        return if (paymentRequirements == null || paymentRequirements.requirementsList.isNullOrEmpty()) {
            log.warn("No payment requirements found, using static fields for employee")
            getBankDetailFromStaticFieldsWithCustomFields()
        } else {
            log.info("Using dynamic fields for employee")
            getBankDetailFromDynamicFieldsWithCustomFields(paymentRequirements.requirementsList.first())
        }
    }

    private fun getBankDetailFromStaticFieldsWithCustomFields(): Map<String, String> {
        log.info("Using static fields for bank details of employee")
        return mapOf(
            "$BANK_DATA_SPEC_PREFIX.accountHolderName" to "profile.firstName",
            "$BANK_DATA_SPEC_PREFIX.accountNumber" to "bankAccounts[0].accountNumber",
            "$BANK_DATA_SPEC_PREFIX.bankName" to "bankAccounts[0].bankName",
            "$BANK_DATA_SPEC_PREFIX.bankBranch" to "bankAccounts[0].routingInfo[type=BRANCH_CODE].number",
            "$BANK_DATA_SPEC_PREFIX.swiftCode" to "bankAccounts[0].routingInfo[type=SWIFT_CODE].number",
            "$BANK_DATA_SPEC_PREFIX.localBankCode" to "bankAccounts[0].routingInfo[type=BANK_IDENTIFICATION_CODE].number",
            "$BANK_DATA_SPEC_PREFIX.country" to "locations.workAddress.country",
        )
    }

    fun getBankDetailFromDynamicFieldsWithCustomFields(requirement: IntegrationPaymentAccountRequirement): Map<String, String> {
        val requirementFields = requirement.accountRequirementsList ?: return emptyMap()
        log.info("Using dynamic fields for bank details of employee")
        val bankDetails = mutableMapOf<String, String>()
        requirementFields.forEach { field ->
            val key = field.requirementKey
            val value = when (val mappedKey = field.mappedKey) {
                "accountHolderName" -> "profile.firstName"
                "accountNumber" -> "bankAccounts[0].accountNumber"
                "bankName" -> "bankAccounts[0].bankName"
                "branchName", "branchCode" -> "bankAccounts[0].routingInfo[type=BRANCH_CODE].number"
                "swiftCode" -> "bankAccounts[0].routingInfo[type=SWIFT_CODE].number"
                "localBankCode" -> "bankAccounts[0].routingInfo[type=BANK_IDENTIFICATION_CODE].number"
                "ifscCode" -> "bankAccounts[0].routingInfo[type=IFSC_CODE].number"
                "iban" -> "bankAccounts[0].routingInfo[type=IBAN].number"
                "routingNumber" -> "bankAccounts[0].routingInfo[type=ROUTING_NUMBER].number"
                "country" -> "locations.workAddress.country"
                else -> "customFields.fields.${mappedKey}"
            }
            bankDetails["$BANK_DATA_SPEC_PREFIX.$key"] = value
        }
        return bankDetails
    }

    @Transactional
    fun handleFieldMappingsOnDisconnection(integrationId: Long) {
        log.info("Unmapped entity mappings and deleting field mappings by integrationId: $integrationId")

        try {
            updateAndSaveEntityMappingsOnDisconnection(integrationId)
            updateAndSaveFieldMappingsOnDisconnection(integrationId)
            updateAndSaveEnumValuesOnDisconnection(integrationId)
        } catch (e: Exception) {
            log.error("[HandleFieldMappingsOnDisconnection] Throw exception: ${e.message} ", e)
        }
    }

    private fun updateAndSaveEntityMappingsOnDisconnection(integrationId: Long) {
        val entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        val updatedEntityMappings = entityMappings.map {
            it.apply {
                isEnabled = false
                status = LegalMappingStatus.UNMAPPED
            }
        }

        if (updatedEntityMappings.isNotEmpty()) {
            legalEntityMappingRepository.saveAll(updatedEntityMappings)
        }
    }

    private fun updateAndSaveFieldMappingsOnDisconnection(integrationId: Long) {
        val fieldMappings = fieldsMappingRepository.findByIntegrationIdAndIsActive(integrationId)
        val updatedFieldMappings = fieldMappings.map {
            it.apply {
                isActive = false
            }
        }

        if (updatedFieldMappings.isNotEmpty()) {
            fieldsMappingRepository.saveAll(updatedFieldMappings)
        }
    }

    private fun updateAndSaveEnumValuesOnDisconnection(integrationId: Long) {
        val externalPlatformValues = externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId)
        val updatedExternalPlatformValues = externalPlatformValues.map {
            it.apply {
                isDeleted = true
            }
        }

        if (updatedExternalPlatformValues.isNotEmpty()) {
            externalPlatformValuesRepository.saveAll(updatedExternalPlatformValues)
        }
    }

    private fun restoreArchivedReceivedEventsForEntityRetry(receivedEvents: List<JpaReceivedEventArchive>): List<JpaReceivedEvent> {
        return receivedEvents.map { event ->
            JpaReceivedEvent(
                eventId = event.eventId,
                syncId = event.syncId,
                integrationId = event.integrationId,
                eventType = event.eventType,
                syncDataType = event.syncDataType,
                errors = event.errors,
                identifiervalue = event.identifiervalue,
                receivedTime = event.receivedTime,
                data = event.data,
                confirmedByUser = event.confirmedByUser,
                processed = false,
                isEntityEnabled = true,
                entityId = event.entityId,
                entityCountry = event.entityCountry
            )
        }
    }

    fun getKnitFieldsV2(jpaCompanyIntegration: JpaCompanyIntegration): List<FieldMappingV2> {
        val knitFields = getKnitFields(
            jpaCompanyIntegration.id!!,
            jpaCompanyIntegration.companyId,
            jpaCompanyIntegration
        )
        val externalPlatformValues = getExternalEnumValues(jpaCompanyIntegration, knitFields)
        val thirdPartyFields = formatKnitFields(knitFields)
        val updatedExternalPlatformValues =
            getFixedEnumValues(externalPlatformValues, jpaCompanyIntegration.id!!, thirdPartyFields)
        val unmappedFieldsOutput = formatUnmappedFields(
            updatedExternalPlatformValues,
            thirdPartyFields,
            jpaCompanyIntegration
        )
        return unmappedFieldsOutput.map { FieldMappingV2(
            it.key,
            it.label,
            false,
            if (it.children != null) it.children.map { child ->
                FieldMappingV2(
                    child.key,
                    child.label,
                    false,
                    emptyList()
                )
            } else emptyList<FieldMappingV2>()
        ) }
    }

    fun getIntegrationFieldsMappingProfile(
        entityId: Long,
        integrationId: Long,
    ): IntegrationFieldsMappingOutputV2 {
        // Fetch integration and validate
        val integration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
        val companyId = integration.companyId

        // Fetch legal entity and validate
        log.info("Fetching legal entity and company integration")
        val matchedLegalEntity = newCompanyServiceAdapter.getLegalEntities(companyId)
            .find { it.id == entityId }
            ?: throw EntityNotFoundException("Not found legal entity with entityId=$entityId")

        return try {
            log.info("Attempting to fetch field mapping profile from field-mapping-service")

            // Get or create profile
            val profile = getOrCreateProfile(companyId, entityId, integrationId, matchedLegalEntity, integration)
            val mappingStatus = determineMappingStatus(profile)

            val sourceFields = getKnitFieldsV2(integration)

            val targetFields = getOnboardDataSpecs(matchedLegalEntity, companyId)
                .map { dataSpec ->
                    val builder = FieldMappingV2.newBuilder().key(dataSpec.key).label(dataSpec.label)
                    val children = dataSpec.valuesList.map { value ->
                        FieldMappingV2.newBuilder()
                            .key(value)
                            .label(value)
                            .build()
                    }
                    builder.children(children).build()
                }
            // Build and return response
            dataMapper.map(
                profileId = UUID.fromString(profile.id),
                integrationId = integrationId,
                matchedLegalEntity = matchedLegalEntity,
                mappingStatus = mappingStatus.name,
                sourceFields = sourceFields,
                targetFields = targetFields,
                companyId = companyId
            )
        } catch (e: Exception) {
            log.error("Error fetching field mapping profile from field-mapping-service: ${e.message}", e)
            IntegrationFieldsMappingOutputV2.newBuilder().build()
        }
    }

    /**
     * Gets an existing profile or creates a new one if none exists
     */
    private fun getOrCreateProfile(
        companyId: Long,
        entityId: Long,
        integrationId: Long,
        legalEntity: CompanyOuterClass.LegalEntity,
        integration: JpaCompanyIntegration
    ):  Profile{
        // Get all profiles for the company
        val profiles = fieldMappingServiceAdapter.listProfiles(companyId)

        // Find the profile for this entity and integration
        val profile = profiles.profilesList.firstOrNull { profile ->
            // Check if the profile has config data for this entity and integration
            profile.configMap.fieldsMap["entityId"]?.stringValue == entityId.toString() &&
                    profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
        } ?: createNewProfile(companyId, entityId, integrationId, legalEntity, integration)

        //update the status of the mapping
        val legalEntityMapping =
                legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId).orElseThrow {
                    EntityNotFoundException("Not found legal entity mapping for entityId $entityId")
                }
            val status = determineMappingStatus(profile)
            log.info("Legal entity mapping status need to be updated from ${legalEntityMapping.status} to $status")
            if (legalEntityMapping.status != status) {
                legalEntityMapping.status = status
                legalEntityMappingRepository.save(legalEntityMapping)
            }

        return profile
    }

    /**
     * Creates a new profile for the entity and integration
     */
    private fun createNewProfile(
        companyId: Long,
        entityId: Long,
        integrationId: Long,
        legalEntity: CompanyOuterClass.LegalEntity,
        integration: JpaCompanyIntegration
    ): Profile {
        log.info("No field mapping profile found for entityId=$entityId and integrationId=$integrationId, creating new profile")

        val configMap = Struct.newBuilder().putAllFields(
            mapOf(
                "entityId" to Value.newBuilder().setStringValue(entityId.toString()).build(),
                "integrationId" to Value.newBuilder().setStringValue(integrationId.toString()).build(),
            )
        ).build()

        val profileRequest = Profile.newBuilder()
            .setName("${legalEntity.legalName} - ${integration.platform.name}")
            .setDescription("Profile for ${legalEntity.legalName} - ${integration.platform.name}")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setConfigMap(configMap).addAllRules(emptyList()).build()

        return fieldMappingServiceAdapter.createProfile(profileRequest).profile
    }

    /**
     * Determines the mapping status based on the profile and its rules
     */
    private fun determineMappingStatus(
        profile: Profile
    ): LegalMappingStatus {
        if (!profile.isActive || profile.rulesList.isEmpty()) {
            return LegalMappingStatus.UNMAPPED
        }

        // Check if any required fields are unmapped
        val hasUnmappedRequiredFields = profile.rulesList.any { rule ->
            rule.isRequired && rule.sourceField.isNullOrEmpty()
        }

        return if (hasUnmappedRequiredFields) {
            LegalMappingStatus.PARTIALLY_MAPPED
        } else {
            LegalMappingStatus.FULLY_MAPPED
        }
    }
}