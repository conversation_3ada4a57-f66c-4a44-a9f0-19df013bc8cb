package com.multiplier.integration.service

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.integration.service.InboundDataExtractionService.BaseExtractor.Companion.extractDataFromPathsAsString
import org.springframework.stereotype.Service

@Service
class InboundDataExtractionService {

    class BaseExtractor {
        companion object {
            val objectMapper = ObjectMapper().apply {
                registerKotlinModule()
                configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true)
                configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true)
            }

            private fun extractDataFromPaths(data: Map<String, Any>, vararg paths: String): Any? {
                for (path in paths) {
                    val value = extractDataFromPath(data, path)
                    if (value != null) return value
                }
                return null
            }

            fun extractDataFromPath(data: Map<String, Any>, path: String): Any? {
                val keys = path.split("[", "]", ".").filter { it.isNotBlank() }
                var currentData: Any? = data
                for (key in keys) {
                    currentData = when (currentData) {
                        is Map<*, *> -> currentData[key]
                        is List<*> -> extractFromList(currentData, key)
                        else -> null // Invalid path or data structure
                    }
                }
                return currentData
            }

            fun extractDataFromPathsAsString(data: Map<String, Any>, vararg paths: String): String? {
                return when (val extractedData = extractDataFromPaths(data, *paths)) {
                    null -> null
                    is String -> extractedData
                    else -> objectMapper.writeValueAsString(extractedData)
                }
            }

            fun extractDataFromPathAsString(data: Map<String, Any>, path: String): String? {
                return when (val extractedData = extractDataFromPath(data, path)) {
                    null -> null
                    is String -> extractedData
                    else -> objectMapper.writeValueAsString(extractedData)
                }
            }

            private fun extractFromList(list: List<*>, key: String): Any? =
                if (key.contains("=")) extractFromListWithConditions(list, key) else extractFromListWithIndex(list, key)

            fun extractFromListWithConditions(list: List<*>, key: String): Any? {
                val filterConditions = key.split(",").map { it.trim() }
                val filteredItems = list.filter {
                    val element = it as Map<*, *>?
                    element != null && filterConditions.all { condition ->
                        val (filterKey, filterValue) = condition.split("=")
                        element[filterKey]?.toString() == filterValue
                    }
                }
                return filteredItems.firstOrNull()
            }

            fun extractFromListWithIndex(list: List<*>, key: String): Any? =
                key.toIntOrNull()?.let { if (it in list.indices) list[it] else null }
        }
    }

    class CountryExtractor {
        companion object {
            fun extractCountry(eventData: Map<String, Any>): String? =
                extractDataFromPathsAsString(
                    eventData,
                    "locations.workAddress.country",
                    "locations.presentAddress.country",
                    "locations.permanentAddress.country",
                    "customFields.fields.country"
                )

            fun extractState(eventData: Map<String, Any>): String? =
                extractDataFromPathsAsString(
                    eventData,
                    "locations.workAddress.state",
                    "locations.presentAddress.state",
                    "locations.permanentAddress.state",
                    "customFields.fields.state"
                )
        }
    }
}