package com.multiplier.integration.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.integration.service.exception.CustomerErrorCode

object FileExtractorFactory {

    fun getExtractor(input: IntegrationInput): FileExtractor {
        return when {
            input.module == BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA -> getEORTimeSheetExtractor(input.uri)
            else -> throw CustomerErrorCode.FILE_EXTRACTOR_NOT_SUPPORTED.toBusinessException(
                message = "File extractor not supported for the input $input"
            )
        }
    }

    private fun getEORTimeSheetExtractor(uri: String): FileExtractor {
        return when {
            uri.contains("/ukg/") && uri.endsWith(".xlsx") -> UKGTimeSheetFileExtractor
            uri.contains("/hubstaff/") && uri.endsWith(".csv") -> CsvTimeSheetFileExtractor
            else -> throw CustomerErrorCode.FILE_EXTRACTOR_NOT_SUPPORTED.toBusinessException(
                message = "Unsupported file format or source in URI: $uri"
            )
        }
    }
}