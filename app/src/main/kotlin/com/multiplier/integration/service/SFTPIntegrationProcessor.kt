package com.multiplier.integration.service

import com.multiplier.common.concurrent.MplExecutor
import com.multiplier.integration.adapter.api.BulkUploadServiceAdapter
import com.multiplier.integration.adapter.api.CUSTOMER_INTEGRATION_SERVICE_GROUP
import com.multiplier.integration.adapter.api.CreateJobInput
import com.multiplier.integration.adapter.api.DataGroup
import com.multiplier.integration.adapter.api.GetValidationReportInput
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaBulkJobTracker
import com.multiplier.integration.repository.model.URIType
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class SFTPIntegrationProcessor(
    private val sftpDirectoryService: SFTPDirectoryService,
    private val bulkUploadServiceAdapter: BulkUploadServiceAdapter,
    private val bulkUploadTrackerService: BulkJobTrackerService,
    private val notificationService: NotificationsService,
) : IntegrationProcessor {

    override fun startIntegration(input: IntegrationInput) {
        log.info { "Start integration with input: $input" }
        val fileInputStream = sftpDirectoryService.downloadFile(input.uri)

        val extractor = FileExtractorFactory.getExtractor(input)
        val dataEntries: List<Map<String, String>> = extractor.extractEntries(fileInputStream)
        log.info { "Extract data successfully with total entry count: ${dataEntries.size}" }

        val job = bulkUploadServiceAdapter.createJob(buildCreateJobInput(input, dataEntries))
        log.info { "Create bulk upload job with jobId ${job.id} successfully" }

        val progressingURI = sftpDirectoryService.moveFileToProcessingDir(input.uri)
        log.info { "Move file to processing dir successfully for jobId: ${job.id}" }

        createBulkJobTracker(input, job.id, progressingURI)
        bulkUploadServiceAdapter.validateJob(job.id)
        log.info { "Trigger job validation successfully" }
    }

    private fun createBulkJobTracker(input: IntegrationInput, bulkJobId: Long, originalFileURI: String) {
        val tracker = JpaBulkJobTracker(
            jobId = bulkJobId,
            jobStatus = BulkJobStatus.VALIDATION_IN_PROGRESS,
            companyId = input.companyId,
            entityId = input.entityId,
            groupName = CUSTOMER_INTEGRATION_SERVICE_GROUP,
            moduleNames = setOf(input.module.name),
            originalFileURI = originalFileURI,
            originalURIType = URIType.SFTP,
        )
        bulkUploadTrackerService.createBulkJobTracker(tracker)
    }

    private fun buildCreateJobInput(integrationInput: IntegrationInput, data: List<Map<String, String>>): CreateJobInput {
        return CreateJobInput(
            companyId = integrationInput.companyId,
            entityId = integrationInput.entityId,
            moduleNames = setOf(integrationInput.module.name),
            jobData = listOf(
                DataGroup(
                    name = getIntegrationDataGroupName(integrationInput),
                    moduleName = integrationInput.module.name,
                    rows = data,
                )
            )
        )
    }

    override fun handleValidationFailure(bulkJobTrackerId: Long) {
        val bulkJobTracker = bulkUploadTrackerService.findByIdOrThrow(bulkJobTrackerId)
        val validationReport = bulkUploadServiceAdapter.getValidationReport(GetValidationReportInput(bulkJobTracker.jobId))
        val reportInputStream = WebDownloader.downloadAsInputStream(validationReport.uri)
        val reportURI = sftpDirectoryService.uploadReportFile(reportInputStream, bulkJobTracker.originalFileURI!!)
        val archiveURI = sftpDirectoryService.moveFileToArchive(bulkJobTracker.originalFileURI!!)
        bulkUploadServiceAdapter.cancelJob(jobId = bulkJobTracker.jobId)
        updateNewURIsForBulkJobTracker(bulkJobTracker, BulkJobStatus.VALIDATION_FAILED, archiveURI, reportURI)
        MplExecutor.runAsync { notificationService.notifySFTPValidationFailure(bulkJobTrackerId) }
    }

    override fun handleValidationSuccess(bulkJobTrackerId: Long) {
        val bulkJobTracker = bulkUploadTrackerService.findByIdOrThrow(bulkJobTrackerId)
        bulkUploadServiceAdapter.commitJob(bulkJobTracker.jobId)
        updateJobStatusForBulkJobTracker(bulkJobTracker, BulkJobStatus.UPSERT_IN_PROGRESS)
    }

    private fun updateJobStatusForBulkJobTracker(bulkJobTracker: JpaBulkJobTracker, jobStatus: BulkJobStatus) {
        val input = BulkJobTrackerDTO(
            id = bulkJobTracker.id!!,
            jobStatus = jobStatus,
            originalFileURI = bulkJobTracker.originalFileURI!!,
            reportFileURI = bulkJobTracker.reportFileURI,
            reportURIType = bulkJobTracker.reportURIType
        )
        bulkUploadTrackerService.updateBulkJobTracker(input)
    }

    override fun handleDataCreationSuccess(bulkJobTrackerId: Long) {
        log.info { "Handle data creation success for bulk job tracker id: $bulkJobTrackerId" }
        val bulkJobTracker = bulkUploadTrackerService.findByIdOrThrow(bulkJobTrackerId)
        val archiveURI = sftpDirectoryService.moveFileToArchive(bulkJobTracker.originalFileURI!!)
        updateNewURIsForBulkJobTracker(bulkJobTracker, BulkJobStatus.UPSERT_SUCCESSFUL, archiveURI)
        MplExecutor.runAsync { notificationService.notifySFTPUpsertSuccess(bulkJobTrackerId) }
        log.info { "Handle data creation success for bulk job tracker id: $bulkJobTrackerId successfully" }
    }

    private fun updateNewURIsForBulkJobTracker(
        bulkJobTracker: JpaBulkJobTracker,
        jobStatus: BulkJobStatus,
        originalFileURI: String,
        reportFileURI: String? = null,
    ) {
        val updatingBulkJobTracker = BulkJobTrackerDTO(
            id = bulkJobTracker.id!!,
            jobStatus = jobStatus,
            originalFileURI = originalFileURI,
            reportFileURI = reportFileURI,
            reportURIType = reportFileURI?.let { URIType.SFTP },
        )
        bulkUploadTrackerService.updateBulkJobTracker(updatingBulkJobTracker)
    }
}
