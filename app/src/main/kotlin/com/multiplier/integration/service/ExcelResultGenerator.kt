package com.multiplier.integration.service

import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.HorizontalAlignment
import org.apache.poi.ss.usermodel.RichTextString
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.ss.usermodel.WorkbookFactory
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileNotFoundException
import java.io.InputStream
import java.net.URL

const val CHARACTER_WIDTH_MULTIPLIER = 256
const val VALIDATION_ERRORS_COL_HEADER = "Validation errors"
const val VALIDATION_ERRORS_COL_DESCRIPTION = "Please address these issues:"
const val MINIMUM_COLUMN_WIDTH = "a few words of space".length * CHARACTER_WIDTH_MULTIPLIER
const val PIXEL_TO_POINTS_RATIO = 20
const val ERROR_MESSAGE_SEPARATOR = "\n"
const val PROMPT_BOX_LENGTH_LIMIT = 255
const val DEFAULT_NUMBER_OF_ROWS = 500
const val EMPLOYEE_ID_COLUMN_INDEX = 1
const val FIRST_NAME_COLUMN_INDEX = 2
const val LAST_NAME_COLUMN_INDEX = 3
const val EMAIL_ADDRESS_COLUMN_INDEX = 4
const val GENDER_COLUMN_INDEX = 5
const val ENTITY_COUNTRY_COLUMN_INDEX = 6
const val DEFAULT_VALUE = "NA"

private const val ROW_OFFSET = 2

data class CellStyleConfig(
    val fontName: String = "Calibri",
    val fontHeight: Short = 11,
    val isBold: Boolean = false,
    val verticalAlignment: VerticalAlignment = VerticalAlignment.CENTER,
    val horizontalAlignment: HorizontalAlignment = HorizontalAlignment.LEFT,
    val wrapText: Boolean = true,
)

operator fun Sheet.get(rowIndex: Int): Row = this.getRow(rowIndex)

operator fun Row.get(cellIndex: Int): Cell? = this.getCell(cellIndex)

private fun Sheet.setCellValue(
    rowIndex: Int,
    colIndex: Int,
    value: String,
    style: CellStyle? = null,
) {
    this[rowIndex].setCellValue(colIndex, value, style)
}

private fun Row.setCellValue(colIndex: Int, value: String, style: CellStyle? = null) {
    val cell = this[colIndex] ?: this.createCell(colIndex)
    cell.setCellValue(value)
    style?.let { cell.cellStyle = style }
}

private fun Workbook.createErrorCellStyle(): CellStyle {
    return createCustomCellStyle(CellStyleConfig(fontHeight = 11, isBold = false))
}

fun Workbook.createCustomCellStyle(config: CellStyleConfig): CellStyle {
    val font =
        this.createFont().apply {
            fontName = config.fontName
            fontHeightInPoints = config.fontHeight
            bold = config.isBold
        }
    return this.createCellStyle().apply {
        setFont(font)
        verticalAlignment = config.verticalAlignment
        alignment = config.horizontalAlignment
        wrapText = config.wrapText
    }
}

private fun Workbook.createHeaderCellStyle(): CellStyle {
    return createCustomCellStyle(CellStyleConfig(fontHeight = 11, isBold = true))
}

private fun Workbook.createDescriptionHeaderCellStyle(): CellStyle {
    return createCustomCellStyle(CellStyleConfig(fontHeight = 9, isBold = true))
}

private fun setErrorCell(
    errorsByRow: Map<Int, List<EmployeeValidationError>>,
    sheet: Sheet,
    workbook: Workbook,
    rowIndex: Int,
    rowOffset: Int,
    errorCellStyle: CellStyle,
) {
    val errors = errorsByRow[rowIndex - rowOffset]
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = 0,
        text =
        workbook.createRichText(
            errors?.joinToString(separator = ERROR_MESSAGE_SEPARATOR) { it.error } ?: ""),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = EMPLOYEE_ID_COLUMN_INDEX,
        text =
        workbook.createRichText(
            errors?.firstOrNull()?.hrMemberDataInput?.get("employeeId") ?: ""
        ),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = FIRST_NAME_COLUMN_INDEX,
        text =
        workbook.createRichText(
            errors?.firstOrNull()?.hrMemberDataInput?.get("firstName") ?: ""
        ),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = LAST_NAME_COLUMN_INDEX,
        text =
        workbook.createRichText(
            errors?.firstOrNull()?.hrMemberDataInput?.get("lastName") ?: ""
        ),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = EMAIL_ADDRESS_COLUMN_INDEX,
        text =
        workbook.createRichText(
            errors?.firstOrNull()?.hrMemberDataInput?.get("email") ?: ""
        ),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = GENDER_COLUMN_INDEX,
        text =
        workbook.createRichText(
            errors?.firstOrNull()?.hrMemberDataInput?.get("gender") ?: ""
        ),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = ENTITY_COUNTRY_COLUMN_INDEX,
        text =
        workbook.createRichText(
            errors?.firstOrNull()?.entityCountry ?: ""
        ),
        style = errorCellStyle,
    )
    sheet[rowIndex].heightInPoints = (errors?.size ?: 1) * sheet.defaultRowHeightInPoints
}

private fun mapStatusAndEventTypeToStatusString(status: String, eventType: EventType?): String {
    if (status != EventStatus.SUCCESS.name) {
        return status
    }
    if (eventType in listOf(EventType.SERVICE_INTERNAL_CREATE_CONTRACT, EventType.INCOMING_ONBOARDING_STATUS_UPDATE)) {
        return "Created"
    }
    if (eventType in listOf(EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED, EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED, EventType.INCOMING_MEMBER_ADDRESS_UPDATED, EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)) {
        return "Updated"
    }
    return "$eventType $status"
}

private fun setCellContentForSyncSummaryResult(
    syncSummaryResult: SyncSummaryResult,
    sheet: Sheet,
    workbook: Workbook,
    rowIndex: Int,
    errorCellStyle: CellStyle,
) {
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = 0,
        text = workbook.createRichText(mapStatusAndEventTypeToStatusString(syncSummaryResult.status, syncSummaryResult.eventType)),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = 1,
        text = workbook.createRichText(syncSummaryResult.error ?: DEFAULT_VALUE),
        style = errorCellStyle,
    )
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = 2,
        text = workbook.createRichText(syncSummaryResult.contractId.toString()),
        style = errorCellStyle,
    )
    val employeeInfo = syncSummaryResult.employeeInfo
    if (employeeInfo != null) {
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 3,
            text = workbook.createRichText(employeeInfo.firstName?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 5,
            text = workbook.createRichText(employeeInfo.lastName?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 6,
            text = workbook.createRichText(employeeInfo.emailAddress?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 7,
            text = workbook.createRichText(employeeInfo.designation?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 8,
            text = workbook.createRichText(employeeInfo.startDate?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 9,
            text = workbook.createRichText(employeeInfo.locationId?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 10,
            text = workbook.createRichText(employeeInfo.addressLine1?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 11,
            text = workbook.createRichText(employeeInfo.addressLine2?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 12,
            text = workbook.createRichText(employeeInfo.province?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 13,
            text = workbook.createRichText(employeeInfo.country?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 14,
            text = workbook.createRichText(employeeInfo.postalCode?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 15,
            text = workbook.createRichText(employeeInfo.workEmailAddress?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
        sheet.setRichText(
            rowIndex = rowIndex,
            colIndex = 16,
            text = workbook.createRichText(employeeInfo.contactNumber?.value ?: DEFAULT_VALUE),
            style = errorCellStyle,
        )
    }
}

private fun Workbook.createRichText(value: String) = this.creationHelper.createRichTextString(value)

private fun Sheet.setRichText(
    rowIndex: Int,
    colIndex: Int,
    text: RichTextString,
    style: CellStyle,
) {
    val cell = this[rowIndex][colIndex] ?: this.getRow(rowIndex).createCell(colIndex)
    cell.setCellValue(text)
    cell.cellStyle = style
}

private fun Workbook.flush(): ByteArrayOutputStream {
    val outputStream = ByteArrayOutputStream()
    this.write(outputStream)
    outputStream.close()
    this.close()
    return outputStream
}

data class EmployeeValidationError(
    val error: String,
    val rowNumber: Int,
    val columnName: String,
    val employeeName: String,
    val hrMemberDataInput: Map<String, String>? = null,
    val companyId: Long? = null,
    val integrationId: String,
    val entityCountry: String? = null,
)

data class SyncSummaryResult(
    val contractId: Long,
    val eventType: EventType?,
    val status: String,
    val error: String?,
    val employeeInfo: EmployeeInfo?
)

data class EmployeeInfo(
    val firstName: EmployeeDetail?,
    val lastName: EmployeeDetail?,
    val emailAddress: EmployeeDetail?,
    val designation: EmployeeDetail?,
    val startDate: EmployeeDetail?,
    val locationId: EmployeeDetail?,
    val addressLine1: EmployeeDetail?,
    val addressLine2: EmployeeDetail?,
    val province: EmployeeDetail?,
    val country: EmployeeDetail?,
    val postalCode: EmployeeDetail?,
    val workEmailAddress: EmployeeDetail?,
    val contactNumber: EmployeeDetail?
)

data class EmployeeDetail(
    val value: String?,
    val isUpdated: Boolean? = false
)

fun getResource(path: String): URL? = object {}.javaClass.getResource(path)

fun getResourceAsStream(path: String): InputStream = object {}.javaClass.getResourceAsStream(path) ?: throw FileNotFoundException("Not found file $path")

fun getFileFromResources(path: String): File =
    getResource(path)?.let { File(it.path) }
        ?: throw IntegrationIllegalArgumentException("File not found $path")

object ExcelResultGenerator {
    fun addValidationErrorsToInputSheet(
        inputSheet: File,
        validationErrors: List<EmployeeValidationError>,
    ): ByteArray {
        if (validationErrors.isEmpty()) {
            return inputSheet.readBytes()
        }

        val errorsByRow = validationErrors.groupBy { it.rowNumber }

        val workbook = WorkbookFactory.create(inputSheet)
        val errorCellStyle = workbook.createErrorCellStyle()

        val sheet = workbook.getSheetAt(0)

        for (rowIndex in 0..sheet.lastRowNum) {
            when (rowIndex) {
                0 -> continue
                1 -> {
                    sheet.setCellValue(
                        rowIndex, 0, VALIDATION_ERRORS_COL_HEADER, workbook.createHeaderCellStyle()
                    )
                }

                2 -> {
                    sheet.setCellValue(
                        rowIndex,
                        0,
                        VALIDATION_ERRORS_COL_DESCRIPTION,
                        workbook.createDescriptionHeaderCellStyle()
                    )
                }

                3 -> continue
                else -> {
                    setErrorCell(
                        errorsByRow = errorsByRow,
                        sheet = sheet,
                        workbook = workbook,
                        rowIndex = rowIndex,
                        rowOffset = 3,
                        errorCellStyle = errorCellStyle
                    )
                }
            }
        }

        sheet[0].height = 0
        sheet.createFreezePane(2, ROW_OFFSET)
        sheet.autoSizeColumn(0)

        return workbook.flush().toByteArray()
    }

    fun addSyncSummaryResultToInputSheet(
        inputSheet: InputStream,
        results: List<SyncSummaryResult>,
    ): ByteArray {
        if (results.isEmpty()) {
            return inputSheet.readBytes()
        }

        val workbook = WorkbookFactory.create(inputSheet)
        val errorCellStyle = workbook.createErrorCellStyle()

        val sheet = workbook.getSheetAt(0)
        var rowIndex = 2
        for (result in results) {
            if (rowIndex >= sheet.physicalNumberOfRows) {
                sheet.createRow(sheet.physicalNumberOfRows)
            }
            setCellContentForSyncSummaryResult(
                syncSummaryResult = result,
                sheet = sheet,
                workbook = workbook,
                rowIndex = rowIndex,
                errorCellStyle = errorCellStyle
            )
            rowIndex += 1
        }

        return workbook.flush().toByteArray()
    }
}