package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.LeaveTypeMappingRepository
import com.multiplier.integration.repository.model.JpaLeaveTypesMapping
import com.multiplier.integration.service.exception.IntegrationNotFoundException
import com.multiplier.integration.service.exception.InternalAPIException
import com.multiplier.integration.service.exception.KnitIntegrationException
import com.multiplier.integration.types.ExternalLeaveType
import com.multiplier.integration.types.LeaveTypeMappingDefinition
import com.multiplier.integration.types.SaveLeaveTypesMappingInput
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.utils.validateIntegrationCompanyMatch
import kotlinx.coroutines.runBlocking
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TimeOffSyncService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val leaveTypeMappingRepository: LeaveTypeMappingRepository,
    private val timeoffServiceAdapter: TimeoffServiceAdapter,
    private val knitAdapter: KnitAdapter
) {

    private val internalTypeIdModifier = 100000

    fun getInternalLeaveTypes(currentUserCompanyId: Long): List<ExternalLeaveType> {
        try {
            val internalTimeOffTypesResponse = timeoffServiceAdapter.getCompanyTimeOffTypes(currentUserCompanyId)

            return (internalTimeOffTypesResponse?.systemTimeOffTypesList ?: emptyList())
                .map {
                    ExternalLeaveType((internalTypeIdModifier + it.id).toString(), it.label, it.key)
                } + ((internalTimeOffTypesResponse?.companyTimeOffTypesList ?: emptyList())
                .map {
                    ExternalLeaveType((internalTypeIdModifier + it.id).toString(), it.label, it.key)
                })
        } catch (e: Exception) {
            throw InternalAPIException()
        }
    }

    fun getExternalLeaveTypes(companyId: Long?, integrationId: Long, isOpsUser: Boolean = false): List<ExternalLeaveType> {
        val matchingIntegration = companyIntegrationRepository.findById(integrationId)
            .orElseThrow { IntegrationNotFoundException("Integration not found for integrationId=$integrationId") }
        validateIntegrationCompanyMatch(companyId, matchingIntegration.companyId, isOpsUser)

        val externalLeaveTypesResponse = runBlocking {
            knitAdapter.getLeaveTypes(companyId!!, matchingIntegration.platform.id!!)
        }

        return externalLeaveTypesResponse.takeIf { externalLeaveTypesResponse.success!! }
            ?.data?.leaveTypes?.map { ExternalLeaveType(it.id, it.name, it.type?.name ?: "UNKNOWN") }
            ?: throw KnitIntegrationException("Error when fetching leave types from " + matchingIntegration.platform.name)
    }

    fun getLeaveTypeMappingDefinition(
        currentUserCompanyId: Long,
        integrationId: Long
    ): List<LeaveTypeMappingDefinition> {
        val jpaLeaveTypesMappings =
            leaveTypeMappingRepository.findByCompanyIdAndIntegrationId(currentUserCompanyId, integrationId)
        return jpaLeaveTypesMappings.takeIf { it.isNotEmpty() }
            ?.map {
                LeaveTypeMappingDefinition(
                    it.id,
                    it.integrationId,
                    it.externalTypeId.toString(),
                    it.internalTypeId.toString()
                )
            }
            .orEmpty()
    }

    @Transactional
    fun saveLeaveTypesMapping(companyId: Long, input: SaveLeaveTypesMappingInput): TaskResponse {
        if (input.mapping.isNullOrEmpty()) {
            return TaskResponse.newBuilder()
                .success(false)
                .message("No mapping found in the request")
                .build()
        }

        val existingLeaveTypesMapping =
            leaveTypeMappingRepository.findByCompanyIdAndIntegrationId(companyId, input.integrationId)
        leaveTypeMappingRepository.deleteAllInBatch(existingLeaveTypesMapping)

        val companyInternalLeaveTypes = getInternalLeaveTypes(companyId)

        val newLeaveTypesMapping = input.mapping.map {
            JpaLeaveTypesMapping(
                it.internalTypeId,
                getLeaveTypeLabelFromLeaveTypeKey(it.internalTypeId, companyInternalLeaveTypes),
                it.externalTypeId,
                input.integrationId,
                companyId
            )
        }
        leaveTypeMappingRepository.saveAll(newLeaveTypesMapping)

        return TaskResponse.newBuilder()
            .success(true)
            .message("Successfully save leave type mapping")
            .build()
    }

    private fun getLeaveTypeLabelFromLeaveTypeKey(
        leaveTypeKey: String,
        companyInternalLeaveTypes: List<ExternalLeaveType>,
    ): String? {
        return companyInternalLeaveTypes.firstOrNull { it.leaveType == leaveTypeKey }?.name
    }
}