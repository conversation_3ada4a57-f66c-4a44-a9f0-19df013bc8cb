package com.multiplier.integration.service

import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.service.exception.CustomerErrorCode
import okhttp3.OkHttpClient
import okhttp3.Request
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.util.concurrent.TimeUnit

object WebDownloader {
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(3, TimeUnit.MINUTES)
        .writeTimeout(3, TimeUnit.MINUTES)
        .build()

    fun downloadAsInputStream(url: String): InputStream {
        val request = Request.Builder().url(url).build()
        val response = okHttpClient.newCall(request).execute()
        if (!response.isSuccessful) throw CustomerErrorCode.INTERNAL_ERROR.toSystemException(
            message = "Failed to download file from URL: $url",
        )
        return response.body?.byteStream() ?: ByteArrayInputStream(ByteArray(0))

    }
}
