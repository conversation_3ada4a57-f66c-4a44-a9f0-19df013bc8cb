package com.multiplier.integration.service

import com.multiplier.common.aws.s3.CopyRequest
import com.multiplier.common.aws.s3.DeleteFileRequest
import com.multiplier.common.aws.s3.DeleteRequest
import com.multiplier.common.aws.s3.DownloadRequest
import com.multiplier.common.aws.s3.S3Service
import com.multiplier.common.aws.s3.UploadRequest
import com.multiplier.integration.utils.FTPDirectoryUtil
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.io.InputStream

private val log = KotlinLogging.logger {}

@Service
class SFTPDirectoryService(
    private val s3Service: S3Service,
) {
    fun downloadFile(uri: String): InputStream {
        val downloadRequest = DownloadRequest(
            fileName = FTPDirectoryUtil.getFileName(uri),
            path = FTPDirectoryUtil.getFilePath(uri),
        )
        return s3Service.download(downloadRequest)
    }

    /**
     * Moves the file to the processing directory and returns the new URI.
     *
     * Example:
     * ```
     * Input:  c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx
     * Output: c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * ```
     * @param sourceFileURI The URI of the source file.
     * @return The URI of the file in the processing directory.
     */
    fun moveFileToProcessingDir(sourceFileURI: String): String {
        val processingURI = copyFileToProcessingDir(sourceFileURI)
        deleteFile(sourceFileURI)
        return processingURI
    }

    /**
     * Copy the file to the processing directory and returns the processing URI.
     *
     * Example:
     * ```
     * Input:  c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx
     * Output: c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * ```
     * @param sourceFileURI The URI of the source file.
     * @return The URI of the file in the processing directory.
     */
    private fun copyFileToProcessingDir(sourceFileURI: String): String {
        val processingFilePath = FTPDirectoryUtil.generateProcessingFilePath(sourceFileURI)
        val processingFileName = FTPDirectoryUtil.generateProcessingFileName(sourceFileURI)
        val copyRequest = CopyRequest(
            sourceFileName = FTPDirectoryUtil.getFileName(sourceFileURI),
            sourcePath = FTPDirectoryUtil.getFilePath(sourceFileURI),
            destinationFilename = processingFileName,
            destinationPath = processingFilePath
        )
        s3Service.copy(copyRequest)
        return "$processingFilePath/$processingFileName"
    }

    /**
     * Moves the file to the archive directory and returns the archive URI.
     *
     * Example:
     * ```
     * Input:  c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * Output: c_aira/ukg/timesheets/archive/UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * ```
     * @param sourceFileURI The URI of the source file.
     * @return The URI of the file in the archive directory.
     */
    fun moveFileToArchive(sourceFileURI: String): String {
        val archiveURI = copyFileToArchive(sourceFileURI)
        deleteFile(sourceFileURI)
        return archiveURI
    }

    /**
     * Copy the file to the archive directory and returns the archive URI.
     *
     * Example:
     * ```
     * Input:  c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * Output: c_aira/ukg/timesheets/archive/UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * ```
     * @param sourceFileURI The URI of the source file.
     * @return The URI of the file in the archive directory.
     */
    private fun copyFileToArchive(sourceFileURI: String): String {
        val sourceFilePath = FTPDirectoryUtil.getFilePath(sourceFileURI)
        val sourceFileName = FTPDirectoryUtil.getFileName(sourceFileURI)
        val archiveFilePath = FTPDirectoryUtil.generateArchiveFilePath(sourceFileURI)
        val copyRequest = CopyRequest(
            sourceFileName = sourceFileName,
            sourcePath = sourceFilePath,
            destinationFilename = sourceFileName,
            destinationPath = archiveFilePath
        )
        s3Service.copy(copyRequest)
        return "$archiveFilePath/$sourceFileName"
    }


    /**
     * Uploads the report file and returns the report URI.
     *
     * @param fileInputStream The input stream of the report file to upload.
     * @param originalFileURI The URI of the original file.
     * @return The URI of the uploaded report file.
     */
    fun uploadReportFile(fileInputStream: InputStream, originalFileURI: String): String {
        val filePath = FTPDirectoryUtil.generateReportFilePath(originalFileURI)
        val fileName = FTPDirectoryUtil.generateReportFileName(originalFileURI)
        val uploadRequest = UploadRequest(
            fileName = fileName,
            data = fileInputStream,
            path = filePath,
        )
        s3Service.upload(uploadRequest)
        return "$filePath/$fileName"
    }

    private fun deleteFile(uri: String) {
        runCatching {
            s3Service.delete(
                request = DeleteRequest(
                    filePaths = setOf(
                        DeleteFileRequest(
                            fileName = FTPDirectoryUtil.getFileName(uri),
                            path = FTPDirectoryUtil.getFilePath(uri),
                        )
                    )
                )
            )
        }.onFailure { e ->
            // When an exception is thrown, we consider it to be non-critical.
            log.warn(e) { "Could not delete file: $uri" }
        }
    }
}
