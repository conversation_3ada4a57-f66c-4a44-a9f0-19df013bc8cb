package com.multiplier.integration.service

import com.multiplier.integration.repository.model.URIType
import org.springframework.stereotype.Service

@Service
class IntegrationProcessorFactory(
    private var sftpIntegrationProcessor: SFTPIntegrationProcessor
) {

    fun getProcessor(uriType: URIType): IntegrationProcessor {
        return when (uriType) {
            URIType.SFTP -> sftpIntegrationProcessor
        }
    }
}
