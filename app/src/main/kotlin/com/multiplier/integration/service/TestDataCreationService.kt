package com.multiplier.integration.service

import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.adapter.model.PerformanceReviewRequest
import com.multiplier.integration.handlers.contract.ContractCreationEventHandler
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.UpdateMemberFieldsRequest
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDate

const val TEST_EMPLOYEE_NAME_PREFIX = "tstmplyeeee-"
const val TESTING_EMAIL_PREFIX = "petar.novakovic+"
const val TESTING_EMAIL_SUFFIX = "@usemultiplier.com"
const val POSITION = "SDE"

@Service
class TestDataCreationService(
    private val contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter,
    private val eventLogService: EventLogService,
    private val contractCreationEventHandler: ContractCreationEventHandler,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter : MemberServiceAdapter,
    private val employeeService: EmployeeService,
    private val syncService: SyncService,
    private val contractIntegrationRepository: CompanyIntegrationRepository,
    private val knitAdapter: KnitAdapter,
) {

    private val log = KotlinLogging.logger {}


    fun createEmployeesForGpSync(companyId: Long,
                                           legalEntityId: Long,
                                          countryCode: String,
                                         numberOfEmployees: Int) : String {

       knitAdapter.setTestOngoing(true)
        syncService.setCompanyId(companyId)
        syncService.setEntityId(legalEntityId)
        syncService.setCountryCode(countryCode)
        syncService.setLocalSyncInprogress(true)

        for (i in 1..numberOfEmployees) {
            val randomEventId = (1..10).map { ('0'..'9').random() }.joinToString("")

            val email =  TESTING_EMAIL_PREFIX + randomEventId + TESTING_EMAIL_SUFFIX

            employeeService.createEmployee(companyId, "Milan", "Milann", email, POSITION, email)

        }
           val integrationId = contractIntegrationRepository.findByCompanyIdAndEnabled(companyId, true)?.firstOrNull()?.accountToken ?: throw IllegalStateException("No integration found for company ID: $companyId")
            knitAdapter.startSync(integrationId, PlatformCategory.HRIS, true)

        while (syncService.getLocalSyncIsInProgress() || syncService.getCreatedContractIds().isEmpty()) {
            log.info("Waiting for incoming sync to finish. sleeping")
            Thread.sleep(1000)
        }

        val createdContractId = syncService.getCreatedContractIds()[0]
        val contract = contractServiceAdapter.findContractByContractId(createdContractId)
        var member = memberServiceAdapter.findMemberByMemberId(contract.memberId)
        val updatedMember = member.toBuilder()
            .setFirstName("Nebojshaaaaaa")
            .build()
        employeeService.updateEmployee(companyId, null, updatedMember, contract, null)
        knitAdapter.setTestOngoing(false)
       return "OK"
    }

    fun createEmployeesForEorSync(companyId: Long, legalEntityId : Long, countryCode: String, numberOfEmployees: Int): List<Long> {

        val employeeIds = mutableListOf<Long>()

        for (i in 1..numberOfEmployees) {
            val onboardingEmployeeData = generateOnboardingEmployeeData(companyId, legalEntityId, countryCode)

            val extendedOnboardingEmployeeData = onboardingEmployeeData + mapOf(
                "countryCode" to Country.CountryCode.valueOf(countryCode).name,
                "templateVersion" to GP_BULK_ONBOARDING_TEMPLATE_VERSION
            )

            val bulkOnboardingRequest = BulkContractOnboardingRequest(
                companyId = companyId,
                entityId = legalEntityId,
                context = OnboardingType.GLOBAL_PAYROLL,
                countryCode = Country.CountryCode.valueOf(countryCode),
                contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
                data = GroupedEmployeeData(employeeData = extendedOnboardingEmployeeData)
            )

            val response = contractOnboardingServiceAdapter.bulkOnboarding(bulkOnboardingRequest)

            val contractId = response.get(0)


            val randomEventId = (1..10).map { ('0'..'9').random() }.joinToString("")

            val eventLog = eventLogService.createEventLog(
                type = EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
                eventId = randomEventId,
                status = EventStatus.TO_BE_PROCESSED,
                payload = "dummyPayload",
                contractId = contractId
            )

           contractCreationEventHandler.processEvent(eventLog, true)

            val performanceReviewRequest = PerformanceReviewRequest(
                contractId = contractId,
                effectiveDate = LocalDate.now(),
                amount = 7000.0,
                additionalPays = emptyList()
            )

            val contract = contractServiceAdapter.findContractByContractId(contractId)


            val memberUpdateBuilder = UpdateMemberFieldsRequest.newBuilder().setMemberId(contract.memberId).setFirstName("bla").build()
            memberServiceAdapter.updateMemberFields(memberUpdateBuilder)

            contractServiceAdapter.performanceReviewBulkCreateAsApproved(performanceReviewRequest)

        }

        return employeeIds
    }

    fun generateOnboardingEmployeeData(
        companyId: Long?,
        legalEntityId: Long?,
        countryCode: String?
    ): Map<String, String> {
        val employeeData = mutableMapOf<String, String>().apply {
            put("bank.bankName", "E12345")
            put("firstName", TEST_EMPLOYEE_NAME_PREFIX + "testUser")
            put("lastName", "testUser")
            val randomInfix = (1..10).map { ('a'..'z').random() }.joinToString("")
            put("email", TESTING_EMAIL_PREFIX + randomInfix + TESTING_EMAIL_SUFFIX)
            put("gender", "MALE")
            put("phoneNumber", "+************")
            put("address.line1", "Address Line 1")
            put("address.city", "Address City")
            put("address.country", "Vietnam")
            put("address.postalCode", "34000")
            put("position", "SDE")
            put("startOn", "2024-09-14")
            val randomEmployeeId = (1..10).map { ('0'..'9').random() }.joinToString("")
            put("employeeId", randomEmployeeId)
            put("rateFrequency", "MONTHLY")
            put("payrollFrequency", "MONTHLY")
            put("basePay", "5000")
            put("race", "Vietnamese")
            put("immigrationStatus", "Citizen")
            put("nid", "123321")
            put("bank.accountHolderName", "Account Holder Name")
            put("bank.address.country", "SG")
            put("bank.swiftCode", "VTCBVNVXXXX")
            put("bank.accountNumber", "***********")
        }
        return employeeData
    }

}

