package com.multiplier.integration.service.email

import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import com.multiplier.integration.types.NotificationType
import com.multiplier.integration.utils.toCapitalize
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class AdminEmailTemplateGenerator {

    @Value("\${platform.userservice.system.notification.email}")
    private lateinit var systemNotificationEmail: String

    fun getEmailTemplateForEmployeeImportCompletion(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.IntegrationEmployeeImportCompletedEmailToAdmin.toString()
        builder.subject = "Data sync completed from $platformName to Multiplier"
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForIntegrationCredentialExpired(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.IntegrationCredentialsExpiredEmailToAdmin.toString()
        builder.subject = "Important Note - Auto sync with $platformName failed."
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForIntegrationConnected(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.IntegrationConnectedEmailToAdmin.toString()
        builder.subject = "$platformName is now connected."
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForStaleGPSyncDeactivated(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.StaleGPSyncDeactivatedToCompany.toString()
        builder.subject = "A Global Payroll sync for $platformName is disabled."
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForIntegrationDisconnected(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.IntegrationDisconnectedEmailToAdmin.toString()
        builder.subject = "Reconnect $platformName on Multiplier"
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForOutgoingSyncDisabled(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.IntegrationEmployeeSyncTurnedOnEmailToAdmin.toString()
        builder.subject = "You just turned off employee sync from Multiplier to $platformName."
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForAutoSyncFailed(
        adminEmail: String,
        platformName: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.IntegrationAutoSyncFailedEmailToAdmin.toString()
        builder.subject = "Action Required! Auto sync failed."
        builder.from = systemNotificationEmail
        builder.to = adminEmail

        return builder
    }

    fun getEmailTemplateForSFTPIntegrationUpsertSuccess(
        adminEmail: String,
        entriesCount: Int,
        fileName: String,
        feature: String,
    ): EmailNotificationBody.Builder {
        return EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationSFTPUpsertSuccessEmailToAdmin.toString())
            .setSubject("${feature.toCapitalize()} upload: $entriesCount values updated successfully - ($fileName)")
            .setFrom(systemNotificationEmail)
            .setTo(adminEmail)
    }

    fun getEmailTemplateForSFTPIntegrationValidationFailure(
        adminEmail: String,
        fileName: String,
        feature: String,
    ): EmailNotificationBody.Builder {
        return EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationSFTPValidationFailureEmailToAdmin.toString())
            .setSubject("${feature.toCapitalize()}: Some Entries Could Not Be Processed – ($fileName)")
            .setFrom(systemNotificationEmail)
            .setTo(adminEmail)
    }

    fun getEmailTemplateForSftpAccessRequest(
        opsEmail: String
    ): EmailNotificationBody.Builder {
        return EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationSFTPAccessRequestEmailToOps.toString())
            .setSubject("New SFTP Access Request")
            .setFrom(systemNotificationEmail)
            .setTo(opsEmail)
    }
}
