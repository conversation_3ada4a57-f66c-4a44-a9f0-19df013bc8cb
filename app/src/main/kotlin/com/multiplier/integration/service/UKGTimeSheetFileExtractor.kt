package com.multiplier.integration.service

import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Workbook
import java.io.InputStream
import java.time.format.DateTimeFormatter

object UKGTimeSheetFileExtractor : FileExtractor {

    override fun extractEntries(inputStream: InputStream): List<Map<String, String>> {
        val workbook = ExcelProcessor.createXSSFWorkbookOrThrow(inputStream)
        return extractEntries(workbook)
    }

    private fun extractEntries(workbook: Workbook): List<Map<String, String>> {
        val sheet = workbook.getSheetAt(0)
        val entries = mutableListOf<Map<String, String>>()
        var currentEmployeeId: String? = null

        // Skip the first row as it contains headers
        for (rowIdx in 1..sheet.lastRowNum) {
            val row = sheet.getRow(rowIdx) ?: continue
            val columnA = row.getCell(0)?.stringCellValue ?: ""
            val columnB = row.getCell(1)

            when {
                // Check the employee id row
                columnA.equals("Employee ID", ignoreCase = true) -> {
                    currentEmployeeId = columnB?.getEmployeeId()
                }

                // Check for timesheet entry row
                columnA.isBlank() && columnB.hasCellValue() && currentEmployeeId != null -> {
                    val date = row.getDate()
                    val startTime = row.getStartTime()
                    val endDate = row.getEndTime()

                    entries.add(
                        mapOf(
                            "employeeId" to currentEmployeeId,
                            "date" to date,
                            "workStartTime" to startTime,
                            "workEndTime" to endDate,
                        )
                    )
                }
            }
        }

        return filterInvalidEntries(entries)
    }

    private fun filterInvalidEntries(entries: List<Map<String, String>>): List<Map<String, String>> {
        return entries.filterNot { entry -> entry.values.contains("-") }
    }

    private fun Cell?.hasCellValue(): Boolean {
        return this != null && this.cellType != CellType.BLANK
    }

    private fun Cell.getEmployeeId(): String {
        return when (this.cellType) {
            CellType.STRING -> this.stringCellValue
            CellType.NUMERIC -> this.numericCellValue.toInt().toString()
            else -> "-"
        }
    }

    private fun Row.getDate(): String {
        return this.getCell(1).localDateTimeCellValue.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))
    }

    private fun Row.getStartTime(): String {
        val startTime = this.getCell(4).stringCellValue
        return formatTime(startTime)
    }

    private fun Row.getEndTime(): String {
        val endTime = this.getCell(5).stringCellValue
        return formatTime(endTime)
    }

    /**
     * Formats the time string to the desired format.
     *
     * Example:
     * ```
     * Input: 06:01a
     * Output: 06:01
     *
     * Input: 3:22p
     * Output: 15:22
     * ```
     * @param time The time string to format.
     * @return The formatted time string.
     */
    fun formatTime(time: String): String {
        val timePattern = Regex("(\\d{1,2}):(\\d{2})([ap])")
        return timePattern.replace(time) { matchResult ->
            val hour = matchResult.groupValues[1].toInt()
            val minute = matchResult.groupValues[2]
            val amPm = matchResult.groupValues[3]
            val formattedHour = if (amPm == "p" && hour != 12) hour + 12 else hour
            String.format("%02d:%s", formattedHour, minute)
        }
    }
}