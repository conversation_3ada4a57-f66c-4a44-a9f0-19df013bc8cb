package com.multiplier.integration.service

import com.multiplier.growthbook.sdk.GrowthBookSDK
import com.multiplier.growthbook.sdk.GrowthBookSdkBuilder
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

enum class FeatureFlag(val flag: String) {
    CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED("integration-field-mappings-enabled"),
    BAMBOO_PAYSLIP_FOLDER_NAME("bamboo-integration-payslip-folder-name"),
    ACCOUNTING_INTEGRATION_ENABLED("accounting-integration-enabled"),
    KNIT_V2_DATA_MODEL("knit-v2-data-model"),
    MTM_KNIT_INTEGRATION_ID("mtm_knit_integration_id"),
    MTM_SUPPORTED_COUNTRIES("mtm_supported_countries"),
    MTM_SUPPORTED_DEPARTMENTS("mtm_departments"),
    SYNC_COMPENSATION_UPDATE("sync-compensation-update"),
    FIELD_MAPPING_SERVICE_ENABLED("field-mapping-service-enabled"),
    ;

    override fun toString(): String {
        return flag
    }

    companion object {
        fun fromFlag(flag: String): FeatureFlag? {
            return values().find { it.flag == flag }
        }
    }
}

@Service
@EnableScheduling
class FeatureFlagService(
    @Value("\${growthbook.base-url}") baseUrl: String,
    @Value("\${growthbook.env-key}") envKey: String,
) {
    private val log = KotlinLogging.logger {}
    var sdkInstance: GrowthBookSDK = GrowthBookSdkBuilder(envKey, baseUrl)
        .setRefreshHandler { this.flagsRefreshHandler(it) }
        .initialize()

    fun isOn(
        id: FeatureFlag,
        attributes: Map<String, Any> = emptyMap(),
    ): Boolean {
        return sdkInstance.feature(id.flag, attributes).on
    }

    fun getStringValue(
        id: FeatureFlag,
        attributes: Map<String, Any> = emptyMap(),
    ): String {
        return sdkInstance.feature(id.flag, attributes).value as String
    }

    @Scheduled(fixedRateString = "\${growthbook.refresh-frequency-ms}")
    fun refreshFeatureFlags() {
        log.debug("[refreshFeatureFlags] - START")
        sdkInstance.reloadFeatureFlags()
        log.debug("[refreshFeatureFlags] - DONE")
    }

    private fun flagsRefreshHandler(success: Boolean) {
        if (!success) log.warn("[FeatureFlagService] Feature Flags failed to refresh!")
    }

    fun isKnitDataModelV2(companyId: Long): Boolean {
        return isOn(FeatureFlag.KNIT_V2_DATA_MODEL, mapOf("company" to companyId))
    }

    fun isSyncCompensationUpdate(companyId: Long): Boolean {
        return isOn(FeatureFlag.SYNC_COMPENSATION_UPDATE, mapOf("company" to companyId))
    }

    fun isMtmIntegration(integrationId: String, workEmail: String?): Boolean {
        return integrationId == getStringValue(FeatureFlag.MTM_KNIT_INTEGRATION_ID)
                || workEmail?.contains("multiplier") == true
    }
}
