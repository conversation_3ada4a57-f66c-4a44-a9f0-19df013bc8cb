package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.repository.CachePositionsRepository
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.runBlocking
import org.springframework.beans.factory.annotation.Value


@Service
class PositionsService(
    private val cachePositionsRepository: CachePositionsRepository,
    private val knitAdapter: KnitAdapter,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
) {

    private val log = KotlinLogging.logger {}

    @Value("\${positions.cache.ttl}")
    private val cacheTtlSeconds: Long = 86400 // 24 hours

    @Value("\${positions.batch.size}")
    private val batchSize: Int = 3


    fun updateCachedPosition(
        companyId: Long,
        platformId: Long,
        data: GetPositionDetailResponse
    ) {
        if (data.data == null) {
            throw IntegrationInternalServerException("Position data cannot be null")
        }
        try {
            cachePositionsRepository.upsertCachedPosition(companyId, platformId, data, LocalDateTime.now())
        } catch (e: Exception) {
            log.error(e) { "Failed to save/update cached position for company=$companyId, platform=$platformId, error=${e.message}" }
            throw IntegrationInternalServerException("Failed to cache position data")
        }
    }

    fun getPositions(companyId: Long, platformId: Long, ignoreCache: Boolean): GetPositionDetailResponse? {

        if(!ignoreCache)
        {
            // Try to get cached data
            val cachedResult = cachePositionsRepository.findCachedPositions(companyId, platformId)

            // Check if we have valid cached data
            if (cachedResult != null) {
                val secondsSinceUpdate = ChronoUnit.SECONDS.between(cachedResult.second, LocalDateTime.now())
                if (secondsSinceUpdate < cacheTtlSeconds) {
                    log.debug { "Returning cached positions data for company=$companyId, platform=$platformId" }
                    return cachedResult.first
                }
                log.debug { "Cache expired for company=$companyId, platform=$platformId (age=${secondsSinceUpdate}s)" }
            }
        }

        // Cache miss or expired, fetch fresh data
        log.debug { "Fetching fresh positions data for company=$companyId, platform=$platformId" }
        val positionsResponse = knitAdapter.getPositionsDetails(companyId, platformId)
            ?: throw IntegrationInternalServerException("Failed to fetch positions for companyId=$companyId")

        if (!positionsResponse.success) {
            throw IntegrationInternalServerException("Fetching positions was not successful (company=$companyId, platform=$platformId): ${positionsResponse.error?.msg}")
        }

        // Cache the new data
        updateCachedPosition(companyId, platformId, positionsResponse)

        return positionsResponse
    }

    fun cachePositions(platformId: Long) {
        try {
            val integrations = companyIntegrationRepository.findEnabledIntegrationsByPlatformId(platformId)
            log.info { "Found ${integrations.size} enabled integrations for platform=$platformId" }

            runBlocking {
                coroutineScope {
                    integrations.chunked(batchSize).forEach { batch ->
                        batch.map { integration ->
                            async {
                                try {
                                    val positionsResponse = knitAdapter.getPositionsDetails(
                                        companyId = integration.companyId,
                                        platformId = platformId
                                    )

                                    if (positionsResponse == null) {
                                        log.error { "Failed to fetch positions for companyId=${integration.companyId}" }
                                        return@async
                                    }

                                    if (!positionsResponse.success) {
                                        log.error { "Fetching positions was not successful for companyId=${integration.companyId}: ${positionsResponse.error?.msg}" }
                                        return@async
                                    }

                                    updateCachedPosition(
                                        companyId = integration.companyId,
                                        platformId = platformId,
                                        data = positionsResponse
                                    )
                                    log.debug { "Successfully cached positions for companyId=${integration.companyId}, platformId=$platformId" }
                                } catch (e: Exception) {
                                    log.error(e) { "Error processing positions for companyId=${integration.companyId}, platformId=$platformId, error=${e.message}" }
                                }
                            }
                        }.awaitAll()
                    }
                }
            }
            // Process integrations in parallel, but limit concurrency to 3

        } catch (e: Exception) {
            log.error(e) { "Failed to cache positions for platformId=$platformId, error=${e.message}" }
            throw IntegrationInternalServerException("Failed to cache positions for platform")
        }
    }

}
