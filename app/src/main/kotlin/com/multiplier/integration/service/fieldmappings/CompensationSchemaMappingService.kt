package com.multiplier.integration.service.fieldmappings

import com.multiplier.integration.repository.CompensationSchemaConfigRepository
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.LocalDate

private val log = KotlinLogging.logger {}

@Service
class CompensationSchemaConfigService(
    private val repository: CompensationSchemaConfigRepository,
) {

    fun groupEmployeeData(
        entityId: Long,
        input: Map<String, String>,
    ): GroupedEmployeeData {
        val config = repository.findByEntityId(entityId)

        val employeeId = input["employeeId"]
        val employeeConfig = config?.config?.employeeConfigs?.find { it.employeeId == employeeId }

        log.info("Compensation config for entity {}: {}", entityId, config)

        return CompensationSchemaMapper.groupEmployeeData(
            input = input,
            startDate = employeeConfig?.compensationStartDate ?: LocalDate.now(),
            payScheduleName = employeeConfig?.payScheduleName ?: config?.config?.defaultPayScheduleName ?: "Monthly",
        )
    }
}