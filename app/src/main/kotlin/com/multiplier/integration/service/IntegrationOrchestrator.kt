package com.multiplier.integration.service

import com.multiplier.integration.repository.model.URIType
import org.springframework.stereotype.Service

@Service
class IntegrationOrchestrator(
    private val integrationProcessorFactory: IntegrationProcessorFactory,
) {

    fun handleDataIngestion(input: IntegrationInput) {
        val processor = integrationProcessorFactory.getProcessor(input.type)
        processor.startIntegration(input)
    }

    fun handleBulkUploadJobValidationFailure(bulkJobId: Long, uriType: URIType) {
        val processor = integrationProcessorFactory.getProcessor(uriType)
        processor.handleValidationFailure(bulkJobId)
    }

    fun handleBulkUploadJobValidationSuccess(bulkJobId: Long, uriType: URIType) {
        val processor = integrationProcessorFactory.getProcessor(uriType)
        processor.handleValidationSuccess(bulkJobId)
    }

    fun handleBulkUploadJobDataCreationSuccess(bulkJobId: Long, uriType: URIType) {
        val processor = integrationProcessorFactory.getProcessor(uriType)
        processor.handleDataCreationSuccess(bulkJobId)
    }
}