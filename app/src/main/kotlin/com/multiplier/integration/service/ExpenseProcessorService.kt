package com.multiplier.integration.service

import com.fasterxml.jackson.databind.JsonNode
import com.multiplier.contract.schema.currency.Currency
import com.multiplier.core.schema.currency.Currency.CurrencyCode
import com.multiplier.expense.schema.AmountWithCurrency
import com.multiplier.expense.schema.BulkCreateExpensesRequest
import com.multiplier.expense.schema.BulkDeleteExpensesRequest
import com.multiplier.expense.schema.CreateExpensesRequest
import com.multiplier.expense.schema.ExpenseItem
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.CurrencyServiceAdapter
import com.multiplier.integration.adapter.api.ExpenseServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.repository.EntityIntegrationRepository
import com.multiplier.integration.repository.JpaEntityIntegrationRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.repository.model.JpaEntityIntegration
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.type.EntityType
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.KnitIntegrationException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.model.ExpenseData
import com.multiplier.integration.sync.model.ExpenseDetails
import com.multiplier.integration.sync.model.ExpenseStatus
import com.multiplier.integration.utils.convertToLocalDateTimeViaInstant
import jakarta.transaction.Transactional
import mu.KotlinLogging
import org.springframework.stereotype.Service
import java.time.format.DateTimeFormatter
import java.util.concurrent.ConcurrentHashMap

@Service
class ExpenseProcessorService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val expenseServiceAdapter: ExpenseServiceAdapter,
    private val knitAdapter: KnitAdapter,
    private val jpaEntityIntegrationRepository: JpaEntityIntegrationRepository,
    private val entityIntegrationRepository: EntityIntegrationRepository,
    private val reportIdsByExternalExpenseIds: ConcurrentHashMap<String, Pair<String?, String?>>
    = ConcurrentHashMap<String, Pair<String?, String?>>(),
    private val currencyServiceAdapter: CurrencyServiceAdapter,
) {
    private val log = KotlinLogging.logger {}
    private val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")

    @Synchronized
    fun generateExpenseRequestFromExpenseEvents(events: List<JpaReceivedEvent>, eventType: EventType): CreateExpensesRequestWithReportId {
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        // Initialize variables to store the first event's integrationId and reportId for comparison
        var initialIntegrationId: String? = null
        var initialReportId: String? = null
        var contractId: Long? = null
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        var allApproved = true

        events.forEachIndexed { index, event ->
            log.info("Event processing with eventId: ${event.eventId}")
            val receivedEvent = DataMapper.objectMapper.readTree(event.data)
            val eventDataJsonNode: JsonNode = receivedEvent.get("eventData")
            val eventDataJsonString: String = DataMapper.objectMapper.writeValueAsString(eventDataJsonNode)
            val expenseData = DataMapper.objectMapper.readValue(eventDataJsonString, ExpenseData::class.java)
            val expenseDetails = expenseData.details!!;
            val integrationId = event.integrationId!!
            val reportId = expenseDetails.reportId!!
            val expenseStatus = expenseDetails.status

            if (expenseStatus == ExpenseStatus.REIMBURSED || expenseStatus == ExpenseStatus.ARCHIVED) {
                log.info("Ignoring event with eventId=${event.eventId} as the expense status is ${expenseStatus}.")
                throw IntegrationIllegalStateException("Ignoring event with eventId=${event.eventId} as the expense status is ${expenseStatus}")
            }

            if (expenseStatus != ExpenseStatus.APPROVED) {
                allApproved = false
            }

            expenseDetails.receipts = expenseData.receipts;
            // For the first event, set the initialIntegrationId and initialReportId
            if (index == 0) {
                initialIntegrationId = integrationId
                initialReportId = reportId
            }
            // For subsequent events, check if they match the first event's integrationId and reportId
            if (integrationId != initialIntegrationId || reportId != initialReportId) {
                throw IntegrationIllegalStateException("All events must have the same integrationId and reportId")
            }

            /* mapping */
            contractId = handleExpenseEvent(event, expenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, eventType)
            log.info("Event processed successfully with eventId: ${event.eventId}")
        }

        val firstDetail = expenseDetailsList.first()

        val externalId = firstDetail.reportId
        if (externalId != null) {
            val reportIdAndIntegrationId = Pair(initialIntegrationId, initialReportId)
            reportIdsByExternalExpenseIds[externalId] = reportIdAndIntegrationId
        }

        // Assuming expenseDetailsList is already populated with ExpenseDetails objects
        val createRequestItemList = expenseDetailsList.map { expenseDetails ->
            val expenseItemBuilder = ExpenseItem.newBuilder()
                .setTitle(expenseDetails.merchant)
                .setCategory(mapCategory(expenseDetails.category))
                .setMerchant(expenseDetails.merchant)
                .setDate(convertToLocalDateTimeViaInstant(expenseDetails.expenseDate!!).toString())
                .setDescription(expenseDetails.description ?: "")
                .setAmount(expenseDetails.amount!!)
                .setExternalId(expenseDetails.expenseID)
                .addAllReceiptUrls(expenseDetails.receipts?.mapNotNull { it.link } ?: emptyList())
            mapExpenseIdToExpenseAmount[expenseDetails.expenseID]?.let {
                expenseItemBuilder.setAmount(it)
                expenseItemBuilder.setAmountInForeignCurrency(
                    AmountWithCurrency.newBuilder()
                        .setAmount(expenseDetails.amount!!)
                        .setCurrency(expenseDetails.currency)
                )
            }
            expenseItemBuilder.build()
        }.toMutableList()

        val createExpensesRequest = CreateExpensesRequest.newBuilder()
            .addAllItems(createRequestItemList)
            .setContractId(contractId!!)
            .setExternalId(initialReportId)
            .setTitle(firstDetail.reportName)
            .build()

        log.info("Expense request generated successfully for reportId: $initialReportId")
        return Triple(initialReportId, createExpensesRequest, allApproved)
    }

    fun handleExpenseEvent(event: JpaReceivedEvent, expenseData: ExpenseData, expenseDetailsList: MutableList<ExpenseDetails>, mapExpenseIdToExpenseAmount: MutableMap<String?, Double?>, eventType: EventType): Long {
        val expenseDetails = expenseData.details!!;
        val creatorEmail = expenseDetails.creatorEmail!!
        // Check if expense is existed in cache
//        if (eventType == EventType.RECORD_NEW) {
//            val existedExpense = jpaEntityIntegrationRepository.findByExternalId(expenseDetails.reportId!!).firstOrNull()
//            if (existedExpense != null) {
//                throw IntegrationInternalServerException("Cannot create expense ${expenseDetails.reportId} which already existed")
//            }
//        }
        expenseDetails.receipts = expenseData.receipts;
        var contract = contractServiceAdapter.findContractByWorkEmail(creatorEmail)

        if (contract == null || contract.id <= 0) {
            log.info("Cannot find contract for externalExpenseId=${expenseDetails.expenseID} with email ${expenseDetails.creatorEmail}")

            val member = memberServiceAdapter.findMemberByEmailAddress(expenseDetails.creatorEmail!!)
            if (member == null || member.id <= 0) {
                // Member not found, raise a request or throw an exception
                log.warn("Member not found for email ${expenseDetails.creatorEmail}")
                throw EntityNotFoundException("Member not found for email ${expenseDetails.creatorEmail}")
            }
            // Member found, attempt to find contract by member ID
            contract = contractServiceAdapter.findContractByMemberId(member.id)
            if (contract == null || contract.id <= 0) {
                log.warn("Contract not found for member ID ${member.id}")
                throw EntityNotFoundException("Contract not found for member ID ${member.id}")
            }
        }

        if (contract.status.name != "ACTIVE") {
            throw IntegrationIllegalStateException("Ignoring event with eventId=${event.eventId} as the contract status is ${contract.status}")
        }

        if (expenseData.details.status != ExpenseStatus.APPROVED && eventType == EventType.RECORD_NEW) {
            log.info("Ignoring event with eventId=${event.eventId} as the expense status is ${expenseData.details?.status}.")
            throw IntegrationIllegalStateException("Ignoring event with eventId=${event.eventId} as the expense status is ${expenseData.details?.status}")
        }
        if (!validateExpenseCurrency(expenseDetails.currency!!, contract.currency)) {
            log.info("Expense currency ${expenseDetails.currency} does not match with contractId=${contract.id} currency ${contract.currency}. Need to convert currency with amount ${expenseDetails.amount}")
            val sourceCurrency = CurrencyCode.valueOf(expenseDetails.currency)
            val targetCurrency = CurrencyCode.valueOf(contract.currency.name)
            val convertedExpenseAmount = currencyServiceAdapter.getCurrencyExchangeAmount(
                sourceCurrency,
                targetCurrency,
                expenseDetails.amount!!
            )
            mapExpenseIdToExpenseAmount[expenseDetails.expenseID] = convertedExpenseAmount
        }
        expenseDetailsList.add(expenseDetails)
        return contract.id
    }

    private fun mapCategory(expensifyCategory: String?): String {
        if (expensifyCategory == null || expensifyCategory.trim().isEmpty()) {
            return "OTHER";
        }
        return when (expensifyCategory) {
            "Equipment", "Home Office", "Office Supplies" -> "OFFICE_EXPENSES"
            "Meals and Entertainment" -> "MEALS_AND_ENTERTAINMENT"
            "Advertising", "Professional Services" -> "PROFESSIONAL_SERVICES"
            "Utilities", "Rent" -> "RENT_UTILITIES_PHONE"
            "Car", "Travel" -> "TRAVEL_EXPENSES"
            else -> "OTHER"
        }
    }

    private fun validateExpenseCurrency(expenseCurrency: String, contractCurrency: Currency.CurrencyCode): Boolean {
        return expenseCurrency == contractCurrency.name
    }

    fun processBulkCreateExpenseRequest(createExpensesRequestList: MutableList<CreateExpensesRequestWithReportId>, eventType: EventType) {
        if (!createExpensesRequestList.isNullOrEmpty()) {
            val bulkCreateExpenseRequest = BulkCreateExpensesRequest.newBuilder()
            createExpensesRequestList.map { bulkCreateExpenseRequest.addItems(it.second) }

            val response = if (eventType == EventType.RECORD_UPDATE) {
                expenseServiceAdapter.bulkUpsertExpensesNonTransactional(bulkCreateExpenseRequest.build())
            } else {
                expenseServiceAdapter.bulkCreateExpensesNonTransactional(bulkCreateExpenseRequest.build())
            }
            if (response.expenseIdsCount > 0) {
                for ((index, expenseId) in response.expenseIdsList.withIndex()) {
                    val externalId = createExpensesRequestList[index].first!!
                    val matchingIntegrationAndReportData = reportIdsByExternalExpenseIds[externalId]

                    var integrationId: String? = null

                    if (matchingIntegrationAndReportData != null) {
                        integrationId = matchingIntegrationAndReportData.first
                    }

                    val existingEntity = if (eventType == EventType.RECORD_UPDATE && externalId != null) {
                        jpaEntityIntegrationRepository.findByExternalId(externalId).firstOrNull()
                    } else null

                    if (existingEntity == null) {
                        // Create a new entity only if no existing entity with the same reportId or eventType is not RECORD_UPDATE
                        val newEntity = JpaEntityIntegration(
                            entityType = EntityType.EXPENSE.name,
                            internalId = expenseId,
                            externalId = externalId,
                            contractId = createExpensesRequestList[index].second.contractId,
                            integrationId = integrationId,
                            paid = false,
                            processed = false
                        )
                        jpaEntityIntegrationRepository.save(newEntity)
                    } else {
                        log.info("An existing entity with reportId $externalId already exists, skipping creation for externalId $externalId")
                    }
                }
            }

            log.info { response }
        }
    }

    // Deprecate this once scheduler for delete is cleaned up
    fun getExpenseIdFromEventData(event: JpaReceivedEvent): Long? {
        log.info("Processing event with eventId=${event.eventId} and integrationId=${event.integrationId}")
        val receivedEvent = DataMapper.objectMapper.readTree(event.data)
        val eventDataJsonNode: JsonNode = receivedEvent["eventData"]
        val expenseId = eventDataJsonNode["expenseId"]?.asText()

        if (expenseId == null) {
            log.info("No expenseId found in eventData for eventId=${event.eventId}")
            return -1;
        }

        val jpaEntityIntegration = jpaEntityIntegrationRepository.findByExternalId(expenseId).firstOrNull()
        return if (jpaEntityIntegration == null) {
            log.info("Cannot find expense with externalExpenseId=$expenseId")
            null;
        } else {
            jpaEntityIntegration.internalId
        }
    }

    fun processBulkRevokeExpenseRequest(request: BulkDeleteExpensesRequest) {
        val response = expenseServiceAdapter.bulkRevokeExpensesNonTransactional(request)
        log.info { response }
    }

    @Transactional
    fun processPaidExpenseById(expenseId: Long) {
        log.info("Finding entity integration for expenseId $expenseId")
        val entityIntegration =
            entityIntegrationRepository.findFirstByEntityTypeAndInternalId(
                entityType = EntityType.EXPENSE.name,
                internalId = expenseId
            )
        if (entityIntegration == null) {
            log.info("Not found expense with internal ID $expenseId")
            return
        }
        if (entityIntegration.paid) {
            log.info("This expense has already been processed $expenseId")
            return
        }
        val integrationId = entityIntegration.integrationId
            ?: throw EntityNotFoundException("Not found integration id with expense id $expenseId")
        val externalId = entityIntegration.externalId

        entityIntegration.paid = true
        entityIntegrationRepository.save(entityIntegration)

        val unpaidExpenses = entityIntegrationRepository.findByExternalIdAndPaid(externalId, paid = false)
        if (unpaidExpenses.isNullOrEmpty()) {
            if (!knitAdapter.updateExpenseReport(integrationId, externalId)) {
                log.warn("Error calling to Knit to mark report as reimbursed with reportId $externalId")
                throw KnitIntegrationException("Fail update expense report to Knit")
            }
            entityIntegrationRepository.updateProcessedStatusByExternalId(externalId)
        }
    }
}