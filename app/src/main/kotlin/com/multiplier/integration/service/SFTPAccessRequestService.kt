package com.multiplier.integration.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.integration.repository.SFTPAccessRequestRepository
import com.multiplier.integration.repository.model.BulkUploadModule
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.types.SftpAccessRequest
import com.multiplier.integration.types.SftpAccessRequestInput
import mu.KotlinLogging
import org.springframework.stereotype.Service
import com.multiplier.integration.types.BulkUploadModule as GraphBulkUploadModule
import com.multiplier.integration.types.SftpAccessRequestStatus as GraphSftpAccessRequestStatus

@Service
class SFTPAccessRequestService(
    private val sftpAccessRequestRepository: SFTPAccessRequestRepository,
    private val notificationsService: NotificationsService
) {
    private val log = KotlinLogging.logger {}

    fun createSftpAccessRequest(input: SftpAccessRequestInput): SftpAccessRequest {
        log.info { "Creating SFTP access request for input=$input" }

        // Check if a request already exists for this combination
        throwIfExists(input.companyId, input.entityId, input.bulkModule)

        // Create a new request
        val jpaSftpAccessRequest = JpaSFTPAccessRequest(
            companyId = input.companyId,
            entityId = input.entityId,
            bulkModule = BulkUploadModule.valueOf(input.bulkModule.name),
            status = SftpAccessRequestStatus.PENDING,
            mainSFTPDirectory = null
        )

        val savedRequest = sftpAccessRequestRepository.save(jpaSftpAccessRequest)
        log.info { "Created SFTP access request with ID ${savedRequest.id}" }

        // Send email notification to operations team
        notificationsService.sendSFTPAccessRequestNotificationToOps(savedRequest)

        return savedRequest.toSftpAccessRequest()
    }

    /**
     * Checks if an SFTP access request already exists for the given combination of company, entity, and module.
     * Throws an exception if a request already exists.
     *
     * @param companyId The company ID
     * @param entityId The entity ID
     * @param bulkModule The bulk module
     * @throws com.multiplier.common.exception.BusinessException if a request already exists
     */
    private fun throwIfExists(companyId: Long, entityId: Long, bulkModule: GraphBulkUploadModule) {
        val existingRequest = sftpAccessRequestRepository.findByCompanyIdAndEntityIdAndBulkModule(
            companyId = companyId,
            entityId = entityId,
            bulkModule = BulkUploadModule.valueOf(bulkModule.name)
        )

        if (existingRequest != null) {
            throw CustomerErrorCode.BAD_REQUEST.toBusinessException(
                message = "SFTP access request already exists for this company, entity, and module combination"
            )
        }
    }

    private fun JpaSFTPAccessRequest.toSftpAccessRequest(): SftpAccessRequest {
        return SftpAccessRequest.newBuilder()
            .id(this.id!!)
            .companyId(this.companyId)
            .entityId(this.entityId)
            .bulkModule(GraphBulkUploadModule.valueOf(this.bulkModule.name))
            .status(GraphSftpAccessRequestStatus.valueOf(this.status.name))
            .mainSftpDirectory(this.mainSFTPDirectory)
            .build()
    }
}
