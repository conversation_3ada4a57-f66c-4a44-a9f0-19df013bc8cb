package com.multiplier.contract.offboarding.email

import com.multiplier.integration.types.NotificationType
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class MemberEmails {

    @Value("\${platform.userservice.system.notification.email}")
    private lateinit var systemNotificationEmail: String

    fun getEmailToUpdateBasicDetail(
        memberEmail: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.UpdateBasicDetailsOnMultiplierEmailToMember.toString()
        builder.subject = "[Action Required] Update your details on Multiplier."
        builder.from = systemNotificationEmail
        builder.to = memberEmail

        return builder
    }

    fun getEmailToUpdateBankDetail(
        memberEmail: String
    ): EmailNotificationBody.Builder {
        val builder = EmailNotificationBody.newBuilder()
        builder.templateType = NotificationType.UpdateBankDetailsOnMultiplierEmailToMember.toString()
        builder.subject = "[Action Required] Update your bank details on Multiplier."
        builder.from = systemNotificationEmail
        builder.to = memberEmail

        return builder
    }

}
