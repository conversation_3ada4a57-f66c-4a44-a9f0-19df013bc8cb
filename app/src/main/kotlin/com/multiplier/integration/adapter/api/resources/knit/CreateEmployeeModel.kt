package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable

@Serializable
data class CreateEmployeeRequest(
    val employment: Employment? = null,
    val workAddress: WorkAddress? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val workEmail: String? = null,
    var personalEmails: List<String>? = null,
    var metadata: Map<String, String>? = null,
    var employeeNumber: String? = null,
    var startDate: String? = null,
    var birthDate: String? = null,
    val maritalStatus: String? = null,
    var gender: String? = null,
    var employmentType: String? = null,
    val department: String? = null,
    val permanentAddress: Address? = null,
    val presentAddress: Address? = null,
    val companyId: String? = null,
    var location: String? = null,
    val jobTitle: String? = null,
    var employmentHistoryStatus: String? = null,
    var hireDate: String? = null,
    var businessUnit: String? = null,
)

@Serializable
data class CreateKekaEmployeeRequest(
    val employment: Employment? = null,
    val workAddress: WorkAddress? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val workEmail: String? = null,
    var personalEmails: List<String>? = null,
    var metadata: KekaRequestMetaData? = null,
)

@Serializable
data class KekaRequestMetaData(
    val gender: Int? = null,
    val dateOfBirth: String? = null,
    val dateJoined: String? = null,
    val department: String? = null,
    val location: String? = null,
    val jobTitle: String? = null,
    val employeeNumber: String? = null,
    val businessUnit: String? = null,

)

@Serializable
data class Employment(
    val positionId: String? = null,
    val designation: String? = null,
    val workShiftId: String? = null
)

@Serializable
data class WorkAddress(
    val id: String? = null,
    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val city: String? = null,
    val state: String? = null,
    val country: String? = null,
    val zipCode: String? = null
)

@Serializable
data class CreateEmployeeResponse(
    val success: Boolean? = null,
    val data: EmployeeData? = null,
    val errors: List<String>? = null,
    val responseCode: Int? = null
)

@Serializable
data class EmployeeData(
    val employeeId: String? = null
)
