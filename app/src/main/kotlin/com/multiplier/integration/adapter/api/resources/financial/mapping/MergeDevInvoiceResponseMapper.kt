package com.multiplier.integration.adapter.api.resources.financial.mapping

import com.merge.api.resources.accounting.types.*
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionCommonFields
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionInvoice
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionLineItem
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.Optional

fun InvoiceResponse.toDomain() =
    AccountingTransactionInvoice(
        id = this.model.id.orElse(null),
        createdAt = this.model.createdAt.toLocalDateOrNull(),
        modifiedAt = this.model.modifiedAt.toLocalDateOrNull(),
        number = this.model.number.orElse(null),
        totalAmount = this.model.totalAmount.orElse(null),
        issueDate = this.model.issueDate.toLocalDateOrNull(),
        dueDate = this.model.dueDate.toLocalDateOrNull(),
        paidOnDate = this.model.paidOnDate.toLocalDateOrNull(),
        memo = this.model.memo.orElse(""),
        totalDiscount = this.model.totalDiscount.orElse(null),
        subTotal = this.model.subTotal.orElse(null),
        status = this.model.status.orElse(InvoiceStatus.of(InvoiceStatusEnum.SUBMITTED)).toString(),
        totalTaxAmount = this.model.totalTaxAmount.orElse(null),
        balance = this.model.balance.orElse(null),
        remoteUpdatedAt = this.model.remoteUpdatedAt.toLocalDateOrNull(),
        accountingPeriod =
            this.model.accountingPeriod
                .orElse(null)
                ?.toString(),
        contact =
            this.model.contact
                .orElse(InvoiceContact.of(""))
                .toString(),
        commonFields =
            AccountingTransactionCommonFields(
                remoteId = this.model.remoteId.orElse(null),
                exchangeRate = this.model.exchangeRate.orElse(null),
                currency =
                    this.model.currency
                        .orElse(null)
                        ?.toString(),
                company =
                    this.model.company
                        .orElse(null)
                        ?.toString(),
                trackingCategories = this.model.trackingCategories.toTrackingCategoriesList(),
            ),
        lineItems = this.model.lineItems.toLineItemsList(),
    )

fun Optional<OffsetDateTime>.toLocalDateOrNull(): LocalDate? = this.orElse(null)?.toLocalDate()

fun Optional<MutableList<Optional<InvoiceTrackingCategoriesItem>>>.toTrackingCategoriesList(): List<String> =
    this.orElse(mutableListOf()).mapNotNull { it.orElse(null)?.toString() }

fun Optional<List<InvoiceLineItem>>.toLineItemsList(): List<AccountingTransactionLineItem> = this.orElse(emptyList()).map { it.toDomain() }

fun InvoiceLineItem.toDomain() =
    AccountingTransactionLineItem(
        commonFields =
            AccountingTransactionCommonFields(
                remoteId = this.remoteId.orElse(null),
                exchangeRate = this.exchangeRate.orElse(null),
                currency = this.currency.orElse(null)?.toString(),
                company = this.company.orElse(null)?.toString(),
                trackingCategories = this.trackingCategories.toInvoiceLineItemTrackingCategoriesList(),
            ),
        id = this.id.orElse(null),
        quantity = this.quantity.orElse(null),
        unitPrice = this.unitPrice.orElse(null),
        description = this.description.orElse(""),
        totalAmount = this.totalAmount.orElse(null),
        taxRate = this.taxRate.orElse(null),
        item = this.item.orElse(null)?.toString(),
        account = this.account.orElse(null)?.toString(),
    )

fun Optional<MutableList<Optional<InvoiceLineItemTrackingCategoriesItem>>>.toInvoiceLineItemTrackingCategoriesList(): List<String> =
    this.orElse(mutableListOf()).mapNotNull { it.orElse(null)?.toString() }
