package com.multiplier.integration.adapter.api.resources.trinet

import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import kotlinx.serialization.Serializable

@Serializable
data class TerminateEmployeeRequest(
    val terminationDate: String? = null,
)

data class TerminateEmployeeResponse(
    val success: <PERSON><PERSON><PERSON>,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)