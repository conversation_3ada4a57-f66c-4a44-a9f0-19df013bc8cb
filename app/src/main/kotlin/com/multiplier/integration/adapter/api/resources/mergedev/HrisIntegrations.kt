package com.multiplier.integration.adapter.api.resources.mergedev

import com.fasterxml.jackson.annotation.JsonProperty
import io.ktor.resources.*
import kotlinx.serialization.Serializable

@Serializable
@Resource("api/hris/v1")
class HrisIntegrations() {

    @Serializable
    @Resource("link-token")
    class CreateLinkToken(val parent: HrisIntegrations = HrisIntegrations()) {
        data class Request(
            @JsonProperty("end_user_origin_id") val companyId: String,
            @JsonProperty("end_user_organization_name") val displayName: String,
            @JsonProperty("end_user_email_address") val companyEmail: String,
            @JsonProperty("categories") val categories: Array<String>,
            @JsonProperty("integration") val integration: String,
        )

        data class Response(
            @JsonProperty("link_token") val linkToken: String,
        )
    }

    @Serializable
    @Resource("account-token/{publicToken}")
    class AccountToken(val parent: HrisIntegrations = HrisIntegrations(), val publicToken: String) {

        data class Response(
            @JsonProperty("account_token") val accountToken: String,
        )
    }

    @Serializable
    @Resource("delete-account")
    class DeleteAccount(val parent: HrisIntegrations = HrisIntegrations())
}
