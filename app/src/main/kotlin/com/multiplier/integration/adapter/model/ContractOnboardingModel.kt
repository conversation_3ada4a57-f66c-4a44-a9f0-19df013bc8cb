package com.multiplier.integration.adapter.model

import com.multiplier.grpc.common.contract.v2.Contract.ContractType
import com.multiplier.grpc.common.country.v2.Country.CountryCode
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData

data class BulkContractOnboardingRequest(
    val companyId: Long,
    val entityId: Long,
    val context: OnboardingType,
    val countryCode: CountryCode,
    val contractType: ContractType,
    val data: GroupedEmployeeData,
)

enum class OnboardingType {
    GLOBAL_PAYROLL,
    HRIS_PROFILE_DATA
}