package com.multiplier.integration.adapter.api

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.contract.onboarding.schema.BulkOnboardInput
import com.multiplier.contract.onboarding.schema.BulkOnboardOption
import com.multiplier.contract.onboarding.schema.BulkOnboardRequest
import com.multiplier.contract.onboarding.schema.ContractOnboardingServiceGrpc.ContractOnboardingServiceBlockingStub
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import com.multiplier.integration.service.fieldmappings.CompensationSchemaMapper
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface ContractOnboardingServiceAdapter {

    fun bulkOnboarding(request: BulkContractOnboardingRequest): List<Long>

    fun validateBulkOnboarding(request: BulkContractOnboardingRequest): List<String>

    fun getBulkOnboardDataSpecs(request: BulkContractOnboardingRequest): List<BulkOnboardDataSpec>

}

@Service
class ContractOnboardingServiceClient : ContractOnboardingServiceAdapter {

    @GrpcClient("contract-onboarding-service")
    private lateinit var contractOnboardingStub: ContractOnboardingServiceBlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun bulkOnboarding(request: BulkContractOnboardingRequest): List<Long> {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC bulkOnboarding for companyId: ${request.companyId}")
            val onboardingRequest = createBulkOnboardRequest(request)
            val response = contractOnboardingStub.bulkOnboard(onboardingRequest)
            response.resultsList.map { it.contractId }
        }
    }

    override fun validateBulkOnboarding(
        request: BulkContractOnboardingRequest
    ): List<String> {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC validateBulkOnboarding for companyId: ${request.companyId}")
            val onboardingRequest = createBulkOnboardRequest(request)
            val response = contractOnboardingStub.validateBulkOnboarding(onboardingRequest)
            response.validationResultsList.filter { !it.success }.map { it.errorsList.joinToString(",") }
        }
    }

    override fun getBulkOnboardDataSpecs(request: BulkContractOnboardingRequest): List<BulkOnboardDataSpec> {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC getBulkOnboardDataSpecs for companyId: ${request.companyId}")
            val option = BulkOnboardOption.newBuilder()
                .setCompanyId(request.companyId)
                .setContractType(request.contractType)
                .setContext(request.context.name)
                .setEntityId(request.entityId)
                .setCountryCode(request.countryCode)
                .build()
            val response = contractOnboardingStub.getBulkOnboardDataSpecs(option)
            CompensationSchemaMapper.mapToBulkOnboardDataSpec(response.specsList)
        }
    }

    private fun createBulkOnboardRequest(request: BulkContractOnboardingRequest): BulkOnboardRequest {
        val option = BulkOnboardOption.newBuilder()
            .setCompanyId(request.companyId)
            .setContractType(request.contractType)
            .setContext(request.context.name)
            .setEntityId(request.entityId)
            .setCountryCode(request.countryCode)
            .build()

        val inputs = request.data.groups.mapIndexed { index, data ->
            val builder = BulkOnboardInput.newBuilder()
                .setRequestId(index)
                .putAllProperties(data)
            data["group"]?.let { builder.setGroup(it) }
            builder.build()
        }

        log.info("Created bulk onboarding request containing {} employment data items and {} compensation components.", request.data.employeeData.size, request.data.compensationData.size)

        val onboardingRequest = BulkOnboardRequest.newBuilder()
            .setOption(option)
            .addAllInputs(inputs)
            .build()
        return onboardingRequest
    }
}