package com.multiplier.integration.adapter.api

import com.multiplier.integration.service.exception.DownstreamServiceUtils
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.GetMemberByEmailAddressRequest
import com.multiplier.member.schema.GetMemberWithSubfieldsRequest
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.MemberIdRequest
import com.multiplier.member.schema.MemberServiceGrpc
import com.multiplier.member.schema.UpdateMemberFieldsRequest
import com.multiplier.member.schema.UpsertBankDetailsRequest
import com.multiplier.member.schema.UpsertBankDetailsResponse
import com.multiplier.member.schema.UpsertMemberAddressDetailRequest
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface MemberServiceAdapter {
    fun findMemberByMemberId(memberId: Long): Member

    fun findMemberByEmailAddress(emailAddress: String): Member

    fun getMember(memberId: Long): Member

    fun updateMemberFields(request: UpdateMemberFieldsRequest): Member

    fun upsertBankDetails(request: UpsertBankDetailsRequest): UpsertBankDetailsResponse

    fun upsertMemberAddressDetails(request: UpsertMemberAddressDetailRequest): Member
}

@Service
class DefaultMemberServiceClient : MemberServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var memberStub: MemberServiceGrpc.MemberServiceBlockingStub

    @GrpcClient("member-service")
    private lateinit var bulkService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun findMemberByMemberId(memberId: Long): Member {
        return DownstreamServiceUtils.execute {
            val req = GetMemberWithSubfieldsRequest.newBuilder()
                .setMemberId(memberId)
                .addAllSubFields(listOf("addresses", "bankAccounts"))
                .build()
            memberStub.getMemberWithSubfields(req)
        }

    }

    override fun findMemberByEmailAddress(emailAddress: String): Member {
        return DownstreamServiceUtils.execute {
            val req = GetMemberByEmailAddressRequest.newBuilder()
                .setEmailAddress(emailAddress)
                .build()
            memberStub.getMemberByEmailAddress(req)
        }
    }

    override fun getMember(memberId: Long): Member {
        return DownstreamServiceUtils.execute {
            val request = MemberIdRequest.newBuilder().setMemberId(memberId).build()
            memberStub.getMember(request)
        }
    }

    override fun updateMemberFields(request: UpdateMemberFieldsRequest): Member {
        return DownstreamServiceUtils.execute {
            memberStub.updateMemberFields(request)
        }
    }

    override fun upsertMemberAddressDetails(request: UpsertMemberAddressDetailRequest): Member {
        return DownstreamServiceUtils.execute {
            memberStub.upsertMemberAddressDetail(request)
        }
    }

    override fun upsertBankDetails(request: UpsertBankDetailsRequest): UpsertBankDetailsResponse {
        return DownstreamServiceUtils.execute {
            bulkService.upsertBankDetails(request)
        }
    }
}
