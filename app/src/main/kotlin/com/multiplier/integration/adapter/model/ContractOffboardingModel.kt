package com.multiplier.integration.adapter.model

import com.multiplier.integration.types.ContractOffboardingStatus
import com.multiplier.integration.types.ContractOffboardingType
import com.multiplier.integration.types.SeverancePayOption
import java.time.LocalDate
import java.time.LocalDateTime

data class ContractOffBoardingRequest(
    val contractId: Long,
    val lastWorkingDay: String,
    val terminationReason: String,
)

data class RescheduleContractOffBoardingRequest(
    val contractOffboardingId: Long,
    val lastWorkingDay: String,
)

data class ContractOffBoarding(
    val id: Long,
    val contractId: Long,
    val approvedOn: LocalDateTime?,
    val contractOffBoardingStatus: ContractOffboardingStatus,
    val terminationReason: String,
    val contractOffBoardingNotes: String,
    val severancePayOption: SeverancePayOption,
    val contractOffBoardingType: ContractOffboardingType,
    val terminationClauses: List<String>,
    val lastWorkingDay: LocalDate
)