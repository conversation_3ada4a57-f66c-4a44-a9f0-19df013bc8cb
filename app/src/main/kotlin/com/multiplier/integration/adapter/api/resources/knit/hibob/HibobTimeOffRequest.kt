package com.multiplier.integration.adapter.api.resources.knit.hibob

import kotlinx.serialization.Serializable

@Serializable
data class HibobTimeOffRequest(
    val requestRangeType: String? = null,
    val policyType: String? = null,
    val startDate: String? = null,
    val startDatePortion: String? = null,
    val endDate: String? = null,
    val endDatePortion: String? = null,
)

@Serializable
data class HibobPassthroughResponse(
    val data: Boolean? = null,
    val succeeded: Boolean? = null,
    val message: String? = null,
    val errors: String? = null,
)