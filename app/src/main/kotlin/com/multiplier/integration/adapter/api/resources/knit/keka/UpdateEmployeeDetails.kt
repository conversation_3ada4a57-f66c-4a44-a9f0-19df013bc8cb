package com.multiplier.integration.adapter.api.resources.knit.keka

import kotlinx.serialization.Serializable

@Serializable
data class KekaUpdateEmployeeDetailsRequest(
    val currentAddress: KekaAddressDetails? = null,
    val displayName: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val gender: Long? = null,
    val dateOfBirth: String? = null,
    val middleName: String? = null,
    val workPhone: String? = null,
    val personalEmail: String? = null,
    val maritalStatus: Long? = null,
    val marriageDate: String? = null,
    val bloodGroup: Long? = null,
)

@Serializable
data class KekaAddressDetails(
    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val countryCode: String? = null,
    val city: String? = null,
    val state: String? = null,
    val zip: String? = null,
)

@Serializable
data class KekaResponse(
    val data: Boolean? = null,
    val succeeded: Boolean? = null,
    val message: String? = null,
    val errors: String? = null,
)