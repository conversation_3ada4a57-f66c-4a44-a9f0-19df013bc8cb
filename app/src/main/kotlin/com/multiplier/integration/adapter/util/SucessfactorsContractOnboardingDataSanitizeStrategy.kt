package com.multiplier.integration.adapter.util

import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class SuccessFactorsContractOnboardingDataSanitizeAction(
    val contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter
) : ContractOnboardingDataSanitizeAction {

    private val log = KotlinLogging.logger {}

    private val frequencyToMonthlyRatio = mapOf(
        "DAILY" to (5 * 52 / 12.0),
        "ANNUALLY" to (1 / 12.0),
        "HOURLY" to (8 * 5 * 52 / 12.0),
        "WEEKLY" to (52 / 12.0),
        "MONTHLY" to 1.0,
        "BI_WEEKLY" to (52 / 12 / 2.0),
        "QUARTERLY" to (4 / 12.0),
        "SEMIMONTHLY" to 2.0,
    )

    override fun extractCompensation(request: BulkContractOnboardingRequest): Map<String, String> {
        if (request.data.all.isEmpty()) {
            return emptyMap()
        }
        var basePay = request.data.all["basePay"]
        val payrollFrequency = request.data.all["payrollFrequency"]
        var rateFrequency = request.data.all["rateFrequency"]
        val validRateFrequencies = contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(request)
            .firstOrNull { it.key == "rateFrequency" }?.valuesList?.mapNotNull { it }
        if (basePay != null && !validRateFrequencies.isNullOrEmpty() && !validRateFrequencies.contains(rateFrequency)) {
            try {
                val validFrequency = validRateFrequencies[0]
                basePay = (basePay.toDouble() *
                        (frequencyToMonthlyRatio[payrollFrequency] ?: 1.0) /
                        (frequencyToMonthlyRatio[validFrequency] ?: 1.0))
                    .toString()
                rateFrequency = validFrequency
            } catch (e: Exception) {
                log.error(e) {
                    "Error in extracting from payrollFrequency: $payrollFrequency to one of rateFrequency: ${
                        validRateFrequencies.joinToString(",")
                    }"
                }
            }
        }
        return mapOf(
            "basePay" to (basePay ?: ""),
            "rateFrequency" to (rateFrequency ?: ""),
        )
    }

}