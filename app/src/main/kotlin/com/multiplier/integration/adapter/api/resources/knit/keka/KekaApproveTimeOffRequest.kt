package com.multiplier.integration.adapter.api.resources.knit.keka

import kotlinx.serialization.Serializable

@Serializable
data class KekaTimeOffRequest(
    val employeeId: String? = null,
    val requestedBy: String? = null,
    val fromDate: String? = null,
    val toDate: String? = null,
    val fromSession: Int? = null,
    val toSession: Int? = null,
    val leaveTypeId: String? = null,
    val reason: String? = null,
    val note: String? = null
)