package com.multiplier.integration.adapter.api

import com.multiplier.bulkupload.schema.bulkuploadjob.BulkUploadJobServiceGrpc
import com.multiplier.bulkupload.schema.bulkuploadjob.CancelJobRequest
import com.multiplier.bulkupload.schema.bulkuploadjob.CommitJobRequest
import com.multiplier.bulkupload.schema.bulkuploadjob.CreateJobRequest
import com.multiplier.bulkupload.schema.bulkuploadjob.DataRow
import com.multiplier.bulkupload.schema.bulkuploadjob.GetJobRequest
import com.multiplier.bulkupload.schema.bulkuploadjob.GetJobResponse
import com.multiplier.bulkupload.schema.bulkuploadjob.GetValidationReportRequest
import com.multiplier.bulkupload.schema.bulkuploadjob.GetValidationReportResponse
import com.multiplier.bulkupload.schema.bulkuploadjob.ValidateJobRequest
import com.multiplier.transaction.grpc.grpcClient
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service
import com.multiplier.bulkupload.schema.bulkuploadjob.DataGroup as DataGroupGrpc
import com.multiplier.bulkupload.schema.bulkuploadjob.ValidationSummary as ValidationSummaryGrpc
import com.multiplier.bulkupload.schema.bulkuploadjob.ValidationSummaryChunk as ValidationSummaryChunkGrpc

interface BulkUploadServiceAdapter {
    fun getJob(jobId: Long): JobDetail
    fun createJob(input: CreateJobInput): Job
    fun validateJob(jobId: Long): Job
    fun getValidationReport(input: GetValidationReportInput): ValidationReport
    fun commitJob(jobId: Long): Job
    fun cancelJob(jobId: Long)
}

@Service
class BulkUploadServiceClient : BulkUploadServiceAdapter {

    @GrpcClient("bulk-upload-service")
    private lateinit var stub: BulkUploadJobServiceGrpc.BulkUploadJobServiceBlockingStub

    override fun getJob(jobId: Long): JobDetail {
        val request = GetJobRequest.newBuilder().setJobId(jobId).build()
        val response = grpcClient { stub.getJob(request) }
        return response.toJobDetail()
    }

    override fun createJob(input: CreateJobInput): Job {
        val request = input.toCreateJobRequest()
        val response = grpcClient { stub.createJob(request) }
        return Job(id = response.job.id)
    }

    override fun validateJob(jobId: Long): Job {
        val request = ValidateJobRequest.newBuilder().setJobId(jobId).build()
        val response = grpcClient { stub.validateJob(request) }
        return Job(id = response.job.id)
    }

    override fun getValidationReport(input: GetValidationReportInput): ValidationReport {
        val request = input.toGetValidationReportRequest()
        val response = grpcClient { stub.getValidationReport(request) }
        return response.toValidationReport()
    }

    override fun commitJob(jobId: Long): Job {
        val request = CommitJobRequest.newBuilder().setJobId(jobId).build()
        val response = grpcClient { stub.commitJob(request) }
        return Job(id = response.job.id)
    }

    override fun cancelJob(jobId: Long) {
        val request = CancelJobRequest.newBuilder().setJobId(jobId).build()
        grpcClient { stub.cancelJob(request) }
    }
}

private fun CreateJobInput.toCreateJobRequest(): CreateJobRequest {
    return CreateJobRequest.newBuilder()
        .setCompanyId(this.companyId)
        .setEntityId(this.entityId)
        .setGroupName(this.groupName)
        .addAllModuleNames(this.moduleNames)
        .addAllJobData(this.jobData.map { it.toDataGroupGrpc() })
        .build()
}

private fun DataGroup.toDataGroupGrpc(): DataGroupGrpc {
    return DataGroupGrpc.newBuilder()
        .setName(this.name)
        .setModuleName(this.moduleName)
        .addAllDataRows(this.rows.map { it.toDataRowGrpc() })
        .build()
}

private fun Map<String, String>.toDataRowGrpc(): DataRow {
    return DataRow.newBuilder()
        .putAllData(this)
        .build()
}

private fun GetValidationReportInput.toGetValidationReportRequest(): GetValidationReportRequest {
    return GetValidationReportRequest.newBuilder()
        .setJobId(this.jobId)
        .setReportExpirationInMinutes(this.expirationInMinutes)
        .build()
}

private fun GetValidationReportResponse.toValidationReport(): ValidationReport {
    return ValidationReport(uri = this.reportUrl)
}

private fun GetJobResponse.toJobDetail(): JobDetail {
    return JobDetail(
        validationSummary = this.validationSummary.toValidationSummary(),
    )
}

private fun ValidationSummaryGrpc.toValidationSummary(): ValidationSummary {
    return ValidationSummary(
        validationSummaryChunks = this.validationSummaryChunksList.map { it.toValidationSummaryChunk() },
    )
}

private fun ValidationSummaryChunkGrpc.toValidationSummaryChunk(): ValidationSummaryChunk {
    return ValidationSummaryChunk(
        name = this.name,
        identifiedRowCount = this.identifiedRowCount,
        failedRowCount = this.failedRowCount,
    )
}

// We use this const to identify the message type in the Kafka message
// And we only process the message if the message type is CUSTOMER_INTEGRATION_SERVICE
const val CUSTOMER_INTEGRATION_SERVICE_GROUP = "CUSTOMER_INTEGRATION_SERVICE"

data class CreateJobInput(
    val companyId: Long,
    val entityId: Long,
    val groupName: String = CUSTOMER_INTEGRATION_SERVICE_GROUP,
    val moduleNames: Set<String>,
    val jobData: List<DataGroup>,
)

data class DataGroup(
    val name: String = CUSTOMER_INTEGRATION_SERVICE_GROUP,
    val moduleName: String,
    val rows: List<Map<String, String>>,
)

data class Job(
    val id: Long,
)

data class GetValidationReportInput(
    val jobId: Long,
    val expirationInMinutes: Int = 60*24,
)

data class ValidationReport(
    val uri: String,
)

data class JobDetail(
    val validationSummary: ValidationSummary,
)

data class ValidationSummary(
    val validationSummaryChunks: List<ValidationSummaryChunk>
)

data class ValidationSummaryChunk(
    val name: String,
    val identifiedRowCount: Int,
    val failedRowCount: Int,
)


