package com.multiplier.integration.adapter.api

import com.google.protobuf.Timestamp
import com.multiplier.contract.offboarding.schema.ContractOffboardingServiceGrpc.ContractOffboardingServiceBlockingStub
import com.multiplier.contract.offboarding.schema.InitialiseResignationOffboardingRequest
import com.multiplier.contract.offboarding.schema.ContractOffboarding
import com.multiplier.contract.offboarding.schema.GetContractOffboardingsRequest
import com.multiplier.contract.offboarding.schema.RescheduleOffboardingRequest
import com.multiplier.contract.offboarding.schema.VerifyAndCompleteOffboardingRequest
import com.multiplier.integration.adapter.model.ContractOffBoardingRequest
import com.multiplier.integration.adapter.model.RescheduleContractOffBoardingRequest
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.ZoneOffset

private val log = KotlinLogging.logger {}

interface ContractOffBoardingServiceAdapter {
    fun initialiseResignationOffboarding(offboardingRequest: ContractOffBoardingRequest): ContractOffboarding

    fun getContractOffboardings(contractIds: List<Long>): List<ContractOffboarding>

    fun rescheduleOffboarding(rescheduleOffboardingRequest: RescheduleContractOffBoardingRequest): ContractOffboarding

    fun verifyAndCompleteOffboarding(contractOffboardingId: Long): ContractOffboarding
}

@Service
class ContractOffBoardingServiceClient : ContractOffBoardingServiceAdapter {

    @GrpcClient("contract-offboarding-service")
    private lateinit var stub: ContractOffboardingServiceBlockingStub
    override fun initialiseResignationOffboarding(offboardingRequest: ContractOffBoardingRequest): ContractOffboarding {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC initialiseOffboarding with {${offboardingRequest.contractId}}")
            val request = InitialiseResignationOffboardingRequest.newBuilder()
                .setContractId(offboardingRequest.contractId)
                .setLastWorkingDay(offboardingRequest.lastWorkingDay.toGrpcTimestamp())
                .setIsSkippedValidateLastWorkingDay(true)
                .build()
            stub.initialiseResignationOffboarding(request)
        }
    }

    override fun getContractOffboardings(contractIds: List<Long>): List<ContractOffboarding> {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC getContractOffBoardings with {$contractIds}")
            val request = GetContractOffboardingsRequest.newBuilder()
                .addAllContractIds(contractIds)
                .build()
            stub.getContractOffboardings(request).contractOffBoardingList
        }
    }

    override fun rescheduleOffboarding(rescheduleOffboardingRequest: RescheduleContractOffBoardingRequest): ContractOffboarding {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC rescheduleOffboarding with {${rescheduleOffboardingRequest.contractOffboardingId}}")
            val request = RescheduleOffboardingRequest.newBuilder()
                .setContractOffboardingId(rescheduleOffboardingRequest.contractOffboardingId)
                .setRevisedDate(rescheduleOffboardingRequest.lastWorkingDay.toGrpcTimestamp())
                .setIsSkippedValidateLastWorkingDay(true)
                .build()
            stub.rescheduleContractOffboarding(request)
        }
    }

    override fun verifyAndCompleteOffboarding(contractOffboardingId: Long): ContractOffboarding {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC verifyAndCompleteOffboarding with {${contractOffboardingId}}")
            val request = VerifyAndCompleteOffboardingRequest.newBuilder()
                .setContractOffboardingId(contractOffboardingId)
                .build()
            stub.verifyAndCompleteContractOffboarding(request)
        }
    }

    private fun String?.toGrpcTimestamp(): Timestamp {
        if (this != null) {
            val localDate = LocalDate.parse(this)
            val startOfDay = localDate.atStartOfDay(ZoneOffset.UTC)

            return Timestamp.newBuilder()
                .setSeconds(startOfDay.toEpochSecond())
                .setNanos(startOfDay.nano)
                .build()
        } else {
            return Timestamp.getDefaultInstance()
        }
    }
}
