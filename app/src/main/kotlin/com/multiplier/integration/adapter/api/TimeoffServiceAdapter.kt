package com.multiplier.integration.adapter.api

import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import com.multiplier.timeoff.schema.GrpcBulkRevokeTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffResponse
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesRequest
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesResponse
import com.multiplier.timeoff.schema.GrpcContractIds
import com.multiplier.timeoff.schema.GrpcEmpty
import com.multiplier.timeoff.schema.GrpcTimeOffs
import com.multiplier.timeoff.schema.TimeOffServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface TimeoffServiceAdapter {
    fun bulkUpsertTimeOffs(request: GrpcBulkTimeOffRequest): GrpcBulkTimeOffResponse

    fun bulkRevokeTimeoffs(request: GrpcBulkRevokeTimeOffRequest): GrpcEmpty

    fun getCompanyTimeOffTypes(companyId: Long): GrpcCompanyTimeOffTypesResponse?

    fun getTimeOffsByContractIds(request: GrpcContractIds): GrpcTimeOffs
}

@Service
class DefaultTimeoffServiceAdapter : TimeoffServiceAdapter {
    @GrpcClient("timeoff-service") private lateinit var timeoffStub: TimeOffServiceGrpc.TimeOffServiceBlockingStub
    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun bulkUpsertTimeOffs(request: GrpcBulkTimeOffRequest): GrpcBulkTimeOffResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to create bulk timeoff with request: $request")
            val result = timeoffStub.bulkUpsertTimeOffs(request)
            log.info("Successfully called bulk timeoff with result: $result")
            result
        }
    }

    override fun bulkRevokeTimeoffs(request: GrpcBulkRevokeTimeOffRequest): GrpcEmpty {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to revoke bulk timeoff with request: $request")
            val result = timeoffStub.bulkRevokeTimeOffs(request)
            log.info("Successfully called revoke bulk timeoff with result: $result")
            result
        }
    }

    override fun getCompanyTimeOffTypes(companyId: Long): GrpcCompanyTimeOffTypesResponse? {
        return DownstreamServiceUtils.execute {
            val request = GrpcCompanyTimeOffTypesRequest.newBuilder().setCompanyId(companyId).build()
            val result = timeoffStub.getCompanyTimeOffTypes(request)
            log.info("Successfully getCompanyTimeOffTypes with result: $result")
            result
        }
    }

    override fun getTimeOffsByContractIds(request: GrpcContractIds): GrpcTimeOffs {
        return DownstreamServiceUtils.execute {
            log.info("Attempting get timeoffs by contractIds with request: $request")
            val result = timeoffStub.getTimeOffsByContractIds(request)
            log.info("Successfully got timeoffs by contractIds with result: $result")
            result
        }
    }
}