package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable

@Serializable
data class CompensationPlanDetail(
    val planId: String? = null,
    val planName: String? = null,
    val description: String?= null,
    val amount: Double? = null,
    val percentage: Double? = null,
    val currency: String? = null,
    val payPeriod: String? = null,
    val frequency: String? = null,
    val type: String? = null,
    val targetShares: Double? = null
)

@Serializable
data class CompensationPlanData(
    val fixed: List<CompensationPlanDetail>?,
    val variable: List<CompensationPlanDetail>?,
    val stock: List<CompensationPlanDetail>?
)

@Serializable
data class GetCompensationPlanResponse(
    val success: Boolean,
    val data: CompensationPlanData? = null,
    val error: ApiError? = null,
    val responseCode: Int? = null
)