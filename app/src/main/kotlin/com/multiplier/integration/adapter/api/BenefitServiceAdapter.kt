package com.multiplier.integration.adapter.api;

import com.multiplier.common.exception.toSystemException
import com.multiplier.core.schema.grpc.benefit.Benefit.ContractBenefitDocumentsResponse
import com.multiplier.core.schema.grpc.benefit.Benefit.IdInput
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service;

interface BenefitServiceAdapter {
    fun getContractBenefitDocumentsByContractId(contractId: Long): ContractBenefitDocumentsResponse
}

@Service
class DefaultBenefitServiceClient : BenefitServiceAdapter {

    @GrpcClient("core-service")
    private lateinit var stub: BenefitServiceGrpc.BenefitServiceBlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getContractBenefitDocumentsByContractId(contractId: Long): ContractBenefitDocumentsResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to find benefit documents with contractId: $contractId")
            val req = IdInput.newBuilder().setId(contractId).build()
            val result = stub.getContractBenefitDocumentsByContractId(req)
            log.info("Successfully retrieved contract with ID: $contractId.")
            result
        }
    }
}

