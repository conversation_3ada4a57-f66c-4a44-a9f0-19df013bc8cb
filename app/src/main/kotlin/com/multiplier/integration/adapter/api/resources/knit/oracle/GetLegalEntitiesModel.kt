package com.multiplier.integration.adapter.api.resources.knit.oracle

import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

@Serializable
data class GetLegalEntitiesResponse(
    val success: Boolean? = null,
    val data: LegalEntityResponseWrapper? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
) {
    // Extension function to easily get parsed legal entities
    fun getLegalEntities(): List<LegalEntityItem>? {
        return data?.response?.body?.let { bodyString ->
            Json.decodeFromString<LegalEntitiesBody>(bodyString).items
        }
    }
}

// Wrapper for the nested response structure
@Serializable
data class LegalEntityResponseWrapper(
    val response: LegalEntityBodyWrapper? = null
)

// Contains the body with JSON string
@Serializable
data class LegalEntityBodyWrapper(
    val body: String? = null // Contains JSON string with items array
)

// Used to parse the body string content
@Serializable
data class LegalEntitiesBody(
    val items: List<LegalEntityItem>? = null,
    val count: Int? = null,
    val hasMore: Boolean? = null,
    val limit: Int? = null,
    val offset: Int? = null,
    val links: List<LinkItem>? = null
)

@Serializable
data class LegalEntityItem(
    val OrganizationId: Long? = null,
    val EffectiveStartDate: String? = null,
    val EffectiveEndDate: String? = null,
    val Name: String? = null,
    val LegislationCode: String? = null,
    val PayrollStatutoryUnitId: Long? = null,
    val links: List<LinkItem>? = null
)

@Serializable
data class LinkItem(
    val rel: String? = null,
    val href: String? = null,
    val name: String? = null,
    val kind: String? = null
) 