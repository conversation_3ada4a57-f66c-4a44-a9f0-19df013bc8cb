package com.multiplier.integration.adapter.api.resources.knit.bamboo

import kotlinx.serialization.Serializable

@Serializable
data class GetWorkLocationsResponse(
    val success: Boolean? = null,
    val data: WorkLocationResponse? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class WorkLocationResponse(
    val response: WorkLocationResponseBody? = null,
    val parsedData: Lists? = null  // Include parsed XML data
)

@Serializable
data class WorkLocationResponseBody(
    val body: String? = null, // Raw XML string
    val headers: Map<String, String>? = null
)

// Models representing the XML structure
@Serializable
data class Lists(
    val list: List<ListData>? = null
)

@Serializable
data class ListData(
    val fieldId: String? = null,
    val alias: String? = null,
    val name: String? = null,
    val options: Options? = null
)

@Serializable
data class Options(
    val option: List<OptionData>? = null
)

@Serializable
data class OptionData(
    val id: String? = null,
    val archived: String? = null,
    val createdDate: String? = null,
    val archivedDate: String? = null,
    val name: String? = null
)
