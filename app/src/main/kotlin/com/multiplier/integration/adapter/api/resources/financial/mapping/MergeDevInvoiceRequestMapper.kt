package com.multiplier.integration.adapter.api.resources.financial.mapping

import com.merge.api.resources.accounting.types.*
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionLineItem
import com.multiplier.integration.adapter.api.resources.financial.MergeDevInvoiceRequest
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Named
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.*
import java.util.stream.Collectors

@Mapper(componentModel = "spring", config = IgnoreUnmappedMapperConfig::class)
interface MergeDevInvoiceRequestMapper {

    @Mapping(target = "type", expression = "java(mapType())")
    @Mapping(source = "invoice.contact", target = "contact", qualifiedByName = ["mapContact"])
    @Mapping(source = "invoice.number", target = "number")
    @Mapping(source = "invoice.issueDate", target = "issueDate", qualifiedByName = ["mapDate"])
    @Mapping(source = "invoice.dueDate", target = "dueDate", qualifiedByName = ["mapDate"])
    @Mapping(source = "invoice.status", target = "status", qualifiedByName = ["mapStatus"])
    @Mapping(source = "invoice.commonFields.exchangeRate", target = "exchangeRate")
    @Mapping(source = "invoice.commonFields.currency", target = "currency", qualifiedByName = ["mapCurrency"])
    @Mapping(source = "invoice.totalAmount", target = "totalAmount")
    @Mapping(source = "invoice.memo", target = "memo")
//    @Mapping(source = "invoice.commonFields.company", target = "company", qualifiedByName = ["mapCompany"])
    @Mapping(source = "invoice.commonFields.trackingCategories", target = "trackingCategories", qualifiedByName = ["mapTrackingCategories"])
    @Mapping(source = "invoice.lineItems", target = "lineItems", qualifiedByName = ["mapInvoiceLineItems"])
    fun toInvoiceRequest(mergeDevInvoiceRequest: MergeDevInvoiceRequest): InvoiceRequest

    @Mapping(target = "type", expression = "java(mapType())")
    @Mapping(source = "invoice.contact", target = "contact", qualifiedByName = ["mapContact"])
    @Mapping(source = "invoice.number", target = "number")
    @Mapping(source = "invoice.issueDate", target = "issueDate", qualifiedByName = ["mapDate"])
    @Mapping(source = "invoice.dueDate", target = "dueDate", qualifiedByName = ["mapDate"])
    @Mapping(source = "invoice.commonFields.exchangeRate", target = "exchangeRate")
    @Mapping(source = "invoice.commonFields.currency", target = "currency", qualifiedByName = ["mapCurrency"])
    @Mapping(source = "invoice.memo", target = "memo")
    @Mapping(source = "invoice.commonFields.trackingCategories", target = "trackingCategories", qualifiedByName = ["mapTrackingCategories"])
    @Mapping(source = "invoice.lineItems", target = "lineItems", qualifiedByName = ["mapInvoiceLineItems"])
    fun toPatchInvoiceRequest(mergeDevInvoiceRequest: MergeDevInvoiceRequest): InvoiceRequest

    @Named("mapContact")
    fun mapContact(contact: String): InvoiceRequestContact {
        return InvoiceRequestContact.of(contact)
    }

    @Named("mapType")
    fun mapType(): InvoiceRequestType {
        return InvoiceRequestType.of(InvoiceTypeEnum.ACCOUNTS_PAYABLE)
    }

    @Named("mapDate")
    fun mapDate(date: LocalDate): OffsetDateTime {
        val offset: ZoneOffset = ZoneOffset.UTC
        return date.atStartOfDay().atOffset(offset)
    }

    @Named("mapStatus")
    fun mapStatus(status: String): InvoiceRequestStatus {
        if(status =="AUTHORIZED") {
            return InvoiceRequestStatus.of(InvoiceStatusEnum.SUBMITTED)
        }
        return InvoiceRequestStatus.of(InvoiceStatusEnum.PAID)
    }

    @Named("mapCompany")
    fun mapCompany(company: String): InvoiceRequestCompany? {
        company.let { return InvoiceRequestCompany.of(company) }
    }

    @Named("mapCurrency")
    fun mapCurrency(currency: String): InvoiceRequestCurrency {
        return InvoiceRequestCurrency.of(currency)
    }

    @Named("mapTrackingCategories")
    fun mapTrackingCategories(trackingCategories: List<String>): List<Optional<InvoiceRequestTrackingCategoriesItem>> {
        return trackingCategories.map { Optional.of(InvoiceRequestTrackingCategoriesItem.of(it)) }
    }

    @Named("mapInvoiceLineItems")
    fun mapInvoiceLineItems(lineItems: List<AccountingTransactionLineItem>): List<InvoiceLineItemRequest> {
        return lineItems.stream()
            .map { mapLineItem(it) }
            .collect(Collectors.toList())
    }

    fun mapLineItem(lineItem: AccountingTransactionLineItem): InvoiceLineItemRequest {
        val builder = InvoiceLineItemRequest.builder()
            .description(getInput(lineItem.description))
            .quantity(Optional.ofNullable(lineItem.quantity).orElse(0.0))
            .unitPrice(Optional.ofNullable(lineItem.unitPrice).orElse(0.0))
            .totalAmount(Optional.ofNullable(lineItem.totalAmount).orElse(0.0))
            .currency(Optional.ofNullable(lineItem.commonFields?.currency).map { InvoiceLineItemRequestCurrency.of(it) }.orElse(null))
            .exchangeRate(getInput(lineItem.commonFields?.exchangeRate))
            .account(Optional.ofNullable(lineItem.item).map { InvoiceLineItemRequestAccount.of(it) }.orElse(null))
            .trackingCategories(Optional.ofNullable(lineItem.commonFields?.trackingCategories).map { getLineItemCategories(it) }.orElse(emptyList()))

        Optional.ofNullable(lineItem.account).ifPresent { account ->
            builder.account(InvoiceLineItemRequestAccount.of(account))
        }
        return builder.build()
    }

    fun getInput(input: String?): String {
        return Optional.ofNullable(input).orElse("")
    }

    fun getLineItemCategories(trackingCategories: List<String>): List<Optional<InvoiceLineItemRequestTrackingCategoriesItem>> {
        return trackingCategories.map { Optional.of(InvoiceLineItemRequestTrackingCategoriesItem.of(it)) }
    }
}