package com.multiplier.integration.adapter.api

import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirements
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementsRequest
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementsServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface PaymentServiceAdapter {
    fun getIntegrationPaymentAccountRequirements(request: IntegrationPaymentAccountRequirementsRequest): IntegrationPaymentAccountRequirements
}

@Service
class DefaultPaymentServiceClient : PaymentServiceAdapter {

    @GrpcClient("pay-se")
    private lateinit var stub: IntegrationPaymentAccountRequirementsServiceGrpc.IntegrationPaymentAccountRequirementsServiceBlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getIntegrationPaymentAccountRequirements(request: IntegrationPaymentAccountRequirementsRequest): IntegrationPaymentAccountRequirements {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to getIntegrationPaymentAccountRequirements request: $request")
            val result = stub.getIntegrationPaymentAccountRequirements(request)
            log.info("Successfully retrieved payment account requirement, result: $result")
            result
        }
    }
}