package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable

@Serializable
data class CreateDocumentRequest(
    val employeeId: String? = null,
    val fileName: String? = null,
    val fileUrl: String? = null,
    val contentType: String? = null,
    val category: String? = null,
    val fileContent: String? = null,
    val comment: String? = null
)

@Serializable
data class CreateDocumentResponse(
    val success: Boolean,
    val data: DocumentData? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class DocumentData(
    val documentId: String? = null
)

@Serializable
data class ErrorResponse(
    val msg: String? = null
)
