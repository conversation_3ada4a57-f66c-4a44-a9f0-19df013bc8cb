package com.multiplier.integration.adapter.model.knit

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeData(
        val profile: Profile? = null,
        val contactInfo: ContactInfo? = null,
        val orgStructure: OrgStructure? = null,
        val locations: Locations? = null,
        val dependents: List<Dependent>? = null,
        val bankAccounts: List<BankAccountDetails>? = null,
        val employeeKYC: KYCDetails? = null,
        val employeeIdentificationData: List<IDData>? = null,
        val employeeProfilePicture: ProfilePicture? = null,
        val customFields: CustomFields? = null,
        val rawValues: RawValues? = null,
        val compensation: CompensationData? = null,
        val employeeDetailData: EmployeeDetailData? = null
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Profile(
        val firstName: String? = null,
        val lastName: String? = null,
        val id: String? = null,
        val workEmail: String? = null,
        val startDate: String? = null,
        val birthDate: String? = null,
        val terminationDate: String? = null,
        val employmentStatus: String? = null,
        val maritalStatus: String? = null,
        val gender: String? = null,
        val employmentType: String? = null,
        val employeeNumber: String? = null,
)

data class ContactInfo(
        val personalEmails: List<String>?,
        val phones: List<Phone>?
)

data class Phone(
        val type: String? = null,
        val number: String? = null
)

data class OrgStructure(
        val designation: String? = null,
        val department: String? = null,
        val manager: EmployeeManager? = null
)

data class EmployeeManager(
        val id: String? = null,
        val workEmail: String? = null
)

data class Locations(
        val workAddress: EmployeeLocation?,
        val permanentAddress: EmployeeLocation?,
        val presentAddress: EmployeeLocation?
)

data class EmployeeLocation(
        val addressLine1: String? = null,
        val addressLine2: String? = null,
        val city: String? = null,
        val state: String? = null,
        val country: String? = null,
        val zipCode: String? = null,
        val addressType: String? = null
)

data class Dependent(
        val id: String? = null,
        val firstName: String? = null,
        val lastName: String? = null,
        val birthDate: String? = null,
        val relation: String? = null,
        val gender: String? = null
)

data class BankAccountDetails(
        val accountNumber: String? = null,
        val accountType: String? = null,
        val bankName: String? = null,
        val branchCode: String? = null,
        val ifscCode: String? = null,
        val paymentMode: String? = null,
        val swiftCode: String? = null,
        val iban: String? = null,
        val bankIdentificationCode: String? = null,
        val routingNumber: String? = null
)

data class KYCDetails(
        val aadharNumber: String? = null,
        val aadharURL: String? = null,
        val panNumber: String? = null,
        val panURL: String? = null
)

data class IDData(
        val type: String? = null,
        val subType: String? = null,
        val identificationNumber: String? = null
)

data class ProfilePicture(
        val pictureName: String? = null,
        val pictureURL: String?
)

data class CustomFields(
        val fields: Map<String, Any>?
)

data class RawValues(
        val profile: RawProfileValues?
)

data class RawProfileValues(
        val employmentStatus: String? = null,
        val maritalStatus: String? = null,
        val gender: String? = null,
        val employmentType: String? = null
)

data class CompensationData(
        val fixed: List<CompensationItem>?,
        val variable: List<CompensationItem>?
)

data class CompensationItem(
        val type: String? = null,
        val amount: String? = null,
        val currency: String? = null,
        val payPeriod: String? = null,
        val frequency: String? = null,
        val startDate: String? = null,
        val endDate: String? = null,
        val percentage: String? = null,
        val planId: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeDetailData(
        val firstName: String? = null,
        val lastName: String? = null,
        val addressLine1: String? = null,
        val addressLine2: String? = null,
        val country: String? = null,
        val city: String? = null,
        val state: String? = null,
        val postalCode: String? = null,
        val startDate: String? = null,
        val emailAddress: String? = null,
        val workEmailAddress: String? = null,
        val contactNumber: String? = null,
        val locationId: String? = null,
        val designation: String? = null,
)
