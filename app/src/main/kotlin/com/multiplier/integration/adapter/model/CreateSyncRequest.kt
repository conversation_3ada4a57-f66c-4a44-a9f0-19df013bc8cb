package com.multiplier.integration.adapter.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.multiplier.integration.types.PlatformCategory
import kotlinx.serialization.Serializable

@Serializable
data class CreateSyncRequest(
    val dataType: String = "employee",
    val syncType: String,
    val honorScheduler: Boolean = true
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SyncStartResponse(
    val success: Boolean,
    val data: SyncDataResponse
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SyncDataResponse(
    val syncJobId: String?,
    val syncRunId: String?
)

fun createSyncRequest(isInitialSync: Boolean, category: PlatformCategory): CreateSyncRequest {
    val syncType = if (isInitialSync) "initial_sync" else "delta_sync"
    val dataType = when (category) {
        PlatformCategory.EXPENSES -> "expense"
        else -> "employee"
    }
    return CreateSyncRequest(dataType = dataType, syncType = syncType)
}