package com.multiplier.integration.adapter.api

import com.multiplier.core.common.rest.client.docgen.DocGenRestClient
import com.multiplier.core.common.rest.client.docgen.enums.TemplateType
import com.multiplier.core.common.rest.client.docgen.model.CreateDocumentRequest
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.core.common.rest.client.docgen.model.GenerateDocumentRequest
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

interface DocgenServiceAdapter {
    fun getDocument(documentId: Long?): DocumentResponse?
    fun createDocument(value: CreateDocumentRequest?): DocumentResponse
    fun replaceDocument(documentId: Long?, file: MultipartFile?): DocumentResponse?
    fun <T> generateDocument(
        documentId: Long?,
        value: GenerateDocumentRequest<T>?
    ): DocumentResponse?

    fun <T> freezeDocument(documentId: Long?, value: GenerateDocumentRequest<T>?): DocumentResponse?
    fun uploadDocument(type: TemplateType, file: MultipartFile): DocumentResponse
}

@Service
@Primary
class DefaultDocGenServiceClient(private val docGenClient: DocGenRestClient) :
    DocgenServiceAdapter {
    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getDocument(documentId: Long?): DocumentResponse? {
        return DownstreamServiceUtils.execute {
            log.info("Calling getDocument {}", documentId)
            if (documentId == null) {
                null
            } else docGenClient.getDocument(documentId)
        }
    }

    override fun createDocument(value: CreateDocumentRequest?): DocumentResponse {
        return DownstreamServiceUtils.execute {
            log.info("Calling createDocument {}", value)
            docGenClient.createDocument(value)
        }
    }

    override fun replaceDocument(documentId: Long?, file: MultipartFile?): DocumentResponse? {
        return DownstreamServiceUtils.execute {
            log.info("Calling replaceDocument {}", documentId)
            if (documentId == null) {
                null
            } else docGenClient.replaceDocument(documentId, file)
        }
    }

    override fun <T> generateDocument(
        documentId: Long?,
        value: GenerateDocumentRequest<T>?
    ): DocumentResponse? {
        return DownstreamServiceUtils.execute {
            log.info("generating document for id={}", documentId)
            if (documentId == null) {
                null
            } else docGenClient.generateDocument(documentId, value)
        }
    }

    override fun <T> freezeDocument(
        documentId: Long?,
        value: GenerateDocumentRequest<T>?
    ): DocumentResponse? {
        return DownstreamServiceUtils.execute {
            log.info("Calling freezeDocument {}", documentId)
            if (documentId == null) {
                null
            } else docGenClient.freezeDocument(documentId, value)
        }
    }

    override fun uploadDocument(type: TemplateType, file: MultipartFile): DocumentResponse {
        return DownstreamServiceUtils.execute {
            log.info("Calling uploadDocument {}", type)
            val document = docGenClient.createDocument(CreateDocumentRequest().type(type.name))
            docGenClient.replaceDocument(document.id(), file)
        }
    }
}

@Service
class MockDocgenServiceAdapter : DocgenServiceAdapter {
    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getDocument(documentId: Long?): DocumentResponse? {
        log.warn(
            "Mocking getDocument $documentId. This should never happen outside of local/test environments.")
        return DocumentResponse()
    }

    override fun createDocument(value: CreateDocumentRequest?): DocumentResponse {
        log.warn(
            "Mocking createDocument $value. This should never happen outside of local/test environments.")
        return DocumentResponse()
    }

    override fun replaceDocument(documentId: Long?, file: MultipartFile?): DocumentResponse? {
        log.warn(
            "Mocking replaceDocument $documentId. This should never happen outside of local/test environments.")
        return DocumentResponse()
    }

    override fun <T> generateDocument(
        documentId: Long?,
        value: GenerateDocumentRequest<T>?
    ): DocumentResponse? {
        log.warn(
            "Mocking generateDocument $documentId. This should never happen outside of local/test environments.")
        return DocumentResponse()
    }

    override fun <T> freezeDocument(
        documentId: Long?,
        value: GenerateDocumentRequest<T>?
    ): DocumentResponse? {
        log.warn(
            "Mocking freezeDocument $documentId. This should never happen outside of local/test environments.")
        return DocumentResponse()
    }

    override fun uploadDocument(type: TemplateType, file: MultipartFile): DocumentResponse {
        log.warn(
            "Mocking uploadDocument $type. This should never happen outside of local/test environments.")
        return DocumentResponse()
    }
}
