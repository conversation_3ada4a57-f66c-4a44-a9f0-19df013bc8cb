package com.multiplier.integration.adapter.api.resources.knit.bamboo

import kotlinx.serialization.Serializable

@Serializable
data class GetEmployeeDirectoryResponse(
    val success: <PERSON><PERSON>an,
    val employees: List<Employee>? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class Employee(
    val id: String,
    val workEmail: String? = null,
)


fun GetEmployeeDirectoryResponse.findByEmail(email: String): Employee? {
    return employees?.find { it.workEmail?.equals(email, ignoreCase = true) == true }
}
