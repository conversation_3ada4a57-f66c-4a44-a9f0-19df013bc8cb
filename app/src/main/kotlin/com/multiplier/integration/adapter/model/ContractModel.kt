package com.multiplier.integration.adapter.model

import com.multiplier.integration.adapter.model.knit.Frequency
import java.time.LocalDate

data class PerformanceReviewRequest(
    val contractId: Long,
    val effectiveDate: LocalDate?,
    val amount: Double?,
    val additionalPays: List<AdditionalPay>?
)

data class AdditionalPay(
    val type: String,
    val amount: Double?,
    val frequency: Frequency?
)