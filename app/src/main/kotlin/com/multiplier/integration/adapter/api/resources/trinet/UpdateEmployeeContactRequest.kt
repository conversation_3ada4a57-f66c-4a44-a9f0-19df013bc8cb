package com.multiplier.integration.adapter.api.resources.trinet

import kotlinx.serialization.Serializable


@Serializable
data class UpdateEmployeeContactRequest(
    val contactList: List<UpdateSingleContactRequest>,
)

@Serializable
data class UpdateSingleContactRequest(
    val effectiveDate: String? = null,
    val accessType: String? = null,
    val media: String? = null,
    val telephoneNumber: String? = null,
    val url: String? = null,
)
