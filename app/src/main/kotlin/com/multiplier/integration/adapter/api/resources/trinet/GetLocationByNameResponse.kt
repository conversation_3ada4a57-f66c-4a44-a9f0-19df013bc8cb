package com.multiplier.integration.adapter.api.resources.trinet

import kotlinx.serialization.Serializable

@Serializable
data class GetLocationByNameResponse(
    val data: List<Location>? = null
)

@Serializable
data class Location(
    val locationDeptCount: Int? = null,
    val locationEmployeeCount: Int? = null,
    val locationId: String? = null,
    val locationName: String? = null,
    val shortDesc: String? = null,
    val state: String? = null,
    val effectiveDate: String? = null,
    val endDate: String? = null,
    val headquarter: String? = null,
    val phone: String? = null,
    val officeHours: String? = null,
    val country: String? = null,
)