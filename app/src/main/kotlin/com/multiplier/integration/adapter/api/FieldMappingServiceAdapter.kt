package com.multiplier.integration.adapter.api

import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.fieldmapping.grpc.schema.CreateProfileRequest
import com.multiplier.fieldmapping.grpc.schema.DeleteProfileRequest
import com.multiplier.fieldmapping.grpc.schema.DeleteProfileResponse
import com.multiplier.fieldmapping.grpc.schema.ExecuteBatchMappingRequest
import com.multiplier.fieldmapping.grpc.schema.ExecuteBatchMappingResponse
import com.multiplier.fieldmapping.grpc.schema.ExecuteMappingRequest
import com.multiplier.fieldmapping.grpc.schema.ExecuteMappingResponse
import com.multiplier.fieldmapping.grpc.schema.FieldMappingServiceGrpc
import com.multiplier.fieldmapping.grpc.schema.GetProfileRequest
import com.multiplier.fieldmapping.grpc.schema.ListProfilesRequest
import com.multiplier.fieldmapping.grpc.schema.ListProfilesResponse
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.fieldmapping.grpc.schema.ProfileResponse
import com.multiplier.fieldmapping.grpc.schema.UpdateProfileRequest
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

/**
 * Interface for the Field Mapping Service Adapter V2
 * This adapter strictly uses the gRPC schema from field-mapping-service
 */
interface FieldMappingServiceAdapter {
    /**
     * Get a field mapping profile by ID
     * @param profileId The profile ID
     * @return The field mapping profile response
     */
    fun getProfile(profileId: String): ProfileResponse

    /**
     * List all field mapping profiles for a customer
     * @param customerId The customer ID
     * @return List profiles response
     */
    fun listProfiles(customerId: Long): ListProfilesResponse

    /**
     * Execute field mapping using a specific profile
     * @param profileId The profile ID to use for mapping
     * @param sourceData The source data to transform
     * @return The execute mapping response
     */
    fun executeMapping(profileId: String, sourceData: Map<String, Any>): ExecuteMappingResponse

    /**
     * Execute batch field mapping using a specific profile
     * @param profileId The profile ID to use for mapping
     * @param sourceDataBatch The batch of source data to transform
     * @return The execute batch mapping response
     */
    fun executeBatchMapping(profileId: String, sourceDataBatch: List<Map<String, Any>>): ExecuteBatchMappingResponse

    /**
     * Create a new field mapping profile
     * @param profile The profile to create
     * @return The create profile response
     */
    fun createProfile(profile: Profile): ProfileResponse
    
    /**
     * Update an existing field mapping profile
     * @param profile The profile to update
     * @return The update profile response
     */
    fun updateProfile(profile: Profile): ProfileResponse

    /**
     * Delete a field mapping profile
     * @param profileId The ID of the profile to delete
     * @return The delete profile response
     */
    fun deleteProfile(profileId: String): DeleteProfileResponse
}

/**
 * Default implementation of FieldMappingServiceAdapterV2
 * This adapter calls the field-mapping-service using gRPC and strictly uses the gRPC schema
 */
@Service
class DefaultFieldMappingServiceAdapter : FieldMappingServiceAdapter {

    @GrpcClient("field-mapping-service")
    private lateinit var fieldMappingStub: FieldMappingServiceGrpc.FieldMappingServiceBlockingStub

    private val log = KotlinLogging.logger {}

    override fun getProfile(profileId: String): ProfileResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get field mapping profile with ID: $profileId via gRPC")
            val request = GetProfileRequest.newBuilder()
                .setProfileId(profileId)
                .build()

            val response = fieldMappingStub.getProfile(request)
            log.info("Successfully retrieved field mapping profile with ID: $profileId via gRPC")
            response
        }
    }

    override fun listProfiles(companyId: Long): ListProfilesResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get field mapping profiles for company ID: $companyId via gRPC")
            val request = ListProfilesRequest.newBuilder()
                .setCompanyId(companyId)
                .build()

            val response = fieldMappingStub.listProfiles(request)
            log.info("Successfully retrieved ${response.profilesCount} field mapping profiles for company ID: $companyId via gRPC")
            response
        }
    }

    override fun executeMapping(profileId: String, sourceData: Map<String, Any>): ExecuteMappingResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to execute field mapping for profile ID: $profileId via gRPC")

            val structBuilder = Struct.newBuilder()
            sourceData.forEach { (key, value) ->
                structBuilder.putFields(key, convertToValue(value))
            }

            val request = ExecuteMappingRequest.newBuilder()
                .setProfileId(profileId)
                .setSourceData(structBuilder.build())
                .build()

            val response = fieldMappingStub.executeMapping(request)
            log.info("Successfully executed field mapping for profile ID: $profileId via gRPC")
            response
        }
    }

    override fun executeBatchMapping(profileId: String, sourceDataBatch: List<Map<String, Any>>): ExecuteBatchMappingResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to execute batch field mapping for profile ID: $profileId via gRPC")

            val requestBuilder = ExecuteBatchMappingRequest.newBuilder()
                .setProfileId(profileId)

            sourceDataBatch.forEach { sourceData ->
                val structBuilder = Struct.newBuilder()
                sourceData.forEach { (key, value) ->
                    structBuilder.putFields(key, convertToValue(value))
                }
                requestBuilder.addSourceDataBatch(structBuilder.build())
            }

            val response = fieldMappingStub.executeBatchMapping(requestBuilder.build())
            log.info("Successfully executed batch field mapping for profile ID: $profileId via gRPC")
            response
        }
    }

    override fun createProfile(profile: Profile): ProfileResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to create field mapping profile: ${profile.name} via gRPC")

            val request = CreateProfileRequest.newBuilder()
                .setProfile(profile)
                .build()

            val response = fieldMappingStub.createProfile(request)
            log.info("Successfully created field mapping profile with ID: ${response.profile.id} via gRPC")
            response
        }
    }

    override fun updateProfile(profile: Profile): ProfileResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to update field mapping profile with ID: ${profile.id} via gRPC")

            val request = UpdateProfileRequest.newBuilder()
                .setProfile(profile)
                .build()

            val response = fieldMappingStub.updateProfile(request)
            log.info("Successfully updated field mapping profile with ID: ${response.profile.id} via gRPC")
            response
        }
    }

    override fun deleteProfile(profileId: String): DeleteProfileResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to delete field mapping profile with ID: $profileId via gRPC")

            val request = DeleteProfileRequest.newBuilder()
                .setProfileId(profileId)
                .build()

            val response = fieldMappingStub.deleteProfile(request)
            log.info("Successfully deleted field mapping profile with ID: $profileId via gRPC")
            response
        }
    }

    /**
     * Converts a Kotlin value to a Protobuf Value
     */
    private fun convertToValue(obj: Any?): Value {
        val valueBuilder = Value.newBuilder()
        
        when (obj) {
            is String -> valueBuilder.setStringValue(obj)
            is Number -> valueBuilder.setNumberValue(obj.toDouble())
            is Boolean -> valueBuilder.setBoolValue(obj)
            is Map<*, *> -> {
                val structBuilder = Struct.newBuilder()
                @Suppress("UNCHECKED_CAST")
                (obj as Map<String, Any>).forEach { (key, value) ->
                    structBuilder.putFields(key, convertToValue(value))
                }
                valueBuilder.setStructValue(structBuilder.build())
            }
            is List<*> -> {
                val listValueBuilder = com.google.protobuf.ListValue.newBuilder()
                obj.forEach { item ->
                    if (item != null) {
                        listValueBuilder.addValues(convertToValue(item))
                    }
                }
                valueBuilder.setListValue(listValueBuilder.build())
            }
            null -> valueBuilder.setNullValue(com.google.protobuf.NullValue.NULL_VALUE)
            else -> valueBuilder.setStringValue(obj.toString())
        }
        
        return valueBuilder.build()
    }
}
