package com.multiplier.integration.adapter.api;

import com.multiplier.common.exception.toSystemException
import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.Currency.CurrencyCode
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.ConversionInstruction
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service;

fun interface CurrencyServiceAdapter {
    fun getCurrencyExchangeAmount(fromCurrency: CurrencyCode, toCurrency: CurrencyCode, amount: Double): Double
}

@Service
class DefaultCurrencyServiceClient : CurrencyServiceAdapter {

    @GrpcClient("core-service")
    private lateinit var stub: CurrencyConversionServiceV2Grpc.CurrencyConversionServiceV2BlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getCurrencyExchangeAmount(
        fromCurrency: CurrencyCode,
        toCurrency: CurrencyCode,
        amount: Double,
    ): Double {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to convert currency amount from: ${fromCurrency.name} to : ${toCurrency.name} with amount: $amount")
            val req = CurrencyV2.CurrencyConversionGrpcRequest.newBuilder()
                .setSourceAmount(
                    CurrencyV2.Amount.newBuilder()
                        .setAmount(amount)
                        .setCurrency(fromCurrency)
                        .build()
                )
                .setTargetCurrency(toCurrency)
                .setConversionInstructions(
                    ConversionInstruction.newBuilder()
                        .setConversionRate(CurrencyV2.CurrencyConversionRate.TOPPED_UP)
                        .setConversionSource(Currency.CurrencyConversionSource.ON_DEMAND)
                        .build()
                )
                .build()
            val result = stub.convertCurrency(req)
            val convertedAmount = result.convertedAmount.amount
            val rate = result.conversionDetails.conversionRate
            log.info("Successfully retrieved converted rate: $rate - $convertedAmount")
            convertedAmount
        }
    }
}

