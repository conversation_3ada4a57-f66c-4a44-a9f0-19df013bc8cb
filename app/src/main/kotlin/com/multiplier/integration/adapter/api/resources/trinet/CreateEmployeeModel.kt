package com.multiplier.integration.adapter.api.resources.trinet

import kotlinx.serialization.Serializable

@Serializable
data class CreateEmployeeRequest(
    val onboardingStatus: String? = null,
    val alternateId: String? = null,
    val name: NameField,
    val preferredName: NameField,
    val biographicalInfo: BiographicalInfo,
    val employmentInfo: EmploymentInfo,
    val locationId: String? = null,
    val homeDeptId: String? = null,
    val roles: List<Role>,
)

@Serializable
data class NameField(
    val firstName: String? = null,
    val lastName: String? = null,
)

@Serializable
data class BiographicalInfo(
    val homeContact: HomeContact,
    val gender: String? = null,
    val genderNonBinary: Boolean,
)

@Serializable
data class HomeContact(
    val address: Address? = null,
    val emailAddress: String? = null,
    val telephoneNumbers: List<PhoneNumber>
)

@Serializable
data class EmploymentInfo(
    val reasonCode: String? = null,
    val startDate: String? = null,
    val seniorityDate: String? = null,
    val workEmail: String? = null,
    val workPhone: String? = null,
    val businessTitle: String? = null,
)

@Serializable
data class Role(
    val role: String? = null,
)

@Serializable
data class Address(
    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val address1: String? = null,
    val address2: String? = null,
    val city: String? = null,
    val state: String? = null,
    val country: String? = null,
    val postalCode: String? = null,
    val county: String? = null,
)

@Serializable
data class PhoneNumber(
    val number: String? = null,
)

@Serializable
data class ValidationEmployeeRequest(
    val firstName: String? = null,
    val lastName: String? = null,
    val address: Address? = null,
    val telephoneNumbers: List<PhoneNumber>,
    val emailAddress: String? = null,
    val workEmail: String? = null,
    val workPhone: String? = null,
    val businessTitle: String? = null
)
