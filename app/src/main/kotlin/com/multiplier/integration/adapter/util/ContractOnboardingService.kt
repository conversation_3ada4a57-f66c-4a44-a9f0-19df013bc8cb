package com.multiplier.integration.adapter.util

import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.platforms.Platform
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData.Group
import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha2CountryCode
import com.neovisionaries.i18n.CountryCode
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class ContractOnboardingService(
    val defaultContractOnboardingDataSanitizeStrategy: DefaultContractOnboardingDataSanitizeStrategy,
    val successFactorsContractOnboardingDataSanitizeAction: SuccessFactorsContractOnboardingDataSanitizeAction,
) {

    private fun getStrategy(platform: Platform): ContractOnboardingDataSanitizeAction {
        return when (platform) {
            Platform.SUCCESSFACTORS -> successFactorsContractOnboardingDataSanitizeAction
            else -> defaultContractOnboardingDataSanitizeStrategy
        }
    }

    fun cleanBulkContractOnboardingRequest(
        request: BulkContractOnboardingRequest,
        platform: Platform
    ): BulkContractOnboardingRequest {
        val strategy = getStrategy(platform)
        return if (request.data.employeeData.isNotEmpty()) {
            var cleanedEmployeeData = strategy.cleanCommonEmployeeData(request.data.employeeData)
            cleanedEmployeeData = cleanedEmployeeData + strategy.extractBankAccountName(cleanedEmployeeData)
            cleanedEmployeeData = cleanedEmployeeData + strategy.extractBankAddress(cleanedEmployeeData)
            cleanedEmployeeData = cleanedEmployeeData + strategy.extractBankRoutingInfo(cleanedEmployeeData)
            cleanedEmployeeData = cleanedEmployeeData + strategy.extractCompensation(request)
            request.copy(
                data = request.data.copy(employeeData = cleanedEmployeeData),
            )
        } else {
            request
        }
    }

    fun cleanBulkContractOnboardingMtmRequest(
        request: BulkContractOnboardingRequest,
        alpha3EmployeeCountry: String
    ): BulkContractOnboardingRequest {
        return if (request.data.employeeData.isNotEmpty()) {
            var cleanedEmployeeData = request.data.employeeData
            var cleanedCompensationData = request.data.compensationData
            val employeeId = request.data.employeeData["employeeId"] ?: ""

            // Fixed bank data
            cleanedEmployeeData = cleanedEmployeeData + mapOf(
                "group" to Group.EMPLOYMENT_DATA.name,
                "bank.accountHolderName" to "Bank Name",
                "bank.accountNumber" to "*********",
                "bank.ifscCode" to "SBIN0001067",
                "bank.swiftCode" to "VCBVVNVX",
                "bank.bankName" to "Bank of China",
                "bank.branchCode" to "001",
                "bank.address.country" to mapToAlpha2CountryCode(alpha3EmployeeCountry),
            )
            // Fixed employee data
            cleanedEmployeeData = cleanedEmployeeData + mapOf(
                "immigrationStatus" to "Citizen",
                "directManagerEmail" to "",
                "religion" to "Other",
            )

            if (alpha3EmployeeCountry.equals(CountryCode.VN.alpha3,true)){
                cleanedEmployeeData = cleanedEmployeeData + mapOf(
                    "numberOfDependants" to "0",
                    "bank.localBankCode" to "********",
                )
            }
            else{
                cleanedEmployeeData = cleanedEmployeeData + mapOf(
                    "numberOfDependents" to "0",
                    "bank.localBankCode" to "7056",
                )
            }

            // Fixed compensation data
            val fixedCompensationData = mapOf(
                "group" to Group.COMPENSATION_DATA.name,
                "employeeId" to employeeId,
                "COMPONENT_NAME" to "Base Salary",
                "BILLING_RATE" to "1",
                "BILLING_RATE_TYPE" to "Value",
                "IS_INSTALLMENT" to "No",
                "START_DATE" to LocalDateTime.now().format(DateTimeFormatter.ISO_DATE),
                "PAY_SCHEDULE_NAME" to "Monthly",
                "BILLING_FREQUENCY" to "MONTHLY",
                "PAY_SCHEDULE" to "Monthly",
                "CURRENCY" to "USD"
            )
            if (cleanedCompensationData.isEmpty()) {
                cleanedCompensationData = listOf(fixedCompensationData)
            } else {
                cleanedCompensationData = cleanedCompensationData.map { it + fixedCompensationData }
            }
            request.copy(
                data = request.data.copy(
                    employeeData = cleanedEmployeeData,
                    compensationData = cleanedCompensationData
                ),
            )
        } else {
            request
        }
    }
}