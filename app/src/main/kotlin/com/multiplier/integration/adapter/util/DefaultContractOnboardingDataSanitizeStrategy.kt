package com.multiplier.integration.adapter.util

import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import org.springframework.stereotype.Component

@Component
class DefaultContractOnboardingDataSanitizeStrategy : ContractOnboardingDataSanitizeAction {

    override fun extractCompensation(request: BulkContractOnboardingRequest): Map<String, String> {
        return mapOf()
    }
}