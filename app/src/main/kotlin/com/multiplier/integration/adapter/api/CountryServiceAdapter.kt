package com.multiplier.integration.adapter.api

import com.multiplier.common.exception.toSystemException
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.Country.GetContractDetailRestrictionRequest
import com.multiplier.country.schema.Country.GetCountryNameByCodeRequest
import com.multiplier.country.schema.Country.GetSupportedCurrencyRequest
import com.multiplier.country.schema.Country.GrpcContractDetailRestriction
import com.multiplier.country.schema.Country.GrpcCountryCode
import com.multiplier.country.schema.CountryServiceGrpc
import com.multiplier.country.schema.contract.Contract
import com.multiplier.country.schema.currency.Currency
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface CountryServiceAdapter {
    fun getCountryNameByCode(countryCode: Country.GrpcCountryCode): String

    fun getSupportedCurrencies(countryCode: Country.GrpcCountryCode, contractType: Contract.GrpcContractType?): Set<Currency.GrpcCurrencyCode>?

    fun getContractDetailRestriction(countryCode: Country.GrpcCountryCode? = null): List<GrpcContractDetailRestriction>
}

@Service
class CountryServiceAdapterImpl : CountryServiceAdapter {

    @GrpcClient("country-service")
    private lateinit var stub: CountryServiceGrpc.CountryServiceBlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getCountryNameByCode(countryCode: Country.GrpcCountryCode): String {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get country name for country code: $countryCode")
            val req = GetCountryNameByCodeRequest.newBuilder().setCountryCode(countryCode).build()
            val countryName = stub.getCountryNameByCode(req).countryName
            log.info("Successfully retrieved country name: $countryName for country code: $countryCode")
            countryName
        }
    }

    override fun getSupportedCurrencies(countryCode: Country.GrpcCountryCode, contractType: Contract.GrpcContractType?): Set<Currency.GrpcCurrencyCode> {
        return DownstreamServiceUtils.execute {
            val res = stub.getSupportedCurrencies(
                GetSupportedCurrencyRequest.newBuilder()
                    .setCountryCode(countryCode)
                    .setContractType(contractType)
                    .build()
            )
            val result: MutableSet<Currency.GrpcCurrencyCode> = HashSet<Currency.GrpcCurrencyCode>()
            for (i in 0 until res.currenciesCount) {
                result.add(Currency.GrpcCurrencyCode.valueOf(res.getCurrencies(i)))
            }
            result
        }
    }

    override fun getContractDetailRestriction(countryCode: GrpcCountryCode?): List<GrpcContractDetailRestriction> {
        return DownstreamServiceUtils.execute {
            val res = stub.getContractDetailRestriction(
                if (countryCode != null) GetContractDetailRestrictionRequest.newBuilder()
                    .setCountryCode(countryCode)
                    .build()
                else GetContractDetailRestrictionRequest.newBuilder()
                    .setCountryCode(GrpcCountryCode.NULL_COUNTRY_CODE)
                    .build()
            )
            res.contractDetailRestrictionsList
        }
    }
}
