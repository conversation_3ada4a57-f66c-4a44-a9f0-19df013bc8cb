package com.multiplier.integration.adapter.api.resources.knit.hibob

import com.multiplier.integration.adapter.api.resources.knit.bamboo.ErrorResponse
import kotlinx.serialization.Serializable

@Serializable
data class LookupEmployeeResponse(
    val success: Boolean,
    val data: HibobEmployeeLookup? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int
)

@Serializable
data class HibobEmployeeLookup(
    val employees: List<HibobEmployee>? = null,
)

@Serializable
data class HibobEmployee(
    val id: String,
    val email: String,
)

@Serializable
data class HibobPassthroughRequest(
    val method: String,
    val path: String,
    val body: String,
)

@Serializable
data class HibobPassthroughRequestPeopleSearchBody(
    val showInactive: Boolean,
    val filters: List<HibobSearchFilter>,
)

@Serializable
data class HibobSearchFilter(
    val fieldPath: String,
    val values: List<String>,
    val operator: String,
)