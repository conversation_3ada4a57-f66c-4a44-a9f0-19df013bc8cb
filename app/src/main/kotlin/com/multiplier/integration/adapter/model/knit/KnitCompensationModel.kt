package com.multiplier.integration.adapter.model.knit

enum class CompensationType {
    SALARY,
    ALLOWANCE,
    BONUS,
    MERIT,
    COMMISSION,
}

enum class Frequency {
    ANNUAL,
    WEEKLY,
    QUARTERLY,
    SEMI_MONTHLY,
    MONTHLY,
    HOURLY,
    BI_WEEKLY,
    DAILY,
    NOT_SPECIFIED
}

data class CompensationItemData(
    val type: String?,
    val planId: String?,
    val amount: Double?,
    val percentage: Double?,
    val currency: String?,
    val payPeriod: String?,
    val frequency: Frequency?,
    val endDate: String?,
    val startDate: String?,
)

data class StockData(
    val planId: String?,
    val type: String?,
    val targetShares: Double?,
    val optionsPercentage: Double?,
    val stockPercentage: Double?,
    val vestingScheduleId: String?,
    val vestingScheduleName: String?
)

data class Compensation(
    val fixed: List<CompensationItemData>?,
    val variable: List<CompensationItemData>?,
    val stock: List<StockData>?
)
