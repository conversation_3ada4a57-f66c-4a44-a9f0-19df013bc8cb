package com.multiplier.integration.adapter.api.resources.financial

import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionInvoice
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionVendorCredit
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditRequestBody
import com.multiplier.integration.types.PlatformCategory

interface Message

data class MergeDevAccountTokenRetrieveRequest(
  val publicToken: String
)

data class MergeDevLinkTokenRequest(
    val endUserEmailAddress: String,
    val endUserOrganizationName: String,
    val endUserOriginId: String,
    val appId: String
)

data class MergeDevInvoiceRequest(
    val accountApi: MergeDevAccountApi,
    val invoice: AccountingTransactionInvoice
)

data class MergeDevVendorCreditRequest(
    val accountApi: MergeDevAccountApi,
    val vendorCredit: VendorCreditRequestBody
)

data class MergeDevAccountApi(
    val accountToken: String,
)


data class MergeDevResponse(
    val message: Message?,
    val error: Error? = null,
    val success: Boolean
)

data class MergeDevLinkTokenMessage(
   val linkToken: String?,
   val integrationName: String?,
   val magicLinkUrl: String?,
): Message

data class MergeDevAccountTokenRetrieveMessage(
    val accountToken: String,
    val integrationName: String,
    val integrationCategories: List<PlatformCategory> = emptyList(),
): Message

data class MergeDevInvoiceMessage(
    val id: String?,
    val remoteId: String?,
    val invoice: AccountingTransactionInvoice
): Message

data class MergeDevVendorCreditMessage(
    val id: String?,
    val remoteId: String?,
    val vendorCredit: AccountingTransactionVendorCredit
): Message

data class MergeDevPaymentResponse (
    val id: String,
    val remoteId: String,
): Message

data class Error(
    val errorCode: String?,
    val errorMessage: String?
)
