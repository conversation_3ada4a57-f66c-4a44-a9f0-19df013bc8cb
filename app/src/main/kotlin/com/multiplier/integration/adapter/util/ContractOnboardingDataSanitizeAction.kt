package com.multiplier.integration.adapter.util

import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.utils.MapperUtil.Companion.mapToAlpha2CountryCode
import com.neovisionaries.i18n.CountryCode
import mu.KLogger
import mu.KotlinLogging
import utils.DateUtils.Companion.parseToFormattedDateString
import utils.StringUtils.Companion.replaceAllNonAlphanumericCharacters

interface ContractOnboardingDataSanitizeAction {

    private val log: KLogger get() = KotlinLogging.logger {}

    fun cleanCommonEmployeeData(employeeData: Map<String, String>): Map<String, String> {
        return employeeData.map { entry ->
            entry.key to when (entry.key) {
                "dateOfBirth", "startOn", "endOn" -> parseToFormattedDateString(entry.value, "yyyy-MM-dd")
                "address.zipcode" -> replaceAllNonAlphanumericCharacters(entry.value, " ")
                else -> entry.value
            }
        }.toMap()
    }

    fun extractBankAccountName(employeeData: Map<String, String>): Map<String, String> {
        val bankAccountName = employeeData["bank.accountHolderName"]
        if (bankAccountName.isNullOrBlank()) {
            val firstName = employeeData["firstName"]
            val lastName = employeeData["lastName"]
            if (!firstName.isNullOrBlank() || !lastName.isNullOrBlank()) {
                return mapOf("bank.accountHolderName" to "${firstName ?: ""} ${lastName ?: ""}".trim())
            }
        }
        return emptyMap()
    }

    fun extractBankAddress(employeeData: Map<String, String>): Map<String, String> {
        var bankAddressCountry = employeeData["bank.address.country"]
        val addressCountry = employeeData["address.country"]
        if (bankAddressCountry.isNullOrBlank() && !addressCountry.isNullOrBlank()) {
            bankAddressCountry = addressCountry
        }
        return mapOf("bank.address.country" to mapToAlpha2CountryCode(bankAddressCountry ?: ""))
    }

    fun extractBankRoutingInfo(employeeData: Map<String, String>): Map<String, String> {
        val bankAddressCountry = employeeData["bank.address.country"]
        val routingNumber = employeeData["bank.routingNumber"]
        var institutionNumber = employeeData["bank.institutionNumber"]
        var transitNumber = employeeData["bank.transitNumber"]
        if (bankAddressCountry == CountryCode.CA.alpha2) {
            if (routingNumber != null && routingNumber.startsWith("0")) {
                if (institutionNumber.isNullOrBlank()) {
                    try {
                        institutionNumber = routingNumber.substring(1, 4)
                    } catch (e: Exception) {
                        log.error(e) { "Error parsing bank.institutionNumber from bank.routingNumber: $routingNumber" }
                    }
                }
                if (transitNumber.isNullOrBlank()) {
                    try {
                        transitNumber = routingNumber.substring(4)
                    } catch (e: Exception) {
                        log.error(e) { "Error parsing bank.transitNumber from bank.routingNumber: $routingNumber" }
                    }
                }
            }
        }
        return mapOf(
            "bank.institutionNumber" to (institutionNumber ?: ""),
            "bank.transitNumber" to (transitNumber ?: "")
        )
    }

    fun extractCompensation(request: BulkContractOnboardingRequest): Map<String, String>
}