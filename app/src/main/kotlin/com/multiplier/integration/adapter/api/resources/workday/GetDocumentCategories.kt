package com.multiplier.integration.adapter.api.resources.workday

import kotlinx.serialization.Serializable

@Serializable
data class DocumentCategoriesResponse(
    val success: Boolean,
    val data: DocumentCategoriesData? = null,
    val error: ErrorResponse? = null,
    var responseCode: Int? = null
)

@Serializable
data class DocumentCategoriesData(
    val categories: List<DocumentCategory>
)

@Serializable
data class DocumentCategory(
    val id: String,
    val name: String
)

@Serializable
data class ErrorResponse(
    val msg: String? = null
)
