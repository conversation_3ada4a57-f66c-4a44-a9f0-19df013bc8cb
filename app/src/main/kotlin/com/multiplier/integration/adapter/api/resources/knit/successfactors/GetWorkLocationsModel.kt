package com.multiplier.integration.adapter.api.resources.knit.successfactors

import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import kotlinx.serialization.Serializable

@Serializable
data class GetSAPWorkLocationsResponse(
    val success: Boolean? = null,
    val data: SAPWorkLocationResponse? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class SAPWorkLocationResponse(
    val response: SAPWorkLocationResponseBody? = null,
)

@Serializable
data class SAPWorkLocationResponseBody(
    val body: SAPWorkLocationNestedResult? = null,
)

@Serializable
data class SAPWorkLocationNestedResult(
    val d: SAPWorkLocationResponseData? = null,
)

@Serializable
data class SAPWorkLocationResponseData(
    val results: List<WorkLocationData>? = null,
)

@Serializable
data class WorkLocationData(
    val name: String? = null,
    val externalCode: String? = null,
    val timezone: String? = null,
    val locationGroup: String? = null,
    val internalCode: String? = null,
)
