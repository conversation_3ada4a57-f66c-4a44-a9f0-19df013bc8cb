package com.multiplier.integration.adapter.api.resources.trinet

import kotlinx.serialization.Serializable

@Serializable
data class CreateEmployeeResponseModel(
    val data: CreateEmployeeResponseModelData
)

@Serializable
data class CreateEmployeeResponseModelData(
    val onboardingStatus: String? = null,
    val newHireType: String? = null,
    val employeeId: String? = null,
    val operatorId: String? = null,
    val personId: String? = null,
    val positionId: String? = null,
    val status: String? = null,
)