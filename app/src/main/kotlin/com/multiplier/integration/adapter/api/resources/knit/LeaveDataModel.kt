package com.multiplier.integration.adapter.api.resources.knit

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.multiplier.integration.sync.model.IsPaid
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.sync.model.Unit
import kotlinx.serialization.Serializable

@Serializable
data class LeaveBalance (
    val leaveType: LeaveType? = null,
    val unit: LeaveUnitEnum? = null,
    val balance: Double? = null,
    val used: Double? = null,
    val isPaid: LeavePaidConditionEnum?  = null,
    val isEligible: LeaveEligibleConditionEnum? = null,
)

@Serializable
data class LeaveType (
    val id: String? = null,
    val name: String? = null,
    val type: LeaveTypeEnum? = null,
)

@Serializable
data class LeaveBalanceResponse (
    val success: Boolean? = null,
    val data: List<LeaveBalance>? = null,
    val errors: List<String>? = null,
    val responseCode: Int? = null,
    val error: ApiError? = null,
)

@Serializable
data class LeaveRequestResponse (
    val success: Boolean? = null,
    val data: LeaveRequestListResponse? = null,
    val errors: List<String>? = null,
    val responseCode: Int? = null,
    val error: ApiError? = null,
)

@Serializable
data class LeaveRequestListResponse (
    val requests: List<LeaveRequest>? = null,
)

@Serializable
@JsonIgnoreProperties(ignoreUnknown = true)
data class LeaveRequest(
    val employeeId: String? = null,
    val id: String? = null,
    val startDate: String? = null,
    val endDate: String? = null,
    val requestedOn: String? = null,
    val note: String? = "",
    val status: Status?,
    val unit: Unit?,
    val amount: Double?,
    val isPaid: IsPaid?
)

@Serializable
@JsonIgnoreProperties(ignoreUnknown = true)
data class LeaveCreateRequest(
    val employeeId: String,
    val leaveTypeId: String,
    val from: String? = null,
    val to: String? = null,
    val unit: Unit,
    val amount: Double,
    var metadata: Map<String, String>? = null,
)

@Serializable
data class LeaveCreateRequestResponse (
    val success: Boolean,
    val data: LeaveCreateRequestResponseData? = null,
    val responseCode: Int? = null,
    val error: ApiError? = null,
)
@Serializable
data class LeaveCreateRequestResponseData (
    val leaveRequestId: String? = null,
)

@Serializable
data class LeaveTypeListResponse (
    val leaveTypes: List<LeaveType>? = null,
)

@Serializable
data class GetLeaveTypesResponse (
    val success: Boolean? = null,
    val data: LeaveTypeListResponse? = null,
    val responseCode: Int? = null,
    val errors: String? = null,
)

enum class LeaveTypeEnum {
    VACATION,
    SICK,
    PERSONAL,
    JURY_DUTY,
    VOLUNTEER,
    BEREAVEMENT,
    NOT_SPECIFIED
}

enum class LeaveUnitEnum {
    DAYS,
    HOURS,
    NOT_SPECIFIED,
}

enum class LeavePaidConditionEnum {
    TRUE,
    FALSE,
    NOT_SPECIFIED
}

enum class LeaveEligibleConditionEnum {
    TRUE,
    FALSE,
    NOT_SPECIFIED
}