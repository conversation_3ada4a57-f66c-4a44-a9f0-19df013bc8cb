package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable

@Serializable
data class Position(
    val positionId: String?,
    val designation: String?,
    val department: String?
)

@Serializable
data class WorkShift(
    val workShiftId: String?,
    val workShiftName: String?
)

@Serializable
data class Data(
    val positions: List<Position>?,
    val workShifts: List<WorkShift>?
)

@Serializable
data class ApiError(
    val msg: String?
)

@Serializable
data class GetPositionDetailResponse(
    val success: Boolean,
    val data: Data? = null,
    val error: ApiError? = null,
    val responseCode: Int? = null  // The HTTP response code
)
