package com.multiplier.integration.adapter.api.resources.financial.vendorCredit


import kotlinx.serialization.Serializable

@Serializable
data class VendorCreditRequestBody(
    val model: VendorCredit
)

@Serializable
data class VendorCredit(
    val id: String? = null,
    val vendor: String,
    val currency: String,
    val company: String? = null,
    val totalAmount: Double? = null,
    val lines: List<VendorCreditItem>? = emptyList(),
    val remoteId: String? = null,
    val createdAt: String? = null,
    val modifiedAt: String? = null,
    val transactionDate: String? = null,
    val exchangeRate: String? = "1",
    val appliedToLines: List<VendorCreditAppliedToLines>? = emptyList(),
    val trackingCategories: List<String>? = emptyList(),
)

@Serializable
data class VendorCreditItem(
    val id: String? = null,
    val company: String? = null,
    val description: String? = null,
    val account: String,
    val remoteId: String? = null,
    val createdAt: String? = null,
    val modifiedAt: String? = null,
    val trackingCategories: List<String>? = emptyList(),
    val taxRate: String? = null,
    val netAmount: Double? = 0.0,
    val exchangeRate: String? = "1",
)

@Serializable
data class VendorCreditAppliedToLines(
    val invoice: String,
    val appliedDate: String? = null,
    val appliedAmount: String,
)