package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable
import java.math.BigDecimal

data class UpdateCompensationRequest(
    val employeeId: String? = null,
    val effectiveDate: String? = null,
    val fixed: List<CompensationDetail>? = null,
    val variable: List<CompensationDetail>? = null,
    val stocks: List<StockDetail>? = null
)

data class CompensationDetail(
    val type: String? = null,
    val currency: String? = null,
    val frequency: String? = null,
    val amount: BigDecimal? = null,
    val planId: String? = null,
    val percentage: Int? = null,
    val payPeriod: String? = null
)

@Serializable
data class StockDetail(
    val planId: String? = null,
    val type: String? = null,
    val targetShares: Int? = null
)

@Serializable
data class UpdateCompensationResponse(
    val success: Boolean? = null,
    val data: ResponseData? = null,
    val error: UpdateCompensationErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class UpdateCompensationErrorResponse(
    val msg: String? = null
)

@Serializable
data class ResponseData(
    val compensationId: String? = null
)
