package com.multiplier.integration.adapter.model

import com.fasterxml.jackson.annotation.JsonProperty
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.utils.toMultiplierType
import dev.merge.client.hris.models.AccountDetails

data class <PERSON>mbo<PERSON><PERSON><PERSON><PERSON>ield(
    val id: String,
    val name: String,
)

data class IntegrationMetadata(
    val name: String,
    val slug: String,
    val image: String,
    @field:JsonProperty("square_image") val squareImage: String,
    val color: String,
    val categories: List<String>,
    @field:JsonProperty("enabled_categories") val enabledCategories: List<PlatformCategory>,
)

data class IntegrationResponse(
    val code: Int,
    val results: List<IntegrationMetadata>,
)

data class AccountData(
    val category: PlatformCategory?,
    val integrationName: String?,
    val integrationSlug: String?,
    val status: String?,
    val email: String?,
)

fun AccountDetails.toAccountData(): AccountData =
    AccountData(
        category = this.category.toMultiplierType(),
        integrationName = this.integration,
        integrationSlug = this.integrationSlug,
        status = this.status,
        email = this.endUserEmailAddress,
    )
