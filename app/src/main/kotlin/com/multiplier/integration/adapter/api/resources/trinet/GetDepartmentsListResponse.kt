package com.multiplier.integration.adapter.api.resources.trinet

import kotlinx.serialization.Serializable

@Serializable
data class GetDepartmentsListResponse(
    val data: List<Department>? = null,
)

@Serializable
data class Department(
    val deptId: String? = null,
    val deptCode: String? = null,
    val deptName: String? = null,
    val shortDesc: String? = null,
    val effectiveDate: String? = null,
    val deptEmployeeCount: Int? = null,
    val deptLocationCount: Int? = null,
    val endDate: String? = null,
)