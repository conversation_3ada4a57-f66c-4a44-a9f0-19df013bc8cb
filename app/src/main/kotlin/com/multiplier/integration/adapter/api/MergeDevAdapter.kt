package com.multiplier.integration.adapter.api

import com.merge.api.MergeApiClient
import com.merge.api.core.ApiError
import com.merge.api.resources.accounting.AccountingClient
import com.merge.api.resources.accounting.invoices.requests.InvoiceEndpointRequest
import com.merge.api.resources.accounting.invoices.requests.PatchedInvoiceEndpointRequest
import com.merge.api.resources.accounting.linktoken.requests.EndUserDetailsRequest
import com.merge.api.resources.accounting.payments.requests.PaymentEndpointRequest
import com.merge.api.resources.accounting.types.CategoriesEnum
import com.multiplier.integration.accounting.domain.CompanyPayableExternalTransactionNotFoundException
import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.adapter.api.resources.financial.*
import com.multiplier.integration.adapter.api.resources.financial.mapping.MergeDevInvoiceRequestMapper
import com.multiplier.integration.adapter.api.resources.financial.mapping.toDomain
import com.multiplier.integration.adapter.api.resources.financial.payment.MergeDevPaymentCreateRequest
import com.multiplier.integration.adapter.api.resources.financial.payment.MergeDevPaymentRequestMapper
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditRequestBody
import com.multiplier.integration.types.CurrencyCode
import com.multiplier.integration.types.PlatformCategory
import io.ktor.client.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.http.content.*
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNamingStrategy
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class MergeDevAdapter(
    private val mergeDevInvoiceRequestMapper: MergeDevInvoiceRequestMapper,
    private val mergeDevPaymentRequestMapper: MergeDevPaymentRequestMapper,
    @Qualifier("mergeDevHttpClient") private val httpClient: HttpClient,
) : FinancialIntegration {
    @Value("\${platform.merge-dev.api-key}")
    private lateinit var apiKey: String

    @Value("\${platform.merge-dev.api-url}")
    private lateinit var apiUrl: String

    private final val accountTokenHeader = "X-Account-Token"
    private final val accountingPrefix = "/api/accounting/v1/"

    @OptIn(ExperimentalSerializationApi::class)
    val json =
        Json {
            namingStrategy = JsonNamingStrategy.SnakeCase
            encodeDefaults = false
            ignoreUnknownKeys = true // This will ignore any unknown keys in the JSON
            isLenient = true // Allow lenient parsing of numbers, strings, etc.
            explicitNulls = false // This will allow missing fields to be set to null
        }

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun createLinkToken(mergeDevLinkTokenRequest: MergeDevLinkTokenRequest): MergeDevResponse =
        handleApiCall {
            val response =
                getMergeAccountingClient().linkToken()?.create(
                    EndUserDetailsRequest
                        .builder()
                        .endUserEmailAddress(mergeDevLinkTokenRequest.endUserEmailAddress)
                        .endUserOrganizationName(mergeDevLinkTokenRequest.endUserOrganizationName)
                        .endUserOriginId(mergeDevLinkTokenRequest.endUserOriginId)
                        .categories(listOf(CategoriesEnum.ACCOUNTING))
                        .integration(mergeDevLinkTokenRequest.appId)
                        .build(),
                )
            MergeDevResponse(
                success = true,
                message =
                    MergeDevLinkTokenMessage(
                        linkToken = response?.linkToken ?: "",
                        integrationName = response?.integrationName?.orElse("") ?: "",
                        magicLinkUrl = response?.magicLinkUrl?.orElse("") ?: "",
                    ),
            )
        }

    override fun retrieveAccountToken(mergeDevAccountTokenRetrieveRequest: MergeDevAccountTokenRetrieveRequest): MergeDevResponse =
        handleApiCall {
            val response = getMergeAccountingClient().accountToken()?.retrieve(mergeDevAccountTokenRetrieveRequest.publicToken)
            MergeDevResponse(
                success = true,
                message =
                    MergeDevAccountTokenRetrieveMessage(
                        accountToken = response?.accountToken ?: "",
                        integrationName = response?.integration?.name ?: "",
                        integrationCategories =
                            response
                                ?.integration
                                ?.categories
                                ?.orElse(emptyList())
                                ?.mapNotNull { category -> mapCategories(category) }
                                ?: emptyList(),
                    ),
            )
        }

    override fun createInvoice(mergeDevInvoiceRequest: MergeDevInvoiceRequest): MergeDevResponse =
        handleApiCall {
            val invoiceRequest = mergeDevInvoiceRequestMapper.toInvoiceRequest(mergeDevInvoiceRequest)
            logger.info("Creating invoice with request: $invoiceRequest")
            val response =
                getMergeApiClient(mergeDevInvoiceRequest.accountApi.accountToken).accounting().invoices().create(
                    InvoiceEndpointRequest
                        .builder()
                        .model(invoiceRequest)
                        .build(),
                )
            logger.info("Invoice created with id: $response")
            MergeDevResponse(
                success = true,
                message =
                    MergeDevInvoiceMessage(
                        id = response?.model?.id?.orElse("") ?: "",
                        remoteId = response?.model?.remoteId?.orElse("") ?: "",
                        invoice = response.toDomain(),
                    ),
            )
        }

    override fun updateInvoice(
        mergeDevInvoiceRequest: MergeDevInvoiceRequest,
        id: String,
    ): MergeDevResponse =
        handleApiCall {
            val invoiceRequest = mergeDevInvoiceRequestMapper.toPatchInvoiceRequest(mergeDevInvoiceRequest)
            logger.info("Updating invoice with request: $invoiceRequest")
            val response =
                getMergeApiClient(mergeDevInvoiceRequest.accountApi.accountToken).accounting().invoices().partialUpdate(
                    id,
                    PatchedInvoiceEndpointRequest
                        .builder()
                        .model(invoiceRequest)
                        .build(),
                )
            logger.info("Invoice updated with id: $response")
            MergeDevResponse(
                success = true,
                message =
                    MergeDevInvoiceMessage(
                        id = response?.model?.id?.orElse("") ?: "",
                        remoteId = response?.model?.remoteId?.orElse("") ?: "",
                        invoice = response.toDomain(),
                    ),
            )
        }

    override fun getTransactionAmounts(
        externalId: String,
        accountToken: String,
    ): ExternalCompanyPayableAmount {
        logger.info { "Calling merge client for the amounts of external company payable id = $externalId" }
        try {
            val externalInvoice =
                getMergeApiClient(accountToken)
                    .accounting()
                    .invoices()
                    .retrieve(externalId)

            val currency = CurrencyCode.valueOf(externalInvoice.currency.get().toString())
            val dueAmount = Amount(currencyCode = currency, value = externalInvoice.balance.get())
            val totalAmount = Amount(currencyCode = currency, value = externalInvoice.totalAmount.get())

            return ExternalCompanyPayableAmount(
                dueAmount = dueAmount,
                totalAmount = totalAmount,
                paidAmount = totalAmount.minus(dueAmount),
            ).also {
                logger.info { "The amounts found for external invoice id = $externalId are $it" }
            }
        } catch (e: Exception) {
            exceptionMessageHandler(e)
            throw CompanyPayableExternalTransactionNotFoundException("Get external transaction. Id = $externalId failed")
        }
    }

    override fun createPayment(
        mergeDevPaymentCreateRequest: MergeDevPaymentCreateRequest,
        accountToken: String,
    ): MergeDevResponse =
        handleApiCall {
            logger.info { "Calling merge client for creation of payment for company payable. Request = $mergeDevPaymentCreateRequest" }
            val paymentRequest = mergeDevPaymentRequestMapper.mapPaymentRequestMapper(mergeDevPaymentCreateRequest)
            val response =
                getMergeApiClient(accountToken)
                    .accounting()
                    .payments()
                    .create(
                        PaymentEndpointRequest
                            .builder()
                            .model(paymentRequest)
                            .build(),
                    )

            MergeDevResponse(
                message =
                    MergeDevPaymentResponse(
                        id = response.model.id.get(),
                        remoteId = response.model.remoteId.get(),
                    ),
                success = true,
            ).also {
                logger.info { "Created Response = $it for request = $mergeDevPaymentCreateRequest" }
            }
        }

    override fun createVendorCredit(mergeDevVendorCreditRequest: MergeDevVendorCreditRequest): MergeDevResponse =
        handleApiCall {
            val url = apiUrl + accountingPrefix + "vendor-credits"
            logger.info { "Calling merge client for creation of vendor credit for company payable. Request  $mergeDevVendorCreditRequest" }

            val body =
                TextContent(json.encodeToString(mergeDevVendorCreditRequest.vendorCredit), ContentType.Application.Json)
            logger.info { "Request url: $url" }
            runBlocking {
                val response = httpPost(url, mergeDevVendorCreditRequest.accountApi.accountToken, body)
                val responseBody = response.bodyAsText()
                val httpStatusCode = response.status.value
                logger.info { "httpStatusCode: $httpStatusCode" }
                if (response.status == HttpStatusCode.OK || response.status == HttpStatusCode.Created) {
                    val accountingResponse = json.decodeFromString<VendorCreditRequestBody>(responseBody).toDomain()
                    logger.info { "external id:: ${accountingResponse.id}" }
                    MergeDevResponse(
                        success = true,
                        message =
                            MergeDevVendorCreditMessage(
                                id = accountingResponse.id,
                                remoteId = accountingResponse.commonFields?.remoteId,
                                vendorCredit = accountingResponse,
                            ),
                    )
                } else {
                    logger.error { "Vendor credit creation failed with status $httpStatusCode: $responseBody" }
                    throw ApiError(
                        "Failed to create vendor credit: ${response.status.description}",
                        httpStatusCode,
                        responseBody,
                    )
                }
            }
        }

    private fun handleApiCall(apiCall: () -> MergeDevResponse): MergeDevResponse =
        try {
            apiCall()
        } catch (e: Exception) {
            exceptionMessageHandler(e)
            MergeDevResponse(
                success = false,
                message = null,
                error =
                    Error(
                        errorMessage = e.message ?: "Unknown error",
                        errorCode = null,
                    ),
            )
        }

    private fun exceptionMessageHandler(e: Exception) {
        val message: String? = if (e is ApiError) e.body().toString() else e.message
        logger.error("API call error: $message")
        logger.error(e.stackTraceToString())
    }

    fun mapCategories(it: CategoriesEnum?): PlatformCategory =
        when (it) {
            CategoriesEnum.ACCOUNTING -> PlatformCategory.ACCOUNTING
            CategoriesEnum.ATS -> PlatformCategory.ATS
            CategoriesEnum.CRM -> PlatformCategory.CRM
            CategoriesEnum.HRIS -> PlatformCategory.HRIS
            CategoriesEnum.TICKETING -> PlatformCategory.TICKETING
            else -> PlatformCategory.ACCOUNTING
        }

    fun getMergeAccountingClient(): AccountingClient =
        MergeApiClient
            .builder()
            .apiKey(apiKey)
            .build()
            .accounting()

    fun getMergeApiClient(accountToken: String): MergeApiClient =
        MergeApiClient
            .builder()
            .accountToken(accountToken)
            .apiKey(apiKey)
            .build()

    suspend fun httpPost(
        url: String,
        accountToken: String,
        body: Any,
    ): HttpResponse =
        httpClient.post(url) {
            setBody(body)
            headers {
                append(accountTokenHeader, accountToken)
            }
        }
}
