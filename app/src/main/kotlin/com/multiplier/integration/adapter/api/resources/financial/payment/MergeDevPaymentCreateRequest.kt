package com.multiplier.integration.adapter.api.resources.financial.payment

import com.multiplier.integration.accounting.domain.common.Amount
import java.time.LocalDateTime

data class MergeDevPaymentCreateRequest(
    val amount: Amount,
    val account: String,
    val contact: String,
    val createdDate: LocalDateTime,
    val appliedInvoice: PaymentAppliedInvoices,
)

data class PaymentAppliedInvoices(
    val appliedAmount: Amount,
    val appliedDate: LocalDateTime, //check time zones.
    val externalInvoiceId: String,
)