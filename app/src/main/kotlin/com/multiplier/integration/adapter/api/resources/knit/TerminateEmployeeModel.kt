package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class TerminateEmployeeRequest(
    val employeeId: String? = null,
    val terminationDate: String? = null,
    val terminationReason: String? = null,
)

@Serializable
data class TerminateEmployeeResponse(
    val success: Boolean,
    val data: JsonObject? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class GetTerminationReasonResponse(
    val success: Boolean,
    val data: TerminationReasonResponse? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class TerminationReasonResponse(
    val reasons: List<TerminationReason>? = null,
)

@Serializable
data class TerminationReason(
    val id: String,
    val name: String,
)

