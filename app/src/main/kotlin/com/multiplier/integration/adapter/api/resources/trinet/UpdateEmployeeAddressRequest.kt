package com.multiplier.integration.adapter.api.resources.trinet

import kotlinx.serialization.Serializable

@Serializable
data class UpdateEmployeeAddressRequest(
    val effectiveDate: String? = null,
    val addressType: String? = null,
    val address1: String? = null,
    val city: String? = null,
    val postalCode: String? = null,
    val country: String? = null,
    val state: String? = null,
)