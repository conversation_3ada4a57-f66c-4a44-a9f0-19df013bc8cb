package com.multiplier.integration.adapter

import com.multiplier.integration.repository.SFTPAccessRequestRepository
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.rest.model.SFTPWebhookRequest
import com.multiplier.integration.service.BulkModule
import com.multiplier.integration.service.IntegrationInput
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.utils.FTPDirectoryUtil
import mu.KotlinLogging
import org.springframework.stereotype.Component
import com.multiplier.common.exception.MplSystemException
import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest

/**
 * Adapter for converting SFTPWebhookRequest to IntegrationInput.
 * This adapter extracts the necessary information from the SFTP file URI
 * to create an IntegrationInput object for processing.
 */
@Component
class SFTPWebhookAdapter(
    private val sftpAccessRequestRepository: SFTPAccessRequestRepository
) {
    private val log = KotlinLogging.logger {}

    /**
     * Converts an SFTPWebhookRequest to an IntegrationInput.
     * This method extracts the company ID, entity ID, and bulk module from the file URI
     * by looking up the SFTP access request that matches the directory path.
     *
     * @param request The SFTPWebhookRequest to convert
     * @return The converted IntegrationInput
     * @throws MplSystemException if no matching SFTP access request is found
     */
    fun toIntegrationInput(request: SFTPWebhookRequest): IntegrationInput {
        log.info { "Converting SFTPWebhookRequest to IntegrationInput: $request" }

        // Extract the directory path from the file URI
        val directoryPath = FTPDirectoryUtil.extractMainDirectoryPath(request.fileURI)
        log.info { "Extracted directory path: $directoryPath" }

        // Find the SFTP access request that matches the directory path
        val sftpAccessRequest = findSftpAccessRequestByDirectory(directoryPath)
            ?: throw CustomerErrorCode.ENTITY_NOT_FOUND.toSystemException(
                message = "No approved SFTP access request found for directory: $directoryPath"
            )

        log.info { "Found SFTP access request: $sftpAccessRequest" }

        // Create and return the IntegrationInput
        return IntegrationInput(
            type = URIType.SFTP,
            uri = request.fileURI,
            companyId = sftpAccessRequest.companyId,
            entityId = sftpAccessRequest.entityId,
            module = BulkModule.valueOf(sftpAccessRequest.bulkModule.name)
        )
    }

    /**
     * Finds an SFTP access request by directory path.
     * This method searches for an approved SFTP access request with a matching main SFTP directory.
     *
     * @param directoryPath The directory path to search for
     * @return The matching SFTP access request, or null if none is found
     */
    private fun findSftpAccessRequestByDirectory(directoryPath: String): JpaSFTPAccessRequest? {
        log.info { "Finding SFTP access request for directory path: $directoryPath" }

        // Find an approved SFTP access request where the directory path exactly matches the main SFTP directory
        val matchingRequest = sftpAccessRequestRepository.findByStatusAndMainSFTPDirectory(
            status = SftpAccessRequestStatus.APPROVED,
            mainSFTPDirectory = directoryPath
        )

        log.info { "Found matching SFTP access request: $matchingRequest" }

        return matchingRequest
    }
}
