package com.multiplier.integration.adapter.api

import com.multiplier.integration.accounting.domain.DepartmentService
import com.multiplier.integration.accounting.domain.model.Department
import com.multiplier.orgmanagement.schema.Department.GrpcContractDepartment
import com.multiplier.orgmanagement.schema.Department.GrpcContractIds
import com.multiplier.orgmanagement.schema.Department.GrpcDepartmentStatus
import com.multiplier.orgmanagement.schema.DepartmentServiceGrpc.DepartmentServiceBlockingStub
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Component

@Component
class GrpcDepartmentServiceAdapter : DepartmentService {
    @GrpcClient("org-management-service")
    private lateinit var departmentServiceBlockingStub: DepartmentServiceBlockingStub

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun findDepartmentForContractId(contractId: Long): Department? {
        logger.info { "Getting department with contractId: $contractId" }
        return try {
            val response = departmentServiceBlockingStub.getDepartmentsByContractIds(
                GrpcContractIds.newBuilder().addContractIds(contractId).build()
            )
            response.contractDepartmentsList.find { it.department.status == GrpcDepartmentStatus.CREATED }?.toDomain()
        } catch (e: Exception) {
            logger.error(e) { "Failed to get department with contractId $contractId" }
            throw RuntimeException("Failed to get department with contractId $contractId", e)
        }
    }
}

fun GrpcContractDepartment.toDomain(): Department =
    Department(
        id = this.department.id,
        name = this.department.name
    )