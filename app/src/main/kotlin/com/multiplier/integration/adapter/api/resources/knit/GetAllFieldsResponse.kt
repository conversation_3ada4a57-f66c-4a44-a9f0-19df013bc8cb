package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable

@Serializable
data class GetAllFieldsResponse(
    val success: Boolean,
    val data: FieldDataList? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class FieldData(
    val fieldId: String? = null,
    val fieldFromApp: String? = null,
    val mappedKey: String? = null,
    val dataType: String? = null,
    val label: String? = null,
    val filters: List<FilterData>? = null,
    var isCustomField: Boolean? = false
)

@Serializable
data class FilterData(
    val key: String? = null,
    val value: String? = null,
)


@Serializable
data class FieldDataList(
    val default: List<FieldData>? = null,
    val unmapped: List<FieldData>? = null,
    val mapped: List<FieldData>? = null
)

@Serializable
data class GetFieldValuesResponse(
    val success: Boolean,
    val data: Field? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class Field(
    val fields: List<FieldValues>? = null
)

@Serializable
data class FieldValues(
    val id: String? = null,
    val label: String? = null,
)

@Serializable
data class AddCustomFieldMappingResponse(
    val success: Boolean,
    val data: String? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class AddCustomFieldMappingRequest(
    val fieldId: String,
    val fieldFromApp: String,
    val mappedKey: String,
    val dataType: String
)

@Serializable
data class DepartmentsListResponse(
    val success: Boolean,
    val data: DepartmentsData? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class DepartmentsData(
    val departments: List<Department>? = null
)

@Serializable
data class Department(
    val id: String,
    val name: String,
    val companyId: String
)
