package com.multiplier.integration.adapter.api

import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.CompanyPayableAdapter
import com.multiplier.integration.adapter.api.mapper.toDomain
import com.multiplier.payable.grpc.schema.GetCompanyPayableByIdRequest
import com.multiplier.payable.grpc.schema.GetInvoicesByIdsRequest
import com.multiplier.payable.grpc.schema.PayableServiceGrpc
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient

import org.springframework.stereotype.Component

@Component
class GrpcCompanyPayableAdapter: CompanyPayableAdapter {

    @GrpcClient("payable-service")
    private lateinit var payableServiceBlockingStub: PayableServiceGrpc.PayableServiceBlockingStub

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun getCompanyPayable(companyPayableId: Long): CompanyPayable {
        logger.info { "Getting company payable with ID: $companyPayableId" }
        try {
            val response = payableServiceBlockingStub.getCompanyPayableById(
                GetCompanyPayableByIdRequest.newBuilder()
                    .setCompanyPayableId(companyPayableId)
                    .build()
            )
            return response.companyPayable.toDomain()
        } catch (e: Exception) {
            logger.error(e) { "Failed to get company payable with id $companyPayableId" }
            throw RuntimeException("Failed to get company payable with id $companyPayableId", e)
        }
    }

    override fun getCompanyPayableIdsFromInvoiceIds(invoiceIds: List<Long>): List<Long> {
        logger.info { "Getting company payable ids with invoice ids: $invoiceIds" }
        try {
            val response = payableServiceBlockingStub.getInvoicesByIds(
                GetInvoicesByIdsRequest.newBuilder()
                    .addAllIds(invoiceIds)
                    .build()
            )

            return response.grpcInvoiceByIdList.map { it.grpcInvoice.companyPayableId }
        } catch (e: Exception) {
            logger.error(e) { "Failed to get company payable ids with invoice ids: $invoiceIds" }
            throw RuntimeException("Failed to get company payable ids with invoice ids $invoiceIds", e)
        }
    }
}