package com.multiplier.integration.adapter.api.resources.knit

import kotlinx.serialization.Serializable

@Serializable
data class UpdateEmployeeDetailsRequest(
    val presentAddress: Address? = null,
    val workAddress: WorkAddress? = null,
    val employment: Employment? = null,
    val employeeId: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    var workEmail: String? = null,
    var personalEmails: List<String>? = null,
    var startDate: String? = null,
    var birthDate: String? = null,
    var gender: String? = null,
    var employmentType: String? = null,
    var test: Boolean? = false
)

@Serializable
data class Address(
    val id: String? = null,
    val addressLine1: String? = null,
    val addressLine2: String? = null,
    val city: String? = null,
    val state: String? = null,
    val country: String? = null,
    val zipCode: String? = null
)

@Serializable
data class UpdateEmployeeDetailsResponse(
    val success: Boolean? = null,
    val data: ResponseData? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)
