package com.multiplier.integration.adapter.api.resources.financial.mapping

import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionCommonFields
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionLineItem
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionVendorCredit
import com.multiplier.integration.accounting.domain.mapping.AppliedToLines
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditAppliedToLines
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditItem
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditRequestBody
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

fun VendorCreditRequestBody.toDomain() =
    AccountingTransactionVendorCredit(
        id = this.model.id!!,
        createdAt = this.model.createdAt.isoDateToLocalDate(),
        modifiedAt = this.model.modifiedAt.isoDateToLocalDate(),
        totalAmount = this.model.totalAmount,
        vendor = this.model.vendor,
        commonFields =
            AccountingTransactionCommonFields(
                remoteId = this.model.remoteId,
                exchangeRate = this.model.exchangeRate,
                currency = this.model.currency,
                company = this.model.company,
                trackingCategories = this.model.trackingCategories ?: emptyList(),
            ),
        lineItems = this.model.lines?.map { it.toDomain() } ?: emptyList(),
        appliedToLines = this.model.appliedToLines?.map { it.toDomain() } ?: emptyList(),
    )

fun VendorCreditItem.toDomain() =
    AccountingTransactionLineItem(
        commonFields =
            AccountingTransactionCommonFields(
                remoteId = this.remoteId,
                exchangeRate = this.exchangeRate,
                company = this.company,
                trackingCategories = this.trackingCategories ?: emptyList(),
            ),
        id = this.id,
        description = this.description,
        totalAmount = this.netAmount,
        taxRate = this.taxRate,
        account = this.account,
    )

fun VendorCreditAppliedToLines.toDomain() =
    AppliedToLines(
        invoice = this.invoice,
        date = this.appliedDate.isoDateToLocalDate(),
        amount = this.appliedAmount.toDouble(),
    )

fun String?.isoDateToLocalDate(): LocalDate? {
    if (this == null) {
        return null
    }
    val formatter = DateTimeFormatter.ISO_ZONED_DATE_TIME
    return ZonedDateTime.parse(this, formatter).toLocalDate()
}