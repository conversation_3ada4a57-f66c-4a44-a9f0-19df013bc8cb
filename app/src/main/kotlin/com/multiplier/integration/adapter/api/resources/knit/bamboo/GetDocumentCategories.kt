package com.multiplier.integration.adapter.api.resources.knit.bamboo

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject


@Serializable
data class GetDocumentCategoriesResponse(
    val success: Boolean,
    val categories: List<Category>? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class Category(
    val id: String? = null,
    val name: String? = null,
    val canRenameCategory: Boolean? = null,
    val canDeleteCategory: Boolean? = null,
    val canUploadFiles: Boolean = true,
    val displayIfEmpty: Boolean? = null
)

@Serializable
data class ErrorResponse(
    val msg: String? = null
)

@Serializable
data class PassthroughResponse(
    val success: Boolean,
    val data: PassthroughResponseData? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class PassthroughResponseData(
    val response: PassthroughResponseBody
)

@Serializable
data class PassthroughResponseBody(
    val body: String,
    val headers: JsonObject
)