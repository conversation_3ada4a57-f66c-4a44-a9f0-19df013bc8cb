package com.multiplier.integration.adapter.api.mapper

import com.multiplier.grpc.common.currency.v2.Currency
import com.multiplier.integration.accounting.domain.MultiplierAccountingSystemIdentifier
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.common.Tax
import com.multiplier.integration.accounting.domain.model.*
import com.multiplier.integration.types.CurrencyCode
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.payable.common.schema.GrpcAmount
import com.multiplier.payable.common.schema.GrpcDate
import com.multiplier.payable.common.schema.GrpcTax
import com.multiplier.payable.grpc.schema.GrpcCompanyPayable
import com.multiplier.payable.grpc.schema.GrpcPayableStatus
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteLineItem
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteStatus
import com.multiplier.payable.grpc.schema.lineitem.GrpcLineItem
import java.time.LocalDate

fun GrpcCompanyPayable.toDomain(): CompanyPayable {
    val financialTransactionType = getTransactionType(this)
    return if (financialTransactionType == FinancialTransactionType.INVOICE) {
        this.toInvoice()
    } else {
        this.toCreditNote()
    }
}

private fun GrpcCompanyPayable.toInvoice(): Invoice =
    Invoice(
        id = this.grpcInvoice.id,
        companyPayableId = this.id,
        companyId = this.companyId,
        createdDate =
            this.grpcInvoice.createdDate
                .takeIf { grpcInvoice.hasCreatedDate() }
                ?.toDomain() ?: this.createdOn.toLocalDate(),
        status = this.status.toDomain(),
        lineItems = this.grpcInvoice.lineItemsList.map { it.toDomain() },
        dueDate = this.grpcInvoice.dueDate.toLocalDate(),
        amountPaid = this.grpcInvoice.amountPaid.toAmount(this.currency),
        amountDue = this.grpcInvoice.amountDue.toAmount(this.currency),
        totalAmount = this.grpcInvoice.totalAmount.toAmount(this.currency),
        reference = this.grpcInvoice.reference,
        multiplierExternalInvoiceId = MultiplierAccountingSystemIdentifier(this.grpcInvoice.invoiceId),
        multiplierExternalInvoiceNumber = MultiplierAccountingSystemIdentifier(this.grpcInvoice.invoiceNo),
        appliedCreditNoteIds = this.grpcInvoice.relatedCreditNoteIdsList,
    )

private fun GrpcCompanyPayable.toCreditNote(): CreditNote =
    CreditNote(
        id = this.creditNote.id,
        companyPayableId = this.id,
        companyId = this.companyId,
        createdDate =
            this.creditNote.createdDate
                .takeIf { this.creditNote.hasCreatedDate() }
                ?.toDomain()
                ?: this.createdOn.toLocalDate(),
        status = this.status.toDomain(),
        creditNoteStatus = this.creditNote.status.toDomain(),
        amountUnApplied = this.creditNote.amountUnapplied,
        amountApplied = this.creditNote.amountApplied,
        amountTotal = this.creditNote.amountTotal,
        currencyCode = CurrencyCode.valueOf(this.creditNote.currencyCode),
        lineItems = this.creditNote.itemsList.map { it.toDomain() },
        appliedToInvoiceIds = this.creditNote.appliedInvoicesList,
    )

private fun getTransactionType(grpcCompanyPayable: GrpcCompanyPayable): FinancialTransactionType =
    when {
        grpcCompanyPayable.hasGrpcInvoice() -> FinancialTransactionType.INVOICE
        grpcCompanyPayable.hasCreditNote() -> FinancialTransactionType.CREDIT_NOTE
        else -> throw IllegalArgumentException("Unknown transaction type")
    }

fun GrpcPayableStatus.toDomain(): CompanyPayableStatus = CompanyPayableStatus.valueOf(this.name)

fun GrpcLineItem.toDomain(): LineItem =
    LineItem(
        description = this.description,
        quantity = this.quantity,
        unitPrice = this.unitPrice.toDomain(),
        tax = this.tax.toDomain(),
        contractId = this.contractId,
        memberName = this.memberName,
        lineItemType = this.itemType.name,
        amountInBaseCurrency = this.amountInBaseCurrency.toDomain(),
        countryName = this.countryName,
        grossAmount = this.grossAmount.toDomain(),
        startInvoiceCycleDate = startInvoiceCycleDate?.takeIf { hasStartInvoiceCycleDate() }?.toDomain(),
        endInvoiceCycleDate = endInvoiceCycleDate?.takeIf { hasEndInvoiceCycleDate() }?.toDomain(),
    )

fun GrpcCreditNoteLineItem.toDomain(): LineItem =
    LineItem(
        description = this.description,
        quantity = this.quantity,
        unitPrice = this.unitAmount.toDomain(),
        tax = this.tax.toDomain(),
        contractId = this.contractId,
        memberName = this.memberName,
        lineItemType = this.lineItemType.name,
        amountInBaseCurrency = this.amountInBaseCurrency.toDomain(),
        countryName = this.countryName,
        grossAmount = this.grossAmount.toDomain(),
        startInvoiceCycleDate = startInvoiceCycleDate?.takeIf { hasStartInvoiceCycleDate() }?.toDomain(),
        endInvoiceCycleDate = endInvoiceCycleDate?.takeIf { hasEndInvoiceCycleDate() }?.toDomain(),
    )

fun GrpcAmount.toDomain(): Amount =
    Amount(
        value = this.value,
        currencyCode = this.currencyCode.toDomain(),
    )

fun Currency.CurrencyCode.toDomain(): CurrencyCode = CurrencyCode.valueOf(this.name.removePrefix("CURRENCY_CODE_"))

fun GrpcTax.toDomain(): Tax =
    Tax(
        taxRate = this.taxRate,
        taxType = this.taxType,
        amount = this.taxAmount.toDomain(),
    )

fun GrpcDate.toDomain(): LocalDate = LocalDate.of(this.year, this.month, this.day)

fun GrpcCreditNoteStatus.toDomain(): CreditNoteStatus =
    when (this) {
        GrpcCreditNoteStatus.CREDIT_NOTE_STATUS_FULLY_APPLIED -> CreditNoteStatus.FULLY_APPLIED
        else -> throw IllegalArgumentException("Unknown credit note status")
    }

private fun Double.toAmount(currency: String): Amount =
    Amount(
        value = this,
        currencyCode = CurrencyCode.valueOf(currency.removePrefix("CURRENCY_CODE_")),
    )
