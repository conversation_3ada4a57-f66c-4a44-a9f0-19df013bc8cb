package com.multiplier.integration.adapter.api

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.common.exception.toSystemException
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.trinet.CreateDepartmentRequest
import com.multiplier.integration.adapter.api.resources.trinet.CreateDepartmentResponse
import com.multiplier.integration.adapter.api.resources.trinet.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.trinet.CreateEmployeeResponseModel
import com.multiplier.integration.adapter.api.resources.trinet.CreateLocationRequest
import com.multiplier.integration.adapter.api.resources.trinet.CreateLocationResponse
import com.multiplier.integration.adapter.api.resources.trinet.GeneralTriNetResponse
import com.multiplier.integration.adapter.api.resources.trinet.GetAccessTokenResponse
import com.multiplier.integration.adapter.api.resources.trinet.GetDepartmentsListResponse
import com.multiplier.integration.adapter.api.resources.trinet.GetLocationByNameResponse
import com.multiplier.integration.adapter.api.resources.trinet.TerminateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.trinet.TerminateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeAddressRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeContactRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeNameRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeTitleRequest
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.adapter.model.knit.EmployeeDetailData
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonNamingStrategy
import mu.KotlinLogging
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.IOException
import java.util.*
import java.util.concurrent.TimeUnit

interface TriNetAPIAdapter {
    fun getAccessToken(username: String, password: String): GetAccessTokenResponse?

    fun createEmployee(companyIntegration: JpaCompanyIntegration, platformId: Long, documentData: CreateEmployeeRequest): CreateEmployeeResponseModel?

    fun updateEmployeeName(companyIntegration: JpaCompanyIntegration, employeeId: String, platformId: Long, updateNameRequest: UpdateEmployeeNameRequest): GeneralTriNetResponse?
    fun updateEmployeeContacts(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateEmployeeContactRequest: UpdateEmployeeContactRequest
    ): GeneralTriNetResponse?

    fun updateEmployeeTitle(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateTitleRequest: UpdateEmployeeTitleRequest
    ): GeneralTriNetResponse?

    fun getDepartmentsList(companyIntegration: JpaCompanyIntegration): GetDepartmentsListResponse?

    fun terminateEmployee(companyIntegration: JpaCompanyIntegration, externalEmployeeId: String, data: TerminateEmployeeRequest): TerminateEmployeeResponse
    fun getLocationByName(companyIntegration: JpaCompanyIntegration, name: String): Any?
    fun createLocation(
        companyIntegration: JpaCompanyIntegration,
        locationData: CreateLocationRequest
    ): CreateLocationResponse?

    fun updateEmployeeAddress(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateEmployeeAddressRequest: UpdateEmployeeAddressRequest
    ): GeneralTriNetResponse?

    fun createDepartment(
        companyIntegration: JpaCompanyIntegration,
        locationData: CreateDepartmentRequest
    ): CreateDepartmentResponse?
}

@Service
class DefaultTriNetAPIAdapter(
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    ): TriNetAPIAdapter {
    @Value("\${platform.trinet.api-url}")
    private lateinit var baseApiUrl: String
  
    private val log = KotlinLogging.logger {}

    private var defaultClient: OkHttpClient? = null

    companion object {
        var companyDepartmentCode = hashMapOf<String, String>()
        var companyAccessToken = hashMapOf<String, GetAccessTokenResponse?>()
        var shouldIgnoreTokenCheck: Boolean = false

        fun isTokenExpired(companyId: String): Boolean {
            val tokenAccquiredTime = companyAccessToken.get(companyId)?.tokenAcquiredTime
            val tokenExpiresIn = companyAccessToken.get(companyId)?.expiresIn?.toLong()

            if (tokenAccquiredTime == null || tokenExpiresIn == null) return true
            val currentTime = System.currentTimeMillis() / 1000;
            return currentTime - tokenAccquiredTime > tokenExpiresIn
        }

        fun setIgnoreTokenCheck(shouldIgnoreTokenCheck: Boolean) {
            this.shouldIgnoreTokenCheck = shouldIgnoreTokenCheck
        }
    }

    @OptIn(ExperimentalSerializationApi::class)
    val json = Json {
        namingStrategy = JsonNamingStrategy.SnakeCase
        ignoreUnknownKeys = true  // This will ignore any unknown keys in the JSON
    }

    @OptIn(ExperimentalSerializationApi::class)
    val jsonEncoder = Json {
        ignoreUnknownKeys = true  // This will ignore any unknown keys in the JSON
        explicitNulls = true
    }

    val jsonDecoder = Json {
        ignoreUnknownKeys = true  // This will ignore any unknown keys in the JSON
    }

    fun setDefaultHttpClient(client: OkHttpClient?) {
        this.defaultClient = client
    }

    override fun getAccessToken(username: String, password: String): GetAccessTokenResponse? {
        val url = "$baseApiUrl/oauth/accesstoken?grant_type=client_credentials"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()
            
        val basicAuthToken = Base64.getEncoder().encodeToString("$username:$password".toByteArray())
        val request = Request.Builder()
            .url(url)
            .get()
            .addHeader("accept", "application/json")
            .addHeader("Authorization", "Basic $basicAuthToken")
            .build()

        return try {
            val response = client.newCall(request).execute()
            log.info { "Successfully getting token positions from TriNet" }

            val httpStatusCode = response.code

            val responseBody = response.body?.string()
            log.info { "Response Body: $responseBody" }

            if (response.isSuccessful) {
                json.decodeFromString<GetAccessTokenResponse>(responseBody ?: "")
                    .copy(responseCode = httpStatusCode, tokenAcquiredTime = System.currentTimeMillis() / 1000)
            } else {
                log.error { "Error fetching token: $httpStatusCode, ${response.message}" + responseBody.toString() }
                null
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while fetching token" }
            null
        }
    }

    override fun getDepartmentsList(companyIntegration: JpaCompanyIntegration): GetDepartmentsListResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken
        
        val url = "$baseApiUrl/v1/company/$companyId/departments"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val request = Request.Builder()
            .url(url)
            .get()
            .addHeader("accept", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            val response = client.newCall(request).execute()
            log.info { "Successfully getting departments from TriNet" }

            val httpStatusCode = response.code

            val responseBody = response.body?.string()
            log.info { "Response Body: $responseBody" }

            if (response.isSuccessful) {
                jsonDecoder.decodeFromString<GetDepartmentsListResponse>(responseBody ?: "")
            } else {
                log.error { "Error fetching departments: $httpStatusCode, ${response.message}" + responseBody.toString() }
                null
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while fetching departments" }
            null
        }
    }

    override fun getLocationByName(companyIntegration: JpaCompanyIntegration, name: String): GetLocationByNameResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken

        val url = "$baseApiUrl/v1/company/$companyId/locations?locationName=${name}"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val request = Request.Builder()
            .url(url)
            .get()
            .addHeader("accept", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            val response = client.newCall(request).execute()
            log.info { "Successfully getting locations from TriNet" }

            val httpStatusCode = response.code

            val responseBody = response.body?.string()
            log.info { "Response Body: $responseBody" }

            if (response.isSuccessful) {
                jsonDecoder.decodeFromString<GetLocationByNameResponse>(responseBody ?: "")
            } else if (response.code == 404) { // location not found
                log.error { "Location $name not found" + responseBody.toString() }
                null
            } else {
                log.error { "Error fetching locations: $httpStatusCode, ${response.message}" + responseBody.toString() }
                throw IntegrationIllegalStateException("Could not fetch locations from TriNet")
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while fetching locations" }
            null
        }
    }

    override fun terminateEmployee(
        companyIntegration: JpaCompanyIntegration,
        externalEmployeeId: String,
        data: TerminateEmployeeRequest,
    ): TerminateEmployeeResponse {
        val url = "$baseApiUrl/v1/termination/${companyIntegration.externalCompanyId}/$externalEmployeeId/TAsubmit"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()
        val companyId = companyIntegration.externalCompanyId!!
        getTokenOrRefetch(companyId, companyIntegration.accountName!!, companyIntegration.accountToken)

        val body = jsonEncoder.encodeToString(data).toRequestBody("application/json".toMediaType())

        val request = Request.Builder()
            .url(url)
            .post(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info("[TriNet TerminateEmployee] Getting response from calling $url on TriNet")
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info("[TriNet TerminateEmployee] Response Body: $responseBody")

                if (response.isSuccessful) {
                    TerminateEmployeeResponse(
                        success = true,
                        responseCode = httpStatusCode
                    )
                } else {
                    log.error("[TriNet TerminateEmployee] Error terminating new employee: $httpStatusCode, ${response.message}" + responseBody.toString())
                    TerminateEmployeeResponse(
                        success = false,
                        error = ErrorResponse(
                            msg = response.message
                        ),
                        responseCode = httpStatusCode
                    )
                }
            }
        } catch (e: IOException) {
            log.error("[TriNet TerminateEmployee] Exception terminating new employee", e)
            TerminateEmployeeResponse(
                success = false,
                error = ErrorResponse(
                    msg = e.message
                )
            )
        }
    }

    override fun createEmployee(companyIntegration: JpaCompanyIntegration, platformId: Long, documentData: CreateEmployeeRequest): CreateEmployeeResponseModel? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken
        val url = "$baseApiUrl/v1/hire/$companyId/international-worker"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(documentData).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .post(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    jsonDecoder.decodeFromString<CreateEmployeeResponseModel>(responseBody ?: "")
                } else {
                    log.error { "Error creating new employee: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception creating new employee" }
            null
        }
    }

    override fun createLocation(companyIntegration: JpaCompanyIntegration, locationData: CreateLocationRequest): CreateLocationResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken
        val url = "$baseApiUrl/v1/manage-company/$companyId/locations"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(locationData).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .post(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    jsonDecoder.decodeFromString<CreateLocationResponse>(responseBody ?: "")
                } else {
                    log.error { "Error creating new location: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception creating new location" }
            null
        }
    }

    override fun createDepartment(companyIntegration: JpaCompanyIntegration, request: CreateDepartmentRequest): CreateDepartmentResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken
        val url = "$baseApiUrl/v1/manage-company/$companyId/departments"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(request).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .post(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    jsonDecoder.decodeFromString<CreateDepartmentResponse>(responseBody ?: "")
                } else {
                    log.error { "Error creating new location: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception creating new department" }
            null
        }
    }

    @Synchronized
    fun getOrFetchDepartmentCode(companyIntegration: JpaCompanyIntegration): String? {
        val deptId = companyDepartmentCode.get(companyIntegration.externalCompanyId!!)
        return if (deptId != null) {
            deptId
        } else {
            log.info("[TriNet] Department not found in cache, fetching department for company ${companyIntegration.companyId}")
            val departmentsList = getDepartmentsList(companyIntegration)!!.data
            val multiplierDepartment = departmentsList?.firstOrNull { it.deptCode == "MPINT" }
            if (multiplierDepartment != null) {
                companyDepartmentCode.set(companyIntegration.externalCompanyId!!, multiplierDepartment.deptId!!)
                return multiplierDepartment.deptId!!
            } else {
                return null
            }
        }
    }

    fun getTokenOrRefetch(companyId: String, username: String, password: String) {
        if (shouldIgnoreTokenCheck) return

        if (companyAccessToken.get(companyId) == null || isTokenExpired(companyId)) {
            val tokenResponse = getAccessToken(username, password)
            companyAccessToken[companyId] = tokenResponse
        }
    }

    override fun updateEmployeeName(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateNameRequest: UpdateEmployeeNameRequest,
    ): GeneralTriNetResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken

        val url = "$baseApiUrl/v1/identity/$companyId/$employeeId/names"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)


        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(updateNameRequest).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .put(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    json.decodeFromString<GeneralTriNetResponse>(responseBody ?: "")
                } else {
                    log.error { "Error updating employee name: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while updating employee name" }
            null
        }
    }

    override fun updateEmployeeContacts(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateEmployeeContactRequest: UpdateEmployeeContactRequest,
    ): GeneralTriNetResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken

        val url = "$baseApiUrl/v1/identity/$companyId/$employeeId/contacts"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)


        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(updateEmployeeContactRequest).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .put(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    json.decodeFromString<GeneralTriNetResponse>(responseBody ?: "")
                } else {
                    log.error { "Error updating employee contacts: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while updating employee contacts" }
            null
        }
    }

    override fun updateEmployeeTitle(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateTitleRequest: UpdateEmployeeTitleRequest,
    ): GeneralTriNetResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken

        val url = "$baseApiUrl/v1/manage-employee/$companyId/$employeeId/jobs"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(updateTitleRequest).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .put(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    json.decodeFromString<GeneralTriNetResponse>(responseBody ?: "")
                } else {
                    log.error { "Error updating employee title: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while updating employee title" }
            null
        }
    }

    override fun updateEmployeeAddress(
        companyIntegration: JpaCompanyIntegration,
        employeeId: String,
        platformId: Long,
        updateEmployeeAddressRequest: UpdateEmployeeAddressRequest,
    ): GeneralTriNetResponse? {
        val companyId = companyIntegration.externalCompanyId
        val username = companyIntegration.accountName
        val password = companyIntegration.accountToken

        val url = "$baseApiUrl/v2/identity/$companyId/$employeeId/addresses"
        val client = defaultClient ?: OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.MINUTES) // Increase connect timeout to 1 minute
            .writeTimeout(1, TimeUnit.MINUTES) // Increase write timeout to 1 minute
            .readTimeout(1, TimeUnit.MINUTES) // Increase read timeout to 1 minute
            .build()

        getTokenOrRefetch(companyId!!, username!!, password)

        val mediaType = "application/json".toMediaType()

        val body = jsonEncoder.encodeToString(updateEmployeeAddressRequest).toRequestBody(mediaType)

        val request = Request.Builder()
            .url(url)
            .put(body)
            .addHeader("accept", "application/json")
            .addHeader("content-type", "application/json")
            .addHeader("Authorization", "Bearer ${companyAccessToken[companyId]?.accessToken}")
            .build()

        return try {
            client.newCall(request).execute().use { response ->
                log.info { "Getting response from calling $url on TriNet" }
                val httpStatusCode = response.code
                val responseBody = response.body?.string()
                log.info { "Response Body: $responseBody" }

                if (response.isSuccessful) {
                    json.decodeFromString<GeneralTriNetResponse>(responseBody ?: "")
                } else {
                    log.error { "Error updating employee address: $httpStatusCode, ${response.message}" + responseBody.toString()}
                    null
                }
            }
        } catch (e: IOException) {
            log.error(e) { "Exception while updating employee address" }
            null
        }
    }

    fun updateInternalTriNetEmployeeData(
        employee: JpaPlatformEmployeeData,
        newTriNetData: EmployeeDetailData?,
        externalEmployeeId: String
    ) {
        log.info("[TriNetAdapter] Updating internal employee data for externalEmployeeId=$externalEmployeeId")
        val objectMapper = jacksonObjectMapper()
        val employeeData = objectMapper.readValue(employee.employeeData, EmployeeData::class.java)
        val newEmployeeData = employeeData.copy(employeeDetailData = newTriNetData)
        try {
            employee.employeeData = objectMapper.writeValueAsString(newEmployeeData)
            platformEmployeeDataRepository.save(employee)
            log.info("[TriNetAdapter] Finished update internal employee data for externalEmployeeId=$externalEmployeeId")
        } catch (e: Exception) {
            log.error(
                "[TriNetAdapter] Error updating internal employee data for externalEmployeeId=$externalEmployeeId",
            )
            throw CustomerErrorCode.INTERNAL_ERROR.toSystemException(e.message, e)
        }
    }
}