package com.multiplier.integration.adapter.model

data class UpsertExpenseRequest(
    val companyId: Long,
    val inputs: List<ExpenseItem>
)

data class ExpenseItem(
    var externalExpenseId: String?,
    var employeeId: String?,
    var employeeName: String?,
    var expenseTitle: String?,
    var expenseType: String?,
    var expenseDate: String?,
    var expenseCurrencyCode: String?,
    var expenseAmount: String?,
    var expenseDescription: String?,
)

data class UpsertExpenseResponse(
    val success: Boolean,
    val message: String? = null
)

