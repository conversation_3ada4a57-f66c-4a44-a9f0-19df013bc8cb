package com.multiplier.integration.adapter.api.resources.knit.bamboo

import kotlinx.serialization.Serializable

@Serializable
data class BambooAddTimeOffResponse(
    val success: Boolean,
    val data: BambooTimeOffResponse? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int
)

@Serializable
data class BambooPassthroughRequest(
    val method: String,
    val path: String,
    val body: String? = null,
    val headers: Map<String, String>? = null,
)

@Serializable
data class BambooPassthroughRequestHeaders(
    val accept: String = "application/json",
)

@Serializable
data class BambooTimeOffRequest(
    val status: String,
    val start: String,
    val end: String,
    val timeOffTypeId: Int,
    val amount: Double,
)

@Serializable
data class BambooTimeOffResponse(
    val id: String,
    val employeeId: String,
    val start: String,
    val end: String,
    val created: String,
    val status: TimeOffStatus,
    val name: String,
    val type: TimeOffType?,
    val amount: TimeOffAmount?,
    val notes: List<Map<String, String>>?,
    val dates: Map<String, String>?,
    val comments: List<TimeOffComment>?,
    val approvers: List<TimeOffApprover>?,
    val actions: TimeOffActions?,
    val policyType: String?,
    val usedYearToDate: Int?,
    val balanceOnDateOfRequest: Int?
)

@Serializable
data class TimeOffStatus(
    val status: String,
    val lastChanged: String,
    val lastChangedByUserId: String
)

@Serializable
data class TimeOffType(
    val id: String,
    val name: String
)

@Serializable
data class TimeOffAmount(
    val unit: String,
    val amount: String
)

@Serializable
data class TimeOffComment(
    val employeeId: String,
    val comment: String,
    val commentDate: String,
    val commenterName: String
)

@Serializable
data class TimeOffApprover(
    val userId: String,
    val displayName: String,
    val employeeId: String,
    val photoUrl: String
)

@Serializable
data class TimeOffActions(
    val view: Boolean,
    val edit: Boolean,
    val cancel: Boolean,
    val approve: Boolean,
    val deny: Boolean,
    val bypass: Boolean
)

@Serializable
data class ApproveTimeOffRequest(
    val status: String,
    val note: String
)

@Serializable
data class BambooApproveTimeOffResponse(
    val success: Boolean,
    val error: ErrorResponse? = null,
    val responseCode: Int?
)