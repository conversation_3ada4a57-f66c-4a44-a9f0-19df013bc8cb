package com.multiplier.integration.adapter.model

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.currency.Currency
import com.multiplier.integration.utils.toMergeDevType
import com.multiplier.integration.utils.toMultiplierType
import com.multiplier.integration.utils.toOffsetDateTime
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import dev.merge.client.hris.apis.EmployeesApi
import dev.merge.client.hris.models.Employee
import dev.merge.client.hris.models.EmployeeEndpointRequest
import dev.merge.client.hris.models.EmployeeRequest
import dev.merge.client.hris.models.EmploymentStatusEnum
import java.time.LocalDateTime

data class EmployeeData(
    val firstName: String,
    val lastName: String,
    val fullName: String,
    val username: String,
    val personalEmail: String,
    val workEmail: String?,
    val id: String,
    val phoneNumber: String,
    val gender: Gender,
    val maritalStatus: Member.MaritalStatus,
    val dateOfBirth: LocalDateTime?,
    val startDate: LocalDateTime?,
    val endDate: LocalDateTime?,
    val employmentActive: Boolean,
    val position: String,
    val contactDetails: ContactDetails?
)

data class BankData(
    val bankName: String,
    val accountNumber: String,
    val accountType: String,
)

data class CompensationData(
    val amount: Double?,
    val currency: Currency.CurrencyCode?,
    val frequency: CompensationOuterClass.RateFrequency?
)

data class BasicDetails(
    val firstName: String,
    val lastName: String,
    val fullLegalName: String,
    val gender: Gender,
)

data class ContactDetails(
    val phoneNumber: String?,
    val addressLine1: String?,
    val addressLine2: String?,
    val city: String?,
    val state: String?,
    val zipCode: String?,
    val countryCode: CountryCode?,
    val countryName: String?,
)

fun Employee.toEmployeeData() =
    EmployeeData(
        id = this.employeeNumber ?: "",
        firstName = this.firstName ?: "",
        lastName = this.lastName ?: "",
        fullName = this.displayFullName ?: "",
        username = this.username ?: "",
        personalEmail = this.personalEmail ?: "",
        phoneNumber = this.mobilePhoneNumber ?: "",
        gender = this.gender.toMultiplierType(),
        maritalStatus = this.maritalStatus.toMultiplierType(),
        dateOfBirth = this.dateOfBirth?.toLocalDateTime(),
        startDate = this.startDate?.toLocalDateTime(),
        endDate = this.terminationDate?.toLocalDateTime(),
        workEmail = this.workEmail,
        employmentActive =
            this.employmentStatus?.let {
                when (it) {
                    EmploymentStatusEnum.ACTIVE -> true
                    else -> false
                }
            }
                ?: false,
        position = "",
        contactDetails = null
    )

fun EmployeeData.toEmployeeRequest() =
    EmployeesApi.EmployeesCreateRequest(
        employeeEndpointRequest =
            EmployeeEndpointRequest(
                model =
                    EmployeeRequest(
                        firstName = this.firstName,
                        lastName = this.lastName,
                        displayFullName = this.fullName,
                        username = this.username,
                        personalEmail = this.personalEmail,
                        workEmail = this.workEmail,
                        employeeNumber = this.id,
                        mobilePhoneNumber = this.phoneNumber,
                        gender = this.gender.toMergeDevType(),
                        maritalStatus = this.maritalStatus.toMergeDevType(),
                        dateOfBirth = this.dateOfBirth?.toOffsetDateTime(),
                        startDate = this.startDate?.toOffsetDateTime(),
                        terminationDate = this.endDate?.toOffsetDateTime(),
                        employmentStatus =
                            if (this.employmentActive) EmploymentStatusEnum.ACTIVE
                            else EmploymentStatusEnum.INACTIVE),
            ))
