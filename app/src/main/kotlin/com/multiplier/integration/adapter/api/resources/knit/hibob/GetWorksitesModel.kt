package com.multiplier.integration.adapter.api.resources.knit.hibob

import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaAddressDetails
import kotlinx.serialization.Serializable

@Serializable
data class GetWorksitesResponse(
    val success: Boolean? = null,
    val data: WorkSiteResponse? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class KekaGetAllLocationsResponse(
    val succeeded: Boolean? = null,
    val message: String? = null,
    val errors: List<String?>? = null,
    val error: ErrorResponse? = null,
    val data: List<KekaLocationResponse>? = null,
    val responseCode: Int? = null
)
@Serializable
data class KekaLocationResponse(
    val id: String? = null,
    val name: String? = null,
    val description: String? = null,
    val address: KekaAddressDetails? = null,
)

@Serializable
data class WorkSiteResponse(
    val response: WorkSiteResponseBody? = null,
    val responseJson: WorkSiteResponseBodySerialized? = null
)

@Serializable
data class WorkSiteResponseBody(
    val body: String? = null,
    val headers: Map<String, String>? = null
)

@Serializable
data class WorkSiteResponseBodySerialized(
    val body: WorkSiteData? = null
)

@Serializable
data class WorkSiteData(
    val name: String? = null,
    val values: List<WorkSite>? = null,
    val items: List<WorkSite>? = null
)

@Serializable
data class WorkSite(
    val id: Int? = null,
    val value: String? = null,
    val name: String? = null,
    val archived: Boolean? = null,
    val children: List<WorkSite>? = null
)

@Serializable
data class PassthroughRequest(
    val method: String,
    val path: String,
    val body: String? = null,
    val headers: Map<String, String>? = null,
)