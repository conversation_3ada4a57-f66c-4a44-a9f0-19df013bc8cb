package com.multiplier.integration.adapter.api

import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.adapter.api.resources.financial.*
import com.multiplier.integration.adapter.api.resources.financial.payment.MergeDevPaymentCreateRequest

interface FinancialIntegration {
    fun createLinkToken(mergeDevLinkTokenRequest: MergeDevLinkTokenRequest): MergeDevResponse
    fun retrieveAccountToken(mergeDevAccountTokenRetrieveRequest: MergeDevAccountTokenRetrieveRequest): MergeDevResponse
    fun createInvoice(mergeDevInvoiceRequest: MergeDevInvoiceRequest): MergeDevResponse
    fun updateInvoice(mergeDevInvoiceRequest: MergeDevInvoiceRequest, id: String): MergeDevResponse
    fun getTransactionAmounts(
        externalId: String,
        accountToken: String
    ): ExternalCompanyPayableAmount
    fun createPayment(
        mergeDevPaymentCreateRequest: MergeDevPaymentCreateRequest,
        accountToken: String,
    ): MergeDevResponse
    fun createVendorCredit(mergeDevVendorCreditRequest: MergeDevVendorCreditRequest): MergeDevResponse
}