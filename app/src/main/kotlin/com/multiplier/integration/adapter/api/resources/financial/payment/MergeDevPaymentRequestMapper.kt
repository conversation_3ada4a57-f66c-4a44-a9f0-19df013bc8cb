package com.multiplier.integration.adapter.api.resources.financial.payment

import com.merge.api.resources.accounting.types.*
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneId

@Component
class MergeDevPaymentRequestMapper {

    fun mapPaymentRequestMapper(
        mergeDevPaymentCreateRequest: MergeDevPaymentCreateRequest
    ): PaymentRequest {
        val paymentRequestBuilder = PaymentRequest.builder()
            .transactionDate(OffsetDateTime.of(mergeDevPaymentCreateRequest.createdDate,
                ZoneId.systemDefault().rules.getOffset(Instant.now()))
            )
            .account(PaymentRequestAccount.of(mergeDevPaymentCreateRequest.account))
            .contact(PaymentRequestContact.of(mergeDevPaymentCreateRequest.contact))
            .currency(PaymentRequestCurrency.of(CurrencyEnum.valueOf(mergeDevPaymentCreateRequest.amount.currencyCode.toString())))
            .exchangeRate("1") //we are creating payment in the same currency as invoice, hence the currency of the contact. Hence the exchange rate can be 1.
            .totalAmount(mergeDevPaymentCreateRequest.amount.value)

        mergeDevPaymentCreateRequest.appliedInvoice.let {
            paymentRequestBuilder.appliedToLines(
                listOf(
                    PaymentRequestAppliedToLinesItem.of(
                        PaymentLineItemRequest.builder()
                            .appliedAmount(mergeDevPaymentCreateRequest.amount.value.toString())
                            .relatedObjectId(it.externalInvoiceId)
                            .relatedObjectType("INVOICE")
                            .build()
                    )
                )
            )
        }

        return paymentRequestBuilder.build()
    }

}
