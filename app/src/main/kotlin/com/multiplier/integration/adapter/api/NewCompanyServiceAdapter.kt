package com.multiplier.integration.adapter.api

import com.multiplier.company.schema.grpc.*
import com.multiplier.company.schema.grpc.CompanyOuterClass.GetLegalEntitiesRequest
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface NewCompanyServiceAdapter {

    fun getCompanyById(companyId: Long): CompanyOuterClass.Company

    fun getCompanyAdmins(companyId: Long): CompanyOuterClass.CompanyUsers

    fun getLegalEntities(companyId: Long): List<LegalEntity>
}

@Service
class NewCompanyServiceClient : NewCompanyServiceAdapter {

    @GrpcClient("company-service") private lateinit var companyStub: CompanyServiceGrpc.CompanyServiceBlockingStub

    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun getCompanyById(companyId: Long): CompanyOuterClass.Company {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC getCompanyById request: $companyId")
            val request = CompanyOuterClass.GetCompanyRequest.newBuilder().setId(companyId).build()
            companyStub.getCompany(request).also {
                log.info("getCompanyById successfully: $companyId")
            }
        }
    }

    override fun getCompanyAdmins(companyId: Long): CompanyOuterClass.CompanyUsers {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC getCompanyAdmins request: $companyId")
            val request = CompanyOuterClass.GetCompanyRequest.newBuilder().setId(companyId).build()
            companyStub.getCompanyAdmins(request).also {
                log.info("getCompanyAdmins successfully: $companyId")
            }
        }
    }

    override fun getLegalEntities(companyId: Long): List<LegalEntity> {
        return DownstreamServiceUtils.execute {
            log.info("Calling gRPC getLegalEntities request: $companyId")
            val request = GetLegalEntitiesRequest.newBuilder().addAllCompanyIds(listOf(companyId)).build()
            companyStub.getLegalEntities(request).entitiesList.also {
                log.info("getLegalEntities successfully: $companyId")
            }
        }
    }
}