package com.multiplier.integration.adapter.api.resources.knit.successfactors

import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import kotlinx.serialization.Serializable

@Serializable
data class GetSAPBusinessUnitsResponse(
    val success: Boolean? = null,
    val data: SAPBusinessUnitsResponse? = null,
    val error: ErrorResponse? = null,
    val responseCode: Int? = null
)

@Serializable
data class SAPBusinessUnitsResponse(
    val response: SAPBusinessUnitsResponseBody? = null,
)

@Serializable
data class SAPBusinessUnitsResponseBody(
    val body: SAPBusinessUnitsNestedResult? = null,
)

@Serializable
data class SAPBusinessUnitsNestedResult(
    val d: SAPBusinessUnitsResponseData? = null,
)

@Serializable
data class SAPBusinessUnitsResponseData(
    val results: List<BusinessUnitData>? = null,
)

@Serializable
data class BusinessUnitData(
    val name: String? = null,
    val externalCode: String? = null,
    val description: String? = null,
)
