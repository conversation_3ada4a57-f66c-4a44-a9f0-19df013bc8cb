import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.knit.EmployeeLocation

data class EmployeeRequest(
        val firstName: String?,
        val lastName: String?,
        val workEmail: String?,
        val personalEmails: List<String>?,
        val employment: Employment?,
        val workAddress: EmployeeLocation?
)

data class Employment(
        val positionId: String,
        val designation: String,
        val workShiftId: String
)

fun employeeRequestFromEmployeeData(
        employeeData: com.multiplier.integration.adapter.model.EmployeeData
): EmployeeRequest {
    return EmployeeRequest(
            firstName = employeeData.firstName,
            lastName = employeeData.lastName,
            workEmail = employeeData.workEmail ?: "",
            personalEmails = listOf(employeeData.personalEmail),
            employment = Employment(
                    positionId = "",
                    designation = employeeData.position,
                    workShiftId = ""
            ),
            workAddress = EmployeeLocation(
                    addressLine1 = employeeData.contactDetails?.addressLine1,
                    addressLine2 = employeeData.contactDetails?.addressLine2,
                    city = employeeData.contactDetails?.city,
                    state = employeeData.contactDetails?.state,
                    country = employeeData.contactDetails?.countryName,
                    zipCode = employeeData.contactDetails?.zipCode,
                    addressType = ""
            )
    )
}

fun employeeRequestFromBasicDetails(
        basicDetails: BasicDetails
): EmployeeRequest {
    return EmployeeRequest(
            firstName = basicDetails.firstName,
            lastName = basicDetails.lastName,
            employment = null,
            personalEmails = null,
            workEmail = null,
            workAddress = null
    )
}

fun employeeRequestFromContactDetails(
        contactDetails: ContactDetails
): EmployeeRequest {
    return EmployeeRequest(
            firstName = null,
            lastName = null,
            employment = null,
            personalEmails = null,
            workEmail = null,
            workAddress =  EmployeeLocation(
                    addressLine1 = contactDetails.addressLine1,
                    addressLine2 = contactDetails.addressLine2,
                    city = contactDetails.city,
                    state = contactDetails.state,
                    country = contactDetails.countryName,
                    zipCode = contactDetails.zipCode,
                    addressType = ""
            )
    )
}

data class CreateEmployeeResponse(
        val success: Boolean,
        val employeeId: Long? = null,
        val errorMessage: String? = null
)

data class UpdateEmployeeResponse(
        val success: Boolean,
        val errorMessage: String? = null
)