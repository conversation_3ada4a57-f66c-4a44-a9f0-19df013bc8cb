package com.multiplier.integration.adapter.api

import com.multiplier.common.exception.toSystemException
import com.multiplier.expense.schema.BulkCreateExpensesRequest
import com.multiplier.expense.schema.BulkCreateExpensesResponse
import com.multiplier.expense.schema.BulkDeleteExpensesRequest
import com.multiplier.expense.schema.ExpenseServiceGrpc
import com.multiplier.expense.schema.GrpcBulkDeleteExpensesResponse
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

interface ExpenseServiceAdapter {
    fun bulkCreateExpensesNonTransactional(request: BulkCreateExpensesRequest): BulkCreateExpensesResponse
    fun bulkUpsertExpensesNonTransactional(request: BulkCreateExpensesRequest): BulkCreateExpensesResponse
    fun bulkRevokeExpensesNonTransactional(request: BulkDeleteExpensesRequest): GrpcBulkDeleteExpensesResponse

}

@Service
class DefaultExpenseServiceAdapter : ExpenseServiceAdapter {
    @GrpcClient("expense-service") private lateinit var expenseStub: ExpenseServiceGrpc.ExpenseServiceBlockingStub
    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun bulkCreateExpensesNonTransactional(request: BulkCreateExpensesRequest): BulkCreateExpensesResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to create bulk expenses with request: $request")
            val result = expenseStub.bulkCreateExpensesNonTransactional(request)
            log.info("Successfully created bulk expenses with result: $result")
            result
        }
    }

    override fun bulkUpsertExpensesNonTransactional(request: BulkCreateExpensesRequest): BulkCreateExpensesResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to upsert bulk expenses with request: $request")
            val result = expenseStub.bulkUpsertExpensesNonTransactional(request)
            log.info("Successfully upsert bulk expenses with result: $result")
            result
        }
    }

    override fun bulkRevokeExpensesNonTransactional(request: BulkDeleteExpensesRequest): GrpcBulkDeleteExpensesResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to bulk delete (revoke) expenses with request: $request")
            val result = expenseStub.bulkDeleteExpenses(request)
            log.info("Successfully bulk delete (revoke) expenses with result: $result")
            result
        }
    }
}