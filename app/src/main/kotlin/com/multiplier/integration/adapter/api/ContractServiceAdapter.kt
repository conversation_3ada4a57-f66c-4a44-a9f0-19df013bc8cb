package com.multiplier.integration.adapter.api

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationServiceGrpc
import com.multiplier.contract.schema.compensationsource.CompensationSourceOuterClass
import com.multiplier.contract.schema.compensationsource.CompensationSourceServiceGrpc
import com.multiplier.contract.schema.compensationsource.compensationSourceByEntityRequest
import com.multiplier.contract.schema.compensationsource.entityRequest
import com.multiplier.contract.schema.contract.BulkContract.InviteBulkMemberContractsRequest
import com.multiplier.contract.schema.contract.BulkContract.InviteBulkMemberContractsResponse
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc.BulkContractServiceBlockingStub
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractFilters
import com.multiplier.contract.schema.contract.ContractOuterClass.GetContractByIdAnyStatusRequest
import com.multiplier.contract.schema.contract.ContractOuterClass.GetContractByMemberIdRequest
import com.multiplier.contract.schema.contract.ContractOuterClass.GetContractMemberIdsRequest
import com.multiplier.contract.schema.contract.ContractOuterClass.GetContractsByIdsRequest
import com.multiplier.contract.schema.contract.ContractOuterClass.UpdateContractEmployeeIdRequest
import com.multiplier.contract.schema.contract.ContractServiceGrpc.ContractServiceBlockingStub
import com.multiplier.contract.schema.onboarding.Onboarding.GetOnboardingStatusRequest
import com.multiplier.contract.schema.onboarding.Onboarding.GetOnboardingStatusResponse
import com.multiplier.contract.schema.onboarding.OnboardingServiceGrpc.OnboardingServiceBlockingStub
import com.multiplier.contract.schema.performance.PerformanceReviewOuterClass.PerformanceReviewBulkCreateAsApprovedRequest
import com.multiplier.contract.schema.performance.PerformanceReviewOuterClass.PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay
import com.multiplier.contract.schema.performance.PerformanceReviewServiceGrpc.PerformanceReviewServiceBlockingStub
import com.multiplier.integration.adapter.model.PerformanceReviewRequest
import com.multiplier.integration.adapter.model.knit.Frequency
import com.multiplier.integration.service.exception.DownstreamServiceUtils
import com.multiplier.integration.utils.mapToGoogleDate
import net.devh.boot.grpc.client.inject.GrpcClient
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import utils.StringUtils

interface ContractServiceAdapter {
    fun findContractByContractId(contractId: Long): ContractOuterClass.Contract
    fun findOnboardingByContractIdAndExperience(contractId: Long, experience: String): GetOnboardingStatusResponse
    fun findContractByMemberId(memberId: Long): ContractOuterClass.Contract
    fun getCurrentCompensation(contractId: Long): CompensationOuterClass.Compensation
    fun inviteBulkMemberContracts(request: InviteBulkMemberContractsRequest): InviteBulkMemberContractsResponse
    fun findContractByWorkEmail(workEmail: String): ContractOuterClass.Contract
    fun performanceReviewBulkCreateAsApproved(request: PerformanceReviewRequest): List<Long>
    fun getContractIdsByContractFilters(filters: ContractFilters, isTest: Boolean): List<Long>
    fun getContractMemberEmailsByContractIds(contractIds: Set<Long>): Map<Long, String>
    fun updateContractEmployeeId(contractId: Long, employeeId: String): ContractOuterClass.Contract
    fun getContractsByContractIds(contractIds: Set<Long>): List<ContractOuterClass.Contract>
    fun isUsingNewCompensationSchema(legalEntityIds: List<Long>): Map<Long, Boolean>
}

@Service
class DefaultContractServiceClient : ContractServiceAdapter {

    @GrpcClient("contract-service") private lateinit var contractStub: ContractServiceBlockingStub
    @GrpcClient("contract-service") private lateinit var bulkContractStub: BulkContractServiceBlockingStub
    @GrpcClient("contract-service") private lateinit var onboardingStub: OnboardingServiceBlockingStub
    @GrpcClient("contract-service") private lateinit var compensationStub: CompensationServiceGrpc.CompensationServiceBlockingStub
    @GrpcClient("contract-service") private lateinit var performanceReviewStub: PerformanceReviewServiceBlockingStub
    @GrpcClient("contract-service") private lateinit var compensationSourceServiceGrpc: CompensationSourceServiceGrpc.CompensationSourceServiceBlockingStub
    
    private val log = LoggerFactory.getLogger(this.javaClass)

    override fun findContractByContractId(contractId: Long): ContractOuterClass.Contract {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to find contract with ID: $contractId")
            val req = GetContractByIdAnyStatusRequest.newBuilder().setId(contractId).build()
            val result = contractStub.getContractByIdAnyStatus(req)
            log.info("Successfully retrieved contract with ID: $contractId.")
            result
        }
    }

    override fun findOnboardingByContractIdAndExperience(contractId: Long, experience: String): GetOnboardingStatusResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to find onboarding status for contract ID: $contractId with experience: $experience")
            val req =
                GetOnboardingStatusRequest.newBuilder().setContractId(contractId).setExperience(experience).build()
            val result = onboardingStub.getOnboardingStatus(req)
            log.info("Successfully retrieved onboarding status for contract ID: $contractId.")
            result
        }
    }

    override fun findContractByMemberId(memberId: Long): ContractOuterClass.Contract {
        return DownstreamServiceUtils.execute {

            log.info("Attempting to find contract by member ID: $memberId")
            val req = GetContractByMemberIdRequest.newBuilder().setId(memberId).build()
            val result = contractStub.getContractByMemberId(req)
            log.info("Successfully retrieved contract for member ID: $memberId.")
            result
        }
    }

    override fun updateContractEmployeeId(contractId: Long, employeeId: String): ContractOuterClass.Contract {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to update contract employee ID for contract ID: $contractId with employee ID: $employeeId")
            val req =
                UpdateContractEmployeeIdRequest.newBuilder().setContractId(contractId).setEmployeeId(employeeId).build()
            val result = contractStub.updateContractEmployeeId(req)
            log.info("Successfully updated contract employee ID for contract ID: $contractId.")
            result
        }
    }

    override fun getContractsByContractIds(contractIds: Set<Long>): List<ContractOuterClass.Contract> {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get contracts by contract IDs")
            val result = contractStub.getContractsByIdsAnyStatus(
                GetContractsByIdsRequest.newBuilder().addAllId(contractIds)
                    .build()
            )
            log.info("Successfully retrieved contracts by contract IDs")
            result.contractsList
        }
    }

    override fun isUsingNewCompensationSchema(legalEntityIds: List<Long>): Map<Long, Boolean> {
        log.info("Checking isUsingNewCompensationSchema for entity ids={}", legalEntityIds)
        val request = compensationSourceByEntityRequest {
            request.addAll(
                legalEntityIds.map {
                    entityRequest {
                        this.entityId = it
                        entityType = CompensationSourceOuterClass.EntityType.COMPANY_ENTITY
                    }
                })
        }

        val response = compensationSourceServiceGrpc.getCompensationSourceByEntityIds(request)

        val result = response.sourceByEntityIdMap.mapValues { it.value == CompensationSourceOuterClass.CompensationSource.COMPENSATION_SERVICE }
        log.info("isUsingNewCompensationSchema result: {}", result)

        return result
    }

    override fun getCurrentCompensation(contractId: Long): CompensationOuterClass.Compensation {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get current compensation for contract ID: $contractId")
            val req = CompensationOuterClass.IdMessage.newBuilder().setId(contractId).build()
            val result = compensationStub.getCurrentCompensation(req)
            log.info("Successfully retrieved current compensation for contract ID: $contractId.")
            result
        }
    }

    override fun inviteBulkMemberContracts(request: InviteBulkMemberContractsRequest): InviteBulkMemberContractsResponse {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to invite bulk member contracts with request: $request")
            val result = bulkContractStub.inviteBulkMemberContracts(request)
            log.info("Successfully invited bulk member contracts with request: $request.")
            result
        }
    }

    override fun findContractByWorkEmail(workEmail: String): ContractOuterClass.Contract {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to find contract by work email: ${StringUtils.maskEmail(workEmail)}")
            val req = ContractOuterClass.GetContractByWorkEmailRequest.newBuilder().setWorkEmail(workEmail).build()
            val result = contractStub.getContractByWorkEmail(req)
            log.info("Successfully retrieved contract for workEmail: ${StringUtils.maskEmail(workEmail)}.")
            result
        }
    }

    override fun performanceReviewBulkCreateAsApproved(request: PerformanceReviewRequest): List<Long> {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to bulk create performance review as approved: $request")
            val input = PerformanceReviewBulkCreateAsApprovedRequest.Input.newBuilder()
                .setContractId(request.contractId)
            if (request.amount != null) {
                input.setBasePayAmount(request.amount)
            }
            if (request.effectiveDate != null) {
                input.setEffectiveDate(request.effectiveDate.mapToGoogleDate())
            }
            if (!request.additionalPays.isNullOrEmpty()) {
                val additionalPays = request.additionalPays.map {
                    AdditionalPay.newBuilder()
                        .setAmount(it.amount!!)
                        .setLabel(it.type)
                        .setFrequency(it.frequency.mapToRateFrequency())
                        .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                        .build()
                }
                input.addAllAdditionalPays(additionalPays)
            }
            val req = PerformanceReviewBulkCreateAsApprovedRequest.newBuilder()
                .addAllInputs(listOf(input.build()))
                .build()
            val result = performanceReviewStub.performanceReviewBulkCreateAsApproved(req)
            log.info("Successfully bulk create performance review as approved.")
            result.savedPerformanceReviewIdsList
        }
    }

    override fun getContractIdsByContractFilters(filters: ContractFilters, isTest: Boolean): List<Long> {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get contract IDs by filters: $filters")
            val req = GetContractMemberIdsRequest.newBuilder().setIsTest(isTest).setContractFilters(filters).build()
            val result = contractStub.getContractIdsByContractFilters(req)
            log.info("Successfully retrieved contracts ids for companyId: $filters")
            result.idsList
        }
    }

    override fun getContractMemberEmailsByContractIds(contractIds: Set<Long>): Map<Long, String> {
        return DownstreamServiceUtils.execute {
            log.info("Attempting to get contract member emails by contract IDs: $contractIds")
            val req = ContractOuterClass.GetContractMemberEmailsByContractIdsRequest.newBuilder()
                .addAllContractId(contractIds)
                .build()
            val result = contractStub.getContractMembersEmailByContractIds(req)
            log.info("Successfully retrieved contract member emails for contract IDs: $contractIds")
            result.memberEmailByContractIdMap
        }
    }

    private fun Frequency?.mapToRateFrequency(): CompensationOuterClass.RateFrequency {
        if (this == null) {
            return CompensationOuterClass.RateFrequency.RATE_FREQUENCY_NULL
        }
        return when (this) {
            Frequency.ANNUAL -> CompensationOuterClass.RateFrequency.ANNUALLY
            Frequency.WEEKLY -> CompensationOuterClass.RateFrequency.WEEKLY
            Frequency.QUARTERLY -> CompensationOuterClass.RateFrequency.QUATERLY
            Frequency.SEMI_MONTHLY -> CompensationOuterClass.RateFrequency.SEMIMONTHLY
            Frequency.MONTHLY -> CompensationOuterClass.RateFrequency.MONTHLY
            Frequency.HOURLY -> CompensationOuterClass.RateFrequency.HOURLY
            Frequency.BI_WEEKLY -> CompensationOuterClass.RateFrequency.BI_WEEKLY
            Frequency.DAILY -> CompensationOuterClass.RateFrequency.DAILY
            else -> CompensationOuterClass.RateFrequency.RATE_FREQUENCY_NULL
        }
    }
}
