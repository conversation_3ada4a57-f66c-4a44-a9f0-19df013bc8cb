package com.multiplier.integration.adapter.api.resources.knit

import com.fasterxml.jackson.annotation.JsonProperty
import com.multiplier.integration.adapter.model.EmployeeData
import io.ktor.resources.*
import kotlinx.serialization.Serializable

@Serializable
@Resource("")
class KnitIntegrations() {

    @Serializable
    @Resource("auth.createSession")
    class CreateAuthToken(val parent: KnitIntegrations = KnitIntegrations()) {

        data class Request(
                @JsonProperty("originOrgId") val originOrgId: String,
                @JsonProperty("originOrgName") val originOrgName: String,
                @JsonProperty("originUserEmail") val originUserEmail: String,
                @JsonProperty("originUserName") val originUserName: String,
                @JsonProperty("filters") val filters: List<Filter> = listOf(
                        Filter(
                                category = "HRIS",
                                apps = emptyList()
                        )
                ),
                @JsonProperty("clearErrors") val clearErrors: Boolean
        )

        data class Response(
                @JsonProperty("success") val success: Boolean,
                @JsonProperty("msg") val message: Message?,
                @JsonProperty("error") val error: Error?
        )

        data class Message(
                @JsonProperty("token") val token: String
        )

        data class Error(
                @JsonProperty("msg") val message: String
        )

        data class Filter(
                @JsonProperty("category") val category: String,
                @JsonProperty("apps") val apps: List<String>
        )
    }

    @Serializable
    @Resource("hr.employees.positions")
    class EmployeePositions(val parent: KnitIntegrations = KnitIntegrations()) {

        data class Response(
                @JsonProperty("success") val success: Boolean,
                @JsonProperty("data") val data: PositionData?,
                @JsonProperty("error") val error: Error?
        )

        data class PositionData(
                @JsonProperty("positions") val positions: List<Position>,
                @JsonProperty("workShifts") val workShifts: List<WorkShift>? = null
        )

        data class WorkShift(
                @JsonProperty("workShiftId") val workShiftId: String,
                @JsonProperty("workShiftName") val workShiftName: String
        )


        data class Position(
                @JsonProperty("positionId") val positionId: String,
                @JsonProperty("designation") val designation: String,
                @JsonProperty("department") val department: String
        )

        data class Error(
                @JsonProperty("msg") val message: String
        )
    }

    @Serializable
    @Resource("hr.employees.create")
    class EmployeeCreate(val parent: KnitIntegrations = KnitIntegrations()) {
        data class Request(
                @JsonProperty("name") val name: String,
                @JsonProperty("position") val position: String,
                @JsonProperty("department") val department: String
        )

        data class Response(
                @JsonProperty("success") val success: Boolean,
                @JsonProperty("data") val data: EmployeeData? = null,
                @JsonProperty("error") val error: ErrorData? = null
        )
        data class EmployeeData(
                @JsonProperty("employeeId") val employeeId: String
        )

        data class ErrorData(
                @JsonProperty("msg") val message: String
        )
    }

}

