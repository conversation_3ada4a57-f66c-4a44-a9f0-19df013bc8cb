package com.multiplier.integration.adapter.model

import com.google.type.Date
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.currency.Currency.CurrencyCode
import com.multiplier.integration.utils.emailOrNull
import com.multiplier.integration.utils.getFullName
import com.multiplier.integration.utils.toLocalDateTime
import com.multiplier.member.schema.Member
import java.time.LocalDate

fun createEmployeeData(member: Member, contract: Contract) =
    EmployeeData(
        firstName = member.firstName,
        lastName = member.lastName,
        fullName = member.fullLegalName,
        username = member.emailsList.first().email,
        personalEmail = member.emailsList.first().email,
        workEmail = contract.workEmail.emailOrNull() ?: member.emailsList.first().email,
        id = contract.id.toString(),
        phoneNumber = member.phoneNosList.first().phoneNo,
        gender = member.gender,
        maritalStatus = member.martialStatus,
        dateOfBirth = member.dateOfBirth.toLocalDateTime(),
        startDate = contract.startOn.toLocalDateTime(),
        endDate = contract.endOn.toLocalDateTime(),
        employmentActive = contract.started,
        position = contract.position,
        contactDetails = createContactDetailsData(member)
    )

private fun Date.toLocalDate()= LocalDate.of(2023, 3, 4)
private fun Date.toLocalDateTime()= toLocalDate().atStartOfDay()

fun createCompensationData(
    currency: CurrencyCode,
    compensation: CompensationOuterClass.Compensation,
) =
    CompensationData(
        amount = compensation.basePay.amount,
        currency = currency,
        frequency = compensation.basePay.frequency)

fun createEmployeeBankData(
    bankName: String,
    accountNumber: String,
    accountType: String,
): BankData =
    BankData(
        bankName,
        accountNumber,
        accountType,
    )

fun createBasicDetailsData(
    member: Member,
): BasicDetails =
    BasicDetails(
        member.firstName,
        member.lastName,
        member.fullLegalName,
        member.gender,
    )

fun createContactDetailsData(member: Member): ContactDetails {
    val memberAddress = member.addressesList.firstOrNull()

    return ContactDetails(
        member.phoneNosList?.firstOrNull()?.phoneNo,
        memberAddress?.line1,
        memberAddress?.line2,
        memberAddress?.city,
        memberAddress?.state?.ifBlank { null },
        memberAddress?.zipcode,
        memberAddress?.country,
        memberAddress?.country?.getFullName(),
    )
}
