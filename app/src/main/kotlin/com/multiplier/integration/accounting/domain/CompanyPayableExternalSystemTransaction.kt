package com.multiplier.integration.accounting.domain

import com.multiplier.integration.accounting.domain.mapping.AccountingExternalEntity
import com.multiplier.integration.accounting.domain.mapping.AccountingTransaction
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType

data class CompanyPayableExternalSystemTransaction(
    val id: Long? = null,
    val integrationId: Long,
    val externalSystemTransactionId: AccountingExternalEntity? = null,
    val companyPayableId: Long,
    val entityId: Long,
    val financialTransactionType: FinancialTransactionType,
    val requestPayload: AccountingTransaction? = null,
    val responsePayload: AccountingTransaction? = null,
)
