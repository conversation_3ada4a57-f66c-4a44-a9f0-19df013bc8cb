package com.multiplier.integration.accounting.db.repo

import com.multiplier.integration.accounting.db.JpaAccountingExternalPaymentAccountMapping
import org.springframework.data.jpa.repository.JpaRepository

interface JpaAccountingExternalPaymentAccountMappingRepository: JpaRepository<JpaAccountingExternalPaymentAccountMapping, Long> {

    fun findByIntegrationId(companyIntegrationId: Long): JpaAccountingExternalPaymentAccountMapping?

}