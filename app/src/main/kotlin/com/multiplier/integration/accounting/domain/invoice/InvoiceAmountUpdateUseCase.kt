package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.creditnote.CreditNoteServiceAdapter
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyFactory
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class InvoiceAmountUpdateUseCase(
    private val companyPayableExternalSystemService: CompanyPayableExternalSystemService,
    private val mergeDevService: MergeDevService,
    private val invoicePaymentStrategyFactory: InvoicePaymentStrategyFactory,
    private val creditNoteServiceAdapter: CreditNoteServiceAdapter,
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun amountUpdateHandler(companyPayable: CompanyPayable) {
        val companyPayableId = companyPayable.companyPayableId

        logger.info { "Amount update handling for company payable id = $companyPayableId" }

        //this assumes that the companyPayable is already present in the integration. If otherwise, change this and
        //call creation flow from here.
        val companyPayableExternalSystemTransaction =
            companyPayableExternalSystemService.findExternalSystemCompanyPayable(companyPayableId, FinancialTransactionType.INVOICE)


        val invoiceExternalSystemAggregate = InvoiceExternalSystemAggregate(companyPayableExternalSystemTransaction)

        invoiceExternalSystemAggregate.updateAmount(
            companyPayable,
            invoicePaymentStrategyFactory,
            creditNoteServiceAdapter,
            mergeDevService
        )

    }
}