package com.multiplier.integration.accounting.domain

import com.multiplier.integration.accounting.domain.invoice.InvoiceAmountUpdateUseCase
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class FinancialTransactionAmountUpdatedHandlerService(
    private val companyPayableAdapter: CompanyPayableAdapter,
    private val invoiceAmountUpdateUseCase: InvoiceAmountUpdateUseCase, //choosing not to use factory for now. Can be used later.
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun amountUpdateHandler(companyPayableId: Long) {
        logger.info { "Handling amount update for company payable id = $companyPayableId" }
        val companyPayable = companyPayableAdapter.getCompanyPayable(companyPayableId)
        invoiceAmountUpdateUseCase.amountUpdateHandler(companyPayable)
    }

}