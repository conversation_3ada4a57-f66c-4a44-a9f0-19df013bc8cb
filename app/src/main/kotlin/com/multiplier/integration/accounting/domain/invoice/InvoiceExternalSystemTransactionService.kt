package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.mapping.*
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.multiplier.integration.accounting.domain.model.Invoice
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class InvoiceExternalSystemTransactionService(
    private val financialTransactionLineItemsMapper: FinancialTransactionLineItemsMapper,
    private val accountingTransactionInvoiceMapper: AccountingTransactionInvoiceMapper,
    private val companyPayableExternalSystemService: CompanyPayableExternalSystemService,
    private val companyAccountingIntegrationMappingReadService: CompanyAccountingIntegrationMappingReadService,
    private val mergeDevService: MergeDevService,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun upsert(upsertCompanyPayableExternalTransaction: UpsertCompanyPayableExternalSystemTransaction) {
        validate(upsertCompanyPayableExternalTransaction)

        val companyPayable = upsertCompanyPayableExternalTransaction.companyPayable as Invoice
        val multiplierVendorMapping = getMultiplierVendorMapping(upsertCompanyPayableExternalTransaction.customerIntegrationId)

        val updatedInvoice = updateInvoiceLineItems(companyPayable, upsertCompanyPayableExternalTransaction.customerIntegrationId)
        val mappedAccountingTransaction = mapToAccountingTransaction(updatedInvoice, multiplierVendorMapping.externalVendor.id)

        var companyPayableExternalSystemTransaction = createCompanyPayableExternalSystemTransaction(
            upsertCompanyPayableExternalTransaction, updatedInvoice, mappedAccountingTransaction
        )

        try {
            val existingTransaction = companyPayableExternalSystemService.findExternalSystemCompanyPayable(
                updatedInvoice.companyPayableId,
                FinancialTransactionType.INVOICE
            )

            logger.info { "Handling existing transaction for company payable $existingTransaction" }

            companyPayableExternalSystemTransaction = handleTransaction(
                companyPayableExternalSystemTransaction, existingTransaction, updatedInvoice
            )
            saveTransaction(companyPayableExternalSystemTransaction, existingTransaction)
        } catch (e: Exception) {
            logger.error(e) { "Failed to create external system company payable for company payable ID: ${updatedInvoice.companyPayableId} with error: ${e.message}" }
            throw e
        }
    }

    private fun getMultiplierVendorMapping(customerIntegrationId: Long): ExternalMultiplierVendorMapping {
        return companyAccountingIntegrationMappingReadService.readExternalVendorMapping(customerIntegrationId)
            ?: throw IllegalArgumentException("Vendor mapping not found for customer integration id $customerIntegrationId")
    }

    private fun updateInvoiceLineItems(invoice: Invoice, customerIntegrationId: Long): Invoice {
        return invoice.copy(
            lineItems = financialTransactionLineItemsMapper.lineItemMapping(
                FinancialTransactionLineItemsMapperInput(
                    integrationId = customerIntegrationId,
                    lineItems = invoice.lineItems,
                )
            )
        )
    }

    private fun mapToAccountingTransaction(invoice: Invoice, externalVendorId: String): AccountingTransaction {
        logger.info { "Mapping invoice for companyPayableId ${invoice.companyPayableId} to accounting transaction" }
        return accountingTransactionInvoiceMapper.mapFinancialTransactionToAccountingTransaction(invoice, externalVendorId)
    }

    private fun createCompanyPayableExternalSystemTransaction(
        createCompanyPayableExternalTransaction: UpsertCompanyPayableExternalSystemTransaction,
        invoice: Invoice,
        mappedAccountingTransaction: AccountingTransaction
    ): CompanyPayableExternalSystemTransaction {
        return CompanyPayableExternalSystemTransaction(
            integrationId = createCompanyPayableExternalTransaction.customerIntegrationId,
            companyPayableId = invoice.companyPayableId,
            financialTransactionType = createCompanyPayableExternalTransaction.financialTransactionType,
            requestPayload = mappedAccountingTransaction,
            entityId = invoice.companyId,
        )
    }

    private fun handleTransaction(
        transaction: CompanyPayableExternalSystemTransaction,
        existingTransaction: CompanyPayableExternalSystemTransaction?,
        invoice: Invoice
    ): CompanyPayableExternalSystemTransaction {
        return if (existingTransaction?.externalSystemTransactionId != null &&
            existingTransaction.externalSystemTransactionId.externalId.isNotBlank()
        ) {
            logger.info { "External system company payable found for companyPayableId ${existingTransaction.companyPayableId}" }
            mergeDevService.updateInvoice(transaction, existingTransaction.externalSystemTransactionId)
        } else {
            logger.info { "No external system company payable found for companyPayableId ${invoice.companyPayableId}" }
            mergeDevService.createInvoice(transaction)
        }
    }

    private fun saveTransaction(
        transaction: CompanyPayableExternalSystemTransaction,
        existingTransaction: CompanyPayableExternalSystemTransaction?
    ) {
        if (existingTransaction != null) {
            update(transaction.copy(id = existingTransaction.id))
        } else {
            create(transaction)
        }
    }

    private fun create(companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction) {
        logger.info { "Creating company payable external system transaction for company payable ID: ${companyPayableExternalSystemTransaction.companyPayableId}" }
        companyPayableExternalSystemService.createExternalSystemCompanyPayable(companyPayableExternalSystemTransaction)
        logger.info { "Company payable external system transaction created for company payable ID: ${companyPayableExternalSystemTransaction.companyPayableId}" }
    }

    private fun update(updateCompanyPayableExternalTransaction: CompanyPayableExternalSystemTransaction) {
        logger.info("Updating company payable external system for company payable ID: ${updateCompanyPayableExternalTransaction.companyPayableId}")
        companyPayableExternalSystemService.updateExternalSystemCompanyPayable(updateCompanyPayableExternalTransaction)
        logger.info("Updated Company payable external system for company payable ID: ${updateCompanyPayableExternalTransaction.companyPayableId}")
    }

    private fun validate(createCompanyPayableExternalTransaction: UpsertCompanyPayableExternalSystemTransaction) {
        val companyPayable = createCompanyPayableExternalTransaction.companyPayable
        require(companyPayable is Invoice) {
            "Company payable ${companyPayable.companyPayableId} is not an invoice"
        }
    }
}