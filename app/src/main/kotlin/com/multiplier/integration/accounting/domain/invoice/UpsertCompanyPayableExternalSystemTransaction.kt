package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType

data class UpsertCompanyPayableExternalSystemTransaction(
    val customerIntegrationId: Long,
    val companyPayable: CompanyPayable,
    val financialTransactionType: FinancialTransactionType,
    val isForce: Boolean = false,
)
