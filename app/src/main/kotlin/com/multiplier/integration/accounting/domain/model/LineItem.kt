package com.multiplier.integration.accounting.domain.model

import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.common.Tax
import com.multiplier.integration.accounting.domain.mapping.LineItemAccountMapping
import java.time.LocalDate

data class LineItem(
     val description: String,
     val quantity: Double,
     val unitPrice: Amount,
     val tax: Tax,
     val contractId: Long?,
     val memberName: String,
     val lineItemType: String, //to be decided whether to enum or no.
     val amountInBaseCurrency: Amount,
     val countryName: String?,
     val grossAmount: Amount,
     val startInvoiceCycleDate: LocalDate?,
     val endInvoiceCycleDate: LocalDate?,
     val lineItemAccountMapping: LineItemAccountMapping? = null//this will be used for tracking ids.
)
