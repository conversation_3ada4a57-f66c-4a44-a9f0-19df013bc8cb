package com.multiplier.integration.accounting.domain.model

import com.multiplier.integration.types.CurrencyCode
import java.time.LocalDate

data class CreditNote(
    val id: Long,
    override val companyPayableId: Long,
    override val companyId: Long,
    override val createdDate: LocalDate,
    override val status: CompanyPayableStatus,
    val amountApplied: Double,
    val amountUnApplied: Double,
    val amountTotal: Double,
    val creditNoteStatus: CreditNoteStatus,
    val currencyCode: CurrencyCode,
    val lineItems: List<LineItem>,
    val appliedToInvoiceIds: List<Long>? = emptyList()
): CompanyPayable(
    companyPayableId = companyPayableId,
    companyId = companyId,
    createdDate = createdDate,
    status = status,
    type = FinancialTransactionType.CREDIT_NOTE,
)