package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.LineItem

interface AccountingTransactionMapper {
   fun mapFinancialTransactionToAccountingTransaction(companyPayable: CompanyPayable, contact: String): AccountingTransaction

   fun mapLineItemToAccountingTransactionLineItem(lineItem: LineItem, companyPayable: CompanyPayable): AccountingTransactionLineItem {
      return AccountingTransactionLineItem(
         commonFields = AccountingTransactionCommonFields(
            currency = lineItem.unitPrice.currencyCode.name,
            trackingCategories = lineItem.lineItemAccountMapping?.externalDepartments?.map { it.id } ?: emptyList(),
         ),
         item = lineItem.lineItemAccountMapping?.externalAccount?.id,
         description = lineItem.description,
         quantity = lineItem.quantity,
         totalAmount = lineItem.grossAmount.value,
         unitPrice = lineItem.unitPrice.value,
      )
   }
}