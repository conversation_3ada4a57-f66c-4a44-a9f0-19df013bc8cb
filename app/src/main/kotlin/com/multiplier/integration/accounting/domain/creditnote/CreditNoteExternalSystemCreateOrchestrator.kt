package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.domain.FinancialTransactionUpsertOrchestrator
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class CreditNoteExternalSystemCreateOrchestrator(
    private val creditNoteCreateUseCase: CreditNoteCreateUseCase
) : FinancialTransactionUpsertOrchestrator {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }
    // once we have patch for updating the vendor credit note then this would be used that is why name is upsertHandler
    // also we need to keep in mind how update could be handled as we might have applied payment to it in some cases(credit note applied amount is less than invoice amount)
    override fun upsertHandler(companyPayable: CompanyPayable) {
        logger.info { "Creating credit note for company payable ID: ${companyPayable.companyPayableId}" }
        creditNoteCreateUseCase.createHandler(companyPayable)

    }

    override fun getTransactionType(): FinancialTransactionType = FinancialTransactionType.CREDIT_NOTE
}