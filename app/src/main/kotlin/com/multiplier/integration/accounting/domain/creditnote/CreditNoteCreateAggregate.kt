package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableAdapter
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.mapping.FinancialTransactionLineItemsMapper
import com.multiplier.integration.accounting.domain.mapping.FinancialTransactionLineItemsMapperInput
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.accounting.domain.model.CreditNoteStatus
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType

class CreditNoteCreateAggregate {
    fun handle(
        companyPayableAdapter: CompanyPayableAdapter,
        companyPayableExternalSystemService: CompanyPayableExternalSystemService,
        mergeDevService: MergeDevService,
        financialTransactionLineItemsMapper: FinancialTransactionLineItemsMapper,
        companyPayable: CompanyPayable,
    ): CompanyPayableExternalSystemTransaction? {
        require(companyPayable is CreditNote) {
            "the company payable(id = ${companyPayable.companyPayableId}) must be a credit note"
        }

        require(companyPayable.creditNoteStatus == CreditNoteStatus.FULLY_APPLIED) {
            "The credit note should be FULLY APPLIED to create vendor credit applicable.Company Payable Id = ${companyPayable.companyPayableId}" +
                " Credit note ID = ${companyPayable.id}"
        }

        val companyPayableIds =
            companyPayable.appliedToInvoiceIds?.let {
                require(it.isNotEmpty() && it.size == 1) {
                    "Constraint violation. Constraint = At-most one credit note is applied to invoice. " +
                        "Company Payable id = ${companyPayable.id} and applied to invoice = $it"
                }
                companyPayableAdapter.getCompanyPayableIdsFromInvoiceIds(it)
            } ?: throw IllegalArgumentException("Applied invoice IDs cannot be null")

        require(companyPayableIds.size == 1) {
            "Could not find the invoice for invoiceIds: ${companyPayable.appliedToInvoiceIds}"
        }

        val invoicedExternalSystemCompanyPayable =
            companyPayableExternalSystemService.findExternalSystemCompanyPayable(
                companyPayableIds.first(),
                FinancialTransactionType.INVOICE,
            )

        requireNotNull(invoicedExternalSystemCompanyPayable) {
            "Invoice is not present for this company payable: ${companyPayableIds.first()}"
        }

        val externalTransactionAmount =
            mergeDevService.getAmountsForExternalTransaction(
                invoicedExternalSystemCompanyPayable,
            )

        require(companyPayable.amountTotal <= externalTransactionAmount.dueAmount.value) {
            """
            credit note applied amount is not less than or equal to due amount.
            credit note amount: ${companyPayable.amountApplied} for company payable id: ${companyPayable.companyPayableId}
            external due amount: ${externalTransactionAmount.dueAmount.value} for company payable id: ${invoicedExternalSystemCompanyPayable.companyPayableId}
            """.trimIndent()
        }

        return mergeDevService.createVendorCredit(
            ExternalVendorCreditCreationRequest(
                creditNote =
                    updateCreditNoteLineItems(
                        companyPayable,
                        invoicedExternalSystemCompanyPayable.integrationId,
                        financialTransactionLineItemsMapper,
                    ),
                integrationId = invoicedExternalSystemCompanyPayable.integrationId,
                companyId = companyPayable.companyId,
                invoicedExternalSystemCompanyPayable = invoicedExternalSystemCompanyPayable,
            ),
        )
    }

    private fun updateCreditNoteLineItems(
        creditNote: CreditNote,
        customerIntegrationId: Long,
        financialTransactionLineItemsMapper: FinancialTransactionLineItemsMapper,
    ): CreditNote =
        creditNote.copy(
            lineItems =
                financialTransactionLineItemsMapper.lineItemMapping(
                    FinancialTransactionLineItemsMapperInput(
                        integrationId = customerIntegrationId,
                        lineItems = creditNote.lineItems,
                    ),
                ),
        )
}
