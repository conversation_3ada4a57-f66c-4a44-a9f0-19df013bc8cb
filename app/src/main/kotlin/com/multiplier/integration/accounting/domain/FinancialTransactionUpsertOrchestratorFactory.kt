package com.multiplier.integration.accounting.domain

import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class FinancialTransactionUpsertOrchestratorFactory(
    private val financialTransactionUpsertOrchestrators: List<FinancialTransactionUpsertOrchestrator>,
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun getHandler(financialTransactionType: FinancialTransactionType): FinancialTransactionUpsertOrchestrator {
        logger.info { "Returning orchestrator for transaction type: $financialTransactionType" }
        return financialTransactionUpsertOrchestrators.first { it.getTransactionType() == financialTransactionType }
    }

}