package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.DepartmentService
import com.multiplier.integration.accounting.domain.model.LineItem
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class FinancialTransactionLineItemsMapper(
    private val companyAccountingIntegrationMappingReadService: CompanyAccountingIntegrationMappingReadService,
    private val departmentService: DepartmentService,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun lineItemMapping(financialTransactionLineItemsMapperInput: FinancialTransactionLineItemsMapperInput): List<LineItem> {
        val integrationId = financialTransactionLineItemsMapperInput.integrationId
        val companyAccountingIntegrationMappings = companyAccountingIntegrationMappingReadService.read(integrationId)

        return financialTransactionLineItemsMapperInput.lineItems.map { lineItem ->
            logger.info { "Mapping line item of type: ${lineItem.lineItemType} and contractId = ${lineItem.contractId}" }

            val departmentMapping = getDepartmentMapping(lineItem.contractId, companyAccountingIntegrationMappings)
            val lineItemAccountMapping =
                getLineItemAccountMapping(lineItem.lineItemType, departmentMapping, companyAccountingIntegrationMappings)
            requireNotNull(lineItemAccountMapping) { "No mapping found for line item type: ${lineItem.lineItemType}" }

            logger.info { "External departments before copy: ${lineItemAccountMapping.externalDepartments}" }

            val updatedLineItem =
                lineItem
                    .copy(
                        lineItemAccountMapping =
                            LineItemAccountMapping(
                                lineItemType = lineItem.lineItemType,
                                externalAccount = lineItemAccountMapping.externalAccount,
                                externalDepartments = lineItemAccountMapping.externalDepartments,
                            ),
                    ).apply {
                        logger.info {
                            "Mapped line item of type: ${this.lineItemAccountMapping?.externalAccount} to external departments: ${this.lineItemAccountMapping?.externalDepartments}"
                        }
                    }
            updatedLineItem
        }
    }

    private fun getDepartmentMapping(
        contractId: Long?,
        companyAccountingIntegrationMapping: CompanyAccountingIntegrationMapping,
    ): DepartmentMapping? {
        val department = contractId?.takeIf { it > 0 }?.let { departmentService.findDepartmentForContractId(it) }
        return department?.let {
            companyAccountingIntegrationMapping.departmentMappings
                .find { it.departmentId == department.id }
                .also { logger.info { "Found department mapping for contractId = $contractId and departmentId = ${department.id}" } }
        }
    }

    private fun getLineItemAccountMapping(
        lineItemType: String,
        department: DepartmentMapping?,
        companyAccountingIntegrationMapping: CompanyAccountingIntegrationMapping,
    ): LineItemAccountMapping? =
        getLineItemMappingForDepartment(lineItemType, department)
            ?: getLineItemMappingForLegalEntity(lineItemType, companyAccountingIntegrationMapping)
            ?: getFallbackLineItemMapping(lineItemType, companyAccountingIntegrationMapping)
                .also { logger.info { "No line item mapping found for lineItemType = $lineItemType" } }

    private fun getLineItemMappingForDepartment(
        lineItemType: String,
        department: DepartmentMapping?,
    ): LineItemAccountMapping? =
        department
            ?.lineItemAccountMapping
            ?.find { it.lineItemType == lineItemType }
            ?.copy(externalDepartments = setOf(department.externalDepartmentMapping))
            .also { logger.info { "Found line item mapping for lineItemType = $lineItemType and departmentId = $it" } }

    private fun getLineItemMappingForLegalEntity(
        lineItemType: String,
        companyAccountingIntegrationMapping: CompanyAccountingIntegrationMapping,
    ): LineItemAccountMapping? =
        companyAccountingIntegrationMapping.legalEntityMappings
            .firstOrNull()
            ?.lineItemAccountMapping
            ?.find { it.lineItemType == lineItemType }
            .also { logger.info { "Found line item mapping for lineItemType = $lineItemType and legalEntityId = $it" } }

    private fun getFallbackLineItemMapping(
        lineItemType: String,
        companyAccountingIntegrationMapping: CompanyAccountingIntegrationMapping,
    ): LineItemAccountMapping? =
        companyAccountingIntegrationMapping.fallbackLineItemMappings
            ?.find { it.lineItemType == lineItemType }
            .also { logger.info { "Found fallback line item mapping for lineItemType = $lineItemType and companyId = $it" } }
}
