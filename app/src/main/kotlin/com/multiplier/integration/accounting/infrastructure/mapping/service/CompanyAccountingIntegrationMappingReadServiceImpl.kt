package com.multiplier.integration.accounting.infrastructure.mapping.service

import com.multiplier.integration.accounting.db.JpaAccountingDepartmentMapping
import com.multiplier.integration.accounting.db.JpaAccountingLegalEntityMapping
import com.multiplier.integration.accounting.db.repo.*
import com.multiplier.integration.accounting.domain.mapping.*
import com.multiplier.integration.accounting.domain.mapping.exception.ExternalMultiplierMappingNotFoundException
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
internal class CompanyAccountingIntegrationMappingReadServiceImpl(
    private val jpaAccountingDepartmentMappingRepository: JpaAccountingDepartmentMappingRepository,
    private val jpaAccountingExternalMultiplierVendorMappingRepository: JpaAccountingExternalMultiplierVendorMappingRepository,
    private val jpaAccountingLegalEntityMappingRepository: JpaAccountingLegalEntityMappingRepository,
    private val jpaAccountingLineItemMappingRepository: JpaAccountingLineItemMappingRepository,
    private val jpaAccountingExternalPaymentAccountMappingRepository: JpaAccountingExternalPaymentAccountMappingRepository,
): CompanyAccountingIntegrationMappingReadService {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    @Transactional(readOnly = true)
    override fun read(companyIntegrationId: Long): CompanyAccountingIntegrationMapping {
        val jpaLineItemMappingRepository = jpaAccountingLineItemMappingRepository.findByMappedParentIdAndMappedParentType(
            companyIntegrationId,
            LineItemMappingParentType.COMPANY_INTEGRATION
        )

        logger.info { "Reading company accounting integration mapping for company integration id $companyIntegrationId" }

        return CompanyAccountingIntegrationMapping(
            integrationId = companyIntegrationId,
            legalEntityMappings = getLegalEntityMappings(companyIntegrationId),
            departmentMappings = getDepartmentMappings(companyIntegrationId),
            multiplierVendorMapping = getExternalMultiplierVendorMapping(companyIntegrationId),
            fallbackLineItemMappings = jpaLineItemMappingRepository.map { it.toDomain() }
        ).also { logger.info { "Company accounting integration mapping read successfully" } }
    }

    override fun readExternalVendorMapping(companyIntegrationId: Long): ExternalMultiplierVendorMapping? {
        return getExternalMultiplierVendorMapping(companyIntegrationId).also { logger.info { "External vendor mapping read successfully" } }
    }

    override fun readExternalPaymentAccount(companyIntegrationId: Long): ExternalMultiplierPaymentMapping? {
        logger.info { "Mapping payment account for company integration id = $companyIntegrationId" }
        val jpaAccountingExternalPaymentBankAccountMapping =
            jpaAccountingExternalPaymentAccountMappingRepository.findByIntegrationId(companyIntegrationId)
                ?: throw ExternalMultiplierMappingNotFoundException("No external payment mapping found. IntegrationId = $companyIntegrationId")

        return ExternalMultiplierPaymentMapping(
            companyIntegrationId = jpaAccountingExternalPaymentBankAccountMapping.integrationId,
            externalBankAccount = jpaAccountingExternalPaymentBankAccountMapping.externalPaymentAccount
        ).also { logger.info { "Mapped payment account = $it" } }
    }

    private fun getLegalEntityMappings(companyIntegrationId: Long): List<LegalEntityMapping> {
        val jpaLegalEntityMappings = jpaAccountingLegalEntityMappingRepository.findByIntegrationId(companyIntegrationId)
        return jpaLegalEntityMappings.map { getLegalEntityMapping(it) }
    }

    private fun getLegalEntityMapping(
        jpaAccountingLegalEntityMapping: JpaAccountingLegalEntityMapping
    ): LegalEntityMapping {

        val legalEntityLineItemMappings = getLineItemMapping(
            jpaAccountingLegalEntityMapping.id!!,
            LineItemMappingParentType.LEGAL_ENTITY_INTEGRATION
        )

        logger.info { "Reading legal entity mapping for legal entity id ${jpaAccountingLegalEntityMapping.entityId}" }
        return LegalEntityMapping(
            id = jpaAccountingLegalEntityMapping.id!!,
            internalLegalEntityId = jpaAccountingLegalEntityMapping.entityId,
            externalMapping = jpaAccountingLegalEntityMapping.externalEntity,
            lineItemAccountMapping = legalEntityLineItemMappings
        ).also { logger.info { "Legal entity mapping read successfully" } }
    }

    private fun getDepartmentMappings(companyIntegrationId: Long): List<DepartmentMapping> {
        val jpaDepartmentMappings = jpaAccountingDepartmentMappingRepository.findByIntegrationId(companyIntegrationId)
        return jpaDepartmentMappings.map { getDepartmentMapping(it) }
    }

    private fun getDepartmentMapping(
        jpaAccountingDepartmentMapping: JpaAccountingDepartmentMapping
    ): DepartmentMapping {

        val departmentINTEGRTIONLineItemMappings = getLineItemMapping(
            jpaAccountingDepartmentMapping.id!!,
            LineItemMappingParentType.DEPARTMENT_INTEGRATION
        )
        logger.info { "Reading department mapping for department id ${jpaAccountingDepartmentMapping.departmentId}" }
        return DepartmentMapping(
            id = jpaAccountingDepartmentMapping.id!!,
            departmentId = jpaAccountingDepartmentMapping.departmentId,
            externalDepartmentMapping = jpaAccountingDepartmentMapping.externalDepartment,
            lineItemAccountMapping = departmentINTEGRTIONLineItemMappings
        ).also { logger.info { "Department mapping read successfully" } }
    }

    private fun getExternalMultiplierVendorMapping(
        companyIntegrationId: Long
    ): ExternalMultiplierVendorMapping? {
        val jpaExternalMultiplierVendorMapping = jpaAccountingExternalMultiplierVendorMappingRepository
            .findByIntegrationId(companyIntegrationId)
            ?: return null

        logger.info { "Reading external vendor mapping for company integration id $companyIntegrationId" }

        return ExternalMultiplierVendorMapping(
            id = jpaExternalMultiplierVendorMapping.id!!,
            externalVendor = jpaExternalMultiplierVendorMapping.externalVendor
        ).also { logger.info { "External vendor mapping read successfully" } }
    }

    private fun getLineItemMapping(mappedParentId: Long, mappedParentType: LineItemMappingParentType): List<LineItemAccountMapping> {
        val jpaLineItemsMappings = jpaAccountingLineItemMappingRepository.findByMappedParentIdAndMappedParentType(mappedParentId, mappedParentType)
        logger.info { "Reading line item mappings for parent id $mappedParentId and parent type $mappedParentType" }
        return jpaLineItemsMappings.map { it.toDomain() }
    }

}