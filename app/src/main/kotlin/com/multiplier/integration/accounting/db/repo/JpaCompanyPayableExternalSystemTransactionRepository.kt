package com.multiplier.integration.accounting.db.repo

import com.multiplier.integration.accounting.db.JpaCompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import org.springframework.data.jpa.repository.JpaRepository

interface JpaCompanyPayableExternalSystemTransactionRepository:
    JpaRepository<JpaCompanyPayableExternalSystemTransaction, Long> {
    fun findFirstByCompanyPayableIdAndFinancialTransactionType(
        companyPayableId: Long,
        financialTransactionType: FinancialTransactionType
    ): JpaCompanyPayableExternalSystemTransaction?
}