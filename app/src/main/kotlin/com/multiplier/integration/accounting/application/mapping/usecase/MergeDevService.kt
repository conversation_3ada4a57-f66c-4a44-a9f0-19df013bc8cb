package com.multiplier.integration.accounting.application.mapping.usecase

import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.creditnote.ExternalVendorCreditCreationRequest
import com.multiplier.integration.accounting.domain.mapping.*
import com.multiplier.integration.accounting.domain.mapping.exception.ExternalMultiplierMappingNotFoundException
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.multiplier.integration.adapter.api.MergeDevAdapter
import com.multiplier.integration.adapter.api.resources.financial.*
import com.multiplier.integration.adapter.api.resources.financial.payment.MergeDevPaymentCreateRequest
import com.multiplier.integration.adapter.api.resources.financial.payment.PaymentAppliedInvoices
import com.multiplier.integration.repository.CompanyIntegrationRepository
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class MergeDevService(
    private val mergeDevAdapter: MergeDevAdapter,
    private val jpaCompanyIntegrationRepository: CompanyIntegrationRepository,
    private val companyAccountingIntegrationMappingService: CompanyAccountingIntegrationMappingReadService,
    private val companyPayableExternalSystemService: CompanyPayableExternalSystemService,
    private val vendorCreditMapper: AccountingTransactionVendorCreditMapper,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun createInvoice(
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction,
    ): CompanyPayableExternalSystemTransaction {
        val companyIntegration = getCompanyIntegration(companyPayableExternalSystemTransaction.integrationId)
        val createInvoice =
            mergeDevAdapter.createInvoice(
                mergeDevInvoiceRequest =
                    buildMergeDevInvoiceRequest(
                        companyIntegration.accountToken,
                        companyPayableExternalSystemTransaction.requestPayload,
                    ),
            )

        logger.info { "Invoice created response: $createInvoice " }

        if (!createInvoice.success) {
            throw RuntimeException("Failed to create invoice")
        }
        val mergeDevInvoiceMessage = createInvoice.message as MergeDevInvoiceMessage

        logger.info { "Invoice created with id: ${mergeDevInvoiceMessage.id}" }

        return buildUpdatedTransaction(companyPayableExternalSystemTransaction, mergeDevInvoiceMessage)
    }

    fun updateInvoice(
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction,
        externalId: AccountingExternalEntity,
    ): CompanyPayableExternalSystemTransaction {
        val companyIntegration = getCompanyIntegration(companyPayableExternalSystemTransaction.integrationId)
        val patchInvoice =
            mergeDevAdapter.updateInvoice(
                mergeDevInvoiceRequest =
                    buildMergeDevInvoiceRequest(
                        companyIntegration.accountToken,
                        companyPayableExternalSystemTransaction.requestPayload,
                    ),
                id = externalId.id,
            )

        logger.info { "Invoice patched response: $patchInvoice " }

        if (!patchInvoice.success) {
            throw RuntimeException("Failed to patch invoice")
        }
        val mergeDevInvoiceMessage = patchInvoice.message as MergeDevInvoiceMessage

        logger.info { "Invoice patched with id: ${mergeDevInvoiceMessage.id}" }

        return buildUpdatedTransaction(companyPayableExternalSystemTransaction, mergeDevInvoiceMessage)
    }

    fun getAmountsForExternalTransaction(
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction,
    ): ExternalCompanyPayableAmount {
        val accountingExternalEntity = companyPayableExternalSystemTransaction.externalSystemTransactionId
        val integrationId = companyPayableExternalSystemTransaction.integrationId

        requireNotNull(accountingExternalEntity) {
            "The external integration cannot be null at this point. companyPayableExternalTransaction id =" +
                " ${companyPayableExternalSystemTransaction.id}"
        }

        logger.info {
            "Finding amounts for external company payable id = ${accountingExternalEntity.id} and integrationId = $integrationId"
        }
        val companyIntegration = getCompanyIntegration(integrationId)

        return mergeDevAdapter.getTransactionAmounts(accountingExternalEntity.id, companyIntegration.accountToken)
    }

    fun createPayment(paymentCreateRequest: ExternalPaymentCreationRequest): CompanyPayableExternalSystemTransaction {
        val companyIntegration = getCompanyIntegration(paymentCreateRequest.integrationId)

        val companyPayableExternalTransaction =
            companyPayableExternalSystemService.findExternalSystemCompanyPayable(
                paymentCreateRequest.companyPayableId,
                FinancialTransactionType.INVOICE,
            )

        val mergePaymentCreationResponse =
            mergeDevAdapter.createPayment(
                MergeDevPaymentCreateRequest(
                    amount = paymentCreateRequest.amount,
                    account =
                        companyAccountingIntegrationMappingService
                            .readExternalPaymentAccount(paymentCreateRequest.integrationId)
                            ?.externalBankAccount
                            ?.id
                            ?: throw ExternalMultiplierMappingNotFoundException(
                                "No external mapping for payment. IntegrationId ${companyIntegration.id}",
                            ),
                    contact =
                        companyAccountingIntegrationMappingService
                            .readExternalVendorMapping(paymentCreateRequest.integrationId)
                            ?.externalVendor
                            ?.id
                            ?: throw ExternalMultiplierMappingNotFoundException(
                                "No external vendor mapping. IntegrationId ${companyIntegration.id} ",
                            ),
                    appliedInvoice =
                        PaymentAppliedInvoices(
                            appliedAmount = paymentCreateRequest.amount,
                            appliedDate = paymentCreateRequest.createdDate,
                            externalInvoiceId =
                                companyPayableExternalTransaction?.externalSystemTransactionId?.id
                                    ?: throw ExternalMultiplierMappingNotFoundException(
                                        "No external id found for ${paymentCreateRequest.companyPayableId}",
                                    ),
                        ),
                    createdDate = paymentCreateRequest.createdDate,
                ),
                accountToken = companyIntegration.accountToken,
            )

        require(mergePaymentCreationResponse.message is MergeDevPaymentResponse) {
            "The response has to be present"
        }

        logger.info { "Creating payment external transaction record for company payable id = ${paymentCreateRequest.companyPayableId}" }

        return companyPayableExternalSystemService.createExternalSystemCompanyPayable(
            CompanyPayableExternalSystemTransaction(
                integrationId = companyIntegration.id!!,
                externalSystemTransactionId =
                    AccountingExternalEntity(
                        id = mergePaymentCreationResponse.message.id,
                        externalId = mergePaymentCreationResponse.message.remoteId,
                        name = "Payment created for company payable id = ${paymentCreateRequest.companyPayableId}",
                    ),
                companyPayableId = paymentCreateRequest.companyPayableId, // payment doesn't have company payable id in system, hence using invoice's company payable id.
                entityId = paymentCreateRequest.companyId,
                financialTransactionType = FinancialTransactionType.PAYMENT,
            ),
        )
    }

    fun createVendorCredit(
        externalVendorCreditCreationRequest: ExternalVendorCreditCreationRequest,
    ): CompanyPayableExternalSystemTransaction? {
        val companyIntegration = getCompanyIntegration(externalVendorCreditCreationRequest.integrationId)
        val vendorCreditRequest =
            vendorCreditMapper.mapFinancialTransactionToAccountingTransaction(
                creditNote = externalVendorCreditCreationRequest.creditNote,
                contact =
                    companyAccountingIntegrationMappingService
                        .readExternalVendorMapping(externalVendorCreditCreationRequest.integrationId)
                        ?.externalVendor
                        ?.id
                        ?: throw ExternalMultiplierMappingNotFoundException(
                            "No external vendor mapping. IntegrationId ${companyIntegration.id} ",
                        ),
                companyPayableExternalSystemTransaction = externalVendorCreditCreationRequest.invoicedExternalSystemCompanyPayable,
            )

        logger.info {
            "Creating vendor credit external transaction record for company payable id = ${externalVendorCreditCreationRequest.creditNote.companyPayableId}"
        }

        val createExternalSystemCompanyPayable =
            companyPayableExternalSystemService.createExternalSystemCompanyPayable(
                CompanyPayableExternalSystemTransaction(
                    integrationId = externalVendorCreditCreationRequest.integrationId,
                    companyPayableId = externalVendorCreditCreationRequest.creditNote.companyPayableId,
                    entityId = externalVendorCreditCreationRequest.companyId,
                    financialTransactionType = FinancialTransactionType.CREDIT_NOTE,
                ),
            )

        val mergeDevResponse =
            mergeDevAdapter.createVendorCredit(
                mergeDevVendorCreditRequest =
                    MergeDevVendorCreditRequest(
                        vendorCredit = vendorCreditRequest,
                        accountApi =
                            MergeDevAccountApi(
                                accountToken = companyIntegration.accountToken,
                            ),
                    ),
            )

        require(mergeDevResponse.message is MergeDevVendorCreditMessage) {
            "The response has to be present"
        }

        logger.info {
            "Updating existing vendor credit external transaction record for company payable id = ${externalVendorCreditCreationRequest.creditNote.companyPayableId}"
        }

        return companyPayableExternalSystemService.updateExternalSystemCompanyPayable(
            createExternalSystemCompanyPayable.copy(
                externalSystemTransactionId =
                    AccountingExternalEntity(
                        id = mergeDevResponse.message.id ?: "",
                        externalId = mergeDevResponse.message.remoteId ?: "",
                        name = "Vendor credit created for company payable id = ${externalVendorCreditCreationRequest.creditNote.companyPayableId}",
                    ),
            ),
        )
    }

    private fun getCompanyIntegration(integrationId: Long) =
        jpaCompanyIntegrationRepository
            .findById(integrationId)
            .apply {
                require(isPresent) { "Company integration not found for integrationId $integrationId" }
            }.get()

    private fun buildMergeDevInvoiceRequest(
        accountToken: String,
        requestPayload: AccountingTransaction?,
    ) = MergeDevInvoiceRequest(
        MergeDevAccountApi(accountToken = accountToken),
        invoice = requestPayload as AccountingTransactionInvoice,
    )

    private fun buildUpdatedTransaction(
        transaction: CompanyPayableExternalSystemTransaction,
        message: MergeDevInvoiceMessage,
    ) = transaction.copy(
        externalSystemTransactionId =
            message.id?.let {
                AccountingExternalEntity(
                    id = it,
                    externalId = message.remoteId ?: "",
                    name = "",
                )
            },
        responsePayload = message.invoice,
    )
}