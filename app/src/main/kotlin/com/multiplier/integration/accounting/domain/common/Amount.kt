package com.multiplier.integration.accounting.domain.common

import com.multiplier.integration.types.CurrencyCode

data class Amount(
    val currencyCode: CurrencyCode,
    val value: Double,
) {

    fun isGreaterThan(amount: Amount?): Boolean {
        if (amount == null) return true
        validateSameCurrency(amount)
        return this.value > amount.value
    }

    fun isNotEqualTo(amount: Amount): <PERSON><PERSON><PERSON> {
        validateSameCurrency(amount)
        return this.value != amount.value
    }

    fun isAmountZero(): Boolean {
        return this.value == 0.00
    }

    fun minus(amount: Amount?): Amount {
        if (amount == null) return this.copy()
        validateSameCurrency(amount)
        return Amount(currencyCode, this.value - amount.value)
    }

    private fun validateSameCurrency(amount: Amount) {
        require(this.currencyCode == amount.currencyCode) {
            "The currency codes are not same, hence not possible to tell which is greater"
        }
    }

}
