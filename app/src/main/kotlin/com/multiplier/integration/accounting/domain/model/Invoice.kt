package com.multiplier.integration.accounting.domain.model

import com.multiplier.integration.accounting.domain.MultiplierAccountingSystemIdentifier
import com.multiplier.integration.accounting.domain.common.Amount
import java.time.LocalDate

data class Invoice(
    val id: Long,
    val multiplierExternalInvoiceId: MultiplierAccountingSystemIdentifier,
    val reference: String,
    val multiplierExternalInvoiceNumber: MultiplierAccountingSystemIdentifier,
    val dueDate: LocalDate,
    val lineItems: List<LineItem>,
    val amountPaid: Amount,
    val amountDue: Amount,
    val totalAmount: Amount,
    val appliedCreditNoteIds: List<Long>? = emptyList(),
    override val companyPayableId: Long,
    override val companyId: Long,
    override val createdDate: LocalDate,
    override val status: CompanyPayableStatus,
): CompanyPayable(
    companyPayableId = companyPayableId,
    companyId = companyId,
    createdDate = createdDate,
    status = status,
    type = FinancialTransactionType.INVOICE,
)
