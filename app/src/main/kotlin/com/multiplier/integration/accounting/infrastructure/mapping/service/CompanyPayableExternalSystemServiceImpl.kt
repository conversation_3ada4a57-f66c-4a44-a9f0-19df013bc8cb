package com.multiplier.integration.accounting.infrastructure.mapping.service

import com.multiplier.integration.accounting.db.JpaCompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.db.repo.JpaCompanyPayableExternalSystemTransactionRepository
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CompanyPayableExternalSystemServiceImpl(
    private val jpaCompanyPayableExternalSystemTransactionRepository: JpaCompanyPayableExternalSystemTransactionRepository,
) : CompanyPayableExternalSystemService {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun findExternalSystemCompanyPayable(
        companyPayableId: Long,
        financialTransactionType: FinancialTransactionType,
    ): CompanyPayableExternalSystemTransaction? {

        val jpaCompanyPayableExternalSystemTransaction =
            jpaCompanyPayableExternalSystemTransactionRepository.findFirstByCompanyPayableIdAndFinancialTransactionType(
                companyPayableId,
                financialTransactionType
            )

        return jpaCompanyPayableExternalSystemTransaction?.let {
            CompanyPayableExternalSystemTransaction(
                id = it.id,
                integrationId = it.integrationId,
                companyPayableId = it.companyPayableId,
                entityId = it.entityId,
                financialTransactionType = it.financialTransactionType,
                requestPayload = it.requestPayload,
                externalSystemTransactionId = it.externalId,
                responsePayload = it.responsePayload,
            ).also { logger.info { "Company payable external system transaction found: $it" } }
        }
    }

    @Transactional
    override fun createExternalSystemCompanyPayable(
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction,
    ): CompanyPayableExternalSystemTransaction {
        JpaCompanyPayableExternalSystemTransaction(
            integrationId = companyPayableExternalSystemTransaction.integrationId,
            entityId = companyPayableExternalSystemTransaction.entityId,
            externalId = companyPayableExternalSystemTransaction.externalSystemTransactionId,
            companyPayableId = companyPayableExternalSystemTransaction.companyPayableId,
            financialTransactionType = companyPayableExternalSystemTransaction.financialTransactionType,
            requestPayload = companyPayableExternalSystemTransaction.requestPayload,
            responsePayload = companyPayableExternalSystemTransaction.responsePayload,
        ).let {
            return jpaCompanyPayableExternalSystemTransactionRepository.save(it).let { saved ->
                CompanyPayableExternalSystemTransaction(
                    id = saved.id,
                    integrationId = saved.integrationId,
                    externalSystemTransactionId = saved.externalId,
                    companyPayableId = saved.companyPayableId,
                    financialTransactionType = saved.financialTransactionType,
                    requestPayload = saved.requestPayload,
                    responsePayload = saved.responsePayload,
                    entityId = saved.entityId,
                ).also { logger.info { "Company payable external system transaction created: $it" } }
            }
        }
    }

    @Transactional
    override fun updateExternalSystemCompanyPayable(companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction): CompanyPayableExternalSystemTransaction {
        try {
            companyPayableExternalSystemTransaction.id?.let {
                jpaCompanyPayableExternalSystemTransactionRepository.findById(it).ifPresent { saved ->
                    jpaCompanyPayableExternalSystemTransactionRepository.save(
                        saved.copy(
                            externalId = companyPayableExternalSystemTransaction.externalSystemTransactionId,
                            requestPayload = companyPayableExternalSystemTransaction.requestPayload,
                            responsePayload = companyPayableExternalSystemTransaction.responsePayload,
                        )
                    )
                }
            }
        } catch (e: Exception) {
            // Handle the exception, e.g., log it or rethrow it
            logger.error("Error updating external system company payable: ${e.message}", e)
        }
        return companyPayableExternalSystemTransaction
    }
}
