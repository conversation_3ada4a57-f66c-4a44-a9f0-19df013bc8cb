package com.multiplier.integration.accounting.db.repo

import com.multiplier.integration.accounting.db.JpaAccountingLineItemMapping
import com.multiplier.integration.accounting.domain.mapping.LineItemMappingParentType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaAccountingLineItemMappingRepository: JpaRepository<JpaAccountingLineItemMapping, Long> {

    fun findByMappedParentIdAndMappedParentType(
        mappedParentId: Long,
        mappedParentType: LineItemMappingParentType
    ): List<JpaAccountingLineItemMapping>

}