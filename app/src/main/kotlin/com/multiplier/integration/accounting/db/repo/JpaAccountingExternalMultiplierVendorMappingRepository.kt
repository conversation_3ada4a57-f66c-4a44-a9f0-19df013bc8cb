package com.multiplier.integration.accounting.db.repo

import com.multiplier.integration.accounting.db.JpaAccountingExternalMultiplierVendorMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaAccountingExternalMultiplierVendorMappingRepository: JpaRepository<JpaAccountingExternalMultiplierVendorMapping, Long> {

    fun findByIntegrationId(integrationId: Long): JpaAccountingExternalMultiplierVendorMapping?

}