package com.multiplier.integration.accounting.db

import com.multiplier.integration.accounting.domain.mapping.AccountingExternalEntity
import com.multiplier.integration.accounting.domain.mapping.AccountingTransaction
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import org.hibernate.envers.Audited
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(schema = "customer_integration", name = "company_payable_external_system_transaction")
@SequenceGenerator(
    name = "company_payable_external_system_transaction_gen",
    schema = "customer_integration",
    sequenceName = "company_payable_external_system_transaction_seq",
    allocationSize = 1,
    initialValue = 1
)
@EntityListeners(AuditingEntityListener::class)
@Audited
data class JpaCompanyPayableExternalSystemTransaction(

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "company_payable_external_system_transaction_gen")
    var id: Long? = null,

    val integrationId: Long,

    val entityId: Long,

    val companyPayableId: Long,

    @Type(JsonBinaryType::class)
    @Column(columnDefinition = "jsonb")
    val externalId: AccountingExternalEntity? = null,

    @Type(JsonBinaryType::class)
    @Column(columnDefinition = "jsonb")
    val responsePayload: AccountingTransaction? = null,

    @Type(JsonBinaryType::class)
    @Column(columnDefinition = "jsonb")
    val requestPayload: AccountingTransaction? = null,

    @Enumerated(EnumType.STRING)
    val financialTransactionType: FinancialTransactionType,

    @CreatedBy
    @Column(updatable = false)
    var createdBy: Long? = null,

    @CreatedDate
    @Column(updatable = false)
    var createdOn: LocalDateTime? = null,

    @LastModifiedBy
    @Column(updatable = false)
    var updatedBy: Long? = null,

    @LastModifiedDate
    @Column(updatable = false)
    var updatedOn: LocalDateTime? = null,
)