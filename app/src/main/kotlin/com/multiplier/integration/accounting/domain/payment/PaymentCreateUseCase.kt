package com.multiplier.integration.accounting.domain.payment

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class PaymentCreateUseCase(
    private val mergeDevService: MergeDevService,
    private val companyPayableExternalSystemService: CompanyPayableExternalSystemService,
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun createPaymentUseCase(
        paymentCreateRequest: PaymentCreateRequest
    ): CompanyPayableExternalSystemTransaction {
        logger.info { "Creating payment for request = $paymentCreateRequest" }

        val companyPayableExternalSystemTransaction = companyPayableExternalSystemService.findExternalSystemCompanyPayable(
            companyPayableId = paymentCreateRequest.invoice.companyPayableId,
            financialTransactionType = FinancialTransactionType.PAYMENT,
        )

        val paymentAggregate = PaymentAggregate(companyPayableExternalSystemTransaction)

        return paymentAggregate.createPayment(
            paymentCreateRequest,
            mergeDevService
        )
    }

}