package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.model.Invoice

data class InvoicePaymentStrategyFactoryInput(
    val invoice: Invoice,
    val externalCompanyPayableAmount: ExternalCompanyPayableAmount,
    val totalCreditNoteAmount: Amount?,
)
