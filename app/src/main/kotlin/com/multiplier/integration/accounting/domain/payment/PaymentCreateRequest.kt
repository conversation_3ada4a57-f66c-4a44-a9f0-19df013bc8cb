package com.multiplier.integration.accounting.domain.payment

import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.model.Invoice
import java.time.LocalDateTime

data class PaymentCreateRequest(
    val invoice: Invoice,
    val currentExternalInvoiceAmount: ExternalCompanyPayableAmount,
    val appliedCreditNotesAmount: Amount?,
    val integrationId: Long,
    val createdDate: LocalDateTime,
    val companyId: Long,
)
