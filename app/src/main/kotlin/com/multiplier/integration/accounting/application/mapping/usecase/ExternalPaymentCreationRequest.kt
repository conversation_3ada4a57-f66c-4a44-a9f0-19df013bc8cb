package com.multiplier.integration.accounting.application.mapping.usecase

import com.multiplier.integration.accounting.domain.common.Amount
import java.time.LocalDateTime

data class ExternalPaymentCreationRequest(
    val amount: Amount,
    val integrationId: Long,
    val companyPayableId: Long, //For now, it will be created for one company payable. We can extend the functionality later.
    val createdDate: LocalDateTime,
    val companyId: Long,
)
