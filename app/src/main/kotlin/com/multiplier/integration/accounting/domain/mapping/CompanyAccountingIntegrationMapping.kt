package com.multiplier.integration.accounting.domain.mapping

data class CompanyAccountingIntegrationMapping(
    val integrationId: Long? = null,
    val multiplierVendorMapping: ExternalMultiplierVendorMapping? = null,
    val fallbackLineItemMappings: List<LineItemAccountMapping>? = mutableListOf(),
    val legalEntityMappings: List<LegalEntityMapping> = mutableListOf(),
    val departmentMappings: List<DepartmentMapping> = mutableListOf(),
)
