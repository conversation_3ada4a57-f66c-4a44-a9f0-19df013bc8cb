package com.multiplier.integration.accounting.infrastructure.creditnote

import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.creditnote.CreditNoteServiceAdapter
import com.multiplier.integration.types.CurrencyCode
import com.multiplier.payable.grpc.schema.creditnote.GetCreditNoteByIdsRequest
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNote
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteServiceGrpc.GrpcCreditNoteServiceBlockingStub
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Component

@Component
class CreditNoteServiceAdapterImpl: CreditNoteServiceAdapter {

    @GrpcClient("payable-service")
    lateinit var creditNoteService: GrpcCreditNoteServiceBlockingStub

    override fun getTotalAmountForCreditNotes(invoiceId: Long, creditNoteIds: List<Long>): Amount {
        val grpcCreditNotes = creditNoteService.getCreditNoteByIds(
            GetCreditNoteByIdsRequest.newBuilder()
                .addAllIds(creditNoteIds)
                .build()
        )

        val creditNotes = grpcCreditNotes.creditNoteByIdList.map { it.creditNote }

        validateCreditNotes(invoiceId, creditNotes)

        val currencyCode = creditNotes.map { it.currencyCode }.first()

        return Amount(
            CurrencyCode.valueOf(currencyCode),
            creditNotes.sumOf { it.amountApplied }
        )
    }

    private fun validateCreditNotes(
        invoiceId: Long,
        creditNotes: List<GrpcCreditNote>
    ) {
        validateAppliedInvoices(invoiceId, creditNotes)
        validateNoDistinctCurrencies(creditNotes)
    }

    private fun validateAppliedInvoices(
        invoiceId: Long,
        creditNotes: List<GrpcCreditNote>
    ) {
        val appliedInvoicesToAllCreditNotes = creditNotes.flatMap { it.appliedInvoicesList }.find { it != invoiceId }

        require(appliedInvoicesToAllCreditNotes == null) {
            "All credit notes must be applied to the same invoice. Found other applied invoices: $appliedInvoicesToAllCreditNotes"
        }
    }

    private fun validateNoDistinctCurrencies(creditNotes: List<GrpcCreditNote>) {
        val distinctCurrencies = creditNotes.asSequence()
            .map { it.currencyCode }
            .distinct()
            .toList()

        require (distinctCurrencies.size == 1) {
            throw IllegalArgumentException(
                "All credit notes must have the same currency. Found currencies: $distinctCurrencies"
            )
        }
    }

}