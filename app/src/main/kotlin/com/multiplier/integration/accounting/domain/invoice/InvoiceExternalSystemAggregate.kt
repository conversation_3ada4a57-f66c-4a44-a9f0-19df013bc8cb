package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.creditnote.CreditNoteServiceAdapter
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyFactory
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyFactoryInput
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyRequest
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.Invoice

class InvoiceExternalSystemAggregate(
    private val companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction? = null,
) {

    fun updateAmount(
        companyPayable: CompanyPayable,
        invoicePaymentStrategyFactory: InvoicePaymentStrategyFactory,
        creditNoteServiceAdapter: CreditNoteServiceAdapter,
        mergeDevService: MergeDevService,
    ) {

        requireNotNull(companyPayableExternalSystemTransaction) {
            "There is no external transaction present for company payable id = ${companyPayable.companyPayableId}"
        }

        require(companyPayable is Invoice) {
            "the company payable(id = ${companyPayable.companyPayableId}) must be an invoice"
        }

        val appliedCreditNoteTotalAmount = companyPayable.appliedCreditNoteIds?.takeIf { it.isNotEmpty() }?.let {

            require(it.size == 1) {
                "Constraint violation. Constraint = At-most one credit note is applied to invoice. " +
                        "Company Payable id = ${companyPayable.id} and applied credit notes = $it"
            }

            creditNoteServiceAdapter.getTotalAmountForCreditNotes(
                companyPayable.id,
                it
            )
        }

        val externalTransactionAmount = mergeDevService.getAmountsForExternalTransaction(
            companyPayableExternalSystemTransaction
        )

        invoicePaymentStrategyFactory
            .getInvoicePaymentStrategy(
                InvoicePaymentStrategyFactoryInput(
                    companyPayable,
                    externalTransactionAmount,
                    appliedCreditNoteTotalAmount
                )
            )
            .handle(
                InvoicePaymentStrategyRequest(
                    invoice = companyPayable,
                    currentExternalInvoiceAmount = externalTransactionAmount,
                    appliedCreditNoteAmount = appliedCreditNoteTotalAmount,
                    integrationId = companyPayableExternalSystemTransaction.integrationId,
                    companyId = companyPayable.companyId,
                )
            )
    }
}