package com.multiplier.integration.accounting.domain.invoice.payment

import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class InvoicePaymentStrategyFactory(
    invoicePaymentStrategies: List<InvoicePaymentStrategy>,
) {

    private val invoicePaymentStrategyMap: Map<InvoicePaymentStrategyType, InvoicePaymentStrategy> =
        invoicePaymentStrategies.associateBy { it.getStrategyType() }

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun getInvoicePaymentStrategy(
        invoicePaymentStrategyFactoryInput: InvoicePaymentStrategyFactoryInput
    ): InvoicePaymentStrategy {
        val invoice = invoicePaymentStrategyFactoryInput.invoice
        val externalInvoiceAmount = invoicePaymentStrategyFactoryInput.externalCompanyPayableAmount
        val appliedCreditNoteAmount = invoicePaymentStrategyFactoryInput.totalCreditNoteAmount

        if (invoice.totalAmount.isNotEqualTo(externalInvoiceAmount.totalAmount)) {
            logger.info {
                "The current total amount(${invoice.totalAmount} " +
                        "and external invoice total amount(${externalInvoiceAmount.totalAmount} are NOT equal." +
                        "Hence updating external invoice. company Payable id = ${invoice.companyPayableId}"
            }
            return getStrategy(InvoicePaymentStrategyType.INVOICE_UPDATED)
        }

        if (invoice.amountPaid.isGreaterThan(appliedCreditNoteAmount)) {
            return getStrategy(InvoicePaymentStrategyType.CREDIT_NOTE_AND_PAYMENT_VOUCHER)
        }

        logger.info { "No payment required for company payable id = ${invoice.companyPayableId}" }

        return getStrategy(InvoicePaymentStrategyType.NO_PAYMENT)
    }

    private fun getStrategy(strategyType: InvoicePaymentStrategyType): InvoicePaymentStrategy {
        return invoicePaymentStrategyMap[strategyType]
            ?: throw IllegalStateException("Invoice payment strategy for type $strategyType not found")
    }
}