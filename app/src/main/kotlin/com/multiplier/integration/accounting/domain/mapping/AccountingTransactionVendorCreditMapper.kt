package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.accounting.domain.model.LineItem
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCredit
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditAppliedToLines
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditItem
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditRequestBody
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.ZoneOffset

@Component
class AccountingTransactionVendorCreditMapper {
    fun mapFinancialTransactionToAccountingTransaction(
        creditNote: CreditNote,
        contact: String,
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction, // we are applying credit only to single invoice for now
    ): VendorCreditRequestBody =
        VendorCreditRequestBody(
            model =
                VendorCredit(
                    transactionDate = localDateToIsoString(creditNote.createdDate),
                    vendor = contact,
                    currency = creditNote.currencyCode.name,
                    lines = creditNote.lineItems.map { mapLineItemToAccountingTransactionLineItem(it) },
                    trackingCategories = mapTrackingCategories(creditNote),
                    appliedToLines =
                        listOf(
                            VendorCreditAppliedToLines(
                                invoice = companyPayableExternalSystemTransaction.externalSystemTransactionId!!.id,
                                appliedAmount = creditNote.amountTotal.toString(),
                                appliedDate = localDateToIsoString(creditNote.createdDate),
                            ),
                        ),
                ),
        )

    private fun mapLineItemToAccountingTransactionLineItem(lineItem: LineItem): VendorCreditItem =
        VendorCreditItem(
            netAmount = lineItem.grossAmount.value,
            account = lineItem.lineItemAccountMapping!!.externalAccount.id,
        )

    private fun mapTrackingCategories(creditNote: CreditNote): List<String> =
        creditNote.lineItems
            .mapNotNull { it.lineItemAccountMapping?.externalDepartments?.map { department -> department.id } }
            .flatten()
            .distinct()

    private fun localDateToIsoString(date: LocalDate?): String? =
        date?.atStartOfDay(ZoneOffset.systemDefault().rules.getOffset(date.atStartOfDay()))?.toString()
}
