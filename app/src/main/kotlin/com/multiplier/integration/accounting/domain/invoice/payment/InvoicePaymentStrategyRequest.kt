package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.model.Invoice

data class InvoicePaymentStrategyRequest(
    val invoice: Invoice,
    val currentExternalInvoiceAmount: ExternalCompanyPayableAmount,
    val appliedCreditNoteAmount: Amount?,
    val integrationId: Long,
    val companyId: Long,
)
