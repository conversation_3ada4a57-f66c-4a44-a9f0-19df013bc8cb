package com.multiplier.integration.accounting.domain.payment

import com.multiplier.integration.accounting.application.mapping.usecase.ExternalPaymentCreationRequest
import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.model.CompanyPayableStatus

class PaymentAggregate(
    private val companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction?,
) {

    fun createPayment(
        createPaymentCreateRequest: PaymentCreateRequest,
        mergeDevService: MergeDevService,
    ): CompanyPayableExternalSystemTransaction {

        val invoice = createPaymentCreateRequest.invoice

        require(companyPayableExternalSystemTransaction == null) {
            "Payment for company payable already exists. Hence not creating another payment. " +
                    "Company Payable Id = ${invoice.companyPayableId}, external transaction id = ${companyPayableExternalSystemTransaction?.id}"
        }

        require(invoice.status == CompanyPayableStatus.PAID && invoice.amountDue.isAmountZero()) {
            "The invoice should be PAID in full the payment to be applicable.Company Payable Id = ${invoice.companyPayableId}" +
                    " Invoice ID = ${invoice.id}"
        }

        val paymentAmount = invoice.amountPaid
            .minus(createPaymentCreateRequest.appliedCreditNotesAmount)

        return mergeDevService.createPayment(
            ExternalPaymentCreationRequest(
                amount = paymentAmount,
                integrationId = createPaymentCreateRequest.integrationId,
                companyPayableId = invoice.companyPayableId,
                createdDate = createPaymentCreateRequest.createdDate,
                companyId = createPaymentCreateRequest.companyId,
            )
        )
    }
}