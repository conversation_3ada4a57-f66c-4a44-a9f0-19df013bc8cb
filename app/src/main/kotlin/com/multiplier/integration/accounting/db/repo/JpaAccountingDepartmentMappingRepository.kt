package com.multiplier.integration.accounting.db.repo

import com.multiplier.integration.accounting.db.JpaAccountingDepartmentMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaAccountingDepartmentMappingRepository: JpaRepository<JpaAccountingDepartmentMapping, Long> {

    fun findByIntegrationId(integrationId: Long): List<JpaAccountingDepartmentMapping>

}