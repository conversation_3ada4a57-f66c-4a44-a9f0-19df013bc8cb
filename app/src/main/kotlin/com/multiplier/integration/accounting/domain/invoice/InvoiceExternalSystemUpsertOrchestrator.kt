package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.domain.FinancialTransactionUpsertOrchestrator
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.multiplier.integration.service.CustomerIntegrationService
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class InvoiceExternalSystemUpsertOrchestrator(
    private val customerIntegrationService: CustomerIntegrationService,
    private val invoiceExternalSystemTransactionService: InvoiceExternalSystemTransactionService,
) : FinancialTransactionUpsertOrchestrator {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    override fun upsertHandler(companyPayable: CompanyPayable) {
        logger.info { "Creating invoice for company payable ID: ${companyPayable.companyPayableId}" }

        val accountingIntegrationInfo =
            customerIntegrationService.getAccountingIntegrationInfo(companyPayable.companyId)

        logger.info { "Accounting integration info: $accountingIntegrationInfo" }

        invoiceExternalSystemTransactionService.upsert(
            UpsertCompanyPayableExternalSystemTransaction(
                customerIntegrationId = accountingIntegrationInfo.id,
                companyPayable = companyPayable,
                financialTransactionType = getTransactionType(),
            ),
        )
    }

    override fun getTransactionType(): FinancialTransactionType = FinancialTransactionType.INVOICE
}