package com.multiplier.integration.accounting.domain.invoice.payment

import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class NoInvoicePaymentStrategy: InvoicePaymentStrategy {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun getStrategyType(): InvoicePaymentStrategyType = InvoicePaymentStrategyType.NO_PAYMENT

    override fun handle(invoicePaymentStrategyRequest: InvoicePaymentStrategyRequest) {
        logger.info { "No payment was required for request = $invoicePaymentStrategyRequest" }
    }

}