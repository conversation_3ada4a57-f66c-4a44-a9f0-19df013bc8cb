package com.multiplier.integration.accounting.db.repo

import com.multiplier.integration.accounting.db.JpaAccountingLegalEntityMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaAccountingLegalEntityMappingRepository: JpaRepository<JpaAccountingLegalEntityMapping, Long> {

    fun findByIntegrationId(integrationId: Long): List<JpaAccountingLegalEntityMapping>

}