package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.payment.PaymentCreateRequest
import com.multiplier.integration.accounting.domain.payment.PaymentCreateUseCase
import mu.KotlinLogging
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
class InvoicePaymentWithCreditNoteAndPaymentVoucherStrategy(
    private val paymentCreateUseCase: PaymentCreateUseCase,
): InvoicePaymentStrategy {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }
    override fun getStrategyType(): InvoicePaymentStrategyType = InvoicePaymentStrategyType.CREDIT_NOTE_AND_PAYMENT_VOUCHER

    override fun handle(invoicePaymentStrategyRequest: InvoicePaymentStrategyRequest) {
        logger.info { "Payment creation strategy chosen with request = $invoicePaymentStrategyRequest" }

        paymentCreateUseCase.createPaymentUseCase(
            PaymentCreateRequest(
                invoice = invoicePaymentStrategyRequest.invoice,
                currentExternalInvoiceAmount = invoicePaymentStrategyRequest.currentExternalInvoiceAmount,
                appliedCreditNotesAmount = invoicePaymentStrategyRequest.appliedCreditNoteAmount,
                integrationId = invoicePaymentStrategyRequest.integrationId,
                createdDate = LocalDateTime.now(),
                companyId = invoicePaymentStrategyRequest.companyId,
            )
        )
    }

}