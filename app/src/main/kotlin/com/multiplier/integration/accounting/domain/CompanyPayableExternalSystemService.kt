package com.multiplier.integration.accounting.domain

import com.multiplier.integration.accounting.domain.model.FinancialTransactionType


interface CompanyPayableExternalSystemService {

    fun findExternalSystemCompanyPayable(
        companyPayableId: Long,
        financialTransactionType: FinancialTransactionType,
    ): CompanyPayableExternalSystemTransaction?
    fun createExternalSystemCompanyPayable(
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction,
    ): CompanyPayableExternalSystemTransaction

    fun updateExternalSystemCompanyPayable(
        companyPayableExternalSystemTransaction: CompanyPayableExternalSystemTransaction,
    ): CompanyPayableExternalSystemTransaction
}
