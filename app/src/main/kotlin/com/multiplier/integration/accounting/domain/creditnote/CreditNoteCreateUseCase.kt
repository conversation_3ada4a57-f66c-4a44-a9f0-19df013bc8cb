package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableAdapter
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.mapping.FinancialTransactionLineItemsMapper
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class CreditNoteCreateUseCase(
    private val companyPayableAdapter: CompanyPayableAdapter,
    private val companyPayableExternalSystemService: CompanyPayableExternalSystemService,
    private val mergeDevService: MergeDevService,
    private val financialTransactionLineItemsMapper: FinancialTransactionLineItemsMapper,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    fun createHandler(companyPayable: CompanyPayable) {
        logger.info { "vendor credit note create handler for company payable: ${companyPayable.companyPayableId} " }

        require(
            companyPayableExternalSystemService.findExternalSystemCompanyPayable(
                companyPayable.companyPayableId,
                FinancialTransactionType.CREDIT_NOTE,
            ) == null,
        ) {
            "vendor credit transaction already executed for the company payable(id = ${companyPayable.companyPayableId})"
        }

        val creditNoteCreateAggregate = CreditNoteCreateAggregate()
        creditNoteCreateAggregate.handle(
            companyPayableAdapter,
            companyPayableExternalSystemService,
            mergeDevService,
            financialTransactionLineItemsMapper,
            companyPayable,
        )

        logger.info { "vendor credit note created for the company payable id: ${companyPayable.companyPayableId}" }
    }
}
