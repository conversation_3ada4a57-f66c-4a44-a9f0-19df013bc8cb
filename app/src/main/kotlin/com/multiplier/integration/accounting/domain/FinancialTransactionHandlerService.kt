package com.multiplier.integration.accounting.domain

import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class FinancialTransactionHandlerService(
    private val financialTransactionCreationFactory: FinancialTransactionUpsertOrchestratorFactory,
    private val companyPayableAdapter: CompanyPayableAdapter,
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun upsertHandler(companyPayableId: Long) {
        val companyPayable = companyPayableAdapter.getCompanyPayable(companyPayableId)

        logger.info { "FinancialTransactionHandlerService company payable: $companyPayable" }
        financialTransactionCreationFactory.getHandler(companyPayable.type)
            .upsertHandler(companyPayable)
    }
}