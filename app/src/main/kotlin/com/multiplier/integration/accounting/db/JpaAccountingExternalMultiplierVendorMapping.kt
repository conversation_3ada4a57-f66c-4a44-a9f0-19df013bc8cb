package com.multiplier.integration.accounting.db

import com.multiplier.integration.accounting.domain.mapping.AccountingExternalEntity
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import org.hibernate.envers.Audited
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(
    schema = "customer_integration",
    name = "accounting_external_multiplier_vendor_mapping"
)
@SequenceGenerator(
    name = "accounting_external_multiplier_vendor_mapping_gen",
    schema = "customer_integration",
    sequenceName = "accounting_external_multiplier_vendor_mapping_seq",
    allocationSize = 1,
    initialValue = 1
)
@EntityListeners(AuditingEntityListener::class)
@Audited
data class JpaAccountingExternalMultiplierVendorMapping(

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "accounting_external_multiplier_vendor_mapping_gen")
    var id:  Long? = null,

    var integrationId: Long,

    @Type(JsonBinaryType::class)
    @Column(columnDefinition = "jsonb")
    val externalVendor: AccountingExternalEntity,

    @CreatedBy
    @Column(updatable = false)
    var createdBy: Long? = null,

    @CreatedDate
    @Column(updatable = false)
    var createdOn: LocalDateTime? = null,

    @LastModifiedBy
    @Column(updatable = false)
    var updatedBy: Long? = null,

    @LastModifiedDate
    @Column(updatable = false)
    var updatedOn: LocalDateTime? = null,
)
