package com.multiplier.integration.accounting.domain.mapping

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.time.LocalDate

@JsonIgnoreProperties(ignoreUnknown = true)
open class AccountingTransaction(
    open val id: String? = null,
    open val createdAt: LocalDate? = null,
    open val modifiedAt: LocalDate? = null,
    open val commonFields: AccountingTransactionCommonFields? = null,
    open val number: String? = null,
    open val totalAmount: Double? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
class AccountingTransactionInvoice(
    val issueDate: LocalDate? = null,
    val dueDate: LocalDate? = null,
    val paidOnDate: LocalDate? = null,
    val memo: String? = null,
    val totalDiscount: Double? = null,
    val subTotal: Double? = null,
    val status: String? = null,
    val totalTaxAmount: Double? = 0.0,
    val balance: Double? = null,
    val remoteUpdatedAt: LocalDate? = null,
    val accountingPeriod: String? = null,
    val contact: String,
    val lineItems: List<AccountingTransactionLineItem> = emptyList(),
    val appliedVendorCredits: List<AppliedVendorCredit> = emptyList(),
    override val id: String? = null,
    override val createdAt: LocalDate? = null,
    override val modifiedAt: LocalDate? = null,
    override val commonFields: AccountingTransactionCommonFields? = null,
    override val number: String? = null,
    override val totalAmount: Double? = null,
) : AccountingTransaction(
        id = id,
        createdAt = createdAt,
        modifiedAt = modifiedAt,
        commonFields = commonFields,
        number = number,
        totalAmount = totalAmount,
    )

@JsonIgnoreProperties(ignoreUnknown = true)
class AccountingTransactionVendorCredit(
    val transactionDate: LocalDate? = null,
    val vendor: String? = null,
    val appliedToLines: List<AppliedToLines> = emptyList(),
    val lineItems: List<AccountingTransactionLineItem> = emptyList(), // this would be lines for vendor credit
    override val id: String? = null,
    override val createdAt: LocalDate? = null,
    override val modifiedAt: LocalDate? = null,
    override val commonFields: AccountingTransactionCommonFields? = null,
    override val number: String? = null,
    override val totalAmount: Double? = null,
) : AccountingTransaction(
        id = id,
        createdAt = createdAt,
        modifiedAt = modifiedAt,
        commonFields = commonFields,
        number = number,
        totalAmount = totalAmount,
    )

@JsonIgnoreProperties(ignoreUnknown = true)
class AccountingTransactionCommonFields(
    val remoteId: String? = null,
    val currency: String? = null,
    val exchangeRate: String? = "1.0",
    val employee: String? = null,
    val company: String? = null,
    val trackingCategories: List<String> = emptyList(),
)

@JsonIgnoreProperties(ignoreUnknown = true)
class AccountingTransactionLineItem(
    val id: String? = null,
    val quantity: Double? = null,
    val unitPrice: Double? = null,
    val totalAmount: Double? = null, // this would be net amount for vendor credit
    val taxRate: String? = null,
    val item: String? = null,
    val account: String? = null,
    val description: String? = null,
    val inclusiveOfTax: Boolean? = null,
    val commonFields: AccountingTransactionCommonFields? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
class AppliedVendorCredit(
    val vendorCreditId: String? = null,
    val amount: Double? = null,
    val date: LocalDate? = null,
)

class AppliedToLines(
    val invoice: String? = null,
    val date: LocalDate? = null,
    val amount: Double? = null,
)
