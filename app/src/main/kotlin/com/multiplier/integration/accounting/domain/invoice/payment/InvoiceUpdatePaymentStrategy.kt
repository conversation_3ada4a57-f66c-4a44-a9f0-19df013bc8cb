package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.FinancialTransactionUpsertOrchestratorFactory
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class InvoiceUpdatePaymentStrategy(
    private val financialTransactionUpsertOrchestratorFactory: FinancialTransactionUpsertOrchestratorFactory,
): InvoicePaymentStrategy {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    override fun getStrategyType(): InvoicePaymentStrategyType = InvoicePaymentStrategyType.INVOICE_UPDATED

    override fun handle(invoicePaymentStrategyRequest: InvoicePaymentStrategyRequest) {
        logger.info { "The invoice has updated, hence calling the PATCH invoice operation." }
        financialTransactionUpsertOrchestratorFactory.getHandler(FinancialTransactionType.INVOICE)
            .upsertHandler(invoicePaymentStrategyRequest.invoice)
    }
}