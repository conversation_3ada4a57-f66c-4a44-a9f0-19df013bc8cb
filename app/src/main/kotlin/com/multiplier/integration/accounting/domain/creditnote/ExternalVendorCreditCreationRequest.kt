package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.model.CreditNote

data class ExternalVendorCreditCreationRequest(
    val creditNote: CreditNote,
    val integrationId: Long,
    val companyId: Long,
    val invoicedExternalSystemCompanyPayable: CompanyPayableExternalSystemTransaction,
)