package com.multiplier.integration.accounting.application.mapping.usecase

import com.multiplier.integration.accounting.domain.mapping.CompanyAccountingIntegrationMapping
import com.multiplier.integration.accounting.domain.mapping.CompanyAccountingIntegrationMappingReadService
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class CompanyIntegrationMappingReadUseCase(
    private val companyAccountingIntegrationMappingReadService: CompanyAccountingIntegrationMappingReadService
) {

    private companion object {
        private val logger = KotlinLogging.logger {  }
    }

    fun read(companyIntegrationId: Long): CompanyAccountingIntegrationMapping {
        logger.info("Reading the company integration mappings for integration id: $companyIntegrationId")
        return companyAccountingIntegrationMappingReadService.read(companyIntegrationId)
    }
}