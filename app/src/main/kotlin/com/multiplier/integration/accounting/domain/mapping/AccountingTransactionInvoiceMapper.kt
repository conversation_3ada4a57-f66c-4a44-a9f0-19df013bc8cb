package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.Invoice
import org.springframework.stereotype.Component

@Component
class AccountingTransactionInvoiceMapper: AccountingTransactionMapper {

    override fun mapFinancialTransactionToAccountingTransaction(companyPayable: CompanyPayable, contact: String): AccountingTransaction {
        val invoice = companyPayable as Invoice
        return AccountingTransactionInvoice(
            commonFields = AccountingTransactionCommonFields(
                currency = invoice.totalAmount.currencyCode.name,
                trackingCategories = mapTrackingCategories(invoice),
            ),
            issueDate = invoice.createdDate,
            dueDate = invoice.dueDate,
            status = invoice.status.name,
            memo = mapMemo(invoice),
            totalAmount = invoice.totalAmount.value,
            balance = invoice.amountDue.value,
            contact = contact,
            lineItems = invoice.lineItems.map { mapLineItemToAccountingTransactionLineItem(it, companyPayable) },
        )
    }

    private fun mapTrackingCategories(invoice: Invoice): List<String>{
        return invoice.lineItems
            .mapNotNull { it.lineItemAccountMapping?.externalDepartments?.map { department -> department.id } }
            .flatten()
            .distinct()
    }

    private fun mapMemo(invoice: Invoice): String {
        return "${invoice.reference} : ${invoice.multiplierExternalInvoiceNumber.id}"
    }
}