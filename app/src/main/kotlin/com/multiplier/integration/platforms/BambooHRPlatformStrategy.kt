package com.multiplier.integration.platforms

import com.google.protobuf.Timestamp
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.CompensationDetail
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooTimeOffRequest
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.member.schema.Member
import com.multiplier.timeoff.schema.GrpcTimeOff
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime

@Component
class BambooHRPlatformStrategy(
    private val featureFlagService: FeatureFlagService
) :
    PlatformStrategy("BambooHR") {

    private fun buildBaseEmployeeRequest(
        member: Member,
        contract: Contract,
        workSiteId: String?,
        positionDetails: Position?,
        primaryEmail: String
    ) = CreateEmployeeRequest(
        firstName = member.firstName,
        lastName = member.lastName,
        workEmail = contract.workEmail,
        personalEmails = listOf(primaryEmail),
        employment = Employment(
            positionId = positionDetails?.positionId,
            designation = positionDetails?.designation
        ),
        workAddress = WorkAddress(
            id = workSiteId
        ),
    )

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ): CreateEmployeePlatformResponse {
        // as bambooHR won't allow us to create an employee without a workEmail, we need to check if the member has one, or we skip the employee creation
        if (contract.workEmail.isNullOrBlank()) {
            log.warn("Skipping employee creation for contract: ${contract.id}, as member does not have a work email")
            throw IntegrationIllegalStateException("workEmail not found for this contract, will skip employee creation.")
        }

        val integration = getAndCheckCompanyIntegration(companyId)

        val workSiteId = nullOnException { getWorksiteId(companyId, integration.platform.id!!) }
        val positionDetails =
            nullOnException { fetchAndMatchPosition(companyId, integration.platform.id!!, contract.position) }
        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val employmentStatus = if (contract.type in listOf(
                ContractOuterClass.ContractType.CONTRACTOR,
                ContractOuterClass.ContractType.FREELANCER
            )
        ) "Contractor" else "Full-Time"

        val request = mapRequest(
            member,
            contract,
            workSiteId,
            positionDetails,
            primaryEmail,
            companyId,
            employmentStatus
        )

        val existingEmployee = findExternalEmployeeByWorkEmail(companyId, integration.platform.id!!, contract.workEmail)

        if (existingEmployee != null) {
            return mapExternalEmployeeToContract(
                integration,
                request,
                contract.id,
                existingEmployee.id
            )
        }

        return createExternalEmployee(companyId, integration, request, contract.id, integration.platform.id!!)
    }

    private fun mapRequest(
        member: Member,
        contract: Contract,
        workSiteId: String?,
        positionDetails: Position?,
        primaryEmail: String,
        companyId: Long,
        employmentStatus: String
    ): CreateEmployeeRequest {
        val request = buildBaseEmployeeRequest(member, contract, workSiteId, positionDetails, primaryEmail).apply {
            if (featureFlagService.isKnitDataModelV2(companyId)) {
                startDate = listOf(
                    contract.startOn.year,
                    contract.startOn.month.toString().padStart(2, '0'),
                    contract.startOn.day.toString().padStart(2, '0')
                ).joinToString("-")
                employmentType = employmentStatus
            } else {
                metadata = mapOf(
                    "hireDate" to listOf(
                        contract.startOn.year,
                        contract.startOn.month.toString().padStart(2, '0'),
                        contract.startOn.day.toString().padStart(2, '0')
                    ).joinToString("-"),
                    "employmentHistoryStatus" to employmentStatus,
                )
            }
        }
        return request
    }

    private fun createExternalEmployee(
        companyId: Long,
        integration: JpaCompanyIntegration,
        request: CreateEmployeeRequest,
        contractId: Long,
        platformId: Long,
    ): CreateEmployeePlatformResponse {
        log.info("[BambooHRPlatformStrategy] Creating employee for company=$companyId")

        requireNotNull(request.workAddress?.id) { "Work address ID must be present to create an external employee." }
        requireNotNull(request.employment?.positionId) { "Position ID must be present to create an external employee." }

        val response = knitAdapter.createEmployeeRecord(companyId, platformId, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository,
            platformContractIntegrationRepository,
            response.data.employeeId,
            contractId,
            platformName,
            log,
            integration.id!!
        )
        log.info("[BambooHRPlatformStrategy] Created employee record on platform=${integration.platform.name}")

        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = response.data.employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    private fun mapExternalEmployeeToContract(
        integration: JpaCompanyIntegration,
        request: CreateEmployeeRequest,
        contractId: Long,
        employeeId: String
    ): CreateEmployeePlatformResponse {
        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository,
            platformContractIntegrationRepository,
            employeeId,
            contractId,
            platformName,
            log,
            integration.id!!
        )
        log.info("[${platformName}HRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[BambooHRPlatformStrategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("BambooHR platform does not support creating employee without integration data")
    }


    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[BambooHRPlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        val workSiteId = getWorksiteId(companyId, integration.platform.id!!)

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")


        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            workEmail = contract.workEmail.ifBlank { primaryEmail },
            workAddress = WorkAddress(
                id = workSiteId
            ),
            firstName = member.firstName,
            lastName = member.lastName,
            personalEmails = listOf(primaryEmail),
        )

        val response =
            knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            throw IntegrationInternalServerException("update employee failed: ${response.error ?: "Unknown error"}")
        }

        log.info("[BambooHRPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    @Transactional
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[BambooHRPlatformStrategy] updateEmployeeCompensation for company=$companyId, contractId=$contractId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val compensation = contractServiceAdapter.getCurrentCompensation(contractId)

        val fixedCompensation = compensation.basePay
        val variableCompensation = compensation.additionalPaysList

//        Uncomment this when contract service fixes date bug in grpc

//        val effectiveDate: String = compensation.basePay.validFromInclusive?.let { validFromInclusive ->
//            val year = validFromInclusive.year
//            val month = validFromInclusive.month.toString().padStart(2, '0')
//            val day = validFromInclusive.day.toString().padStart(2, '0')
//
//            if (year == 0 || validFromInclusive.month == 0 || validFromInclusive.day == 0) {
//                log.error("One of the date components (year, month, day) is set to 0 for contract ID: $contractId")
//                throw IntegrationIllegalStateException("Invalid date component in ValidFromInclusive for contract ID: $contractId")
//            }
//
//            "$year-$month-$day"
//        } ?: throw IntegrationIllegalStateException("ValidFromInclusive date is not set for contract ID: $contractId")


        val fixedPay = CompensationDetail(
            type = "SALARY",
            currency = fixedCompensation.currency,
            frequency = fixedCompensation.frequency.toString().lowercase(),
            amount = fixedCompensation.amount.toBigDecimal(),
            payPeriod = fixedCompensation.frequency.toString()
        )

        val variablePays = mutableListOf<CompensationDetail>()

        // add BONUS as variable compensations
        for (compensationDetail in variableCompensation) {
            val pay = CompensationDetail(
                type = "BONUS",
                currency = compensationDetail.currency,
                frequency = "annually",
                amount = compensationDetail.amount.toBigDecimal()
            )
            variablePays.add(pay)
        }

        val request = UpdateCompensationRequest(
            employeeId = externalEmployeeId,
            effectiveDate = LocalDate.now().toString(),
            fixed = listOf(fixedPay),
            variable = variablePays
        )

        log.info("Calling knitAdapter to updateEmployeeCompensation for companyId: {}", companyId)
        val response = knitAdapter.updateCompensation(companyId, integration.platform.id!!, request)

        if (response.success != true) {
            throw IntegrationInternalServerException("updateEmployeeCompensation failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails
    ) {
        log.info("[BambooHRPlatformStrategy] Updating employee basic details for company=$companyId, not needed")
        throw UnsupportedOperationException("BambooHR platform does not support updating employee basic details")
    }

    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[BambooHRPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(contractId, integration.platform.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Insurance_Onboarding_Kit#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update OnboardingKit Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("OnboardingKitDocument Update failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[BambooHRPlatformStrategy] Updating updateFactsheetDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Factsheet#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for updateFactsheetDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update factsheet Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("UpdateFactsheetDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[BambooHRPlatformStrategy] Updating updateContractDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Contract#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for ContractDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update contract Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateContractDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[BambooHRPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "SalaryAddendum#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for salary review Document", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update salary review Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateSalaryReviewDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        log.info("[BambooHRPlatformStrategy] upload payslip Document for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)
        val platformId = integration.platform.id!!

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(
            folderType = DocumentFolderType.PAYSLIP,
            integrationId = integration.id!!
        )
            ?: throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        val payslipFolderId = documentFolder.folderId

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            contentType = ".pdf",
            category = payslipFolderId,
        )

        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, platformId, request)

        if (!response.success) {
            throw IntegrationInternalServerException("upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun createApprovedTimeOff(
        integration: JpaCompanyIntegration,
        timeOff: GrpcTimeOff,
        employeeId: String,
        timeoffType: String
    ): Boolean {
        log.info("[BambooHRPlatformStrategy] Adding time off request for employee $employeeId with companyId ${integration.companyId}")
        val addTimeOffResult = knitAdapter.createLeaveRequest(
            companyId = integration.companyId,
            platformId = integration.platform.id!!,
            employeeId = employeeId,
            requestBody = toTimeOffRequest(employeeId, timeoffType, timeOff)
        )

        if (addTimeOffResult.success) {
            log.info("Successfully added time off request for timeoffId=${timeOff.id}")
            log.debug("Approving time off request: ${addTimeOffResult.data?.leaveRequestId}")
            val approveResult = knitAdapter.approveBambooHrTimeOffRequest(
                companyId = integration.companyId,
                platformId = integration.platform.id!!,
                requestId = addTimeOffResult.data?.leaveRequestId!!,
                status = "approved",
                note = "Automatically approved EOR time-off request"
            )
            if (!approveResult.success) {
                log.warn("Failed to approve time off request for timeoffId=${timeOff.id}")
            }
            return approveResult.success
        } else {
            log.warn("Failed to add time off request for timeoffId=${timeOff.id}")
            return false
        }

    }

    override fun cancelTimeOff(
        integration: JpaCompanyIntegration,
        platformTimeoffIntegration: JpaPlatformTimeoffIntegration
    ): Boolean {
        log.info("[BambooHRPlatformStrategy] Cancelling time off request for employee ${platformTimeoffIntegration.employeeId} with companyId ${integration.companyId}")
        val cancelResult = knitAdapter.approveBambooHrTimeOffRequest(
            companyId = integration.companyId,
            platformId = integration.platform.id!!,
            requestId = platformTimeoffIntegration.externalTimeoffId,
            status = "canceled",
            note = "Automatically canceled EOR time-off request"
        )
        return cancelResult.success
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails
    ) {
        log.info(
            "[BambooHRPlatformStrategy] Updating employee contact details for contract $contractId , not needed"
        )
        throw UnsupportedOperationException("BambooHR platform does not support updating employee contact details")
    }

    override fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    ) {
        log.info(
            "[BambooHRPlatformStrategy] Updating employee bank details for contract $contractId"
        )
        throw UnsupportedOperationException("BambooHR platform does not support updating employee bank details")
    }

    fun getWorksiteId(companyId: Long, platformId: Long): String {
        log.info("Fetching work locations for companyId: $companyId")
        val workLocationsResponse = knitAdapter.getBambooWorkLocations(companyId, platformId)

        if (workLocationsResponse.success != true) {
            throw IntegrationInternalServerException("Fetching work locations was not successful: ${workLocationsResponse.error?.msg}")
        }

        // Directly use the parsed data
        val locationList = workLocationsResponse.data?.parsedData?.list
            ?: throw IntegrationInternalServerException("No work locations available for companyId=$companyId")

        val locationName = locationList
            .find { it.alias == "location" }
            ?.options?.option
            ?.firstOrNull()
            ?.name
            ?: throw IntegrationInternalServerException("No location with alias 'location' found for companyId=$companyId")

        return locationName
    }
}
