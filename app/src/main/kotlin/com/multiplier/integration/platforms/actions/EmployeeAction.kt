package com.multiplier.integration.platforms.actions

import mu.KotlinLogging
import org.apache.logging.log4j.util.Strings
import java.io.ByteArrayOutputStream
import java.net.URL
import java.util.*

private val log = KotlinLogging.logger {}

interface EmployeeAction {
    // add fallback to download file from insecure url as doc-gen is a bit unstable
    fun downloadFileAndEncodeBase64(fileUrl: String): String? =
        downloadFileAndEncodeBase64(toSecureUrl(fileUrl)) ?: downloadFileAndEncodeBase64(toInsecureUrl(fileUrl))

    fun downloadFileAndEncodeBase64(url: URL): String? = runCatching {
        url.openStream().use { inputStream ->
            ByteArrayOutputStream().use { buffer ->
                inputStream.copyTo(buffer)
                val result = Base64.getEncoder().encodeToString(buffer.toByteArray())
                if (Strings.isNotEmpty(result))
                    result
                else
                    null
            }
        }
    }.getOrElse { e ->
        log.error("Error while downloading from URL: $url", e)
        null
    }

    fun toSecureUrl(fileUrl: String): URL =
        URL(fileUrl.replace("http://", "https://", ignoreCase = true))

    fun toInsecureUrl(fileUrl: String): URL =
        URL(fileUrl.replace("https://", "http://", ignoreCase = true))

}
