package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.adapter.model.*
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.*
import com.multiplier.integration.platforms.model.Platforms
import com.multiplier.integration.repository.*
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.utils.checkOutgoingSyncEnabled
import com.multiplier.member.schema.Member
import jakarta.persistence.EntityNotFoundException
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime

@Component
class PersonioHRPlatformStrategy() : PlatformStrategy("Personio") {

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse {
        log.info("[PersonioHRPlatformStrategy] Creating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractPosition = contract.position

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        var request = CreateEmployeeRequest(
            employment = Employment(
                positionId = contractPosition,
                designation = contractPosition
            ),
            firstName = member.firstName,
            lastName = member.lastName,
            workEmail = primaryEmail
        )

        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        addEmployeeToContractIntegration(providerRepository, platformRepository, platformContractIntegrationRepository, response.data.employeeId, contract.id, platformName, log, integration.id!!)
        log.info("[PersonioHRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = response.data.employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[PersonioHRPlatformStrategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("Personio platform does not support creating employee without integration data")
    }


    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position

        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = Employment(
                positionId = contractPosition,
                designation = contractPosition
            ),
            firstName = member.firstName,
            lastName = member.lastName,
        )

        val response =
            knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            throw IntegrationInternalServerException("update employee failed: ${response.error ?: "Unknown error"}")
        }

        log.info("[PersonioHRPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    @Transactional
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating employee compensation for company=$companyId")
        throw UnsupportedOperationException("Personio platform does not support updating employee compensation")
    }

    override fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("Personio platform does not support updating employee basic details")
    }

    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val documentCategoriesResponse =
            knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }
        val otherDocumentCategory = documentCategoriesResponse.data?.categories?.find { it.name == "Other documents" }
            ?: documentCategoriesResponse.data?.categories?.first()
        val otherDocumentCategoryId = otherDocumentCategory?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", otherDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Insurance_Onboarding_Kit#${externalEmployeeId}#${LocalDateTime.now()}.pdf",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            category = otherDocumentCategoryId
        )
        log.info("Calling knitAdapter to update OnboardingKit Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("OnboardingKitDocument Update failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating updateFactsheetDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val documentCategoriesResponse =
            knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }
        val otherDocumentCategory = documentCategoriesResponse.data?.categories?.find { it.name == "Other documents" }
            ?: documentCategoriesResponse.data?.categories?.first()
        val otherDocumentCategoryId = otherDocumentCategory?.id
        log.info("Fetched category: {} for updateFactsheetDocument", otherDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Factsheet#${externalEmployeeId}#${LocalDateTime.now()}.pdf",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            category = otherDocumentCategoryId
        )
        log.info("Calling knitAdapter to update factsheet Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("UpdateFactsheetDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating updateContractDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val documentCategoriesResponse =
            knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        val contractCategory = documentCategoriesResponse.data?.categories?.find { it.name == "system:contracts" }
            ?: documentCategoriesResponse.data?.categories?.first()
        val contractCategoryId = contractCategory?.id
        log.info("Fetched category: {} for updateContractDocument", contractCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Contract#${externalEmployeeId}#${LocalDateTime.now()}.pdf",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            category = contractCategoryId
        )
        log.info("Calling knitAdapter to update contract Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateContractDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val documentCategoriesResponse =
            knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }
        val salaryReviewDocumentCategory =
            documentCategoriesResponse.data?.categories?.find { it.name == "system:performance" }
                ?: documentCategoriesResponse.data?.categories?.first()
        val salaryReviewDocumentCategoryId = salaryReviewDocumentCategory?.id
        log.info("Fetched category: {} for updateSalaryReviewDocument", salaryReviewDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "SalaryAddendum#${externalEmployeeId}#${LocalDateTime.now()}.pdf",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            category = salaryReviewDocumentCategoryId
        )
        log.info("Calling knitAdapter to update salary review Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateSalaryReviewDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument,
    ) {
        log.info("[PersonioHRPlatformStrategy] upload payslip Document for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val documentFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!)
            ?: throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        val payslipDocumentCategoryId = documentFolder.folderId
        log.info("Fetched category: {} for uploadPayslipDocument", payslipDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}.pdf",
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            category = payslipDocumentCategoryId,
            contentType = "application/pdf"
        )
        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("Personio platform does not support updating employee contact details")
    }

    override fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    ) {
        log.info("[PersonioHRPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("Personio platform does not support updating employee bank details")
    }

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?,
    ) {
        log.info("[PersonioHRPlatformStrategy] terminateEmployee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)
        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")
        val externalEmployeeId = contractIntegration.platformEmployeeId

        val request = TerminateEmployeeRequest(
            employeeId = externalEmployeeId,
            terminationDate = terminationDate.toString(),
            terminationReason = "dummy"
        )
        log.info("Calling knitAdapter to terminateEmployee for companyId: {}", companyId)
        val response = knitAdapter.terminateEmployee(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("terminateEmployee failed: ${response.error ?: "Unknown error"}")
        }
    }

}
