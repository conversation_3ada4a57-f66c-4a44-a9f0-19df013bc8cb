package com.multiplier.integration.platforms.model

enum class CompensationType {
    SALARY,
    ALLOWANCE,
    BONUS,
    MERIT,
    COMMISSION,
    NOT_SPECIFIED
}

enum class Frequency {
    ANNUAL,
    WEEKLY,
    QUARTERLY,
    SEMI_MONTHLY,
    MONTHLY,
    HOURLY,
    BI_WEEKLY,
    DAILY,
    NOT_SPECIFIED
}

data class CompensationItemData(
        val type: CompensationType,
        val planId: String,
        val amount: Double,
        val percentage: Double?,
        val currency: String,
        val frequency: Frequency
)

data class StockData(
        val planId: String,
        val targetShares: Double,
        val optionsPercentage: Double,
        val stockPercentage: Double,
        val vestingScheduleId: String,
        val vestingScheduleName: String
)

data class CompensationData(
        val effectiveDate: String,
        val fixed: List<CompensationItemData>,
        val variable: List<CompensationItemData>,
        val stock: List<StockData>
)