package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.Address
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequest
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Employee
import com.multiplier.integration.adapter.api.resources.knit.bamboo.findByEmail
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeeEmployeeAction
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.platforms.actions.TerminateEmployeeAction
import com.multiplier.integration.platforms.actions.TerminateEmployeeActionImpl
import com.multiplier.integration.platforms.actions.TimeOffEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateCompensationEmployeeAction
import com.multiplier.integration.platforms.actions.UpdateDetailsEmployeeAction
import com.multiplier.integration.platforms.model.Platforms
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.repository.model.PlatformData
import com.multiplier.integration.repository.model.toPlatformData
import com.multiplier.integration.service.PositionsService
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.service.TimeoffService
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.sync.model.Unit
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.utils.checkOutgoingSyncEnabled
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.integration.utils.toLocalDateTime
import com.multiplier.integration.utils.toYYYYMMDD
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.dateOfBirthOrNull
import com.multiplier.timeoff.schema.GrpcContractIds
import com.multiplier.timeoff.schema.GrpcTimeOff
import com.multiplier.timeoff.schema.GrpcTimeOffs
import com.neovisionaries.i18n.CountryCode
import jakarta.persistence.EntityNotFoundException
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.format.DateTimeFormatter


// TODO: This class should inherit all contracts and provide scaffolding
// TODO: Fix data model, some fields are missing (eg. manager), data between update and create is different?
// TODO: Create shared data class for update
// TODO: platformName should be enum, @see resolvePlatform
abstract class PlatformStrategy(
    val platformName: String
) : CreateEmployeeEmployeeAction,
    UpdateCompensationEmployeeAction,
    UpdateDetailsEmployeeAction,
    TerminateEmployeeAction,
    TimeOffEmployeeAction{

    @Autowired
    protected lateinit var platformRepository: PlatformRepository

    @Autowired
    protected lateinit var providerRepository: ProviderRepository

    @Autowired
    protected lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @Autowired
    protected lateinit var contractServiceAdapter: ContractServiceAdapter

    @Autowired
    protected lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @Autowired
    protected lateinit var positionsService: PositionsService

    @Autowired
    protected lateinit var knitAdapter: KnitAdapter

    @Autowired
    protected lateinit var documentFoldersRepository: DocumentFoldersRepository

    @Autowired
    protected lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @Autowired
    protected lateinit var timeOffServiceAdapter: TimeoffServiceAdapter

    @Autowired
    protected lateinit var timeOffSyncService: TimeOffSyncService

    @Autowired
    protected lateinit var platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository

    @Autowired
    protected lateinit var timeOffService: TimeoffService

    protected val log = KotlinLogging.logger {}

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ): CreateEmployeePlatformResponse {
        log.info("[${platformName}Strategy] Creating employee for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)
        log.info("[${platformName}Strategy] Matching contract position for companyId: {}", companyId)

        // This can be overriden in children
        val request = buildCreateEmployee(integration, companyId, employeeData, member, contract, eventLog)

        val existingEmployee = findExternalEmployeeByWorkEmail(companyId, integration.platform.id!!, contract.workEmail)

        if (existingEmployee != null) {
            return mapExternalEmployeeToContract(
                integration,
                request,
                contract.id,
                existingEmployee.id
            )
        }

        return createExternalEmployee(companyId, integration, request, contract)
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[${platformName}Strategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("${platformName} platform does not support creating employee without integration data")
    }

    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[PlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)


        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")


        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            workEmail = contract.workEmail.ifBlank { primaryEmail },
            workAddress = null,
            firstName = member.firstName,
            lastName = member.lastName,
            personalEmails = listOf(primaryEmail),
        )

        val response =
            knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            throw IntegrationInternalServerException("update employee failed: ${response.error ?: "Unknown error"}")
        }

        log.info("[PlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    private fun createExternalEmployee(
        companyId: Long,
        integration: JpaCompanyIntegration,
        request: CreateEmployeeRequest,
        contract: Contract
    ): CreateEmployeePlatformResponse {
        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException(
                "[${platformName}Strategy] Employee creation failed: ${
                    response.errors?.joinToString(
                        ", "
                    ) ?: "Unknown error"
                }"
            )
        }

        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository,
            platformContractIntegrationRepository,
            response.data.employeeId,
            contract.id,
            platformName,
            log,
            integration.id!!
        )
        log.info("[${platformName}Strategy] Created employee record on platform=${integration.platform.name}")

        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = response.data.employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    protected open fun buildCreateEmployee(
        integration: JpaCompanyIntegration,
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ): CreateEmployeeRequest {
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition, ignoreCache = false)

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val nonPrimaryEmail = member.emailsList
            .firstOrNull { it.type != "primary" }
            ?.email

        val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null
        val address = if (firstMemberAddress !== null) Address(
            addressLine1 = firstMemberAddress.line1 ?: "",
            addressLine2 = firstMemberAddress.line2,
            state = null,
            country = CountryCode.getByAlpha3Code(firstMemberAddress.country?.name).alpha3,
            zipCode = firstMemberAddress.postalCode,
            city = firstMemberAddress.city,
        ) else null

        val request = CreateEmployeeRequest(
            firstName = member.firstName,
            lastName = member.lastName,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            workEmail = contract.workEmail,
            personalEmails = listOf(primaryEmail),
            startDate = contract.startOn.toYYYYMMDD(),
            birthDate = member.dateOfBirthOrNull?.toString(),
            maritalStatus = member.martialStatus?.toString(),
            gender = member.gender.toString(),
            employmentType = contract.type.toString(),
            workAddress = WorkAddress(
                country = contract.country?.toString(),
            ),
            presentAddress = address,

            // TODO: Add manager object
        )

        // Add personalEmails if a non-primary email exists.
        nonPrimaryEmail?.let {
            request.personalEmails = listOf(it)
        }

        return request
    }

    private fun mapExternalEmployeeToContract(
        integration: JpaCompanyIntegration,
        request: CreateEmployeeRequest,
        contractId: Long,
        employeeId: String
    ): CreateEmployeePlatformResponse {
        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository,
            platformContractIntegrationRepository,
            employeeId,
            contractId,
            platformName,
            log,
            integration.id!!
        )
        log.info("[${platformName}HRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    protected fun findExternalEmployeeByWorkEmail(
        companyId: Long,
        platformId: Long,
        workEmail: String
    ): Employee? {
        try {
            val directory =
                knitAdapter.getEmployeeDirectory(companyId, platformId)
            return directory.findByEmail(workEmail)
        } catch (e: Exception) {
            log.error("[PlatformStrategy] Employee lookup using workEmail failed: ", e)
            return null
        }
    }

    protected fun getAndCheckCompanyIntegration(companyId: Long): JpaCompanyIntegration {
        log.info { "[${platformName}Strategy] Fetching enabled integration for company ID: $companyId and platform: ${platformName}" }
        val integration = findEnabledIntegrationByCompanyAndPlatformName(companyId)
            ?: throw IntegrationInternalServerException("[${platformName}Strategy] No enabled integration found for companyId=$companyId with platform '${platformName}'")

        log.info { "[${platformName}Strategy] Found enabled integration for companyId: $companyId" }
        checkOutgoingSyncEnabled(integration)

        return integration
    }


    protected fun findEnabledIntegrationByCompanyAndPlatformName(companyId: Long): JpaCompanyIntegration? {
        val platform =
            getPlatformData() ?: throw IntegrationIllegalStateException("[${platformName}Strategy] Platform data for ${platformName} not found")

        return companyIntegrationRepository.findEnabledIntegration(companyId, platform.id!!)
            ?.firstOrNull()
            ?: throw EntityNotFoundException("[${platformName}Strategy] Enabled integration not found for companyId=$companyId and platformId=${platform.id}")
    }

    // @TODO: Remove this function and in constructor use enum instead
    protected fun resolvePlatform(): Platforms = when(platformName) {
        "BambooHR" -> Platforms.BAMBOOHR
        "Hibob" -> Platforms.HIBOB
        "Keka" -> Platforms.KEKAHR
        "Personio" -> Platforms.PERSONIO
        "SuccessFactors" -> Platforms.SUCCESSFACTORS
        "TriNet" -> Platforms.TRINET
        "Workday" -> Platforms.WORKDAY
        "Zoho People" -> Platforms.ZOHO
        "ADP WorkForceNow" -> Platforms.WORKFORCENOW
        "Oracle HCM" -> Platforms.ORACLE
        "Paychex" -> Platforms.PAYCHEX
        else -> throw IntegrationInternalServerException("Platform data for $platformName not found")
    }


    protected fun getPlatformData(): PlatformData? =
        platformRepository
            .findFirstByCategoryAndName(PlatformCategory.HRIS, resolvePlatform().platformName)
            ?.toPlatformData()


    fun fetchAndMatchPosition(
        companyId: Long,
        platformId: Long,
        contractPosition: String,
        ignoreCache: Boolean = true
    ): Position {
        val positionsResponse = positionsService.getPositions(companyId, platformId, ignoreCache)
            ?: throw IntegrationInternalServerException("[${platformName}Strategy] Failed to fetch positions for companyId=$companyId")

        if (!positionsResponse.success) {
            throw IntegrationInternalServerException("[${platformName}Strategy] Fetching positions was not successful: ${positionsResponse.error?.msg}")
        }

        return positionsResponse.data?.positions?.firstOrNull {
            it.designation.equals(contractPosition, ignoreCase = true)
        } ?: throw IntegrationInternalServerException("[${platformName}Strategy] No matching position found for designation: $contractPosition")
    }

    // @TODO is createEmployeeWithoutIntegrationData even needed and if so what for?

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?
    ) {
        log.info("[${platformName}Strategy] terminateEmployee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        TerminateEmployeeActionImpl(
            knitAdapter,
            platformContractIntegrationRepository,
            integration.platform.id!!,
            platformEmployeeDataRepository
        ).terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason,
            integrationId = integration.id
        )
    }

    protected fun <T> nullOnException(block: () -> T): T? {
        return try {
            block()
        } catch (e: Exception) {
            log.error(e.message, e)
            null
        }
    }

    override fun processTimeOff(integration: JpaCompanyIntegration) {
        log.info("Processing integration: ${integration.id}")
        val syncedEmployees = getSyncedEmployees(integration.id!!)
        if (syncedEmployees.isEmpty()) return

        val contractIdToEmployeeIdMap = getContractIdToEmployeeIdMap(syncedEmployees)
        val timeOffs = getTimeOffs(syncedEmployees)

        processApprovedTimeOffs(integration, timeOffs, contractIdToEmployeeIdMap)
        processDraftTimeOffs(integration, timeOffs)
    }

    private fun getSyncedEmployees(integrationId: Long): List<JpaPlatformContractIntegration> {
        val syncedEmployees = platformContractIntegrationRepository.findByIntegrationId(integrationId)
        if (syncedEmployees.isNullOrEmpty()) {
            log.info("No synced employees found for integration $integrationId")
        } else {
            log.info("Found ${syncedEmployees.size} synced employees for integration $integrationId")
        }
        return syncedEmployees ?: emptyList()
    }

    private fun getContractIdToEmployeeIdMap(syncedEmployees: List<JpaPlatformContractIntegration>): Map<Long, String> {
        return syncedEmployees.associate { it.contractId to it.platformEmployeeId }
    }

    private fun getTimeOffs(syncedEmployees: List<JpaPlatformContractIntegration>): GrpcTimeOffs {
        val contractIds = syncedEmployees.map { it.contractId }
        val timeOffs = timeOffServiceAdapter.getTimeOffsByContractIds(
            GrpcContractIds.newBuilder().addAllIds(contractIds).build()
        )
        log.info("Retrieved ${timeOffs.timeOffsList.size} time offs for integration ${syncedEmployees.first().integrationId}")
        return timeOffs
    }

    private fun processApprovedTimeOffs(integration: JpaCompanyIntegration, timeOffs: GrpcTimeOffs, contractIdToEmployeeIdMap: Map<Long, String>) {
        val approvedTimeOffs = filterApprovedTimeOffs(timeOffs)
        log.info("Found ${approvedTimeOffs.size} approved/taken time offs for integration ${integration.id}")

        val timeOffTypeMappings = getTimeOffTypeMappings(integration)

        log.info("Retrieved ${timeOffTypeMappings.size} time off type mappings for integration ${integration.id}")

        for (timeOff in approvedTimeOffs) {
            if (platformTimeoffDataRepository.existsByInternalTimeoffId(timeOff.id)) {
                log.debug("Time off ${timeOff.id} already exists, skipping")
                continue
            }

            val timeoffType = timeOffTypeMappings[timeOff.timeOffType.key]
            if (timeoffType == null) {
                log.info("Timeoff type not found for timeoffId=${timeOff.id}, skipped this timeoff")
                continue
            }

            val matchingExternalEmployeeId = contractIdToEmployeeIdMap[timeOff.contractId]
            if (matchingExternalEmployeeId == null) {
                log.info("Employee externalId not found for timeoffId=${timeOff.id} and contractId=${timeOff.contractId}, skipped this timeoff")
                continue
            }

            if (addTimeOff(integration, timeOff, matchingExternalEmployeeId, timeoffType)) {
                handleSuccessfulTimeOff(integration, timeOff, matchingExternalEmployeeId)
            } else {
                log.debug("Failed to add time off request for timeoffId=${timeOff.id}")
            }
        }
    }

    private fun filterApprovedTimeOffs(timeOffs: GrpcTimeOffs): List<GrpcTimeOff> {
        return timeOffs.timeOffsList.filter { it.status.name == "APPROVED" || it.status.name == "TAKEN" }
    }

    private fun getTimeOffTypeMappings(integration: JpaCompanyIntegration): Map<String, String> {
        return timeOffSyncService.getLeaveTypeMappingDefinition(integration.companyId, integration.id!!)
            .associate { it.internalTypeId to it.externalTypeId }
    }

    private fun addTimeOff(integration: JpaCompanyIntegration, timeOff: GrpcTimeOff, externalEmployeeId: String, timeoffType: String): Boolean {
        return createApprovedTimeOff(integration, timeOff, externalEmployeeId, timeoffType)
    }

    private fun handleSuccessfulTimeOff(integration: JpaCompanyIntegration, timeOff: GrpcTimeOff, matchingExternalEmployeeId: String) {
        log.info("Successfully added time off request for timeoffId=${timeOff.id}")
        val externalTimeoffId = fetchExternalTimeOffId(integration, timeOff, matchingExternalEmployeeId)
        timeOffService.addTimeoffDataToCache(
            integrationId = integration.id!!,
            contractId = timeOff.contractId,
            externalTimeoffId = externalTimeoffId,
            employeeId = matchingExternalEmployeeId,
            internalTimeoffId = timeOff.id
        )
        log.debug("Added time off data to cache for timeoffId=${timeOff.id}")
    }

    private fun fetchExternalTimeOffId(integration: JpaCompanyIntegration, timeOff: GrpcTimeOff, matchingExternalEmployeeId: String): String {
        val response = knitAdapter.getEmployeeLeaveRequests(
            integrationId = integration.accountToken,
            employeeId = matchingExternalEmployeeId,
            month = timeOff.startDate.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM"))
        )
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")

        val addedLeaveRequest = response.data?.requests?.firstOrNull {
            it.startDate == timeOff.startDate.toLocalDate().format(formatter) &&
                    it.endDate == timeOff.endDate.toLocalDate().format(formatter) &&
                    it.status == Status.APPROVED
        } ?: throw Exception("Failed to fetch leave request for timeoffId=${timeOff.id}")

        return addedLeaveRequest.id!!
    }

    private fun processDraftTimeOffs(integration: JpaCompanyIntegration, timeOffs: GrpcTimeOffs) {
        val draftTimeOffs = timeOffs.timeOffsList.filter { it.status.name != "APPROVED" && it.status.name != "TAKEN" }
        log.info("Found ${draftTimeOffs.size} non approved/taken time offs for integration ${integration.id}")

        for (timeOff in draftTimeOffs) {

            log.debug("Processing draft time off: ${timeOff.id}")
            if (!platformTimeoffDataRepository.existsByInternalTimeoffId(timeOff.id)) {
                log.debug("Draft time off ${timeOff.id} does not exist, skipping cancelation")
                continue
            }
            val platformTimeoffData = platformTimeoffDataRepository.findByInternalTimeoffId(timeOff.id)
            if (cancelTimeOff(integration, platformTimeoffData!!)) {
                log.info("Successfully canceled draft time off request for timeoffId=${timeOff.id}")
                platformTimeoffDataRepository.delete(platformTimeoffData)
            } else {
                log.warn("Failed to cancel draft time off request for timeoffId=${timeOff.id}")
            }
        }
    }


    override fun createApprovedTimeOff(integration: JpaCompanyIntegration, timeOff: GrpcTimeOff, employeeId: String, timeoffType: String): Boolean{

        log.debug("[PlatformStrategy] Adding time off request for employee $employeeId and company ${integration.companyId}")
        val response = knitAdapter.createLeaveRequest(
            companyId = integration.companyId,
            platformId = integration.platform.id!!,
            employeeId = employeeId,
            requestBody = toTimeOffRequest(employeeId, timeoffType, timeOff)
        )
        return response.success
    }

    override fun cancelTimeOff(integration: JpaCompanyIntegration, platformTimeoffIntegration: JpaPlatformTimeoffIntegration):Boolean {
    log.debug("[PlatformStrategy] Cancelling time off request for employee ${platformTimeoffIntegration.employeeId} and company ${integration.companyId}")
    throw UnsupportedOperationException("Platform does not support cancelling time off requests")
    //TODO("Not yet implemented Unified leave cancel api")
    }

    protected fun toTimeOffRequest(
        matchingExternalEmployeeId: String,
        timeoffType: String,
        timeOff: GrpcTimeOff
    ): LeaveCreateRequest {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
        return LeaveCreateRequest(
            employeeId = matchingExternalEmployeeId,
            leaveTypeId = timeoffType,
            from = timeOff.startDate.toLocalDateTime().format(formatter),
            to = timeOff.endDate.toLocalDateTime().format(formatter),
            amount = timeOff.noOfDays,
            unit = Unit.DAYS,
        )
    }

}