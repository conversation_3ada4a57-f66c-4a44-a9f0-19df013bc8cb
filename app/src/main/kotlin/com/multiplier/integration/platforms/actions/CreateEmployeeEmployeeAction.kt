package com.multiplier.integration.platforms.actions

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.adapter.model.knit.EmployeeDetailData
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Member
import mu.KLogger

data class CreateEmployeePlatformResponse(
    val originalRequest: CreateEmployeeRequest? = null,
    val createdEmployeeId: String,
    val companyIntegrationId: Long,
    val employeeDetailData: EmployeeDetailData? = null
)

interface CreateEmployeeEmployeeAction : EmployeeAction {

    fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse

    fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse?

    fun addEmployeeToContractIntegration(
        providerRepository: ProviderRepository,
        platformRepository: PlatformRepository,
        platformContractIntegrationRepository: PlatformContractIntegrationRepository,
        employeeId: String,
        contractId: Long,
        platformName: String,
        log: KLogger,
        integrationId: Long,
    ) {
        log.info("[${platformName}PlatformStrategy] Created employee record with ID $employeeId on platform=$platformName")

        val provider = providerRepository.findFirstByName(ProviderName.KNIT)
            ?: throw IntegrationIllegalStateException("Provider with name KNIT not found")
        val platformData = platformRepository
            .findFirstByCategoryAndName(PlatformCategory.HRIS, platformName)
            ?: throw IntegrationIllegalStateException("Platform data for $platformName not found")

        val jpaPlatformContractIntegration = JpaPlatformContractIntegration(
            contractId = contractId,
            providerId = provider.id ?: throw IntegrationIllegalStateException("Provider ID is null"),
            provider = provider,
            platformId = platformData.id ?: throw IntegrationIllegalStateException("Platform ID is null"),
            platform = platformData,
            platformEmployeeId = employeeId,
            integrationId = integrationId,
            remoteId = "remoteId" // Drop this column
        )

        platformContractIntegrationRepository.save(jpaPlatformContractIntegration)
    }
}

