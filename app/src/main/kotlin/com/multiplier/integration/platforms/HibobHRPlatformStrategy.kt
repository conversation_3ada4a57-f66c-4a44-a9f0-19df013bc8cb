package com.multiplier.integration.platforms

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.PayFrequency
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.CompensationDetail
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobTimeOffRequest
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.utils.MapperUtil
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.member.schema.Member
import com.multiplier.timeoff.schema.GrpcTimeOff
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlin.random.Random

@Component
class HibobHRPlatformStrategy(
) :
    PlatformStrategy("Hibob") {

    private val payPeriodStringMap =
        mapOf(
            CompensationOuterClass.RateFrequency.HOURLY to "Hourly",
            CompensationOuterClass.RateFrequency.DAILY to "Daily",
            CompensationOuterClass.RateFrequency.WEEKLY to "Weekly",
            CompensationOuterClass.RateFrequency.MONTHLY to "Monthly",
            CompensationOuterClass.RateFrequency.ANNUALLY to "Annual")

    private val payFrequencyStringMap =
        mapOf(
            CompensationOuterClass.RateFrequency.WEEKLY to "Weekly",
            CompensationOuterClass.RateFrequency.SEMIMONTHLY to "Twice a month",
            CompensationOuterClass.RateFrequency.MONTHLY to "Monthly",
            CompensationOuterClass.RateFrequency.ONCE to "One time")

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ): CreateEmployeePlatformResponse {
        log.info("[HibobHRPlatformStrategy] Creating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        log.info("Fetching worksites for companyId: {}", companyId)
        val worksiteResponse = knitAdapter.getWorksitesResponse(companyId, integration.platform.id!!)

        if (worksiteResponse.success != true) {
            throw IntegrationInternalServerException("Fetching worksites was not successful: ${worksiteResponse.error?.msg}")
        }

        val workSiteId = worksiteResponse.data?.responseJson?.body?.values
            ?.filterNot { it.archived ?: false }
            ?.firstOrNull { 
                val mappedCode = MapperUtil.mapToAlpha3CountryCode(it.value ?: "")
                mappedCode.isNotBlank() && mappedCode.equals(contract.country, true) 
            }?.value
            ?: throw IntegrationInternalServerException("No active worksites available for companyId=$companyId and country=${contract.country}")

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val request = CreateEmployeeRequest(
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            workAddress = WorkAddress(
                id = workSiteId
            ),
            personalEmails = listOf(primaryEmail),
            firstName = member.firstName,
            workEmail = contract.workEmail.ifBlank { primaryEmail },
            lastName = member.lastName,
        )

        // check if there's existed employee with the same workEmail on Hibob then ignore the creation and add employee to cache
        val lookupResponse = knitAdapter.lookupHibobEmployeeByEmail(companyId, integration.platform.id!!, request.workEmail!!)
        if (lookupResponse.success) {
            val employeeId = lookupResponse.data!!.employees!!.first().id
            addEmployeeToContractIntegration(providerRepository, platformRepository, platformContractIntegrationRepository, employeeId, contract.id, platformName, log, integration.id!!)
            log.info("[HibobHRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
            return CreateEmployeePlatformResponse(originalRequest = request, createdEmployeeId = employeeId, companyIntegrationId = integration.id!!)
        }

        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        addEmployeeToContractIntegration(providerRepository, platformRepository, platformContractIntegrationRepository, response.data.employeeId, contract.id, platformName, log, integration.id!!)
        log.info("[HibobHRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(originalRequest = request, createdEmployeeId = response.data.employeeId, companyIntegrationId = integration.id!!)
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[HibobHRPlatformStrategy] Creating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        log.info("Matching contract position for companyId: {}", companyId)
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, position)

        log.info("Fetching worksites for companyId: {}", companyId)
        val worksiteResponse = knitAdapter.getWorksitesResponse(companyId, integration.platform.id!!)

        if (worksiteResponse.success != true) {
            throw Exception("Fetching worksites was not successful: ${worksiteResponse.error?.msg}")
        }

        val workSiteId = worksiteResponse.data?.responseJson?.body?.values
            ?.filterNot { it.archived ?: false } // Filter out archived worksites if 'archived' is true.
            ?.firstOrNull()?.value
            ?: throw Exception("No active worksites available for companyId=$companyId")


        val request = CreateEmployeeRequest(
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            workAddress = WorkAddress(
                id = workSiteId
            ),
            personalEmails = listOf(primaryEmail),
            firstName = firstName,
            workEmail = workEmail,
            lastName = lastName,
        )

        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw Exception("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        val employeeId = response.data.employeeId
        log.info("[HibobHRPlatformStrategy] Created employee record with ID $employeeId on platform=${integration.platform.name}")


           log.info("[HibobHRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(originalRequest = request, createdEmployeeId = response.data.employeeId, companyIntegrationId = integration.id!!)

    }

    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[HibobHRPlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        log.info("Fetching worksites for companyId: {}", companyId)
        val worksiteResponse = knitAdapter.getWorksitesResponse(companyId, integration.platform.id!!)

        if (worksiteResponse.success != true) {
            throw IntegrationInternalServerException("Fetching worksites was not successful: ${worksiteResponse.error?.msg}")
        }

        val workSiteId = worksiteResponse.data?.responseJson?.body?.values
            ?.filterNot { it.archived ?: false } // Filter out archived worksites if 'archived' is true.
            ?.firstOrNull()?.value
            ?: throw IntegrationInternalServerException("No active worksites available for companyId=$companyId")

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")


        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            personalEmails = listOf(primaryEmail),
            workAddress = WorkAddress(
                id = workSiteId
            ),
            firstName = member.firstName,
            workEmail = contract.workEmail.ifBlank {primaryEmail},
            lastName = member.lastName,
        )

        val response = knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            if (response.error?.msg?.contains("already", ignoreCase = true) == true) {
                val newRequest = request.copy(
                    workEmail = null
                )
                val newResponse = knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, newRequest)

                if (newResponse.success != true) {
                    throw IntegrationInternalServerException("Employee updation failed after retry: ${response.error ?: "Unknown error"}")
                }
            } else {
                // If the error does not contain "already", throw an exception
                throw IntegrationInternalServerException("Employee updation failed: ${response.error ?: "Unknown error"} for [Hibob]")
            }
        }

        log.info("[HibobHRPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    @Transactional
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[HibobHRPlatformStrategy] updateEmployeeCompensation for company=$companyId, contractId=$contractId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val compensation = contractServiceAdapter.getCurrentCompensation(contractId)

        val fixedCompensation = compensation.basePay
        val variableCompensation = compensation.additionalPaysList

//        Uncomment this when contract service fixes date bug in grpc

//        val effectiveDate: String = compensation.basePay.validFromInclusive?.let { validFromInclusive ->
//            val year = validFromInclusive.year
//            val month = validFromInclusive.month.toString().padStart(2, '0')
//            val day = validFromInclusive.day.toString().padStart(2, '0')
//
//            if (year == 0 || validFromInclusive.month == 0 || validFromInclusive.day == 0) {
//                log.error("One of the date components (year, month, day) is set to 0 for contract ID: $contractId")
//                throw IntegrationIllegalStateException("Invalid date component in ValidFromInclusive for contract ID: $contractId")
//            }
//
//            "$year-$month-$day"
//        } ?: throw IntegrationIllegalStateException("ValidFromInclusive date is not set for contract ID: $contractId")


        val fixedPay = CompensationDetail(
            type = "SALARY",
            currency = fixedCompensation.currency,
            frequency = mapMultiplierToKnitFrequency(fixedCompensation.payFrequency),
            amount = fixedCompensation.amount.toBigDecimal(),
            payPeriod = "ANNUAL"
        )

        val compensationPlans = knitAdapter.getCompensationPlan(companyId, integration.platform.id!!)

        val variableCompensationPlans = compensationPlans.data?.variable

        if (variableCompensationPlans == null) {
            log.warn("Variable compensation plans are missing for company ID: $companyId")
        }

        val variableCompensationTypes = variableCompensationPlans?.map { it.type }.orEmpty()
        val variablePays = mutableListOf<CompensationDetail>()

        for (compensationDetail in variableCompensation) {
            val index = variableCompensation.indexOf(compensationDetail)
            val type = variableCompensationTypes.getOrNull(index)

            // Check if the type is not null and the amount is neither null, zero, nor blank.
            if (type != null && compensationDetail.amount != 0.0) {
                val pay = CompensationDetail(
                    type = type,
                    currency = compensationDetail.currency,
                    frequency = mapMultiplierToKnitFrequency(compensationDetail.payFrequency),
                    amount = compensationDetail.amount.toBigDecimal()
                )
                variablePays.add(pay)
            } else {
                // Log the type if the amount is null, zero, or blank
                log.warn("Ignoring compensation with ID: ${compensationDetail.id} and Type: $type - Amount is null, zero, or blank.")
            }
        }

        if (variableCompensation.size < variableCompensationTypes.size) {
            val unhandledTypes = variableCompensationTypes.drop(variableCompensation.size)
            log.info("The following compensation types were not handled due to missing compensation details: $unhandledTypes")
        }

        // Added due to knit's/hibob issue of giving duplicate date error if same date is used for same employee twice
        val randomDays = Random.nextInt(1, 31)
        val effectiveDate = LocalDate.now().plusDays(randomDays.toLong()).toString()

        val request = UpdateCompensationRequest(
            employeeId = externalEmployeeId,
            effectiveDate = effectiveDate,
            fixed = listOf(fixedPay),
            variable = variablePays
        )
        
        log.info("Calling knitAdapter to updateEmployeeCompensation for companyId: {}", companyId)
        val response = knitAdapter.updateCompensation(companyId, integration.platform.id!!, request)

        if (response.success != true) {
            throw IntegrationInternalServerException("updateEmployeeCompensation failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails
    ) {
        log.info("[HibobHRPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("HibobHR platform does not support updating employee basic details")
    }

    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[HibobHRPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Insurance_Onboarding_Kit#${externalEmployeeId}#${LocalDateTime.now()}",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = "confidential"
        )
        log.info("Calling knitAdapter to update OnboardingKit Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("OnboardingKitDocument Update failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[HibobHRPlatformStrategy] Updating updateFactsheetDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Factsheet#${externalEmployeeId}#${LocalDateTime.now()}",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = "confidential"
        )
        log.info("Calling knitAdapter to update factsheet Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("UpdateFactsheetDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[HibobHRPlatformStrategy] Updating updateContractDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Contract#${externalEmployeeId}#${LocalDateTime.now()}",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = "confidential"
        )
        log.info("Calling knitAdapter to update contract Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateContractDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[HibobHRPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "SalaryAddendum#${externalEmployeeId}#${LocalDateTime.now()}",
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = "confidential"
        )
        log.info("Calling knitAdapter to update salary review Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateSalaryReviewDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        log.info("[HibobHRPlatformStrategy] upload payslip Document for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val documentFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!)
            ?: throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        val payslipFolderId = documentFolder.folderId

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}",
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            contentType = ".pdf",
            category = payslipFolderId
        )
        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun createApprovedTimeOff(
        integration: JpaCompanyIntegration,
        timeOff: GrpcTimeOff,
        employeeId: String,
        timeoffType: String
    ): Boolean {
        log.info("[HibobHRPlatformStrategy] Adding time off request for employee $employeeId with companyId ${integration.companyId}")
        val response = knitAdapter.addHibobLeaveRequest(
            companyId = integration.companyId,
            platformId = integration.platform.id!!,
            employeeId = employeeId,
            requestBody = toHibobTimeOffRequest(timeOff, timeoffType)
        )
        return response.succeeded!!
    }

    override fun cancelTimeOff(
        integration: JpaCompanyIntegration,
        platformTimeoffIntegration: JpaPlatformTimeoffIntegration
    ): Boolean {
        log.info("[HibobHRPlatformStrategy] Cancelling time off request for employee ${platformTimeoffIntegration.employeeId} with companyId ${integration.companyId}")
        val cancelResult = knitAdapter.cancelHibobLeaveRequest(
            companyId = integration.companyId,
            platformId = integration.platform.id!!,
            requestId = platformTimeoffIntegration.externalTimeoffId,
            employeeId = platformTimeoffIntegration.employeeId
        )
        return cancelResult.succeeded!!
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails
    ) {
        log.info("[HibobHRPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("HibobHR platform does not support updating employee contact details")
    }

    override fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    ) {
        log.info(
            "[HibobHRPlatformStrategy] Updating employee bank details for contract $contractId")
        throw UnsupportedOperationException("HibobHR platform does not support updating employee bank details")
    }

    private fun mapMultiplierToKnitFrequency(payFrequency: PayFrequency): String {
        return when (payFrequency) {
            PayFrequency.PAY_FREQUENCY_NULL -> "MONTHLY"
            PayFrequency.PAY_FREQUENCY_WEEKLY -> "WEEKLY"
            PayFrequency.PAY_FREQUENCY_BIWEEKLY -> "BI_WEEKLY"
            PayFrequency.PAY_FREQUENCY_MONTHLY -> "MONTHLY"
            PayFrequency.PAY_FREQUENCY_SEMIMONTHLY -> "MONTHLY"
            else -> throw IntegrationIllegalArgumentException("Unsupported pay frequency: $payFrequency")
        }
    }

    private fun toHibobTimeOffRequest(timeOff: GrpcTimeOff, timeoffType: String): HibobTimeOffRequest {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        return HibobTimeOffRequest(
            startDate = formatter.format(timeOff.startDate.toLocalDate()),
            startDatePortion = "all_day",
            endDate = formatter.format(timeOff.endDate.toLocalDate()),
            endDatePortion = "all_day",
            policyType = timeoffType,
            requestRangeType = "days"
        )
    }
}
