package com.multiplier.integration.platforms.actions

import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.timeoff.schema.GrpcTimeOff

interface TimeOffEmployeeAction : EmployeeAction {

    fun processTimeOff(
        integration: JpaCompanyIntegration
    )
    fun createApprovedTimeOff(integration: JpaCompanyIntegration, timeOff: GrpcTimeOff, employeeId: String, timeoffType: String): Boolean
    fun cancelTimeOff(integration: JpaCompanyIntegration, platformTimeoffIntegration: JpaPlatformTimeoffIntegration): <PERSON>olean
}
