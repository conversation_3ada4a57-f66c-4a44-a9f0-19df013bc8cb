package com.multiplier.integration.platforms.actions

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.InternalDocument
import com.multiplier.member.schema.Member

interface UpdateDetailsEmployeeAction :
    UpdateBasicDetailsEmployeeAction,
    UpdateContactDetailsEmployeeAction,
    UpdateBankDetailsEmployeeAction,
    UpdateInsuranceOnboardingKitEmployeeAction,
    UpdateFactsheetEmployeeAction,
    UpdateContractDocumentEmployeeAction,
    UpdateSalaryReviewDocumentEmployeeAction,
    UploadPayslipDocumentEmployeeAction,
    UpdateEmployeeEmployeeAction

interface UpdateBasicDetailsEmployeeAction : EmployeeAction {

    fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails,
    )
}

interface UpdateContactDetailsEmployeeAction : EmployeeAction {

    fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails,
    )
}

interface UpdateBankDetailsEmployeeAction : EmployeeAction {

    fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    )
}

interface UpdateInsuranceOnboardingKitEmployeeAction : EmployeeAction {

    fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    )
}

interface UpdateFactsheetEmployeeAction : EmployeeAction {

    fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    )
}

interface UpdateContractDocumentEmployeeAction : EmployeeAction {
    fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    )
}

interface UpdateSalaryReviewDocumentEmployeeAction : EmployeeAction {
    fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    )
}

interface UploadPayslipDocumentEmployeeAction : EmployeeAction {
    fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    )
}

interface UpdateEmployeeEmployeeAction : EmployeeAction {
    fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: ContractOuterClass.Contract,
        eventLog: JpaEventLog?,
    )
}