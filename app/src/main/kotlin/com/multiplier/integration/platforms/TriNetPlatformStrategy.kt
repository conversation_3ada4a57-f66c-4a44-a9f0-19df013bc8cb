package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.DefaultTriNetAPIAdapter
import com.multiplier.integration.adapter.api.resources.trinet.Address
import com.multiplier.integration.adapter.api.resources.trinet.BiographicalInfo
import com.multiplier.integration.adapter.api.resources.trinet.CreateDepartmentRequest
import com.multiplier.integration.adapter.api.resources.trinet.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.trinet.CreateLocationRequest
import com.multiplier.integration.adapter.api.resources.trinet.EmploymentInfo
import com.multiplier.integration.adapter.api.resources.trinet.HomeContact
import com.multiplier.integration.adapter.api.resources.trinet.NameField
import com.multiplier.integration.adapter.api.resources.trinet.PhoneNumber
import com.multiplier.integration.adapter.api.resources.trinet.Role
import com.multiplier.integration.adapter.api.resources.trinet.TerminateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeAddressRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeContactRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeNameRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeTitleRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateSingleContactRequest
import com.multiplier.integration.adapter.model.*
import com.multiplier.integration.adapter.model.knit.EmployeeDetailData
import com.multiplier.integration.adapter.model.knit.Profile
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.ManualSyncRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.service.exception.InvalidContractProcessingDateException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.utils.checkOutgoingSyncEnabled
import com.multiplier.integration.utils.getFullName
import com.multiplier.member.schema.Member
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.jvm.optionals.getOrNull
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest as OriginalEmployeeRequest
import com.multiplier.integration.adapter.model.knit.EmployeeData as EmployeeDataCache
import com.neovisionaries.i18n.CountryCode as IsoCountryCode
import kotlinx.coroutines.delay
import com.multiplier.integration.service.exception.IntegrationDownstreamException

@Component
class TriNetPlatformStrategy(
    private val triNetAPIAdapter: DefaultTriNetAPIAdapter,
    private val manualSyncRepository: ManualSyncRepository,
    private val eventLogRepository: EventLogRepository,
):
    PlatformStrategy("TriNet") {
    private val ROLE_INTL_MP = "INTL_MP"
    private val ROLE_INTL_MP_C = "INTL_MP_C"
    private val dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    val DEFAULT_FIELD_MAX_LENGTH = 30
    val PHONE_MAX_LENGTH = 14
    val POSTAL_CODE_MAX_LENGTH = 10
    val POSTAL_CODE_MIN_LENGTH = 5
    val COUNTRY_MAX_LENGTH = 2
    val EMAIL_MAX_LENGTH = 70
    val DEFAULT_POSTAL_CODE = "99999"

    private fun checkIsManualSync(syncId: String?): Boolean {
        if (syncId == null) return false

        val manualSync = manualSyncRepository.findBySyncId(syncId).getOrNull()
        return if (manualSync != null && manualSync.type == SyncType.MANUAL_OUTGOING) {
            true
        } else {
            false
        }
    }

    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: ContractOuterClass.Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse {
        log.info("[TriNetPlatformStrategy] Creating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val employeeContractIntegration = platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(contract.id, integration.provider.id, integration.platform.id)

        if (employeeContractIntegration != null) {
            log.error { "Error creating employee via TriNetPlatformStrategy.createEmployee since there is an ongoing request for creating it" }
            if (eventLog != null) {
                eventLogRepository.delete(eventLog)
            }
            throw IntegrationIllegalStateException("Could not create employee.")
        }

        val jpaPlatformContractIntegration = JpaPlatformContractIntegration(
            contractId = contract.id,
            providerId = integration.provider.id!!,
            provider = integration.provider,
            platformId = integration.platform.id ?: throw IntegrationIllegalStateException("Platform ID is null"),
            platform = integration.platform,
            platformEmployeeId = "temp",
            integrationId = integration.id!!,
            remoteId = "remoteId" // Drop this column
        )

        platformContractIntegrationRepository.save(jpaPlatformContractIntegration)

        var isSuccess = false

        try {
            if (!checkIsManualSync(eventLog?.syncId)) {
                checkOutgoingSyncEnabled(integration)
            }

            if (contract.type == ContractOuterClass.ContractType.HR_MEMBER) {
                log.info("Skipping processing for HR_MEMBER contract for TriNet")
                throw IntegrationIllegalStateException("HR_MEMBER contract type is not supported for TriNet platform.")
            }

            log.info("Matching contract position for companyId: {}", companyId)
            val contractPosition =
                if (contract.type == ContractOuterClass.ContractType.CONTRACTOR) "Independent Contractor" else contract.position

            log.info("Fetching worksites for companyId: {}", companyId)

            val primaryEmail = member.emailsList
                .firstOrNull { it.type == "primary" }
                ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

            val nameField = NameField(firstName = member.firstName.take(DEFAULT_FIELD_MAX_LENGTH), lastName = member.lastName.take(DEFAULT_FIELD_MAX_LENGTH))

            val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null
            val address = Address(
                addressLine1 = firstMemberAddress?.line1?.take(DEFAULT_FIELD_MAX_LENGTH),
                addressLine2 = firstMemberAddress?.line2?.take(DEFAULT_FIELD_MAX_LENGTH),
                state = firstMemberAddress?.state,
                country = IsoCountryCode.getByAlpha3Code(firstMemberAddress?.country?.name).alpha2,
                postalCode = getTriNetPostalCode(firstMemberAddress?.postalCode),
                city = firstMemberAddress?.city?.take(DEFAULT_FIELD_MAX_LENGTH),
            )

            val startDate = LocalDate.of(contract.startOn.year, contract.startOn.month, contract.startOn.day)
            val todayHireDate = LocalDate.now()

            throwIfStartDateIsNotToday(startDate, todayHireDate, contract)

            var deptId = triNetAPIAdapter.getOrFetchDepartmentCode(integration)
            if (deptId == null) {
                val createDepartmentResult = triNetAPIAdapter.createDepartment(
                    integration, CreateDepartmentRequest(
                        effectiveDate = todayHireDate.format(dateFormatter),
                        deptName = "MP - International Workers",
                        shortDesc = "MPINT",
                        deptCode = "MPINT",
                    )
                )

                log.info { "[TriNetPlatformStrategy.createEmployee] Create department result $createDepartmentResult" }
                if (createDepartmentResult != null) {
                    deptId = createDepartmentResult.data?.deptId
                } else {
                    throw IntegrationIllegalStateException("Failed to get or create department MPINT for company ${integration.companyId}")
                }
            }
            val locationName = "MP - ${firstMemberAddress?.country?.getFullName()}"
            val location = triNetAPIAdapter.getLocationByName(integration, locationName)?.data?.firstOrNull()
            var locationId: String = ""

            if (location == null) {
                log.info { "[TriNetPlatformStrategy.createEmployee] Location $locationName not found, creating it..." }
                try {
                    val createLocationResult = triNetAPIAdapter.createLocation(
                        integration, CreateLocationRequest(
                            effectiveDate = todayHireDate.format(dateFormatter),
                            address = Address(
                                address1 = "Remote",
                                country = IsoCountryCode.getByAlpha3Code(firstMemberAddress?.country?.name).alpha2,
                                postalCode = getTriNetPostalCode(firstMemberAddress?.postalCode),
                                city = firstMemberAddress?.city,
                                state = firstMemberAddress?.state
                            ),
                            locationName = locationName,
                            remoteOffice = "Y",
                            shortDesc = firstMemberAddress?.country?.name
                        )
                    )
                    log.info { "[TriNetPlatformStrategy.createEmployee] Create location result $createLocationResult" }
                    if (createLocationResult != null) {
                        locationId = createLocationResult.data?.locationId!!
                    } else {
                        throw IntegrationDownstreamException("Failed to create location $locationName")
                    }
                } catch (e: Exception) {
                    log.error { "[TriNetPlatformStrategy.createEmployee] Error creating location $locationName: ${e.message}" }
                    throw IntegrationDownstreamException("Failed to create location $locationName: ${e.message}")
                }
            } else {
                locationId = location.locationId!!
            }

            val request = CreateEmployeeRequest(
                onboardingStatus = "F",
                name = nameField,
                alternateId = contract.id.toString(),
                preferredName = nameField,
                biographicalInfo = BiographicalInfo(
                    homeContact = HomeContact(
                        address = if (member.addressesCount > 0) address else null,
                        emailAddress = primaryEmail.take(EMAIL_MAX_LENGTH),
                        telephoneNumbers = if (member.phoneNosCount > 0) listOf(PhoneNumber(number = member.getPhoneNos(0).phoneNo.take(PHONE_MAX_LENGTH))) else emptyList()
                    ),
                    gender = "M",
                    genderNonBinary = true,
                ),
                employmentInfo = EmploymentInfo(
                    reasonCode = "HIR",
                    startDate = todayHireDate.format(dateFormatter),
                    seniorityDate = startDate.format(dateFormatter),
                    workEmail = if (contract.workEmail != null && contract.workEmail != "") contract.workEmail.take(EMAIL_MAX_LENGTH) else primaryEmail.take(EMAIL_MAX_LENGTH),
                    workPhone = if (member.phoneNosCount > 0) trimPhoneNumber(member.getPhoneNos(0).phoneNo).take(PHONE_MAX_LENGTH) else null,
                    businessTitle = contractPosition.take(DEFAULT_FIELD_MAX_LENGTH),
                ),
                homeDeptId = deptId,
                locationId = locationId,
                roles = if (contract.type == ContractOuterClass.ContractType.CONTRACTOR || contract.type == ContractOuterClass.ContractType.FREELANCER)
                    listOf(Role(role = ROLE_INTL_MP_C))
                else
                    listOf(Role(role = ROLE_INTL_MP))
            )

            val response = triNetAPIAdapter.createEmployee(integration, 0L, request)

            if (response != null) {
                isSuccess = true
                jpaPlatformContractIntegration.platformEmployeeId = response.data.employeeId!!
            }

            return if (response != null) {
                val triNetData = EmployeeDetailData(
                    firstName = nameField.firstName,
                    lastName = nameField.lastName,
                    addressLine1 = address.addressLine1,
                    addressLine2 = address.addressLine2,
                    country = address.country,
                    city = address.city,
                    postalCode = address.postalCode ?: DEFAULT_POSTAL_CODE,
                    startDate = todayHireDate.format(dateFormatter),
                    emailAddress = primaryEmail,
                    workEmailAddress = primaryEmail,
                    contactNumber = if (member.phoneNosCount > 0) trimPhoneNumber(member.getPhoneNos(0).phoneNo) else null,
                    locationId = locationId,
                    designation = contractPosition,
                    state = address.state
                )
                val originalRequest = OriginalEmployeeRequest(
                    firstName = member.firstName,
                    lastName = member.lastName,
                    workEmail = primaryEmail
                )

                CreateEmployeePlatformResponse(
                    originalRequest = originalRequest,
                    response.data.employeeId!!,
                    integration.id!!,
                    triNetData
                )
            } else {
                log.error { "Error creating employee via TriNetPlatformStrategy.createEmployee with request $request" }
                throw IntegrationDownstreamException("Error creating employee via TriNetPlatformStrategy.createEmployee.")
            }
        } finally {
            if (!isSuccess) {
                platformContractIntegrationRepository.delete(jpaPlatformContractIntegration)
            } else {
                platformContractIntegrationRepository.save(jpaPlatformContractIntegration)
            }
        }
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[TriNetPlatformStrategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("TriNet platform does not support creating employee without integration data")
    }

    fun validateFieldLength(field: String?, maxLength: Int, errorMessage: String, errors: MutableList<String>, minLength: Int = 0) {
        if (field != null && field.length > maxLength) {
            errors.add(errorMessage)
        }
        if (field != null && field.length < minLength) {
            errors.add(errorMessage)
        }
    }

    private fun throwIfStartDateIsNotToday(
        startDate: LocalDate,
        todayHireDate: LocalDate,
        contract: ContractOuterClass.Contract
    ) {
        if (startDate.isAfter(todayHireDate)) {
            throw InvalidContractProcessingDateException("Contract start date ${startDate} is not today ${todayHireDate}, skip processing event and move back into queue", contract)
        }
    }

    private fun getTriNetPostalCode(postalCode: String?): String {
        if (postalCode.isNullOrEmpty()) return DEFAULT_POSTAL_CODE

        // prepend zeros to postal code shorter than 5 characters
        return if (postalCode.length < POSTAL_CODE_MIN_LENGTH) {
            postalCode.padStart(POSTAL_CODE_MIN_LENGTH, '0')
        } else {
            postalCode.take(POSTAL_CODE_MAX_LENGTH)
        }
    }

    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: ContractOuterClass.Contract,
        eventLog: JpaEventLog?,
    ) {
        log.info("[TriNetPlatformStrategy] Updating employee for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)

        if (!checkIsManualSync(eventLog?.syncId)) {
            checkOutgoingSyncEnabled(integration)
        }

        val employeeContractIntegration = platformContractIntegrationRepository.findByContractId(contract.id).firstOrNull()
        val employeeDataCache = platformEmployeeDataRepository.findByEmployeeId(employeeContractIntegration?.platformEmployeeId).firstOrNull()
        if (employeeContractIntegration == null || employeeDataCache == null) {
            // employee is not in cache, we proceed with insert it by calling createEmployee
            log.info("[TriNetPlatformStrategy] Employee is not in cache for company $companyId, proceeding to createEmployee...")
            val response = createEmployee(companyId, employeeData, member, contract, eventLog)
            // update the eventType so it would be exported to report as CREATED instead of UPDATED
            if (eventLog != null) {
                eventLog.eventType = EventType.SERVICE_INTERNAL_CREATE_CONTRACT
                eventLogRepository.save(eventLog)
            }
            insertNewlyCreatedEmployeeToCache(response!!)
            return
        }
        val employeeDataCacheObj = DataMapper.objectMapper.readValue(employeeDataCache.employeeData, EmployeeDataCache::class.java)
        val employeeDetailData = employeeDataCacheObj.employeeDetailData
        val externalEmployeeId = employeeContractIntegration.platformEmployeeId
        var newTriNetData = employeeDataCacheObj.employeeDetailData

        if (employeeDetailData?.firstName != member.firstName || employeeDetailData?.lastName != member.lastName) {
            val updateNameRequest = UpdateEmployeeNameRequest(
                effectiveDate = LocalDate.now().format(dateFormatter),
                nameType = "PRF",
                firstName = member.firstName.take(DEFAULT_FIELD_MAX_LENGTH),
                lastName = member.lastName.take(DEFAULT_FIELD_MAX_LENGTH),
            )
            newTriNetData = newTriNetData?.copy(
                firstName = member.firstName,
                lastName = member.lastName,
            )

            triNetAPIAdapter.updateEmployeeName(
                integration,
                externalEmployeeId,
                0L,
                updateNameRequest
            )
                ?: throw IntegrationInternalServerException("Updating employee exception during update name to TriNet")
        }

        val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null
        val country = IsoCountryCode.getByAlpha3Code(firstMemberAddress?.country?.name).alpha2
        if (
            employeeDetailData?.addressLine1 != firstMemberAddress?.line1 ||
            employeeDetailData?.postalCode != firstMemberAddress?.postalCode ||
            employeeDetailData?.city != firstMemberAddress?.city ||
            employeeDetailData?.country != country
        ) {
            val updateAddressRequest = UpdateEmployeeAddressRequest(
                effectiveDate = LocalDate.now().format(dateFormatter),
                addressType = "HOME",
                address1 = firstMemberAddress?.line1?.take(DEFAULT_FIELD_MAX_LENGTH),
                country = country,
                postalCode = getTriNetPostalCode(firstMemberAddress?.postalCode),
                city = firstMemberAddress?.city?.take(DEFAULT_FIELD_MAX_LENGTH),
                state = firstMemberAddress?.state
            )
            newTriNetData = newTriNetData?.copy(
                addressLine1 = firstMemberAddress?.line1,
                city = firstMemberAddress?.city,
                country = country,
                postalCode = getTriNetPostalCode(firstMemberAddress?.postalCode),
                state = firstMemberAddress?.state
            )

            triNetAPIAdapter.updateEmployeeAddress(
                integration,
                externalEmployeeId,
                0L,
                updateAddressRequest
            )
                ?: throw IntegrationInternalServerException("Updating employee exception during update address to TriNet")
        }

        val contractPosition =
            if (contract.type == ContractOuterClass.ContractType.CONTRACTOR) "Independent Contractor" else contract.position
        if (employeeDetailData?.designation != contractPosition) {
            val updateEmployeeTitleRequest = UpdateEmployeeTitleRequest(
                effectiveDate = LocalDate.now().format(dateFormatter),
                reasonId = "XFR",
                businessTitle = contractPosition
            )
            newTriNetData = newTriNetData?.copy(
                designation = updateEmployeeTitleRequest.businessTitle
            )

            triNetAPIAdapter.updateEmployeeTitle(
                integration,
                externalEmployeeId,
                0L,
                updateEmployeeTitleRequest
            )
                ?: throw IntegrationIllegalStateException("Updating employee exception during update employee title to TriNet")
        }

        val contactRequests = mutableListOf<UpdateSingleContactRequest>()
        val telephoneNumber = if (member.phoneNosCount > 0) trimPhoneNumber(member.getPhoneNos(0).phoneNo).take(PHONE_MAX_LENGTH) else null
        if (employeeDetailData?.contactNumber != telephoneNumber) {
            contactRequests.add(
                UpdateSingleContactRequest(
                    effectiveDate = LocalDate.now().format(dateFormatter),
                    accessType = "Work",
                    media = Constants.UpdateContactMedia.PHONE,
                    telephoneNumber = telephoneNumber
                )
            )
            newTriNetData = newTriNetData?.copy(
                contactNumber = telephoneNumber
            )

        }
        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: member.getEmails(0).email
        if (employeeDetailData?.emailAddress != primaryEmail) {
            contactRequests.add(
                UpdateSingleContactRequest(
                    effectiveDate = LocalDate.now().format(dateFormatter),
                    accessType = "Home",
                    media = Constants.UpdateContactMedia.EMAIL,
                    url = if (member.emailsCount > 0) primaryEmail.take(EMAIL_MAX_LENGTH) else null
                )
            )
            newTriNetData = newTriNetData?.copy(
                emailAddress = primaryEmail
            )
        }
        if (employeeDetailData?.workEmailAddress != contract.workEmail) {
            contactRequests.add(
                UpdateSingleContactRequest(
                    effectiveDate = LocalDate.now().format(dateFormatter),
                    accessType = "Work",
                    media = Constants.UpdateContactMedia.EMAIL,
                    url = if (!contract.workEmail.isNullOrEmpty()) contract.workEmail.take(EMAIL_MAX_LENGTH) else ""
                )
            )
            newTriNetData = newTriNetData?.copy(
                workEmailAddress = contract.workEmail
            )
        }

        if (contactRequests.isNotEmpty()) {
            val updateContactRequest = UpdateEmployeeContactRequest(contactList = contactRequests)
            triNetAPIAdapter.updateEmployeeContacts(
                integration,
                externalEmployeeId,
                0L,
                updateContactRequest
            )
                ?: throw IntegrationInternalServerException("Updating employee exception during update contact to TriNet")
        }

        log.info("[TriNetPlatformStrategy] Updating cache after success updating employee on TN")
        triNetAPIAdapter.updateInternalTriNetEmployeeData(employeeDataCache, newTriNetData, externalEmployeeId)
    }

    private fun trimPhoneNumber(phoneNo: String): String {
        return phoneNo.trim('+').replace(" ", "").replace("-", "")
    }

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?,
    ) {
        log.info("[TriNetPlatformStrategy] terminateEmployee for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)
        if (!checkIsManualSync(eventLog?.syncId)) {
            checkOutgoingSyncEnabled(integration)
        }
        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdAndProviderIdAndPlatformId(contractId, integration.provider.id, integration.platform.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found with contractId=$contractId")
        val externalEmployeeId = contractIntegration.platformEmployeeId
        val platformEmployeeData = platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
            integrationId = integration.id!!,
            externalEmployeeId
        ).firstOrNull()
            ?: throw IntegrationInternalServerException("PlatformEmployeeData not found with contractId=$contractId")

        val request = TerminateEmployeeRequest(
            terminationDate = terminationDate.toString()
        )
        log.info("Calling TriNetAdapter to terminateEmployee for companyId: $companyId - employeeId: $externalEmployeeId")
        val response = triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, request)
        if (!response.success) {
            throw IntegrationInternalServerException("[TriNetPlatformStrategy] terminateEmployee failed: ${response.error ?: "Unknown error"}")
        }
        platformEmployeeData.isDeleted = true
        platformEmployeeDataRepository.save(platformEmployeeData)
    }

    //TODO: move to a seperate cacheService later
    fun insertNewlyCreatedEmployeeToCache(response: CreateEmployeePlatformResponse) {
        log.info("Insert new employee data to cache for employeeId=${response.createdEmployeeId} and companyIntegrationId=${response.companyIntegrationId}")
        try {
            val originalRequest = response.originalRequest!!
            val newEmployeeData = com.multiplier.integration.adapter.model.knit.EmployeeData(
                profile = Profile(
                    firstName = originalRequest.firstName,
                    lastName = originalRequest.lastName,
                    workEmail = originalRequest.workEmail,
                    id = response.createdEmployeeId,
                ),
                employeeDetailData = response.employeeDetailData
            )
            val newPlatformEmployeeData = JpaPlatformEmployeeData(
                employeeId = response.createdEmployeeId,
                employeeData = DataMapper.objectMapper.writeValueAsString(newEmployeeData),
                integrationId = response.companyIntegrationId,
                origin = Constants.EmployeeOrigin.INTERNAL.name,
                isDeleted = false
            )
            platformEmployeeDataRepository.save(newPlatformEmployeeData)
        } catch (e: Exception) {
            log.error("Error while inserting new employee data to cache for employeeId=${response.createdEmployeeId} and companyIntegrationId=${response.companyIntegrationId}", e)
        }
    }

    // Added to conform to PlatformStategy abstract class
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData
    ) {
        log.info("[TriNetPlatformStrategy] updateEmployeeCompensation for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating employee compensation")
    }

    override fun updateEmployeeBasicDetails(companyId: Long, contractId: Long, details: BasicDetails) {
        log.info("[TriNetPlatformStrategy] updateEmployeeBasicDetails for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating employee basic details")
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails
    ) {
        log.info("[TriNetPlatformStrategy] updateEmployeeContactDetails for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating employee contact details")
    }

    override fun updateBankDetails(companyId: Long, contractId: Long, bankData: BankData) {
        log.info("[TriNetPlatformStrategy] updateBankDetails for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating bank details")
    }

    override fun updateOnboardingKitDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[TriNetPlatformStrategy] updateOnboardingKitDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating onboarding kit document")
    }

    override fun updateFactsheetDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[TriNetPlatformStrategy] updateFactsheetDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating factsheet document")
    }

    override fun updateContractDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[TriNetPlatformStrategy] updateContractDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating contract document")
    }

    override fun updateSalaryReviewDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[TriNetPlatformStrategy] updateSalaryReviewDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support updating salary review document")
    }

    override fun uploadPayslipDocument(companyId: Long, contractId: Long, document: InternalDocument) {
        log.info("[TriNetPlatformStrategy] uploadPayslipDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("TriNet platform does not support uploading payslip document")
    }
}