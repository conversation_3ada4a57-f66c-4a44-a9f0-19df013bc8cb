package com.multiplier.integration.platforms

import com.multiplier.common.exception.toSystemException
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.adapter.model.*
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.*
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.member.schema.Member
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime

@Component
class WorkdayHRPlatformStrategy() : PlatformStrategy("Workday") {

//    override fun buildCreateEmployee(
//        integration: JpaCompanyIntegration,
//        companyId: Long,
//        employeeData: EmployeeData?,
//        member: Member,
//        contract: Contract,
//        eventLog: JpaEventLog?): CreateEmployeeRequest {
//        return super.buildCreateEmployee(
//            integration,
//            companyId,
//            employeeData,
//            member,
//            contract,
//            eventLog)
//    }


    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[WorkdayHRPlatformStrategy] createEmployeeWithoutIntegrationData for company=$companyId")
        throw UnsupportedOperationException("Workday platform does not support creating employee without integration data")
    }


    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=${contract.id}")

        var includePositionDetails = true
        var positionDetails: Position? = null
        try {
            val contractPosition = contract.position
            positionDetails =
                fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition, ignoreCache = false)
        } catch (e: Exception) {
            if (e.message?.contains("No matching position found for designation") == true) {
                log.warn("Exception caught and handled: ${e.message}")
                includePositionDetails = false
            } else {
                throw CustomerErrorCode.INTERNAL_ERROR.toSystemException(
                    "Exception caught and handled: ${e.message}",
                    e
                )
            }
        }

        val nonPrimaryEmail = member.emailsList
            .firstOrNull { it.type != "primary" }
            ?.email

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = if (includePositionDetails) Employment(
                positionId = positionDetails?.positionId,
                designation = positionDetails?.designation
            ) else null,
            workAddress = WorkAddress(
                state = "US-CA",
                country = "USA",
                zipCode = "90001"
            ),
            firstName = member.firstName,
            lastName = member.lastName,
            workEmail = primaryEmail
        )

        nonPrimaryEmail?.let {
            request.personalEmails = listOf(it)
        }

        val response =
            knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            throw IntegrationInternalServerException("Update employee failed: ${response.error ?: "Unknown error"}")
        }

        log.info("[WorkdayHRPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }


    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(contractId, integration.platform.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Insurance_Onboarding_Kit#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update OnboardingKit Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("OnboardingKitDocument Update failed: ${response.error ?: "Unknown error"}")
        }
    }


    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating updateFactsheetDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Factsheet#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update factsheet Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("UpdateFactsheetDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating updateContractDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Contract#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update contract Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateContractDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "SalaryAddendum#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update salary review Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateSalaryReviewDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        log.info("[WorkdayHRPlatformStrategy] upload payslip Document for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(
            folderType = DocumentFolderType.PAYSLIP,
            integrationId = integration.id!!
        )
            ?: throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        val payslipDocumentCategoryId = documentFolder.folderId
        log.info("Fetched category: {} for updateOnboardingKitDocument", payslipDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            contentType = ".pdf",
            category = payslipDocumentCategoryId
        )
        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating employee basic details for company=$companyId and contractId=$contractId")

        val integrations = platformContractIntegrationRepository.findByContractId(contractId)
        val integration = integrations.firstOrNull()
            ?: throw IntegrationIllegalArgumentException("Integration not found for contractId=$contractId")

        knitAdapter.updateEmployeeRecord(companyId, integration.platformId, integration.platformEmployeeId, details)
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating employee contact details for company=$companyId and contractId=$contractId")

        val integrations = platformContractIntegrationRepository.findByContractId(contractId)
        val integration = integrations.firstOrNull()
            ?: throw IntegrationIllegalArgumentException("Integration not found for contractId=$contractId")

        knitAdapter.updateEmployeeContactDetails(
            companyId,
            integration.platformId,
            integration.platformEmployeeId,
            contactDetails
        )
    }

    override fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    ) {
        log.info("[WorkdayHRPlatformStrategy] Updating employee bank details for company=$companyId and contractId=$contractId")
        throw UnsupportedOperationException("Workday platform does not support updating employee bank details")
    }


    @Transactional
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[WorkdayHRPlatformStrategy] updateEmployeeCompensation for company=$companyId, contractId=$contractId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")

        val compensation = contractServiceAdapter.getCurrentCompensation(contractId)
            ?: throw IntegrationIllegalStateException("Compensation data is missing for contract ID: $contractId")

        val fixedCompensation = compensation.basePay
        val variableCompensation = compensation.additionalPaysList

        val compensationPlans = knitAdapter.getCompensationPlan(companyId, integration.platform.id!!)

        val fixedCompensationPlans = compensationPlans.data?.fixed

        // Find a suitable fixed pay plan
        val fixedPayPlanId = fixedCompensationPlans?.firstOrNull { it.type == "SALARY" }?.planId
            ?: fixedCompensationPlans?.firstOrNull()?.planId

        val fixedPay = CompensationDetail(
            type = "SALARY",
            planId = fixedPayPlanId,
            currency = fixedCompensation.currency,
            frequency = "MONTHLY",
            amount = fixedCompensation.amount.toBigDecimal()
        )
        val variableCompensationPlans = compensationPlans.data?.variable
        if (variableCompensationPlans == null || variableCompensationPlans.size < variableCompensation.size) {
            log.warn("Variable compensation plans are missing or not sufficient for company ID: $companyId")
        }

        val variablePays = mutableListOf<CompensationDetail>()

        for ((index, compensationDetail) in variableCompensation.withIndex()) {
            val plan = variableCompensationPlans?.getOrNull(index)

            // Check if the plan is not null and the amount is neither null, zero, nor blank.
            if (plan != null && compensationDetail.amount != null && compensationDetail.amount != 0.0) {
                val pay = CompensationDetail(
                    type = plan.type,
                    planId = plan.planId,
                    currency = compensationDetail.currency,
                    frequency = "MONTHLY",
                    amount = compensationDetail.amount.toBigDecimal()
                )
                variablePays.add(pay)
            } else {
                log.warn("Ignoring compensation with ID: ${compensationDetail.id} - No corresponding plan found or amount is invalid.")
            }
        }

        val effectiveDate = LocalDate.now().toString()

        val request = UpdateCompensationRequest(
            employeeId = externalEmployeeId,
            effectiveDate = effectiveDate,
            fixed = listOf(fixedPay),
            variable = variablePays
        )

        log.info("Calling knitAdapter to updateEmployeeCompensation for companyId: {}", companyId)
        val response = knitAdapter.updateCompensation(companyId, integration.platform.id!!, request)

        if (response.success != true) {
            throw IntegrationInternalServerException("updateEmployeeCompensation failed: ${response.error ?: "Unknown error"}")
        }
    }

}