package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.member.schema.Member
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Component
class NamelyPlatformStrategy(
    private val featureFlagService: FeatureFlagService
) :
        PlatformStrategy("Namely") {

    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[NamelyPlatformStrategy] Updating employee for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating employee details")
    }

    override fun updateEmployeeBasicDetails(companyId: Long, contractId: Long, details: BasicDetails) {
        log.info("[NamelyPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating employee basic details")
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails,
    ) {
        log.info("[NamelyPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating employee contact details")
    }

    override fun updateBankDetails(companyId: Long, contractId: Long, bankData: BankData) {
        log.info("[NamelyPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating employee bank details")
    }

    override fun updateOnboardingKitDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[NamelyPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating onboarding kit document")
    }

    override fun updateFactsheetDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[NamelyPlatformStrategy] Updating FactsheetDocument for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating factsheet document")
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[NamelyPlatformStrategy] Updating ContractDocument for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating contract document")
    }

    override fun updateSalaryReviewDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[NamelyPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating salary review document")
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        log.info("[NamelyPlatformStrategy] Uploading PayslipDocument for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support uploading payslip document")
    }

    private inline fun <reified T> encodeToString(value: T): String {
        return Json.encodeToString(value)
    }

    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse {
        log.info("[NamelyPlatformStrategy] Creating employee for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support creating employee API")
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String,
    ): CreateEmployeePlatformResponse? {
        log.info("[NamelyPlatformStrategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support creating employee without integration data")
    }

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?,
    ) {
        log.info("[NamelyPlatformStrategy] Terminating employee for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support terminate employee API")

    }

    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[NamelyPlatformStrategy] Updating employee compensation for company=$companyId")
        throw UnsupportedOperationException("Namely platform does not support updating compensation details")
    }
}