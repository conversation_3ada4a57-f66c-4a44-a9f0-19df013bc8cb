package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.grpc.common.toLocalDate
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaAddressDetails
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaTimeOffRequest
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaUpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.platforms.actions.TerminateEmployeeActionImpl
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.utils.mapToGoogleDate
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.member.schema.Member
import com.multiplier.timeoff.schema.GrpcTimeOff
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Component
class KekaHRPlatformStrategy(
    private val featureFlagService: FeatureFlagService
) :
        PlatformStrategy("Keka") {

    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[KekaHRPlatformStrategy] Updating employee for company=$companyId using passthrough")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null

        val request = KekaUpdateEmployeeDetailsRequest(
            currentAddress = KekaAddressDetails(
                addressLine1 = firstMemberAddress?.line1,
                addressLine2 = firstMemberAddress?.line2,
                city = firstMemberAddress?.city,
                state = firstMemberAddress?.state,
                zip = firstMemberAddress?.zipcode,
                countryCode = firstMemberAddress?.country?.name
            ),
            displayName = "${member.firstName} ${member.lastName}",
            firstName = member.firstName,
            lastName = member.lastName,
            middleName = member.middleName,
            workPhone = if (member.phoneNosCount > 0) member.getPhoneNos(0).phoneNo else null,
            dateOfBirth = member.dateOfBirth.toLocalDate().toString(),
            personalEmail = primaryEmail,
            maritalStatus = member.martialStatusValue.toLong(),
        )

        val response = knitAdapter.updateKekaEmployeeDetailsPassthrough(companyId, integration.platform.id!!, externalEmployeeId, request)
        if (response.succeeded != true) {
            throw IntegrationInternalServerException("Update employee failed: ${response.errors ?: "Unknown error"}")
        }

        log.info("[KekaHRPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    override fun updateEmployeeBasicDetails(companyId: Long, contractId: Long, details: BasicDetails) {
        log.info("[KekaHRPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("KekaHR platform does not support updating employee basic details")
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails,
    ) {
        log.info("[KekaHRPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("KekaHR platform does not support updating employee contact details")
    }

    override fun updateBankDetails(companyId: Long, contractId: Long, bankData: BankData) {
        log.info("[KekaHRPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("KekaHR platform does not support updating employee bank details")
    }

    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[KekaHRPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(contractId, integration.platform.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Insurance_Onboarding_Kit#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update OnboardingKit Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("OnboardingKitDocument Update failed: ${response.error ?: "Unknown error"}")
        }
    }


    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[KekaHRPlatformStrategy] Updating updateFactsheetDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Factsheet#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update factsheet Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("UpdateFactsheetDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[KekaHRPlatformStrategy] Updating updateContractDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Contract#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update contract Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateContractDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[KekaHRPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "SalaryAddendum#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse = knitAdapter.getDocCategories(companyId, integration.platform.id!!)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.data?.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.data?.categories?.first()?.id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update salary review Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateSalaryReviewDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        log.info("[KekaHRPlatformStrategy] upload payslip Document for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!)
            ?: throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        val payslipDocumentCategoryId = documentFolder.folderId
        log.info("Fetched category: {} for updateOnboardingKitDocument", payslipDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            contentType = ".pdf",
            category = payslipDocumentCategoryId
        )
        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun createApprovedTimeOff(
        integration: JpaCompanyIntegration,
        timeOff: GrpcTimeOff,
        employeeId: String,
        timeoffType: String
    ): Boolean {
        log.info("[KekaHRPlatformStrategy] Adding time off request for employee $employeeId with companyId ${integration.companyId}")
        val response = knitAdapter.addKekaLeaveRequest(
            companyId = integration.companyId,
            platformId = integration.platform.id!!,
            employeeId = employeeId,
            requestBody = toKekaTimeOffRequest(timeOff, timeoffType.toInt(), employeeId)
        )
        return response.succeeded!!
    }

    private fun toKekaTimeOffRequest(timeOff: GrpcTimeOff, timeoffType: Int, employeeId: String): KekaTimeOffRequest {
        return KekaTimeOffRequest(
            fromDate = timeOff.startDate.toString(),
            toDate = timeOff.endDate.toString(),
            leaveTypeId = timeoffType.toString(),
            requestedBy = employeeId,
            employeeId = employeeId,
        )
    }

    private inline fun <reified T> encodeToString(value: T): String {
        return Json.encodeToString(value)
    }


    private fun buildBaseEmployeeRequest(
        member: Member,
        contract: Contract,
        workSiteId: String,
        primaryEmail: String
    ) = CreateEmployeeRequest(
        workAddress = WorkAddress(id = workSiteId),
        personalEmails = listOf(primaryEmail),
        firstName = member.firstName,
        workEmail = contract.workEmail.ifBlank { primaryEmail },
        lastName = member.lastName
    )

    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse {
        log.info("[KekaHRPlatformStrategy] Creating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position

        log.info("Fetching worksites for companyId: {}", companyId)
        val worksiteResponse = knitAdapter.getAllLocationsKekaHR(companyId, integration.platform.id!!)

        if (worksiteResponse.succeeded != true) {
            throw IntegrationInternalServerException("Fetching worksites was not successful: ${worksiteResponse.error?.msg}")
        }

        val workSiteId = worksiteResponse.data?.firstOrNull()?.id ?:
                        throw IntegrationInternalServerException("No active worksites available for companyId=$companyId")

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val request = buildBaseEmployeeRequest(member, contract, workSiteId, primaryEmail).apply {
            if (featureFlagService.isKnitDataModelV2(companyId)) {
                log.info("Feature flag KNIT_V2_DATA_MODEL is enabled, using new unified data model")
                employeeNumber = contract.id.toString()
                gender = member.genderValue.toString()
                startDate = contract.startOn.toKekaDate()
                birthDate = member.dateOfBirth.toLocalDate().mapToGoogleDate().toKekaDate()
                location = workSiteId
            } else {
                metadata = mapOf(
                    "gender" to member.genderValue.toString(),
                    "dateJoined" to contract.startOn.toKekaDate(),
                    "dateOfBirth" to member.dateOfBirth.toLocalDate().mapToGoogleDate().toKekaDate(),
                    "location" to workSiteId,
                )
            }
        }

        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)

        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("[KekaHRPlatformStrategy] Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        addEmployeeToContractIntegration(providerRepository, platformRepository, platformContractIntegrationRepository, response.data!!.employeeId!!, contract.id, platformName, log, integration.id!!)
        log.info("[KekaHRPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(originalRequest = null, createdEmployeeId = response.data!!.employeeId!!, companyIntegrationId = integration.id!!)
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String,
    ): CreateEmployeePlatformResponse? {
        log.info("[KekaHRPlatformStrategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("KekaHR platform does not support creating employee without integration data")
    }

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?,
    ) {
        val integration = getAndCheckCompanyIntegration(companyId)

        TerminateEmployeeActionImpl(knitAdapter, platformContractIntegrationRepository, integration.platform.id!!, platformEmployeeDataRepository).terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason,
            integrationId = integrationId
        )
    }

    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[KekaHRPlatformStrategy] Updating employee compensation details for company=$companyId")
        throw UnsupportedOperationException("KekaHR platform does not support updating employee compensation details")
    }

    fun com.google.type.Date.toKekaDate(): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00.00000000'Z'")
        return this.toLocalDate().format(formatter)
    }
}