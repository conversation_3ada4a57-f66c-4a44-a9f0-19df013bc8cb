package com.multiplier.integration.platforms

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Category
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BambooDocumentsService(
    private val knitAdapter: KnitAdapter,
    private val featureFlagService: FeatureFlagService,
){

    fun getPayslipFolderId(
        companyId: Long,
        platformId: Long,
        externalEmployeeId: String,
    ): String? = runBlocking {
        val documentCategoriesResponse = knitAdapter.getDocumentCategories(companyId, platformId, externalEmployeeId)

        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        val folders = documentCategoriesResponse.categories
        val targetFolder = featureFlagService.getStringValue(FeatureFlag.BAMBOO_PAYSLIP_FOLDER_NAME, mapOf("company" to companyId))

        matchTargetFolder(folders, targetFolder)
    }

    private fun matchTargetFolder(folders: List<Category>, targetFolder: String): String? {
        val matchingFolder = folders.firstOrNull { it.name.equals(targetFolder, ignoreCase = true) }

        if (matchingFolder == null) {
            log.warn { "Payslip target folder configured as $targetFolder but not found on bamboo. Available folders: ${folders.map { it.name }}"}
            return null
        }

        return matchingFolder.id
    }
}