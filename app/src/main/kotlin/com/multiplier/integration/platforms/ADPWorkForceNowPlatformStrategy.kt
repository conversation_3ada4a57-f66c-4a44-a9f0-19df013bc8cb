package com.multiplier.integration.platforms

import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.service.InternalDocument
import org.springframework.stereotype.Component

@Component
class ADPWorkForceNowPlatformStrategy() : PlatformStrategy("ADP WorkForceNow") {
    override fun updateEmployeeCompensation(companyId: Long, contractId: Long, compensationDetails: CompensationData) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating employee compensation for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating employee compensation")
    }

    override fun updateEmployeeBasicDetails(companyId: Long, contractId: Long, details: BasicDetails) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating employee basic details")
    }

    override fun updateEmployeeContactDetails(companyId: Long, contractId: Long, contactDetails: ContactDetails) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating employee contact details")
    }

    override fun updateBankDetails(companyId: Long, contractId: Long, bankData: BankData) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating employee bank details")
    }

    override fun updateOnboardingKitDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating onboarding kit document for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating onboarding kit document")
    }

    override fun updateFactsheetDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating factsheet document for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating factsheet document")
    }

    override fun updateContractDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating contract document for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating contract document")
    }

    override fun updateSalaryReviewDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[ADPWorkForceNowPlatformStrategy] Updating salary review document for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support updating salary review document")
    }

    override fun uploadPayslipDocument(companyId: Long, contractId: Long, document: InternalDocument) {
        log.info("[ADPWorkForceNowPlatformStrategy] Uploading payslip document for company=$companyId")
        throw UnsupportedOperationException("ADP WorkForceNow platform does not support uploading payslip document")
    }

}
