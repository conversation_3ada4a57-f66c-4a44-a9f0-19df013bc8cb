package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.CompensationDetail
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationRequest
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.TerminateEmployeeActionImpl
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.member.schema.Member
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Component
class ZohoPlatformStrategy : PlatformStrategy("Zoho People") {

    @Override
    override fun buildCreateEmployee(
        integration: JpaCompanyIntegration,
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ): CreateEmployeeRequest {
        val base = super.buildCreateEmployee(integration, companyId, employeeData, member, contract, eventLog)

        val employmentType = if (contract.type in listOf(
                ContractOuterClass.ContractType.CONTRACTOR,
                ContractOuterClass.ContractType.FREELANCER
            )
        ) "Contractor" else "Full-Time"

        return base.copy(
            employmentType = employmentType,
            workAddress = null,
            startDate = listOf(
                contract.startOn.year,
                contract.startOn.month.toString().padStart(2, '0'),
                contract.startOn.day.toString().padStart(2, '0')
            ).joinToString("-"),
            metadata = mapOf(
                "employeeId" to contract.employeeId
            )
        )
    }

    @Transactional
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateEmployeeCompensation for company=$companyId, contractId=$contractId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val compensation = contractServiceAdapter.getCurrentCompensation(contractId)

        val fixedCompensation = compensation.basePay
        val variableCompensation = compensation.additionalPaysList

        val fixedPay = CompensationDetail(
            type = "SALARY",
            currency = fixedCompensation.currency,
            frequency = fixedCompensation.frequency.toString().lowercase(),
            amount = fixedCompensation.amount.toBigDecimal(),
            payPeriod = fixedCompensation.frequency.toString()
        )

        val variablePays = mutableListOf<CompensationDetail>()

        // add BONUS as variable compensations
        for (compensationDetail in variableCompensation) {
            val pay = CompensationDetail(
                type = "BONUS",
                currency = compensationDetail.currency,
                frequency = "annually",
                amount = compensationDetail.amount.toBigDecimal()
            )
            variablePays.add(pay)
        }

        val request = UpdateCompensationRequest(
            employeeId = externalEmployeeId,
            effectiveDate = LocalDate.now().toString(),
            fixed = listOf(fixedPay),
            variable = variablePays
        )

        log.info("Calling knitAdapter to updateEmployeeCompensation for companyId: {}", companyId)
        val response = knitAdapter.updateCompensation(companyId, integration.platform.id!!, request)

        if (response.success != true) {
            throw IntegrationInternalServerException("updateEmployeeCompensation failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateEmployeeBasicDetails for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating employee basic details")
    }

    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateOnboardingKitDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating onboarding kit document")
    }

    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateFactsheetDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating factsheet document")
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateContractDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating contract document")
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateSalaryReviewDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating salary review document")

    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument
    ) {
        log.info("[ZohoPeoplePlatformStrategy] uploadPayslipDocument for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support uploading payslip document")

    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateEmployeeContactDetails for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating employee contact details")
    }

    override fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    ) {
        log.info("[ZohoPeoplePlatformStrategy] updateBankDetails for company=$companyId, contractId=$contractId")
        throw UnsupportedOperationException("Zoho people platform does not support updating bank details")
    }

    fun fetchAndMatchPosition(
        companyId: Long,
        platformId: Long,
        contractPosition: String
    ): Position {
        val positionsResponse = knitAdapter.getPositionsDetails(companyId, platformId)
            ?: throw IntegrationInternalServerException("Failed to fetch positions for companyId=$companyId")

        if (!positionsResponse.success) {
            throw IntegrationInternalServerException("Fetching positions was not successful: ${positionsResponse.error?.msg}")
        }

        return positionsResponse.data?.positions?.firstOrNull {
            it.designation.equals(contractPosition, ignoreCase = true)
        } ?: throw IntegrationInternalServerException("No matching position found for designation: $contractPosition")
    }

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?
    ) {
        log.info("[ZohoPeoplePlatformStrategy] terminateEmployee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        TerminateEmployeeActionImpl(
            knitAdapter,
            platformContractIntegrationRepository,
            integration.platform.id!!,
            platformEmployeeDataRepository
        ).terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason,
            integrationId = integration.id
        )
    }
}
