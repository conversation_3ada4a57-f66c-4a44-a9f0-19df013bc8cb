package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.grpc.common.toLocalDate
import com.multiplier.integration.adapter.api.resources.knit.Address
import com.multiplier.integration.adapter.api.resources.knit.CompensationDetail
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.UpdateCompensationRequest
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress
import com.multiplier.integration.adapter.model.BankData
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.member.schema.Member
import com.neovisionaries.i18n.CountryCode
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Component
class PaychexPlatformStrategy() : PlatformStrategy("Paychex") {

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ): CreateEmployeePlatformResponse {
        log.info("[PaychexPlatformStrategy] Creating employee for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)
        log.info("[PaychexStrategy] Matching contract position for companyId: {}", companyId)

        val request = buildCreateEmployeePaychex(integration, companyId, member, contract)

        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        // Link employee to contract
        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository,
            platformContractIntegrationRepository,
            response.data.employeeId,
            contract.id,
            platformName,
            log,
            integration.id!!
        )
        log.info("[PaychexPlatformStrategy] Created employee record on platform=${integration.platform.name}")

        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = response.data.employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    private fun buildCreateEmployeePaychex(
        integration: JpaCompanyIntegration,
        companyId: Long,
        member: Member,
        contract: Contract,
    ): CreateEmployeeRequest {
        val contractPosition = contract.position
        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        val nonPrimaryEmail = member.emailsList
            .firstOrNull { it.type != "primary" }
            ?.email

        val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null
        val address = if (firstMemberAddress !== null) Address(
            addressLine1 = firstMemberAddress.line1 ?: "",
            addressLine2 = firstMemberAddress.line2,
            state = null,
            country = CountryCode.getByAlpha3Code(firstMemberAddress.country?.name).alpha2,
            zipCode = firstMemberAddress.postalCode,
            city = firstMemberAddress.city,
        ) else null

        val request = CreateEmployeeRequest(
            firstName = member.firstName,
            lastName = member.lastName,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            workEmail = contract.workEmail,
            personalEmails = listOf(nonPrimaryEmail ?: primaryEmail),
            startDate = contract.startOn.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")),
            birthDate = member.dateOfBirth.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")),
            maritalStatus = member.martialStatus?.toString(),
            gender = member.gender.toString(),
            workAddress = WorkAddress(
                addressLine1 = address?.addressLine1 ?: "",
                state = contract.countryStateCode,
                country = contract.country,
                zipCode = address?.zipCode ?: "",
                city = address?.city ?: ""
            ),
            presentAddress = address,
        )

        return request
    }

    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?
    ) {
        log.info("[PaychexPlatformStrategy] Updating employee for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            firstName = member.firstName,
            lastName = member.lastName,
            gender = member.gender.toString(),
            birthDate = member.dateOfBirth.toLocalDate()
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")),
            startDate = contract.startOn.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")),
            employmentType = contract.type.toString()
        )

        val response = knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            throw IntegrationInternalServerException("Employee update failed: ${response.error ?: "Unknown error"}")
        }

        log.info("[PaychexPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    @Transactional
    override fun updateEmployeeCompensation(companyId: Long, contractId: Long, compensationDetails: CompensationData)
        {
            log.info("[PaychexPlatformStrategy] updateEmployeeCompensation for company=$companyId, contractId=$contractId")

            val integration = getAndCheckCompanyIntegration(companyId)

            val contractIntegration = platformContractIntegrationRepository
                .findFirstByContractIdOrderByCreatedOnDesc(contractId)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

            val externalEmployeeId = contractIntegration.platformEmployeeId

            val compensation = contractServiceAdapter.getCurrentCompensation(contractId)

            val fixedCompensation = compensation.basePay

            val compensationPlans = knitAdapter.getCompensationPlan(companyId, integration.platform.id!!)

            val fixedCompensationPlans = compensationPlans.data?.fixed

            // Find a suitable fixed pay plan
            val fixedPayPlanId = fixedCompensationPlans?.firstOrNull { it.type == "SALARY" }?.planId
                ?: fixedCompensationPlans?.firstOrNull()?.planId

            val fixedPay = CompensationDetail(
                type = "SALARY",
                planId = fixedPayPlanId,
                currency = fixedCompensation.currency,
                frequency = "MONTHLY",
                amount = fixedCompensation.amount.toBigDecimal(),
                payPeriod = fixedCompensation.frequency.toString()
            )

            val request = UpdateCompensationRequest(
                employeeId = externalEmployeeId,
                effectiveDate = LocalDate.now().toString(),
                fixed = listOf(fixedPay),
            )

            log.info("Calling knitAdapter to updateEmployeeCompensation for companyId: {}", companyId)
            val response = knitAdapter.updateCompensation(companyId, integration.platform.id!!, request)

            if (response.success != true) {
                throw IntegrationInternalServerException("updateEmployeeCompensation failed: ${response.error ?: "Unknown error"}")
            }
    }

    override fun updateEmployeeBasicDetails(companyId: Long, contractId: Long, details: BasicDetails) {
        log.info("[PaychexPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating basic details")
    }

    override fun updateEmployeeContactDetails(companyId: Long, contractId: Long, contactDetails: ContactDetails) {
        log.info("[PaychexPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating contact details")
    }

    override fun updateBankDetails(companyId: Long, contractId: Long, bankData: BankData) {
        log.info("[PaychexPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating bank details")
    }

    override fun updateOnboardingKitDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[PaychexPlatformStrategy] Updating onboarding kit document for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating onboarding kit document")
    }

    override fun updateFactsheetDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[PaychexPlatformStrategy] Updating factsheet document for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating factsheet document")
    }

    override fun updateContractDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[PaychexPlatformStrategy] Updating contract document for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating contract document")
    }

    override fun updateSalaryReviewDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        log.info("[PaychexPlatformStrategy] Updating salary review document for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support updating salary review document")
    }

    override fun uploadPayslipDocument(companyId: Long, contractId: Long, document: InternalDocument) {
        log.info("[PaychexPlatformStrategy] Uploading payslip document for company=$companyId")
        throw UnsupportedOperationException("Paychex does not support uploading payslip document")
    }
}