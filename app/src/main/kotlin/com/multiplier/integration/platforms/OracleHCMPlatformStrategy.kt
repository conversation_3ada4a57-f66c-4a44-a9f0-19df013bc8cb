package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.Address
import com.multiplier.integration.adapter.api.resources.knit.CreateDocumentRequest
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.adapter.model.*
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.integration.utils.toYYYYMMDD
import com.multiplier.member.schema.Member
import com.neovisionaries.i18n.CountryCode
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Component
class OracleHCMPlatformStrategy(): PlatformStrategy("Oracle HCM") {

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse {
        log.info("[OracleHCMPlatformStrategy] Creating employee for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)

        // Get primary email
        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val contractPosition = contract.position

        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        // Fetch legal entities through passthrough
        log.info("Fetching legal entities for companyId: {}", companyId)
        val legalEntitiesResponse = knitAdapter.getLegalEntitiesOracleHCM(companyId, integration.platform.id!!)
        
        // Get parsed legal entities
        val legalEntities = legalEntitiesResponse.getLegalEntities()

        // Validate response and parsed data
        if (legalEntitiesResponse.success != true || legalEntities.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to fetch legal entities: ${legalEntitiesResponse.error?.msg ?: "No data received"}")
        }

        val legalEntityId = legalEntities.firstOrNull()?.OrganizationId
            ?: throw IntegrationInternalServerException("No legal entities available for companyId=$companyId")


        val firstMemberAddress = if (member.addressesCount > 0) member.getAddresses(0) else null

        val request = CreateEmployeeRequest(
            employment = Employment(
                positionId = positionDetails.positionId,
            ),
            firstName = member.firstName,
            lastName = member.lastName,
            workEmail = contract.workEmail.ifBlank { primaryEmail },
            startDate = contract.startOn.toYYYYMMDD(),
            birthDate = member.dateOfBirth.toLocalDate().toString(),
            presentAddress = Address(
                addressLine1 = firstMemberAddress?.line1,
                addressLine2 = firstMemberAddress?.line2,
                city = firstMemberAddress?.city,
                state = firstMemberAddress?.state,
                country = CountryCode.getByAlpha3Code(firstMemberAddress?.country?.name)?.alpha2,
                zipCode = firstMemberAddress?.postalCode
            ),
            employeeNumber = contract.id.toString(),
            companyId = legalEntityId.toString()
        )

        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        // Link employee to contract
        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository, 
            platformContractIntegrationRepository,
            response.data.employeeId,
            contract.id,
            platformName,
            log,
            integration.id!!
        )
        log.info("[OracleHCMPlatformStrategy] Created employee record on platform=${integration.platform.name}")

        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = response.data.employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: Contract,
        eventLog: JpaEventLog?,
    ) {
        log.info("[OracleHCMPlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contract.id)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        val primaryEmail = member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId,
            employment = Employment(
                positionId = positionDetails.positionId,
                designation = positionDetails.designation
            ),
            firstName = member.firstName,
            lastName = member.lastName,
            workEmail = contract.workEmail.ifBlank { primaryEmail },
            personalEmails = listOf(primaryEmail)
        )

        val response = knitAdapter.updateEmployeeDetails(
            companyId, 
            integration.id!!, 
            integration.platform.id!!, 
            request
        )
        
        if (response.success != true) {
            throw IntegrationInternalServerException("Employee update failed: ${response.error ?: "Unknown error"}")
        }

        log.info("[OracleHCMPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    override fun updateEmployeeCompensation(companyId: Long, contractId: Long, compensationDetails: CompensationData) {
        log.info("[OracleHCMPlatformStrategy] Updating employee compensation for company=$companyId")
        throw UnsupportedOperationException("Oracle HCM platform does not support updating employee compensation")
    }

    override fun updateEmployeeBasicDetails(companyId: Long, contractId: Long, details: BasicDetails) {
        log.info("[OracleHCMPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("Oracle HCM platform does not support updating employee basic details")
    }

    override fun updateEmployeeContactDetails(companyId: Long, contractId: Long, contactDetails: ContactDetails) {
        log.info("[OracleHCMPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("Oracle HCM platform does not support updating employee contact details")
    }

    override fun updateBankDetails(companyId: Long, contractId: Long, bankData: BankData) {
        log.info("[OracleHCMPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("Oracle HCM platform does not support updating employee bank details")
    }

    private fun uploadDocument(
        companyId: Long,
        contractId: Long, 
        document: DocumentResponse,
        documentType: String
    ) {
        log.info("[OracleHCMPlatformStrategy] Updating $documentType document for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)
        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "${documentType}#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf"
        )

        log.info("Calling knitAdapter to update $documentType Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)
        if (!response.success) {
            throw IntegrationInternalServerException("Update${documentType}Document failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateOnboardingKitDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        uploadDocument(companyId, contractId, document, "Insurance_Onboarding_Kit")
    }

    @Transactional
    override fun updateFactsheetDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        uploadDocument(companyId, contractId, document, "Factsheet")
    }

    @Transactional
    override fun updateContractDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        uploadDocument(companyId, contractId, document, "Contract")
    }

    @Transactional
    override fun updateSalaryReviewDocument(companyId: Long, contractId: Long, document: DocumentResponse) {
        uploadDocument(companyId, contractId, document, "SalaryAddendum")
    }

    @Transactional
    override fun uploadPayslipDocument(companyId: Long, contractId: Long, document: InternalDocument) {
        log.info("[OracleHCMPlatformStrategy] Uploading payslip document for company=$companyId")
        val integration = getAndCheckCompanyIntegration(companyId)
        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            contentType = ".pdf"
        )

        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)
        if (!response.success) {
            throw IntegrationInternalServerException("Upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

}