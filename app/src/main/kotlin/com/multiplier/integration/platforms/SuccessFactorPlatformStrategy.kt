package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.adapter.model.*
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.member.schema.Member
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset

@Component
class SuccessFactorsPlatformStrategy(
    private val featureFlagService: FeatureFlagService
) : PlatformStrategy("SuccessFactors") {

    private fun buildBaseEmployeeRequest(
        member: Member,
        workSiteId: String,
        positionDetails: Position,
        primaryEmail: String,
    ) = CreateEmployeeRequest(
        employment = Employment(
            positionId = positionDetails.positionId, designation = positionDetails.designation
        ),
        workAddress = WorkAddress(
            id = workSiteId
        ),
        firstName = member.firstName,
        lastName = member.lastName,
        workEmail = primaryEmail,
    )

    @Transactional
    override fun createEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: ContractOuterClass.Contract,
        eventLog: JpaEventLog?,
    ): CreateEmployeePlatformResponse {
        log.info("[SuccessFactorsPlatformStrategy] Creating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        log.info("Fetching work locations for companyId: {}", companyId)
        val workLocationResponse = knitAdapter.getSuccessFactorsWorkLocations(companyId, integration.platform.id!!)

        if (workLocationResponse.success != true) {
            throw IntegrationInternalServerException("Fetching work locations was not successful: ${workLocationResponse.error?.msg}")
        }

        val workLocationId = workLocationResponse.data?.response?.body?.d?.results?.firstOrNull()?.name
            ?: throw IntegrationInternalServerException("No work location available for companyId=$companyId")

        val primaryEmail =
            member.emailsList.firstOrNull { it.type == "primary" }?.email ?: throw IntegrationIllegalStateException(
                "Primary email not found for member."
            )

        log.info("Fetching business units for companyId: {}", companyId)
        val businessUnitsResponse = knitAdapter.getSuccessFactorsBusinessUnits(companyId, integration.platform.id!!)

        if (businessUnitsResponse.success != true) {
            throw IntegrationInternalServerException("Fetching business units was not successful: ${businessUnitsResponse.error?.msg}")
        }

        val businessUnitValue = businessUnitsResponse.data?.response?.body?.d?.results?.firstOrNull()?.name
            ?: throw IntegrationInternalServerException("No business unit available for companyId=$companyId")

        val generatedUserId = if (member.id in 50000L..60000L) member.id
        else (50000L..60000L).random()
        val startDateOrNow = employeeData?.startDate ?: LocalDateTime.now()
        val startDateInEpoch = startDateOrNow.toLocalDate().atStartOfDay().toEpochSecond(ZoneOffset.UTC)

        val request = buildBaseEmployeeRequest(member, workLocationId, positionDetails, primaryEmail).apply {
            if (featureFlagService.isKnitDataModelV2(companyId)) {
                startDate = "/Date(${startDateInEpoch})"
                employeeNumber = generatedUserId.toString()
                businessUnit = businessUnitValue
            } else {
                metadata = mapOf(
                    "startDate" to "/Date(${startDateInEpoch})",
                    "userId" to generatedUserId.toString(),
                    "businessUnit" to (businessUnit ?: "")
                )
            }
        }
        val response = knitAdapter.createEmployeeRecord(companyId, integration.platform.id!!, request)
        if (response.success != true || response.data?.employeeId == null) {
            throw IntegrationInternalServerException("Employee creation failed: ${response.errors?.joinToString(", ") ?: "Unknown error"}")
        }

        addEmployeeToContractIntegration(
            providerRepository,
            platformRepository,
            platformContractIntegrationRepository,
            response.data.employeeId,
            contract.id,
            platformName,
            log,
            integration.id!!
        )
        log.info("[SuccessFactorsPlatformStrategy] Created employee record on platform=${integration.platform.name}")
        return CreateEmployeePlatformResponse(
            originalRequest = request,
            createdEmployeeId = response.data.employeeId,
            companyIntegrationId = integration.id!!
        )
    }

    override fun createEmployeeWithoutIntegrationData(
        companyId: Long,
        firstName: String,
        lastName: String,
        primaryEmail: String,
        position: String,
        workEmail: String
    ): CreateEmployeePlatformResponse? {
        log.info("[SuccessFactorsPlatformStrategy] Creating employee without integration data for company=$companyId")
        throw UnsupportedOperationException("SuccessFactors platform does not support creating employee without integration data")
    }


    @Transactional
    override fun updateEmployee(
        companyId: Long,
        employeeData: EmployeeData?,
        member: Member,
        contract: ContractOuterClass.Contract,
        eventLog: JpaEventLog?,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating employee for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(contract.id)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=${contract.id}")

        val externalEmployeeId = contractIntegration.platformEmployeeId

        log.info("Matching contract position for companyId: {}", companyId)
        val contractPosition = contract.position
        val positionDetails = fetchAndMatchPosition(companyId, integration.platform.id!!, contractPosition)

        log.info("Fetching work locations for companyId: {}", companyId)
        val workLocationResponse = knitAdapter.getSuccessFactorsWorkLocations(companyId, integration.platform.id!!)

        if (workLocationResponse.success != true) {
            throw IntegrationInternalServerException("Fetching work locations was not successful: ${workLocationResponse.error?.msg}")
        }

        val workLocationId = workLocationResponse.data?.response?.body?.d?.results?.firstOrNull()?.name
            ?: throw IntegrationInternalServerException("No work location available for companyId=$companyId")

        val primaryEmail =
            member.emailsList.firstOrNull { it.type == "primary" }?.email ?: throw IntegrationIllegalStateException(
                "Primary email not found for member."
            )

        val request = UpdateEmployeeDetailsRequest(
            employeeId = externalEmployeeId, employment = Employment(
                positionId = positionDetails.positionId, designation = positionDetails.designation
            ), workAddress = WorkAddress(
                id = workLocationId
            ), firstName = member.firstName, lastName = member.lastName, workEmail = primaryEmail
        )

        val response =
            knitAdapter.updateEmployeeDetails(companyId, integration.id!!, integration.platform.id!!, request)
        if (response.success != true) {
            if (response.error?.msg?.contains("already", ignoreCase = true) == true) {
                val newRequest = request.copy(
                    workEmail = null
                )
                val newResponse = knitAdapter.updateEmployeeDetails(
                    companyId,
                    integration.id!!,
                    integration.platform.id!!,
                    newRequest
                )

                if (newResponse.success != true) {
                    throw IntegrationInternalServerException("Employee updation failed after retry: ${response.error ?: "Unknown error"}")
                }
            } else {
                // If the error does not contain "already", throw an exception
                throw IntegrationInternalServerException("Employee updation failed: ${response.error ?: "Unknown error"}")
            }
        }

        log.info("[SuccessFactorsPlatformStrategy] Updated employee record on platform=${integration.platform.name}")
    }

    @Transactional
    override fun updateEmployeeCompensation(
        companyId: Long,
        contractId: Long,
        compensationDetails: CompensationData,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating employee compensation for company=$companyId")
        throw UnsupportedOperationException("SuccessFactors platform does not support updating employee compensation")
    }

    override fun updateEmployeeBasicDetails(
        companyId: Long,
        contractId: Long,
        details: BasicDetails,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating employee basic details for company=$companyId")
        throw UnsupportedOperationException("SuccessFactors platform does not support updating employee basic details")
    }

    @Transactional
    override fun updateOnboardingKitDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating OnboardingKitDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(contractId)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Insurance_Onboarding_Kit#${externalEmployeeId}#${LocalDateTime.now()}.pdf"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for updateOnboardingKitDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update OnboardingKit Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("OnboardingKitDocument Update failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateFactsheetDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating updateFactsheetDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(contractId)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Factsheet#${externalEmployeeId}#${LocalDateTime.now()}"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for updateFactsheetDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update factsheet Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("UpdateFactsheetDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateContractDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating updateContractDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(contractId)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "Contract#${externalEmployeeId}#${LocalDateTime.now()}"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for updateContractDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update contract Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateContractDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun updateSalaryReviewDocument(
        companyId: Long,
        contractId: Long,
        document: DocumentResponse,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating SalaryReviewDocument for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(contractId)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
            ?: throw IntegrationIllegalStateException("External employee ID not found for contractId=$contractId")
        val fileName = "SalaryAddendum#${externalEmployeeId}#${LocalDateTime.now()}"

        val documentCategoriesResponse =
            knitAdapter.getDocumentCategories(companyId, integration.platform.id!!, externalEmployeeId)
        if (!documentCategoriesResponse.success || documentCategoriesResponse.categories.isNullOrEmpty()) {
            throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        }

        // Use the first category ID from the response
        val firstCategoryId = documentCategoriesResponse.categories.first().id
        log.info("Fetched category: {} for updateSalaryReviewDocument", firstCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.internalDownloadURL().toString()),
            contentType = ".pdf",
            category = firstCategoryId
        )
        log.info("Calling knitAdapter to update salary review Document for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("updateSalaryReviewDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    @Transactional
    override fun uploadPayslipDocument(
        companyId: Long,
        contractId: Long,
        document: InternalDocument,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] upload payslip Document for company=$companyId")

        val integration = getAndCheckCompanyIntegration(companyId)

        val contractIntegration =
            platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(contractId)
                ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")

        val externalEmployeeId = contractIntegration.platformEmployeeId
        val fileName = "Payslip#${externalEmployeeId}#${LocalDateTime.now()}"

        val documentFolder = documentFoldersRepository.findByFolderTypeAndIntegrationId(
            folderType = DocumentFolderType.PAYSLIP,
            integrationId = integration.id!!
        )
            ?: throw IntegrationInternalServerException("Failed to retrieve document categories or no categories available")
        val payslipDocumentCategoryId = documentFolder.folderId
        log.info("Fetched category: {} for uploadPayslipDocument", payslipDocumentCategoryId)

        val request = CreateDocumentRequest(
            employeeId = externalEmployeeId,
            fileName = fileName,
            fileContent = downloadFileAndEncodeBase64(document.downloadUrl),
            contentType = ".pdf",
            category = payslipDocumentCategoryId
        )
        log.info("Calling knitAdapter to upload payslip document  for companyId: {}", companyId)
        val response = knitAdapter.createDocument(companyId, integration.platform.id!!, request)

        if (!response.success) {
            throw IntegrationInternalServerException("upload payslipDocument failed: ${response.error ?: "Unknown error"}")
        }
    }

    override fun updateEmployeeContactDetails(
        companyId: Long,
        contractId: Long,
        contactDetails: ContactDetails,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating employee contact details for company=$companyId")
        throw UnsupportedOperationException("SuccessFactors platform does not support updating employee contact details")
    }

    override fun updateBankDetails(
        companyId: Long,
        contractId: Long,
        bankData: BankData,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Updating employee bank details for company=$companyId")
        throw UnsupportedOperationException("SuccessFactors platform does not support updating employee bank details")
    }

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?,
    ) {
        log.info("[SuccessFactorsPlatformStrategy] Terminating employee for company=$companyId")
        throw UnsupportedOperationException("SuccessFactors platform does not support terminate employee API")
    }
}