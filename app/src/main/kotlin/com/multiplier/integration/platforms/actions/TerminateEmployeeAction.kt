package com.multiplier.integration.platforms.actions

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.TerminateEmployeeRequest
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import mu.KotlinLogging
import java.time.LocalDate

interface TerminateEmployeeAction : EmployeeAction {

    fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog? = null,
        integrationId: Long? = null,
    )
}

class TerminateEmployeeActionImpl(
    private val knitAdapter: KnitAdapter,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val platformId: Long,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository
) : TerminateEmployeeAction {

    private val log = KotlinLogging.logger {}

    override fun terminateEmployee(
        companyId: Long,
        contractId: Long,
        terminationDate: LocalDate,
        terminationReason: String,
        eventLog: JpaEventLog?,
        integrationId: Long?,
    ) {
        if (integrationId == null) {
            throw IntegrationInternalServerException("Can't process without integrationId")
        }
        val contractIntegration = platformContractIntegrationRepository
            .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            ?: throw IntegrationInternalServerException("ContractIntegration not found for contractId=$contractId")
        val externalEmployeeId = contractIntegration.platformEmployeeId
        val platformEmployeeData = platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integrationId, externalEmployeeId)
            .firstOrNull() ?: throw IntegrationInternalServerException("PlatformEmployeeData not found with contractId=$contractId")

        val terminationReasonResponse = knitAdapter.getTerminationReason(companyId, platformId)

        if (!terminationReasonResponse.success) {
            throw IntegrationInternalServerException("Fetching termination reasons was not successful: ${terminationReasonResponse.error?.msg}")
        }

        val terminationReasonObj = terminationReasonResponse.data?.reasons?.firstOrNull{
            it.id.contains("Terminate", ignoreCase = true)
        } ?: terminationReasonResponse.data?.reasons?.firstOrNull() ?: throw IntegrationInternalServerException("No termination reasons available")

        val request = TerminateEmployeeRequest(
            employeeId = externalEmployeeId,
            terminationDate = terminationDate.toString(),
            terminationReason = terminationReasonObj.id
        )
        log.info("Calling knitAdapter to terminateEmployee for companyId: {}", companyId)
        val response = knitAdapter.terminateEmployee(companyId, platformId, request)

        if (!response.success) {
            throw IntegrationInternalServerException("terminateEmployee failed: ${response.error ?: "Unknown error"}")
        }
        platformEmployeeData.isDeleted = true
        platformEmployeeDataRepository.save(platformEmployeeData)
    }
}
