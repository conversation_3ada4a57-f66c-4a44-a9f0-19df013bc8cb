package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.util.Date

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeDependents(
    val id: String?,
    val firstName: String?,
    val lastName: String?,
    val relation: String?,
    val birthDate: Date?,
    val gender: String?
)

enum class RelationType {
    NOT_SPECIFIED,
    PARENT,
    FATHER,
    MOTHER,
    SON,
    DAUGHTER,
    CHILD,
    BROTHER,
    SISTER,
    SIBLING,
    HUSBAND,
    WIFE,
    SPOUSE,
}
