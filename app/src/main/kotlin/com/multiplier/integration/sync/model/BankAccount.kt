package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class BankAccount(
    val accountNumber: String?,
    val accountType: AccountType?,  // Assuming AccountType is an enum
    val bankName: String?,
    val routingInfo: List<RoutingInfo>?,
    val payTypes: List<PayType>?
)

data class RoutingInfo(
    val type: RoutingType?,  // Assuming RoutingType is an enum
    val number: String?
)

enum class AccountType {
    SAVINGS,
    CURRENT,
    CHECKING,
    NOT_SPECIFIED
}

enum class RoutingType {
    ROUTING_NUMBER,
    IBAN,
    SWIFT_CODE,
    IFSC_CODE,
    BANK_IDENTIFICATION_CODE,
    BRANCH_CODE
}

enum class PayType {
    ALL,
    REGULAR,
    EXPENSE,
    BONUS
}