package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.util.Date

@JsonIgnoreProperties(ignoreUnknown = true)
data class ExpensePayment(
    val mode: ExpensePaymentMode?,
    val referenceId: String?,
    val date: Date?,
    val accountId: String?,
    val accountName: String?
)

enum class ExpensePaymentMode {
    CHECK,
    ACCOUNT_TRANSFER
}