package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.util.*

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeLeaveRequest(
    val employeeId: String?,
    val id: String?,
    val startDate: Date?,
    val endDate: Date?,
    val requestedOn: Date? = Date(),
    val note: String? = "",
    val status: Status?,
    val leaveType: LeaveType?,
    val unit: Unit?,
    val amount: Double?,
    val isPaid: IsPaid?
)

enum class Status {
    REQUESTED,
    APPROVED,
    DECLINED,
    CANCELLED,
    DELETED,
    NOT_SPECIFIED
}

enum class Unit {
    DAYS,
    HOURS,
    NOT_SPECIFIED
}

enum class IsPaid {
    TRUE,
    FALSE,
    NOT_SPECIFIED
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class LeaveType(
    val id: String?,
    val name: String?,
    val type: LeaveTypeEnum?
)

enum class LeaveTypeEnum {
    VACATION,
    SICK,
    PERSONAL,
    JURY_DUTY,
    VOLUNTEER,
    BEREAVEMENT,
    NOT_SPECIFIED
}
