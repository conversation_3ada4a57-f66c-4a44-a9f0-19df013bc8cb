package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.util.*
@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeProfile(
    val firstName: String?,
    val lastName: String?,
    val id: String?,
    val workEmail: String?,
    val startDate: Date?,
    val birthDate: Date?,
    val terminationDate: Date?,
    val employmentStatus: EmploymentStatus?,
    val maritalStatus: MaritalStatus?,
    val gender: Gender?,
    val employmentType: EmploymentType?,
    val employeeNumber: String?,
) {
    override fun toString(): String {
        return "EmployeeProfile(firstName=$firstName, lastName=$lastName, id=$id, startDate=$startDate, birthDate=$birthDate, terminationDate=$terminationDate, employmentStatus=$employmentStatus, maritalStatus=$maritalStatus, gender=$gender, employmentType=$employmentType)"
    }
}

enum class EmploymentStatus {
    ACTIVE,
    INACTIVE,
    NOT_SPECIFIED
}

enum class MaritalStatus {
    MARRIED,
    SINGLE,
    WIDOWED,
    SEPARATED,
    DIVORCED,
    NOT_SPECIFIED
}

enum class Gender {
    MALE,
    FEMALE,
    NOT_SPECIFIED,
    NON_BINARY
}

enum class EmploymentType {
    FULL_TIME,
    PART_TIME,
    CONTRACT,
    NOT_SPECIFIED
}