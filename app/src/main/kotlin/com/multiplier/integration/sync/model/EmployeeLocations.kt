package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties


@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeLocations(
    val workAddress: EmployeeLocation?,
    val permanentAddress: EmployeeLocation?,
    val presentAddress: EmployeeLocation?
)

data class EmployeeLocation(
    val addressLine1: String?,
    val addressLine2: String?,
    val city: String?,
    val state: String?,
    val country: String?,
    val zipCode: String?,
    val addressType: AddressType?
)

enum class AddressType {
    WORK,
    HOME_PRESENT,
    HOME_PERMANENT
}