package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties

@JsonIgnoreProperties(ignoreUnknown = true)
data class EmployeeW4Details(
    val filingStatus: String?,
    val hasMultipleJobs: Boolean?,
    val totalDependentsAmount: Double?,
    val qualifiedDependentsAmount: Double?,
    val otherDependentsAmount: Double?,
    val numberOfQualifiedDependents: Int?,
    val numberOfOtherDependents: Int?,
    val otherIncomeAmount: Double?,
    val deductionsAmount: Double?,
    val extraWithholdingAmount: Double?,
    val withholdingStatus: String?
)
