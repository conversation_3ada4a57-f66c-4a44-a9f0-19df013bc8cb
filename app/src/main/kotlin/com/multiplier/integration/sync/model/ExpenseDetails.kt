package com.multiplier.integration.sync.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import java.util.*

@JsonIgnoreProperties(ignoreUnknown = true)
data class ExpenseDetails(
    val expenseID: String?,
    val expenseType: ExpenseType?,
    val count: Double?,
    val rate: Double?,
    val unit: String?,
    val creatorEmail: String?,
    val status: ExpenseStatus?,
    val isReimbursable: ReimbursableStatus?,
    val isACHReimbursed: ARCHReimbursedStatus?,
    val isBillable: BillableStatus?,
    val merchant: String?,
    val category: String?,
    val amount: Double?,
    val currency: String?,
    val expenseDate: Date?,
    val creationDate: Date?,
    val submissionDate: Date?,
    val approvalDate: Date?,
    val reimbursementDate: Date?,
    val reportId: String?,
    val reportName: String?,
    var receipts: List<Receipt>?,
    val description: String?
)

enum class ExpenseType {
    EXPENSE,
    DISTANCE,
    TIME,
    OTHER
}

enum class ExpenseStatus {
    OPEN,
    SUBMITTED,
    APPROVED,
    REIMBURSED,
    ARCHIVED
}

enum class ReimbursableStatus {
    TRUE,
    FALSE,
    NOT_SPECIFIED
}

enum class BillableStatus {
    TRUE,
    FALSE,
    NOT_SPECIFIED
}

enum class ARCHReimbursedStatus {
    TRUE,
    FALSE,
    NOT_SPECIFIED
}