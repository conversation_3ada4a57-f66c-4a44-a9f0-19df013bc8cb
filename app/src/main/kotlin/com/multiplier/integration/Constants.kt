package com.multiplier.integration

/**
 * Common constants used across the codebase
 */
object Constants {
    const val COMPANY_NAME = "name"
    const val NOT_AVAILABLE = "NA"
    const val KNIT_TEST_INTEGRATION_ID = "knit-test-integration"

    enum class EmployeeOrigin {
        INTERNAL, EXTERNAL
    }

    object ScheduledJobName {
        const val RETRY_FAILED_EVENTS = "RetryFailedEvents"
        const val PROCESS_RECEIVED_EVENTS = "ProcessReceivedEvents"
        const val PROCESS_RECEIVED_UPDATE_EVENTS = "ProcessReceivedUpdateEvents"
        const val RETRY_MEMBER_UPDATE_EVENTS = "RetryMemberUpdateEvents"
        const val PROCESS_RECEIVED_DELETE_EVENTS = "ProcessReceivedDeleteEvents"
        const val VERIFY_INTEGRATION_CREDENTIALS_EXPIRY = "VerifyIntegrationCredentialsExpiry"
        const val UPDATE_IN_PROGRESS_MANUAL_SYNC = "updateInProgressManualSync"
        const val TIMEOFF_EVENT_SCHEDULER = "TimeoffEventsScheduler"
        const val ARCHIVE_PROCESSED_EVENTS_SCHEDULER = "ArchiveProcessedEventsScheduler"
        const val UPDATE_STALE_GP_SYNC = "updateStaleGPSync"
        const val EOR_TIMEOFF_SCHEDULER = "EORTimeOffScheduler"
        const val POSITIONS_CACHING_SCHEDULER = "PositionsCacheScheduler"

    }

    object ShedlockTimeConfiguration {
        const val DEFAULT_LOCK_AT_MOST_FOR = "PT40M"
        const val DEFAULT_LOCK_AT_LEAST_FOR = "PT1M"
        const val LOCK_AT_LEAST_FOR_STRING = "PT1M"
        const val LOCK_AT_MOST_FOR_STRING = "PT1M"
        const val EVENT_PROCESSING_LOCK_AT_LEAST = "PT5M"
        const val EVENT_PROCESSING_LOCK_AT_MOST = "PT5M"
    }

    object CustomFieldsName {
        const val WORK_COUNTRY = "workCountry"
    }

    object SyncDataType {
        const val EMPLOYEE = "employee"
        const val EXPENSE = "expense"
    }

    object UpdateContactMedia {
        const val PHONE = "Phone"
        const val EMAIL = "Email"
    }

    val UnsupportedFetchingDocumentCategoriesPlatforms: Set<String> = setOf("BambooHR")
}
