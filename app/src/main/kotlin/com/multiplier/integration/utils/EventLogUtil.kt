package com.multiplier.integration.utils

import com.google.protobuf.Message
import com.google.protobuf.TextFormat
import com.multiplier.common.exception.toSystemException
import com.multiplier.contract.kafka.compensation.SalaryReviewDocumentMessage.SalaryReviewDocumentEventMessage
import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage
import com.multiplier.contract.kafka.contract.ContractDocumentEventMessageOuterClass.ContractDocumentEventMessage
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass
import com.multiplier.contract.kafka.onboarding.ContractOnboardingEventMessageOuterClass.ContractOnboardingEventMessage
import com.multiplier.contract.offboarding.schema.ContractOffboardingEventMessageOuterClass
import com.multiplier.integration.service.exception.CustomerErrorCode
import com.multiplier.member.kafka.MemberEventMessageOuterClass.MemberEventMessage
import com.multiplier.payable.kafka.schema.CompanyPayableEvent
import com.multiplier.schema.kafka.proto.PayrollDocumentEventMessage

fun <T : Message> parseFromString(payload: String, builder: Message.Builder): T {
    TextFormat.getParser().merge(payload, builder)
    @Suppress("UNCHECKED_CAST")
    return builder.build() as T
}

fun <T : Message> parseFromStringMember(payload: String, builder: Message.Builder): T {
    try {
        TextFormat.getParser().merge(payload, builder)
        @Suppress("UNCHECKED_CAST")
        return builder.build() as T
    } catch (e: TextFormat.ParseException) {
        // Log the exception with the payload that caused it for easier debugging.
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse Protobuf text format: $payload", e)
    }
}

fun parseContractOnboardingEventMessageFromString(payload: String): ContractOnboardingEventMessage {
    val builder = ContractOnboardingEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into ContractOnboardingEventMessage.", e)
    }
}

fun parseMemberEventMessageFromString(payload: String): MemberEventMessage {
    val builder = MemberEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into MemberEventMessage.", e)
    }
}

fun parseSalaryReviewUpdateEventMessageFromString(payload: String): SalaryReviewUpdateEventMessage {
    val builder = SalaryReviewUpdateEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into SalaryReviewUpdateEventMessage.", e)
    }
}

fun parseContractDocumentEventMessageFromString(payload: String): ContractDocumentEventMessage {
    val builder = ContractDocumentEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into ContractDocumentEventMessage.", e)
    }
}

fun parseSalaryReviewDocumentEventMessageFromString(payload: String): SalaryReviewDocumentEventMessage {
    val builder = SalaryReviewDocumentEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into SalaryReviewDocumentEventMessage.", e)
    }
}

fun parsePayrollDocumentEventMessageFromString(payload: String): PayrollDocumentEventMessage {
    val builder = PayrollDocumentEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into PayrollDocumentEventMessage.", e)
    }
}

fun parseContractOffboardingEventMessageFromString(payload: String): ContractOffboardingEventMessageOuterClass.ContractOffboardingEventMessage {
    val builder = ContractOffboardingEventMessageOuterClass.ContractOffboardingEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build() // The cast is not needed since we are returning a specific type
    } catch (e: TextFormat.ParseException) {
        // Here you can handle the parsing error, e.g., log it or rethrow it
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into ContractOffboardingEventMessage.", e)
    }
}

fun parseContractEventMessageFromString(payload: String): ContractEventMessageOuterClass.ContractEventMessage {
    val builder = ContractEventMessageOuterClass.ContractEventMessage.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build()
    } catch (e: TextFormat.ParseException) {
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into ContractEventMessage.", e)
    }
}

fun parsePayableEventMessageFromString(payload: String): CompanyPayableEvent {
    val builder = CompanyPayableEvent.newBuilder()
    try {
        TextFormat.getParser().merge(payload, builder)
        return builder.build()
    } catch (e: TextFormat.ParseException) {
        throw CustomerErrorCode.ILLEGAL_ARGUMENT.toSystemException("Failed to parse the Protobuf text format payload into CompanyPayableEvent.", e)
    }
}
