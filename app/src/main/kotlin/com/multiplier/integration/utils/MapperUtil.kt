package com.multiplier.integration.utils

import com.multiplier.integration.platforms.Platform
import com.neovisionaries.i18n.CountryCode
import mu.KotlinLogging

class MapperUtil {

    companion object {

        private val log = KotlinLogging.logger {}

        private val specialCaseMappings = mapOf(
            "united states of america" to "United States"
        )

        fun mapToAlpha3CountryCode(input: String, platform: String): String {
            // Get the base country code using the original mapping function
            val baseCode = mapToAlpha3CountryCode(input)
            
            // Handle Paychex platform special cases
            if (platform.equals(Platform.PAYCHEX.name, ignoreCase = true)) {
                return when (baseCode) {
                    "SEN" -> "SGP"
                    "SGP" -> "SEN"
                    else -> baseCode // Keep other codes unchanged
                }
            }
            
            return baseCode
        }

        fun mapToAlpha3CountryCode(input: String): String {
            val countryCode = getCountryCode(input)
            return if (countryCode != null) countryCode.alpha3 else input
        }

        fun mapToAlpha2CountryCode(input: String): String {
            val countryCode = getCountryCode(input)
            return if (countryCode != null) countryCode.alpha2 else input
        }

        private fun getCountryCode(input: String): CountryCode? {
            val countryStringInput = specialCaseMappings[input.lowercase()] ?: input
            var countryCode = CountryCode.getByCodeIgnoreCase(countryStringInput)
            if (countryCode == null) {
                countryCode = CountryCode.entries.toTypedArray()
                    .firstOrNull { it.getName().equals(countryStringInput, true) }
            }
            if (countryCode == CountryCode.UK) {
                countryCode = CountryCode.GB
            } else if (countryCode == null) {
                log.error { "getCountryCode: Unknown country code or name $input" }
            }
            return countryCode
        }
    }
}