package com.multiplier.integration.utils

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException

fun validateIntegrationCompanyMatch(currentUserCompanyId: Long?, companyId: Long, isOpsUser: Boolean) {
    if (!isOpsUser && currentUserCompanyId != companyId) {
        throw BadRequestException("Integration is not related to company id of current user companyId: $currentUserCompanyId")
    }
}

/**
 * Checks if outgoing sync is enabled for the provided integration.
 * Throws EORSyncDisabledException if it is not.
 *
 * @param integration The integration to check.
 * @throws IllegalStateException if outgoing sync is not enabled.
 */
fun checkOutgoingSyncEnabled(integration: JpaCompanyIntegration) {
    if (!integration.outgoingSyncEnabled) {
        throw IntegrationIllegalStateException(
            "Outgoing sync is disabled for integration id: ${integration.id} with platform: ${integration.platform.name}"
        )
    }
}

fun isOpsUser(currentUser: CurrentUser): Bo<PERSON>an {
    val currentUserExperience = currentUser.context?.experience
    return currentUserExperience == "operations"
}