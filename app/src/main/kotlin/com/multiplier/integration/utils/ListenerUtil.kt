package com.multiplier.integration.utils

import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import mu.KotlinLogging

object ListenerUtil {

    private val log = KotlinLogging.logger {}

    private fun parseContractIdFromProtobuf(payload: String): Long? {
        val pattern = Regex("contract_id:\\s+(\\d+)")
        val altPattern = Regex("contractId:\\s+(\\d+)") // Alternative for protobuf-like format
        return pattern.find(payload)?.groupValues?.get(1)?.toLong()
            ?: altPattern.find(payload)?.groupValues?.get(1)?.toLong()
    }

    private fun parseContractIdFromSimpleFormat(payload: String): Long? {
        val pattern = Regex("contract_id:\\s*(\\d+)")
        return pattern.find(payload)?.groupValues?.get(1)?.toLong()
    }

    private fun parseContractId<PERSON>rom<PERSON>son(payload: String): Long? {
        val pattern = Regex("\"contractId\"\\s*:\\s*(\\d+)")
        return pattern.find(payload)?.groupValues?.get(1)?.toLong()
    }

    fun parseContractId(payload: String, eventType: EventType): Long {
        log.info { "Attempting to parse contract ID for event type: $eventType" }
        val contractId = when (eventType) {
            EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
            EventType.INCOMING_OFFBOARDING_STATUS_UPDATE,
            EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
            EventType.INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE,
            EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
            EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED,
            EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
            EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED  -> parseContractIdFromProtobuf(payload)
            EventType.INCOMING_PAYROLL_PAYSLIP_PUBLISHED  -> parseContractIdFromProtobuf(payload)

            EventType.INCOMING_SALARY_REVIEW_ACTIVATED,
            EventType.INCOMING_SALARY_REVIEW_SIGNED -> parseContractIdFromSimpleFormat(payload)

            EventType.SERVICE_INTERNAL_CREATE_COMPENSATION,
            EventType.SERVICE_INTERNAL_CREATE_INSURANCE_FACTSHEET,
            EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT -> parseContractIdFromJson(payload)

            else -> {
                log.warn { "Unsupported event type for parsing contract ID: $eventType" }
                null
            }
        }

        return contractId ?: throw IntegrationInternalServerException("Contract ID could not be parsed for event type: $eventType, payload: $payload")
    }
}
