package com.multiplier.integration.utils

import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.types.PayFrequency
import com.multiplier.integration.adapter.model.knit.Frequency
import com.multiplier.integration.types.RateFrequency
import java.util.*

fun String?.formatNationalId(): String? {
    if (this == null) {
        return null
    }
    return this.replace("-", "")
}

fun String?.toMultiplierFrequency(): String? {
    if (this == null) {
        return null
    }

    return when (Frequency.valueOf(this)) {
        Frequency.ANNUAL -> "ANNUALLY"
        Frequency.WEEKLY -> PayFrequency.WEEKLY.name
        Frequency.MONTHLY -> PayFrequency.MONTHLY.name
        Frequency.SEMI_MONTHLY -> PayFrequency.SEMIMONTHLY.name
        Frequency.BI_WEEKLY -> PayFrequency.BIWEEKLY.name
        else -> null
    }
}

fun Frequency?.mapToRateFrequency(): RateFrequency? {
    if (this == null) {
        return null
    }
    return when (this) {
        Frequency.ANNUAL -> RateFrequency.ANNUALLY
        Frequency.WEEKLY -> RateFrequency.WEEKLY
        Frequency.QUARTERLY -> RateFrequency.QUATERLY
        Frequency.SEMI_MONTHLY -> RateFrequency.SEMIMONTHLY
        Frequency.MONTHLY -> RateFrequency.MONTHLY
        Frequency.HOURLY -> RateFrequency.HOURLY
        Frequency.BI_WEEKLY -> RateFrequency.BI_WEEKLY
        Frequency.DAILY -> RateFrequency.DAILY
        else -> RateFrequency.CUSTOM
    }
}

fun replaceAllExceptLast(input: String?, replacement: String, lastReplacement: String): String? {
    if (input == null) {
        return null
    }
    val allIndices = mutableListOf<Int>()
    var index = input.indexOf("[]")

    while (index >= 0) {
        allIndices.add(index)
        index = input.indexOf("[]", index + 2)
    }

    if (allIndices.isEmpty()) return input

    val sb = StringBuilder(input)
    var lastIndex = allIndices.last()
    for (i in allIndices.dropLast(1)) {
        sb.replace(i, i + 2, "[${replacement}]")
        lastIndex += replacement.length
    }
    sb.replace(lastIndex, lastIndex + 2, "[${lastReplacement}]")

    return sb.toString()
}

fun String?.populateKeyFromLabel(label: String?): String? {
    if (!this.isNullOrBlank()) {
        return this
    }
    if (label.isNullOrBlank()) {
        return null
    }
    val output = label.split(" ")
        .joinToString("") {
            it.lowercase().replaceFirstChar { char -> char.uppercase() }
        }.replaceFirstChar { it.lowercase(Locale.getDefault()) }

    return "customFields.fields.${output}"
}

fun String?.populateLabelFromKey(key: String?): String? {
    if (!this.isNullOrBlank()) {
        return this
    }
    if (key.isNullOrBlank()) {
        return null
    }
    return key.replace(Regex("\\[[^\\]]*\\]"), "")
        .split(".")
        .joinToString(" ") {
            it.replace(Regex("([a-z])([A-Z])"), "$1 $2")
                .replaceFirstChar { char -> char.uppercase() }
        }
}

fun formatExternalKey(data: FieldData, isMapped: Boolean): String? {
    if (data.mappedKey == null) {
        return null
    }
    val formattedMappedKey = if (isMapped) {
        val subKey = if (!data.filters.isNullOrEmpty()) {
            val listFilter = data.filters.map { f -> "${f.key}=${f.value}" }
            listFilter.joinToString(",")
        } else "0"
        replaceAllExceptLast(data.mappedKey, "0", subKey)
    } else null
    return if (data.isCustomField == true && formattedMappedKey != null) "customFields.fields.${formattedMappedKey}" else formattedMappedKey
}