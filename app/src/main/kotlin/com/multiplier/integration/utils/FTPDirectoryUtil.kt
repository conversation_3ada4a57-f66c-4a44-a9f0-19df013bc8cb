package com.multiplier.integration.utils

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

object FTPDirectoryUtil {
    /**
     * Extracts the file name from the given URI.
     *
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx
     * Output: UKG TS Export Example.xlsx
     * ```
     *
     * @param uri The URI to extract the file name from.
     * @return The file name extracted from the URI.
     */
    fun getFileName(uri: String): String {
        return uri.substringAfterLast("/")
    }

    /**
     * Extracts the file path from the given URI.
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx
     * Output: c_aira/ukg/timesheets/upload
     * ```
     */
    fun getFilePath(uri: String): String {
        return uri.substringBeforeLast("/")
    }

    /**
     * Generates the file path for the processing file from the given original file URI.
     *
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx
     * Output: c_aira/ukg/timesheets/processing
     * ```
     * @param originalFileURI The URI of the original file.
     * @return The file path for the report file.
     */
    fun generateProcessingFilePath(originalFileURI: String): String {
        val originalFilePath = getFilePath(originalFileURI)
        return originalFilePath.substringBeforeLast("/") + "/processing"
    }

    /**
     * Generates the file name for the processing file with current date-time.
     *
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/processing/UKG TS Export Example.xlsx
     * Input: UKG TS Export Example_2025-03-31T20-59-59.xlsx
     * ```
     * @param originalFileURI The URI of the original file.
     * @return The URI for the report file.
     */
    fun generateProcessingFileName(originalFileURI: String): String {
        val fileName = getFileName(originalFileURI)

        // Get the current date-time in the desired format
        val currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH-mm-ss"))

        // Extract file extension and base name
        val lastDotIndex = fileName.lastIndexOf('.')
        return if (lastDotIndex != -1) {
            val baseName = fileName.substring(0, lastDotIndex)
            val extension = fileName.substring(lastDotIndex)
            "${baseName}_$currentDateTime$extension"
        } else {
            // If no extension found, just append the timestamp
            "${fileName}_$currentDateTime"
        }
    }

    /**
     * Generates the file path for the archive file from the given original file URI.
     *
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/processing/UKG TS Export Example.xlsx
     * Output: c_aira/ukg/timesheets/archive
     * ```
     * @param originalFileURI The URI of the original file.
     * @return The file path for the archive file.
     */
    fun generateArchiveFilePath(originalFileURI: String): String {
        val originalFilePath = getFilePath(originalFileURI)
        return originalFilePath.substringBeforeLast("/") + "/archive"
    }

    /**
     * Generates the file path for the report file from the given original file URI.
     *
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/upload/UKG TS Export Example.xlsx
     * Output: c_aira/ukg/timesheets/report
     * ```
     * @param originalFileURI The URI of the original file.
     * @return The file path for the report file.
     */
    fun generateReportFilePath(originalFileURI: String): String {
        val originalFilePath = getFilePath(originalFileURI)
        return originalFilePath.substringBeforeLast("/") + "/report"
    }

    /**
     * Generates the file name for the report file.
     * Since the report generated by our platform is always in xlsx format, we generate report file with xlsx extension.
     *
     * Example:
     * ```
     * Input: c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-30T15-30-21.xlsx
     * Output: UKG TS Export Example_Report_2025-03-30T15-30-21.xlsx
     *
     * Input: c_aira/ukg/timesheets/processing/UKG TS Export Example_2025-03-30T15-30-21.csv
     * Output: UKG TS Export Example_Report_2025-03-30T15-30-21.xlsx
     * ```
     * @param originalFileURI The URI of the original file.
     * @return The file name for the report file.
     */
    fun generateReportFileName(originalFileURI: String): String {
        val fileName = getFileName(originalFileURI)
        val baseName = fileName.substringBeforeLast("_")
        val timestampWithExtension = fileName.substringAfterLast("_")
        // Extract timestamp without extension and always use .xlsx for reports
        val timestamp = timestampWithExtension.substringBeforeLast(".")
        return "${baseName}_Report_$timestamp.xlsx"
    }

    /**
     * Extracts the main directory path from a file URI and ensures it ends with a trailing slash.
     *
     * The method assumes that the main directory is the parent directory of any operation-specific
     * directory (like upload, download, report, etc.). It identifies the main directory by looking
     * for a standard pattern in SFTP file URIs where the file is stored in a subdirectory of the
     * main directory.
     *
     * Examples:
     * ```
     * Input: c_aira/2253166/ukg/timesheets/upload/file.xlsx
     * Output: c_aira/2253166/ukg/timesheets/
     *
     * Input: c_aira/2253166/ukg/timesheets/any_directory/file.xlsx
     * Output: c_aira/2253166/ukg/timesheets/
     *
     * Input: /c_aira/2253166/ukg/timesheets/any_directory/file.xlsx
     * Output: c_aira/2253166/ukg/timesheets/
     * ```
     *
     * @param fileURI The file URI to extract the main directory path from
     * @return The main directory path with a trailing slash (parent directory of the file's immediate directory)
     */
    fun extractMainDirectoryPath(fileURI: String): String {
        // Get the file path (everything before the last '/')
        val filePath = getFilePath(fileURI)

        // Get the parent directory (everything before the last '/' in the file path)
        // This assumes the file is in a subdirectory of the main directory
        val mainDirectory = getFilePath(filePath)

        // Ensure the path ends with a trailing slash
        return normalizeDirectoryPath(mainDirectory)
    }

    /**
     * Normalizes a directory path by:
     * 1. Removing any leading slash to prevent absolute paths
     * 2. Ensuring the path ends with a trailing slash
     *
     * Examples:
     * ```
     * Input: /sftp/company123/entity456
     * Output: sftp/company123/entity456/
     *
     * Input: sftp/company123/entity456/
     * Output: sftp/company123/entity456/
     *
     * Input: /sftp/company123/entity456/
     * Output: sftp/company123/entity456/
     * ```
     *
     * @param directoryPath The directory path to normalize
     * @return The normalized directory path without leading slash and with trailing slash
     */
    fun normalizeDirectoryPath(directoryPath: String): String {
        // Remove leading slash if present
        val pathWithoutLeadingSlash = if (directoryPath.startsWith("/")) {
            directoryPath.substring(1)
        } else {
            directoryPath
        }

        // Ensure trailing slash
        return if (pathWithoutLeadingSlash.endsWith("/")) {
            pathWithoutLeadingSlash
        } else {
            "$pathWithoutLeadingSlash/"
        }
    }
}
