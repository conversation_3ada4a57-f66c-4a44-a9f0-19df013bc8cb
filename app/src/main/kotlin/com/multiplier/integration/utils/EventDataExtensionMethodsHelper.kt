package com.multiplier.integration.utils

import com.multiplier.integration.Constants.NOT_AVAILABLE
import com.multiplier.integration.adapter.model.knit.CompensationItemData
import com.multiplier.integration.adapter.model.knit.CompensationType
import com.multiplier.integration.adapter.model.knit.Frequency
import com.multiplier.integration.sync.model.EventData

fun EventData.getGrossMonthlySalary(): Pair<Double?, String?> {
    val fixedCompensation = this.compensation?.fixed?.firstOrNull { it.type?.uppercase() == CompensationType.SALARY.toString() }

    // try to check if compensation.fixed.type is "Base Salary xxx" like in SAP and treat it as a salary
    if (fixedCompensation == null) {
        val sapSalary = this.compensation?.fixed?.firstOrNull { it.type != null && it.type.contains("Base Salary", ignoreCase = true) }
        return Pair(sapSalary?.amount, sapSalary?.currency)
    }
    return Pair(fixedCompensation?.amount,fixedCompensation?.currency)
}

fun EventData.getVariablePerformanceBonusFrequencyInMonths(): String {
    val performanceBonus = this.compensation?.variable?.firstOrNull { it.type?.uppercase() == CompensationType.BONUS.toString() } // TODO: add more condition to determine which one is performance bonus vs other bonuses like joining bonus, etc
    // for now, we are assuming the first bonus is the performance bonus
    return if (performanceBonus?.frequency != null) {
        frequencyToMonthsString(performanceBonus.frequency)
    } else {
        NOT_AVAILABLE
    }
}

fun EventData.getAllowanceList(): List<CompensationItemData>? {
    return this.compensation?.fixed?.filter { it.type?.uppercase() == CompensationType.ALLOWANCE.toString() }
}

fun frequencyToMonthsString(frequency: Frequency?): String {
    return when (frequency) {
        Frequency.ANNUAL -> "12"
        Frequency.QUARTERLY -> "3"
        Frequency.MONTHLY -> "1"
        Frequency.BI_WEEKLY -> "0.5" // what is the different between bi-weekly and semi-monthly here?
        Frequency.SEMI_MONTHLY -> "0.5"
        else -> NOT_AVAILABLE
    }
}