package com.multiplier.integration.handlers.contract

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.exception.IllegalStateExceptionForTrinet
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.InvalidContractProcessingDateException
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ContractCreationEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService,
    private val notificationsService: NotificationsService,
    private val eventLogRepository: EventLogRepository,
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractCreationEventHandler(eventLogId: String) {
        log.info("Started handling ContractCreationEventHandler for contract ID: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog, false)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of ContractCreationEventHandler for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of ContractCreationEventHandler for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IllegalStateExceptionForTrinet) {
            log.error("IllegalStateExceptionForTrinet encountered during processing of ContractCreationEventHandler for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
            notificationsService.sendAdminAutoSyncFailed(e.companyId, "TriNet", e.employeeName, e.message!!, eventLog.syncId)
        }
        catch (e: InvalidContractProcessingDateException) { //only for TriNet
            log.error("Exception in [handleContractCreationEventHandler]", e)
            eventLogService.handleInvalidProcessingDateEvent(eventLog, e.contract)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing ContractCreationEventHandler for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, "Could not be synced due to an unknown error")
        } finally {
            log.info { "Completed handling of ContractCreationEventHandler for EventLogId: $eventLogId" }
        }
    }

     fun processEvent(eventLog: JpaEventLog, test: Boolean) {
        val contractId = eventLog.contractId ?: run {
            val errorMessage = "Contract ID is null for EventLogId: ${eventLog.eventId}"
            log.error("Contract ID is null for EventLogId: ${eventLog.eventId}")
            throw IntegrationIllegalStateException(errorMessage)
        }

        log.info("Fetching contract by ID: {}", contractId)
        val contract = contractServiceAdapter.findContractByContractId(contractId)

        log.info("Fetching member by ID: {}", contract.memberId)
        val member = memberServiceAdapter.findMemberByMemberId(contract.memberId)

        log.info { "Checking if employee already existed in cache to prevent circular creation ${contract.id}"}

        if (!test) {
            val updateEventLog = eventLogService.findEventLogByEventId(eventLog.eventId)
            updateEventLog.companyId = contract.companyId
            eventLogRepository.save(updateEventLog)
        }

        if (employeeService.checkIfEmployeeAlreadyExistedInCache(contract)) {
            log.info { "Employee with contractId=${contractId} already existed, skipping creation on external platforms"}
            throw IntegrationIllegalStateException("EXISTED")
        } else {
            log.info { "Employee with contractId=${contractId} not found in cache, creating it on external platforms"}
            if ((contract.status == ContractOuterClass.ContractStatus.ACTIVE || contract.status == ContractOuterClass.ContractStatus.OFFBOARDING) || test ) {
                    //TODO(Good to have): here check if for given contract id an event came to create contract from kafka,
                    // if yes -> then update status of the event to make sure it is not processed to avoid duplication
                    log.info { "Creating employee record for contract ID: ${contractId}" }
                    employeeService.createEmployee(contract.companyId, null, member, contract, eventLog)
                    log.info { "Successfully created employee record for contract ID: ${contractId}" }

                    //TODO - add proper platform configuration, exclude TriNet from this for now
                    if (!employeeService.isTriNetPlatform(contract.companyId)) {
                        handleSuccessfulEmployeeCreation(contractId, eventLog)
                    }
                } else {
                log.warn("Ignoring contract creation for contractId: $contractId as current status is ${contract.status}")
            }
        }
    }

    private fun handleSuccessfulEmployeeCreation(contractId: Long, eventLog: JpaEventLog) {
        val payloadJson = buildJsonObject {
            put("contractId", JsonPrimitive(contractId))
        }
        val payloadString = payloadJson.toString()

        val compensationEvent = eventLogService.createEventLog(
            type = EventType.SERVICE_INTERNAL_CREATE_COMPENSATION,
            status = EventStatus.TO_BE_PROCESSED,
            payload = payloadString,
            contractId = contractId,
            syncId = eventLog.syncId
        )

        val factsheetEvent = eventLogService.createEventLog(
            type = EventType.SERVICE_INTERNAL_CREATE_INSURANCE_FACTSHEET,
            status = EventStatus.TO_BE_PROCESSED,
            payload = payloadString,
            contractId = contractId,
            syncId = eventLog.syncId
        )

        val onboardingKitEvent = eventLogService.createEventLog(
            type = EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT,
            status = EventStatus.TO_BE_PROCESSED,
            payload = payloadString,
            contractId = contractId,
            syncId = eventLog.syncId
        )

        log.info("Created compensationEvent: {}, factsheetEvent: {}, onboardingKitEvent: {}",
            compensationEvent.eventId, factsheetEvent.eventId, onboardingKitEvent.eventId)
    }
}