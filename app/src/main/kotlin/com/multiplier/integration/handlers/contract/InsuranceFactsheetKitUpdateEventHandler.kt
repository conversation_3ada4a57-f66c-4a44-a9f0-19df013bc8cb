package com.multiplier.integration.handlers.contract

import com.multiplier.integration.adapter.api.BenefitServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import kotlinx.serialization.json.*
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class InsuranceFactsheetKitUpdateEventHandler(
    private val benefitServiceAdapter: BenefitServiceAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val docgenServiceAdapter: DocgenServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    fun handleInsuranceFactsheetUpdateEvent(eventLogId: String) {

        log.info("Started handleInsuranceFactsheetUpdateEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleInsuranceFactsheetUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of handleInsuranceFactsheetUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleInsuranceFactsheetUpdateEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleInsuranceFactsheetUpdateEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val jsonElement = Json.parseToJsonElement(payloadString)
        val contractId = jsonElement.jsonObject["contractId"]?.jsonPrimitive?.long
            ?: throw IntegrationIllegalStateException("Contract ID missing in event payload")

        log.info("Fetching factsheet doc by contractId: {}", contractId)
        val documentResponse = benefitServiceAdapter.getContractBenefitDocumentsByContractId(contractId)

        // Check if the FactsheetId is null or blank
        val factsheetId = documentResponse.factSheetId
        if (factsheetId.isNullOrBlank()) {
            throw IntegrationIllegalStateException("FactsheetId is missing for contract ID: $contractId")
        }

        log.info("Fetching document by ID: {}", factsheetId)
        val document = docgenServiceAdapter.getDocument(factsheetId.toLong())

        log.info { "Fetching contract by ID: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.updateFactsheetDocument(companyId, contractId, document!!)
    }
}