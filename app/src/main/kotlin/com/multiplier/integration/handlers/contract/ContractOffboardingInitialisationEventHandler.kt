package com.multiplier.integration.handlers.contract

import com.multiplier.integration.adapter.api.ContractOffBoardingServiceAdapter
import com.multiplier.integration.adapter.model.ContractOffBoardingRequest
import com.multiplier.integration.adapter.model.RescheduleContractOffBoardingRequest
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.sync.model.EventData
import com.multiplier.integration.utils.convertDateToLocalDate
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ContractOffboardingInitialisationEventHandler(
    private val contractOffboardingServiceAdapter: ContractOffBoardingServiceAdapter,
) {
    private val logger = KotlinLogging.logger {}
    fun handleContractOffboardingInitialisation(eventData: EventData, contractId: Long) {
        val contractOffboardings = contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId))
        val eventEmployeeProfile = eventData.profile ?: throw EntityNotFoundException("Not found event employee platform data with contractId $contractId")
        val terminationDate = eventEmployeeProfile.terminationDate ?: throw IntegrationInternalServerException("Invalid terminationDate")
        if (contractOffboardings.isNullOrEmpty()) {
            // init contract offboarding when it's not existed
            logger.info("Handle init offboarding employee: contractId=${contractId}, lastWorkingDay=${terminationDate}")
            val offboardingResponse = contractOffboardingServiceAdapter.initialiseResignationOffboarding(
                ContractOffBoardingRequest(
                    contractId = contractId,
                    lastWorkingDay = terminationDate.convertDateToLocalDate().toString(),
                    terminationReason = "Terminated by external platform"
                )
            )
            logger.info("Successfully offboarding employee with contractId=${contractId}, offboardingStatus=${offboardingResponse.contractOffBoardingStatus}")
        } else {
            // reschedule contract offboarding when it's existed
            val existedContractOffboarding = contractOffboardings.find { it.contractId == contractId }
                ?: throw IntegrationInternalServerException("Not found contract offboarding with contractId $contractId")
            logger.info("Handle reschedule offboarding employee: contractOffboardingId=${existedContractOffboarding.id}, revisedDate=${terminationDate}")
            contractOffboardingServiceAdapter.rescheduleOffboarding(
                RescheduleContractOffBoardingRequest(
                    contractOffboardingId = existedContractOffboarding.id,
                    lastWorkingDay = terminationDate.convertDateToLocalDate().toString(),
                )
            )
            logger.info("Successfully reschedule offboarding with contractId=${contractId}")
        }
    }
}