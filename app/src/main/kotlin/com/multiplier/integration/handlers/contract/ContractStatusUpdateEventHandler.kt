package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractStatusUpdateEvent
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseFromString
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ContractStatusUpdateEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractStatusUpdateEvent(event: ContractStatusUpdateEvent) {
        val eventLogId = event.eventLogId
        log.info { "Started handling ContractStatusUpdateEvent for EventLogId: $eventLogId" }

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        log.info { "EventLog found for EventLogId: $eventLogId, proceeding with processing." }
        eventLogService.processEvent(eventLog)

        try {
            val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
            val builder = ContractEventMessage.newBuilder()

            val payload: ContractEventMessage = parseFromString(payloadString, builder)
            val contractId = payload.event.contractId

            log.info { "Fetching contract by ID: $contractId" }
            val contract = contractServiceAdapter.findContractByContractId(contractId)
            val companyId = contract.companyId

            log.info { "Processing status update for ContractId: $contractId, CompanyId: $companyId" }
            when (contract.status) {
                ContractOuterClass.ContractStatus.DELETED -> {
                    log.info { "Contract DELETED: Marking employee as inactive for CompanyId: $companyId, ContractId: $contractId" }
                    employeeService.markEmployeeAsInactive(companyId, contractId)
                    log.info { "Employee marked as inactive for ContractId: $contractId" }
                }
                ContractOuterClass.ContractStatus.ENDED -> {
                    log.info { "Contract ENDED: Marking employee as inactive for CompanyId: $companyId, ContractId: $contractId" }
                    employeeService.markEmployeeAsInactive(companyId, contractId)
                    log.info { "Employee marked as inactive for ContractId: $contractId" }
                }
                else -> {
                    log.warn { "Received unsupported contract status update: ${contract.status} for ContractId: $contractId" }
                }
            }
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during status update for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Exception encountered during status update for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Finalizing handling of ContractStatusUpdateEvent for eventLogId: $eventLogId" }
        }

        log.info { "Completed handling of ContractStatusUpdateEvent for eventLogId: $eventLogId" }
    }
}
