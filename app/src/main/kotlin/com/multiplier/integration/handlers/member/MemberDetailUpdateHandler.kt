package com.multiplier.integration.handlers.member

import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.model.createContactDetailsData
import com.multiplier.integration.core.model.MemberBasicDetailUpdateEvent
import com.multiplier.integration.core.model.MemberContactDetailUpdateEvent
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.exception.IllegalStateExceptionForTrinet
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseContractEventMessageFromString
import com.multiplier.integration.utils.parseFromString
import com.multiplier.integration.utils.parseMemberEventMessageFromString
import com.multiplier.member.schema.MemberUpdateMessage.EventMessage
import com.multiplier.member.schema.MemberUpdateMessage.EventMessageOrBuilder
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class MemberDetailUpdateHandler(
    private val employeeService: EmployeeService,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val eventLogService: EventLogService,
    private val notificationsService: NotificationsService,
    private val eventLogRepository: EventLogRepository
) {
    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleBasicDetailsUpdate(event: MemberBasicDetailUpdateEvent) {
        val eventLogId = event.eventLogId
        log.info("Started handling handleBasicDetailsUpdate for contract ID: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleBasicDetailsUpdate for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of handleBasicDetailsUpdate for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IllegalStateExceptionForTrinet) {
            log.error("IllegalStateExceptionForTrinet encountered during processing of handleBasicDetailsUpdate for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
            notificationsService.sendAdminAutoSyncFailed(e.companyId, "TriNet", e.employeeName, e.message!!, eventLog.syncId)
        } catch (e: IntegrationDownstreamException) {
            log.info("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleBasicDetailsUpdate for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, "Could not be synced due to an unknown error")
            log.info("Retryable event : ${eventLog.eventId} has been handled, status: ${eventLog.status}, retries left: ${eventLog.retriesLeft}, retries done: ${eventLog.retriesDone}")
        } finally {
            log.info { "Completed handling of handleBasicDetailsUpdate for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLog.id)
        val eventContractId = if (eventLog.eventType == EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED) {
            val contractEventMessage = parseContractEventMessageFromString(payloadString)
            val contractEvent = contractEventMessage.event
            contractEvent.contractId
        } else {
            val memberEventMessage = parseMemberEventMessageFromString(payloadString)
            val memberEvent = memberEventMessage.event
            memberEvent.contractId
        }

        val origin = employeeService.getEmployeeDataOrigin(eventContractId)
        if (origin == Constants.EmployeeOrigin.EXTERNAL) {
            log.info("Employee origin is EXTERNAL, skipping update to external platforms")
            return
        }
        log.info("Fetching contract by ID: {}", eventContractId)
        val contract = contractServiceAdapter.findContractByContractId(eventContractId)
        log.info("Fetching member by ID: {}", contract.memberId)
        val member = memberServiceAdapter.findMemberByMemberId(contract.memberId)

        val updateEventLog = eventLog.apply {
            companyId = contract.companyId
            contractId = eventContractId
        }
        eventLogRepository.save(updateEventLog)

        log.info { "Updating employee record for contract ID: ${contract.id}" }
        employeeService.updateEmployee(contract.companyId, null, member, contract, eventLog)
        log.info { "Successfully updated employee record for contract ID: ${contract.id}" }
    }

    @EventListener
    fun handleContactDetailsUpdate(event: MemberContactDetailUpdateEvent) {
        val eventLogId = event.eventLogId
        log.info { "Started handling MemberContactDetailUpdateEvent for EventLogId: $eventLogId" }

        val eventLog = try {
            eventLogService.findEventLogByEventId(eventLogId)
                .also { log.info("Retrieved EventLog for EventLogId: $eventLogId. Beginning to process event.") }
        } catch (e: Exception) {
            log.error(e) { "Failed to retrieve EventLog for EventLogId: $eventLogId" }
            return // Exit if we cannot retrieve the event log
        }

        eventLogService.processEvent(eventLog)

        try {
            val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
            val builder = EventMessage.newBuilder()

            val payload: EventMessageOrBuilder = parseFromString(payloadString, builder)
            val memberId = payload.memberId

            val member = memberServiceAdapter.findMemberByMemberId(memberId)
            val contract = contractServiceAdapter.findContractByMemberId(memberId)

            log.info { "Updating contact details for memberId: $memberId" }
            employeeService.updateEmployeeContactDetails(
                companyId = contract.companyId,
                contractId = contract.id,
                contactDetails = createContactDetailsData(member),
            )
            eventLogService.markEventSuccessful(eventLog)
            log.info { "Successfully updated contact details for memberId: $memberId" }
        } catch (e: IntegrationIllegalStateException) {
            log.error(e) { "IllegalStateException encountered during contact details update for EventLogId: $eventLogId" }
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error(e) { "UnsupportedOperationException encountered during contact details update for EventLogId: $eventLogId" }
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.info("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error(e) { "Exception occurred during contact details update for EventLogId: $eventLogId" }
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling MemberContactDetailUpdateEvent for EventLogId: $eventLogId" }
        }
    }
}
