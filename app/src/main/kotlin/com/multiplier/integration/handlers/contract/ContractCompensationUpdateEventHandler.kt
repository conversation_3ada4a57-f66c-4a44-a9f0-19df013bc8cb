package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.contract.kafka.onboarding.ContractOnboardingEventMessageOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.model.createCompensationData
import com.multiplier.integration.core.model.ContractCompensationUpdateEvent
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseFromString
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ContractCompensationUpdateEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractCompensationUpdateEvent(event: ContractCompensationUpdateEvent) {
        val eventLogId = event.eventLogId
        log.info { "Started handling ContractCompensationUpdateEvent for EventLogId: $eventLogId" }

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        log.info { "Retrieved EventLog for EventLogId: $eventLogId. Beginning to process event." }
        eventLogService.processEvent(eventLog)

        try {
            val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
            val builder = SalaryReviewUpdateEventMessage.newBuilder()

            val payload: SalaryReviewUpdateEventMessage = parseFromString(payloadString, builder)
            val contractId = payload.contractId

            log.info { "Fetched contract information for ContractId: $contractId. Proceeding with compensation update." }
            val contract = contractServiceAdapter.findContractByContractId(contractId)
            val companyId = contract.companyId
            val compensation = contractServiceAdapter.getCurrentCompensation(contract.id)

            log.info { "Updating compensation for Employee associated with ContractId: $contractId" }
            employeeService.updateEmployeeCompensation(
                companyId,
                contract.id,
                createCompensationData(
                    currency = contract.currency,
                    compensation
                )
            )
            log.info { "Successfully updated compensation for ContractId: $contractId" }
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during compensation update for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during compensation update for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Exception occurred during compensation update for EventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling ContractCompensationUpdateEvent for EventLogId: $eventLogId" }
        }
    }
}
