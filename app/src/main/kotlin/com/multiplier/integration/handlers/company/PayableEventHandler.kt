package com.multiplier.integration.handlers.company

import com.multiplier.integration.accounting.domain.FinancialTransactionAmountUpdatedHandlerService
import com.multiplier.integration.accounting.domain.FinancialTransactionHandlerService
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parsePayableEventMessageFromString
import com.multiplier.payable.kafka.schema.CompanyPayableEvent
import com.multiplier.payable.kafka.schema.EventType
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class PayableEventHandler(
    private val financialTransactionHandlerService: FinancialTransactionHandlerService,
    private val financialTransactionAmountUpdatedHandlerService: FinancialTransactionAmountUpdatedHandlerService,
    private val eventLogService: EventLogService,
) {

    private val log = KotlinLogging.logger {}

    fun handlePayableEvent(eventLogId: String) {

        log.info("Started handlePayableEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: Exception) {
            log.error("IllegalStateException encountered during processing of handlePayableEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handlePayableEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val payload: CompanyPayableEvent = parsePayableEventMessageFromString(payloadString)
        val companyPayableId = payload.companyPayableId

        if (payload.eventType == EventType.STATUS_UPDATED) {
            log.info("Sending company payable to process with ID: {}", companyPayableId)
            return financialTransactionHandlerService.upsertHandler(companyPayableId)
        }

        if (payload.eventType == EventType.DUE_AMOUNT_UPDATED) {
            log.info { "Due amount updated event received for company payable id = $companyPayableId" }
            return financialTransactionAmountUpdatedHandlerService.amountUpdateHandler(companyPayableId)
        }
    }
}