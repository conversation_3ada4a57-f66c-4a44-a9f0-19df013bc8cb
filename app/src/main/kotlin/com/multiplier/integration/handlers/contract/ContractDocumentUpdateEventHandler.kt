package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.contract.ContractDocumentEventMessageOuterClass.ContractDocumentEventMessage
import com.multiplier.integration.adapter.api.BenefitServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.core.model.ContractDocumentUpdateEvent
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseContractDocumentEventMessageFromString
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class ContractDocumentUpdateEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val docgenServiceAdapter: DocgenServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    fun handleContractDocumentUpdateEvent(event: ContractDocumentUpdateEvent) {

        val eventLogId = event.eventLogId
        log.info("Started handleContractDocumentUpdateEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleContractDocumentUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of handleContractDocumentUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleContractDocumentUpdateEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleContractDocumentUpdateEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val payload: ContractDocumentEventMessage = parseContractDocumentEventMessageFromString(payloadString)

        val payloadEvent = payload.event
        val documentId = payloadEvent.documentId
        val contractId = payloadEvent.contractId

        log.info("Fetching document by ID: {}", documentId)
        val document = docgenServiceAdapter.getDocument(documentId)

        log.info("fetched document: {} with id: {}", document, documentId)

        log.info { "Fetching contract by ID: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.updateContractDocument(companyId, contractId, document!!)
    }
}
