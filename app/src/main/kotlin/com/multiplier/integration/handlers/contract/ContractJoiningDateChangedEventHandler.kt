package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractJoiningDateChangedEvent
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseFromString
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ContractJoiningDateChangedEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractJoiningDateChangedEvent(event: ContractJoiningDateChangedEvent) {
        val eventLogId = event.eventLogId
        log.info { "Started handling ContractJoiningDateChangedEvent for EventLogId: $eventLogId" }

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        log.info { "EventLog retrieved for EventLogId: $eventLogId, proceeding with event processing." }
        eventLogService.processEvent(eventLog)

        try {
            val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
            val builder = ContractEventMessage.newBuilder()

            val payload: ContractEventMessage = parseFromString(payloadString, builder)
            val contractId = payload.event.contractId

            log.info { "Retrieving contract information by ID: $contractId" }
            val contract = contractServiceAdapter.findContractByContractId(contractId)

            log.info { "Processing joining date change for ContractId: $contractId" }
            employeeService.markEmployeeAsInactive(contract.companyId, contract.id)
            eventLogService.markEventSuccessful(eventLog)

            log.info { "Successfully processed joining date change for ContractId: $contractId" }
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of ContractJoiningDateChangedEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing ContractJoiningDateChangedEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of ContractJoiningDateChangedEvent for EventLogId: $eventLogId" }
        }

        log.info { "ContractJoiningDateChangedEvent processing finished for eventLogId: $eventLogId" }
    }
}
