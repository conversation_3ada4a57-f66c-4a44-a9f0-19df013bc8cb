package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.currency.Currency
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.core.model.ContractCompensationUpdateEvent
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseFromString
import com.multiplier.integration.utils.parseSalaryReviewUpdateEventMessageFromString
import kotlinx.serialization.json.*
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class CompensationCreationEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleCompensationCreationEvent(eventLogId: String) {

        log.info("Started handleCompensationCreationEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processCreationEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleCompensationCreationEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e:UnsupportedOperationException) {
            log.error(
                "UnsupportedOperationException encountered during processing of handleCompensationCreationEvent for EventLogId: $eventLogId",e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleCompensationCreationEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleCompensationCreationEvent for EventLogId: $eventLogId" }
        }
    }

    @EventListener
    fun handleCompensationUpdationEvent(event: ContractCompensationUpdateEvent) {
        val eventLogId = event.eventLogId
        log.info("Started handleCompensationUpdationEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processUpdationEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleCompensationUpdationEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e:UnsupportedOperationException) {
            log.error(
                "UnsupportedOperationException encountered during processing of handleCompensationUpdationEvent for EventLogId: $eventLogId",e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleCompensationUpdationEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleCompensationUpdationEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processUpdationEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val payload: SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage = parseSalaryReviewUpdateEventMessageFromString(payloadString)
        val contractId = payload.contractId

        log.info { "Fetching contract by ID: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.updateEmployeeCompensation(companyId,
            contractId,
            CompensationData( //remove this
                33.4,
                Currency.CurrencyCode.INR,
                CompensationOuterClass.RateFrequency.ANNUALLY
            ))
    }

    private fun processCreationEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val jsonElement = Json.parseToJsonElement(payloadString)
        val contractId = jsonElement.jsonObject["contractId"]?.jsonPrimitive?.long
            ?: throw IntegrationIllegalStateException("Contract ID missing in event payload")

        log.info { "Fetching contract by contractId: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.updateEmployeeCompensation(companyId, contractId, CompensationData(33.4, Currency.CurrencyCode.INR, CompensationOuterClass.RateFrequency.ANNUALLY))
    }
}