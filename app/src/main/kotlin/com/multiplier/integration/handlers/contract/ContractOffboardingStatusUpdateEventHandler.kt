package com.multiplier.integration.handlers.contract

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractOffboardingStatusUpdateEvent
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.utils.parseContractOffboardingEventMessageFromString
import com.multiplier.integration.utils.toLocalDate
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import com.multiplier.contract.offboarding.schema.ContractOffboardingEventMessageOuterClass
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException

class ContractOffboardingStatusUpdateEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {
    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractOffboardingStatusUpdateEvent(event: ContractOffboardingStatusUpdateEvent) {
        val eventLogId = event.eventLogId
        log.info("Started handleContractOffboardingStatusUpdateEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of handleContractOffboardingStatusUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of handleContractOffboardingStatusUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleContractOffboardingStatusUpdateEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleContractOffboardingStatusUpdateEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val contractOffboardingEventMessage: ContractOffboardingEventMessageOuterClass.ContractOffboardingEventMessage = parseContractOffboardingEventMessageFromString(payloadString)
        val contractOffboardingEvent = contractOffboardingEventMessage.event
        val contractId = contractOffboardingEvent.contractId
        val terminationDate = contractOffboardingEvent.terminationDate
        val terminationReason = contractOffboardingEvent.terminationReason

        log.info("Fetching contract by ID: {}", contractId)
        val contract = contractServiceAdapter.findContractByContractId(contractId)

        if (contract.status == ContractOuterClass.ContractStatus.ENDED) {
            employeeService.terminateEmployee(contract.companyId, contractId, terminationDate.toLocalDate(), terminationReason, eventLog)
        }

    }
}