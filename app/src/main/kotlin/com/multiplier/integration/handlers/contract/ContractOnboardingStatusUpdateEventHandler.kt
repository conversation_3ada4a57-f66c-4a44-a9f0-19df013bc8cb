package com.multiplier.integration.handlers.contract

import com.multiplier.contract.schema.onboarding.Onboarding
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.core.model.ContractOnboardingStatusUpdateEvent
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.exception.IllegalStateExceptionForTrinet
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.InvalidContractProcessingDateException
import com.multiplier.integration.utils.parseContractOnboardingEventMessageFromString
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.buildJsonObject
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ContractOnboardingStatusUpdateEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService,
    private val notificationsService: NotificationsService,
    private val eventLogRepository: EventLogRepository,
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractOnboardingStatusUpdateEvent(event: ContractOnboardingStatusUpdateEvent) {

        val eventLogId = event.eventLogId
        log.info("Started handling ContractOnboardingStatusUpdateEvent for contract ID: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during processing of ContractOnboardingStatusUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error("UnsupportedOperationException encountered during processing of ContractOnboardingStatusUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IllegalStateExceptionForTrinet) {
            log.error("IllegalStateExceptionForTrinet encountered during processing of ContractOnboardingStatusUpdateEvent for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
            notificationsService.sendAdminAutoSyncFailed(e.companyId, "TriNet", e.employeeName, e.message!!, eventLog.syncId)
        } catch (e: InvalidContractProcessingDateException) { //only for TriNet
            log.error("Exception in [ContractOnboardingStatusUpdateEventHandler]", e)
            eventLogService.handleInvalidProcessingDateEvent(eventLog, e.contract)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing ContractOnboardingStatusUpdateEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, "Could not be synced due to an unknown error")
        } finally {
            log.info { "Completed handling of ContractOnboardingStatusUpdateEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processEvent(eventLog: JpaEventLog) {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val contractOnboardingEventMessage = parseContractOnboardingEventMessageFromString(payloadString)

        val contractOnboardingEvent = contractOnboardingEventMessage.event
        val contractId = contractOnboardingEvent.contractId

        val experience = contractOnboardingEvent.experience

        if (!listOf("company").contains(experience)) {
            val reason = "Ignoring event for contract ID: $eventLogId because experience: $experience is not supported."
            log.warn(reason)
            eventLogService.handleNonRetryableEvent(eventLog, reason)
            return
        }

        log.info("Fetching contract by ID: {}", contractId)
        val contract = contractServiceAdapter.findContractByContractId(contractId)

        log.info("Fetching onboarding by contractId: {}, experience: {}", contractId, experience)
        val onboarding = contractServiceAdapter.findOnboardingByContractIdAndExperience(contract.id, experience)

        log.info("Fetching member by ID: {}", contract.memberId)
        val member = memberServiceAdapter.findMemberByMemberId(contract.memberId)

        log.info { "Handling onboarding status: ${onboarding.status} for contract ID: ${contract.id}" }

        log.info { "Checking if employee already existed in cache to prevent circular creation ${contract.id}"}

        // update companyId for event entry
        val updateEventLog = eventLogService.findEventLogByEventId(eventLog.eventId)
        updateEventLog.companyId = contract.companyId
        eventLogRepository.save(updateEventLog)

        if (employeeService.checkIfEmployeeAlreadyExistedInCache(contract)) {
            log.info { "Employee with contractId=${contract.id} already existed, skipping creation on external platforms"}
            throw IntegrationIllegalStateException("EXISTED")
        } else {
            log.info { "Employee with contractId=${contract.id} not found in cache, creating it on external platforms"}
            when (onboarding.status) {
//         ORDER_FORM_SENT_TO_MULTIPLIER is being added since contract service is not publishing MEMBER_VERIFICATION_COMPLETED
//         for type CONTRACTOR. Slack chat: https://multiplier-group.slack.com/archives/C02TJE76S68/p1701323066782029
                Onboarding.ContractOnboardingStatus.MEMBER_VERIFICATION_COMPLETED, Onboarding.ContractOnboardingStatus.ACTIVE,
                Onboarding.ContractOnboardingStatus.ORDER_FORM_SENT_TO_MULTIPLIER
                -> {
                    log.info { "Member verification completed for contract ID: ${contract.id}" }


                    log.info { "Creating employee record for contract ID: ${contract.id}" }
                    employeeService.createEmployee(contract.companyId, null, member, contract, eventLog)
                    log.info { "Successfully created employee record for contract ID: ${contract.id}" }

                    //TODO - add proper platform configuration, exclude TriNet from this for now
                    if (!employeeService.isTriNetPlatform(contract.companyId)) {
                        handleSuccessfulEmployeeCreation(contractId)
                    }
                }

                else -> log.warn("Status ${onboarding.status} for contract ID: $contractId is not yet supported")
            }
        }
    }

    private fun handleSuccessfulEmployeeCreation(contractId: Long) {
        val payloadJson = buildJsonObject {
            put("contractId", JsonPrimitive(contractId))
        }
        val payloadString = payloadJson.toString()

        val compensationEvent = eventLogService.createEventLog(
            type = EventType.SERVICE_INTERNAL_CREATE_COMPENSATION,
            status = EventStatus.TO_BE_PROCESSED,
            payload = payloadString,
            contractId = contractId
        )

        val factsheetEvent = eventLogService.createEventLog(
            type = EventType.SERVICE_INTERNAL_CREATE_INSURANCE_FACTSHEET,
            status = EventStatus.TO_BE_PROCESSED,
            payload = payloadString,
            contractId = contractId
        )

        val onboardingKitEvent = eventLogService.createEventLog(
            type = EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT,
            status = EventStatus.TO_BE_PROCESSED,
            payload = payloadString,
            contractId = contractId
        )

        log.info("Created compensationEvent: {}, factsheetEvent: {}, onboardingKitEvent: {}",
            compensationEvent.eventId, factsheetEvent.eventId, onboardingKitEvent.eventId)
    }
}