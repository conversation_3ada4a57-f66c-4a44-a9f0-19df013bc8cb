package com.multiplier.integration.handlers.contract

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.core.model.PayrollPayslipDocumentPublishedEvent
import com.multiplier.integration.core.model.PayrollPayslipDocumentUploadedEvent
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parsePayrollDocumentEventMessageFromString
import com.multiplier.schema.kafka.proto.PayrollDocumentEvent
import com.multiplier.schema.kafka.proto.PayrollDocumentEventMessage
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class PayrollDocumentEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService,
    private val docgenServiceAdapter: DocgenServiceAdapter
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleUploadPayslipEvent(event: PayrollPayslipDocumentUploadedEvent) {
        val eventLogId = event.eventLogId
        log.info("Started handleUploadPayslipEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processUploadEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error(
                "IllegalStateException encountered during processing of handleUploadPayslipEvent for EventLogId: $eventLogId",
                e
            )
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handleUploadPayslipEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handleUploadPayslipEvent for EventLogId: $eventLogId" }
        }
    }

    @EventListener
    fun handlePublishPayslipEvent(event: PayrollPayslipDocumentPublishedEvent) {
        val eventLogId = event.eventLogId
        log.info("Started handlePublishPayslipEvent for eventLogId: $eventLogId")

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        eventLogService.processEvent(eventLog)

        try {
            processPublishEvent(eventLog)
            eventLogService.markEventSuccessful(eventLog)
        } catch (e: IntegrationIllegalStateException) {
            log.error(
                "IllegalStateException encountered during processing of handlePublishPayslipEvent for EventLogId: $eventLogId",
                e
            )
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: UnsupportedOperationException) {
            log.error(
                "UnsupportedOperationException encountered during processing of handlePublishPayslipEvent for EventLogId: $eventLogId",
                e
            )
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.info("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error processing handlePublishPayslipEvent for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Completed handling of handlePublishPayslipEvent for EventLogId: $eventLogId" }
        }
    }

    private fun processPublishEvent(eventLog: JpaEventLog) {
        val payloadEvent = getPayload(eventLog)
        val contractId = payloadEvent.contractId
        val downloadUrl = payloadEvent.downloadUrl

        log.info { "Fetching contract by ID: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.uploadPayslipDocument(companyId, contractId, InternalDocument(downloadUrl = downloadUrl))
    }

    private fun processUploadEvent(eventLog: JpaEventLog) {
        val payloadEvent = getPayload(eventLog)

        val documentId = payloadEvent.documentId
        val contractId = payloadEvent.contractId

        log.info("Fetching document by ID: {}", documentId)
        val document = docgenServiceAdapter.getDocument(documentId)

        log.info { "Fetching contract by ID: $contractId" }
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val companyId = contract.companyId

        employeeService.uploadPayslipDocument(
            companyId = companyId,
            contractId = contractId,
            document = InternalDocument(downloadUrl = document?.internalDownloadURL()?.toString() ?: "")
        )
    }

    private fun getPayload(eventLog: JpaEventLog): PayrollDocumentEvent {
        val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
        val eventLogId = eventLog.id

        log.info("Parsing payloadString: {} for eventLogId: {}", payloadString, eventLogId)
        val payload: PayrollDocumentEventMessage = parsePayrollDocumentEventMessageFromString(payloadString)

        val payloadEvent = payload.event
        return payloadEvent
    }

}
