package com.multiplier.integration.handlers.contract

import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractStartedEvent
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.utils.parseFromString
import mu.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class ContractStartedEventHandler(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val employeeService: EmployeeService,
    private val eventLogService: EventLogService
) {

    private val log = KotlinLogging.logger {}

    @EventListener
    fun handleContractStartedEvent(event: ContractStartedEvent) {
        val eventLogId = event.eventLogId
        log.info { "Started handling ContractStartedEvent for EventLogId: $eventLogId" }

        val eventLog = eventLogService.findEventLogByEventId(eventLogId)
        log.info { "EventLog retrieved for EventLogId: $eventLogId, beginning event processing." }
        eventLogService.processEvent(eventLog)

        try {
            val payloadString = eventLog.eventPayload ?: throw IntegrationIllegalStateException("Event payload cannot be null")
            val builder = ContractEventMessage.newBuilder()

            val payload: ContractEventMessage = parseFromString(payloadString, builder)
            val contractId = payload.event.contractId

            log.info { "Retrieving contract information by ID: $contractId" }
            val contract = contractServiceAdapter.findContractByContractId(contractId)

            log.info { "Initiating ContractStartedEvent for ContractId: $contractId and CompanyId: ${contract.companyId}" }
            employeeService.markEmployeeAsActive(contract.companyId, contractId)
            eventLogService.markEventSuccessful(eventLog)
            log.info { "Employee marked as active for ContractId: $contractId" }
        } catch (e: IntegrationIllegalStateException) {
            log.error("IllegalStateException encountered during ContractStartedEvent processing for EventLogId: $eventLogId", e)
            eventLogService.handleNonRetryableEvent(eventLog, e.message)
        } catch (e: IntegrationDownstreamException) {
            log.warn("DownstreamServiceError for eventLogId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } catch (e: Exception) {
            log.error("Error occurred while marking employee active for ContractId: $eventLogId", e)
            eventLogService.handleRetryableEvent(eventLog, e.message)
        } finally {
            log.info { "Finalizing handling of ContractStartedEvent for EventLogId: $eventLogId" }
        }

        log.info { "Completed handling of ContractStartedEvent for ContractId: $eventLogId" }
    }
}
