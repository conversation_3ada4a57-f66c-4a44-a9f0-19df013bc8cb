package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table

@Entity
@Table(
    name = "platform_timeoff_integration",
    schema = "customer_integration",
)
class JpaPlatformTimeoffIntegration(
    id: Long? = null,

    @Column(name = "integration_id")
    val integrationId: Long,

    @Column(name = "contract_id")
    val contractId: Long,

    @Column(name = "employee_id")
    val employeeId: String,

    @Column(name = "internal_timeoff_id")
    val internalTimeoffId: Long,

    @Column(name = "external_timeoff_id")
    val externalTimeoffId: String,

) : AuditableBaseEntity(id)