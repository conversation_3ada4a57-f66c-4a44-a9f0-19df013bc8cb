package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.*

@Entity
@Table(
    name = "external_platform_values",
    schema = "customer_integration"
)
class JpaExternalPlatformValues(
    @Column(name = "field_id")
    val fieldId: String,
    @Column(name = "mapped_key")
    val mappedKey: String?,
    @Column(name = "integration_id")
    val integrationId: Long,
    @Column(name = "values")
    var values: List<String>?,
    @Column(name = "is_deleted")
    var isDeleted: Boolean = false,
    id: Long? = null,
): AuditableBaseEntity(id)

