package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.types.SyncStatus
import com.multiplier.integration.types.SyncType
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(
    name = "fields_mapping",
    schema = "customer_integration"
)
class JpaFieldsMapping(
    @Column(name = "entity_id")
    val entityId: Long,
    @Column(name = "integration_id")
    val integrationId: Long,
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    var type: FieldType,
    @Column(name = "origin_field")
    val originField: String?,
    @Column(name = "origin_field_label")
    var originFieldLabel: String?,
    @Column(name = "mapped_field")
    var mappedField: String?,
    @Column(name = "mapped_field_label")
    var mappedFieldLabel: String?,
    @Column(name = "is_required")
    var isRequired: Boolean = false,
    @Column(name = "is_active")
    var isActive: Boolean = true,
    @Column(name = "is_calculated")
    var isCalculated: Boolean = false,
    @Column(name = "company_id")
    var companyId: Long,
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    var parent: JpaFieldsMapping? = null,
    @OneToMany(mappedBy = "parent", cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    var children: List<JpaFieldsMapping> = emptyList(),
    id: Long? = null,
): AuditableBaseEntity(id)


enum class FieldType {
    STRING,
    ENUM
}
