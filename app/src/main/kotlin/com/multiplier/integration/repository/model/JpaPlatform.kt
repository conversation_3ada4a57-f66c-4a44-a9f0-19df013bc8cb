package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.types.PlatformCategory
import jakarta.persistence.*

@Entity
@Table(
    name = "platform",
    schema = "customer_integration",
)
class JpaPlatform(
    id: Long? = null,
    @Column(name = "category")
    @Enumerated(EnumType.STRING)
    val category: PlatformCategory = PlatformCategory.HRIS,
    @Column(name = "name") val name: String = "",
    @Column(name = "is_position_dropdown_enabled")
    val isPositionDropdownEnabled: Boolean?,
    @Column(name = "is_special_enum") val isSpecialEnum: Boolean = false
) : AuditableBaseEntity(id)

fun JpaPlatform.toPlatformData(): PlatformData? {
    return PlatformData(id ?: return null, name, this)
}

data class PlatformData(
    val id: Long,
    val name: String,
    val platform: JpaPlatform,
)
