package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.BaseEntity
import jakarta.persistence.*
import org.hibernate.envers.Audited
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(
    name = "sftp_access_request",
    schema = "customer_integration",
    uniqueConstraints = [
        UniqueConstraint(
            name = "uq_sftp_access_request",
            columnNames = ["company_id", "entity_id", "bulk_module"]
        )
    ]
)
@EntityListeners(AuditingEntityListener::class)
@Audited
class JpaSFTPAccessRequest(
    id: Long? = null,

    @Column(name = "company_id")
    val companyId: Long,

    @Column(name = "entity_id")
    val entityId: Long,

    @Column(name = "bulk_module")
    @Enumerated(EnumType.STRING)
    val bulkModule: BulkUploadModule,

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: SftpAccessRequestStatus,

    @Column(name = "main_sftp_directory")
    var mainSFTPDirectory: String?,

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    var createdBy: Long? = null,

    @Column(name = "created_on", updatable = false)
    @CreatedDate
    var createdOn: LocalDateTime? = null,

    @Column(name = "updated_by", updatable = true)
    @LastModifiedBy
    var updatedBy: Long? = null,

    @Column(name = "updated_on", updatable = true)
    @LastModifiedDate
    var updatedOn: LocalDateTime? = null,
) : BaseEntity(id)

enum class SftpAccessRequestStatus {
    PENDING,
    APPROVED,
    REJECTED,
    REVOKED,
}

enum class BulkUploadModule {
    TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
}
