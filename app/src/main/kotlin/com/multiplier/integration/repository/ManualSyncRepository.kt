package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaManualSync
import com.multiplier.integration.types.SyncStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
@JvmDefaultWithCompatibility
interface ManualSyncRepository : JpaRepository<JpaManualSync, Long> {

    fun findTopByIntegrationIdOrderByCreatedOnDesc(integrationId: Long): Optional<JpaManualSync>

    fun findByIntegrationIdOrderByCreatedOnDesc(integrationId: Long): List<JpaManualSync>?

    fun findBySyncId(syncId: String): Optional<JpaManualSync>

    fun findByStatus(status: SyncStatus): List<JpaManualSync>
}
