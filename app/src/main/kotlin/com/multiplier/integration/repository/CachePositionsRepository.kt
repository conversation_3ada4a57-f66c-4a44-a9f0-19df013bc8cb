package com.multiplier.integration.repository

import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.repository.model.*
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
interface CachePositionsRepository : JpaRepository<JpaCachePositions, CachePositionsId> {

    @Query("""
        SELECT new kotlin.Pair(jp.data, jp.updatedOn)
        FROM JpaCachePositions jp
        WHERE jp.id.companyId = :companyId 
        AND jp.id.platformId = :platformId
    """)
    fun findCachedPositions(
        @Param("companyId") companyId: Long,
        @Param("platformId") platformId: Long
    ): Pair<GetPositionDetailResponse, LocalDateTime>?

    @Modifying
    @Transactional
    fun upsertCachedPosition(
        companyId: Long,
        platformId: Long,
        data: GetPositionDetailResponse,
        now: LocalDateTime = LocalDateTime.now()
    ) {
        val id = CachePositionsId(companyId, platformId)
        val existing = findById(id).orElse(
            JpaCachePositions(
                id = id,
                data = data,
                createdOn = now,
                updatedOn = now
            )
        )

        existing.data = data
        existing.updatedOn = now
        save(existing)
    }


}
