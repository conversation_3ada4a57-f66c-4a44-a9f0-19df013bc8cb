package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.JoinColumn
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table(
    name = "platform_contract_integration",
    schema = "customer_integration",
)
class JpaPlatformContractIntegration(

    id: Long? = null,

    @Column(name = "contract_id") val contractId: Long,
    @Column(name = "provider_id", insertable = false, updatable = false) val providerId: Long,
    @ManyToOne(cascade = [CascadeType.MERGE], fetch = FetchType.LAZY)
    @JoinColumn(name = "provider_id", nullable = false)
    val provider: JpaProvider,

    @Column(name = "platform_id", insertable = false, updatable = false) val platformId: Long,
    @ManyToOne(cascade = [CascadeType.MERGE], fetch = FetchType.LAZY)
    @JoinColumn(name = "platform_id", nullable = false)
    val platform: JpaPlatform,

    @Column(name = "platform_employee_id") var platformEmployeeId: String,
    @Column(name = "remote_id") val remoteId: String?,
    @Column(name = "integration_id") var integrationId: Long?,
) : AuditableBaseEntity(id)
