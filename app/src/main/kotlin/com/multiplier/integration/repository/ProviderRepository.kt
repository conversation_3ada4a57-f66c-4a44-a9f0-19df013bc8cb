package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.type.ProviderName
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ProviderRepository : JpaRepository<JpaProvider, Long> {

    fun findFirstByName(name: ProviderName): JpaProvider?
}
