package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaProviderPlatform
import com.multiplier.integration.repository.type.ProviderPlatformStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
@JvmDefaultWithCompatibility
interface ProviderPlatformRepository : JpaRepository<JpaProviderPlatform, Long> {

    fun findByPlatformIdAndStatus(platformId: Long, status: ProviderPlatformStatus): List<JpaProviderPlatform>
}