package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaPendingEmployee
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface PendingEmployeeRepository : JpaRepository<JpaPendingEmployee, Long> {
    fun findByIntegrationIdAndInviteRequested(integrationId: String, inviteRequested: Boolean): List<JpaPendingEmployee>
    @Modifying
    @Transactional
    @Query(
        nativeQuery = true,
        value = "UPDATE customer_integration.pending_employee SET pending_invite = :pendingInvite WHERE identifier_value = :identifier"
    )
    fun setPendingInviteByIdentifier(identifier: String, pendingInvite: Boolean): Int

    @Modifying
    @Transactional
    @Query(
        nativeQuery = true,
        value = "UPDATE customer_integration.pending_employee SET invite_requested = :inviteRequested WHERE integration_id = :integrationId"
    )
    fun setInvitationRequestedForAll(@Param("integrationId") integrationId: String, @Param("inviteRequested") inviteRequested: Boolean): Int

    @Modifying
    @org.springframework.transaction.annotation.Transactional
    @Query(
        value = "DELETE FROM JpaPendingEmployee pe WHERE pe.integrationId = :integrationId")
    fun deleteByIntegrationId(@Param("integrationId") integrationId: String): Int
}
