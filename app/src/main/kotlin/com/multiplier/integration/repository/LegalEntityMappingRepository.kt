package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
@JvmDefaultWithCompatibility
interface LegalEntityMappingRepository : JpaRepository<JpaLegalEntityMapping, Long> {

    fun findByIntegrationId(integrationId: Long): List<JpaLegalEntityMapping>

    fun findByIntegrationIdAndEntityId(integrationId: Long, entityId: Long): Optional<JpaLegalEntityMapping>

}
