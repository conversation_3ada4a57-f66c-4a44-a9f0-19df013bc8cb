package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.types.PlatformCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface CompanyIntegrationRepository : JpaRepository<JpaCompanyIntegration, Long> {

    fun findByCompanyIdIn(companyIds: Set<Long>): List<JpaCompanyIntegration>

    @Query(
        """
        FROM JpaCompanyIntegration e
        WHERE e.companyId = :companyId AND e.platform.id = :platformId AND e.enabled = true
        """,
    )
    fun findEnabledIntegration(
        companyId: Long,
        platformId: Long,
    ): List<JpaCompanyIntegration>?

    @Query(
        """
        FROM JpaCompanyIntegration e 
        WHERE e.companyId = :companyId AND e.provider = :provider AND e.platform = :platform
    """)
    fun findIntegration(
        companyId: Long,
        provider: JpaProvider,
        platform: JpaPlatform
    ): JpaCompanyIntegration?

    fun findByAccountToken(accountToken: String): JpaCompanyIntegration?


    @Query("FROM JpaCompanyIntegration e WHERE e.companyId = :companyId AND e.enabled = :enabled")
    fun findByCompanyIdAndEnabled(
        @Param("companyId") companyId: Long?,
        @Param("enabled") enabled: Boolean?
    ): List<JpaCompanyIntegration?>?

    @Query("FROM JpaCompanyIntegration e WHERE e.companyId = :companyId AND e.enabled = :enabled AND e.platform.category = :category")
    fun findEnabledIntegrationByCompanyIdAndPlaformCategory(
        @Param("companyId") companyId: Long?,
        @Param("enabled") enabled: Boolean?,
        @Param("category") category: PlatformCategory?
    ): List<JpaCompanyIntegration?>?

    @Query("""
        SELECT e FROM JpaCompanyIntegration e 
        WHERE e.platform.id = :platformId AND e.enabled = true
    """)
    fun findEnabledIntegrationsByPlatformId(platformId: Long): List<JpaCompanyIntegration>

    @Query("FROM JpaCompanyIntegration e WHERE e.platform.id = :platformId AND e.provider.id = :providerId AND e.externalCompanyId = :externalCompanyId AND e.enabled = true")
    fun findByProviderIdAndPlatformIdAndExternalCompanyId(providerId: Long, platformId: Long, externalCompanyId: String): List<JpaCompanyIntegration>?

    //TODO need to remove
    @Query("FROM JpaCompanyIntegration e WHERE e.companyId = :companyId AND e.platform.id = :platformId AND e.enabled = true")
    fun findEnabledIntegrationsByCompanyIdAndPlatformId(
        @Param("companyId") companyId: Long,
        @Param("platformId") platformId: Long
    ): List<JpaCompanyIntegration>?

    @Query("FROM JpaCompanyIntegration e WHERE e.enabled = true AND e.outgoingSyncEnabled = true AND e.timeOffSyncEnabled = true")
    fun findEORTimeoffEnabledIntegration(): List<JpaCompanyIntegration>
}
