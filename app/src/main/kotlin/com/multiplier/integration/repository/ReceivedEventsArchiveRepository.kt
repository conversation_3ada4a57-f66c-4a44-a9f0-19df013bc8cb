package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaReceivedEventArchive
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface ReceivedEventsArchiveRepository: JpaRepository<JpaReceivedEventArchive, Long> {
    fun findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(integrationId: String, entityCountry: String, isEntityEnabled: Boolean, processed: Boolean): List<JpaReceivedEventArchive>


    fun findByEventId(eventId: String): JpaReceivedEventArchive

}