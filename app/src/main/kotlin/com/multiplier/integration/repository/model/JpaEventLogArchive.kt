package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import java.time.LocalDateTime

@Entity
@Table(name = "event_log_archive", schema = "customer_integration")
class JpaEventLogArchive(
        id: Long? = null,

        @Column(name = "event_id")
        val eventId: String,

        @Column(name = "event_type")
        @Enumerated(EnumType.STRING)
        var eventType: EventType,

        @Column(name = "event_payload", columnDefinition = "TEXT")
        val eventPayload: String? = null,

        @Column(name = "status")
        @Enumerated(EnumType.STRING)
        var status: EventStatus,

        @Column(name = "retries_done", nullable = false)
        var retriesDone: Int = 0,

        @Column(name = "retries_left", nullable = false)
        var retriesLeft: Int = 3,

        @Column(name = "error_message", columnDefinition = "TEXT")
        var errorMessage: String? = null,

        @Column(name = "last_attempt")
        var lastAttempt: LocalDateTime? = null,

        @Column(name = "next_attempt")
        var nextAttempt: LocalDateTime? = null,

        @Column(name = "contract_id")
        var contractId: Long? = null,

        @Column(name = "sync_id", nullable = true)
        var syncId: String? = null,

        @Column(name = "company_id")
        var companyId: Long? = null,
) : AuditableBaseEntity(id)
