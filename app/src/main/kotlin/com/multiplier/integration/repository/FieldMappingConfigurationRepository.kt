package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.FieldMappingConfigurationType
import com.multiplier.integration.repository.model.JpaFieldMappingConfiguration
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
@JvmDefaultWithCompatibility
interface FieldMappingConfigurationRepository : JpaRepository<JpaFieldMappingConfiguration, Long> {

    fun findByKeyInAndIsDeletedFalseAndPlatformIdIsNull(keys: List<String>): List<JpaFieldMappingConfiguration>

    fun findByKeyInAndIsDeletedFalseAndPlatformId(keys: List<String>, platformId: Long): List<JpaFieldMappingConfiguration>

    fun findByKeyInAndIsDeletedFalse(
        keys: List<String>,
        platformId: Long? = null
    ): List<JpaFieldMappingConfiguration> {
        return if (platformId == null) {
            findByKeyInAndIsDeletedFalseAndPlatformIdIsNull(keys)
        } else {
            findByKeyInAndIsDeletedFalseAndPlatformId(keys, platformId)
        }
    }

    fun findByTypeAndPlatformIdAndIsDeletedFalse(type: FieldMappingConfigurationType, platformId: Long): List<JpaFieldMappingConfiguration>

    fun findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type: FieldMappingConfigurationType): List<JpaFieldMappingConfiguration>

    fun findByTypeAndIsDeletedFalse(
        type: FieldMappingConfigurationType,
        platformId: Long? = null
    ): List<JpaFieldMappingConfiguration> {
        return if (platformId == null) {
            findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type)
        } else {
            findByTypeAndPlatformIdAndIsDeletedFalse(type, platformId)
        }
    }

    fun getMappingsByTypeWithFallback(type: FieldMappingConfigurationType, platformId: Long): List<JpaFieldMappingConfiguration> {
        return findByTypeAndPlatformIdAndIsDeletedFalse(type, platformId)
            .takeIf { it.isNotEmpty() }
            ?: findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type)
    }

}
