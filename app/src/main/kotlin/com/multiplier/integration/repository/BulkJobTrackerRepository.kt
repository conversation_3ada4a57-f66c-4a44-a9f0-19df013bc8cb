package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaBulkJobTracker
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
@Transactional(readOnly = true)
interface BulkJobTrackerRepository: JpaRepository<JpaBulkJobTracker, Long> {

    fun findByJobId(jobId: Long): JpaBulkJobTracker?
}