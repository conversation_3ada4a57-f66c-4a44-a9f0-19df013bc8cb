package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.types.DocumentFolderType
import jakarta.persistence.*

@Entity
@Table(
    name = "document_folders",
    schema = "customer_integration",
)
class JpaDocumentFolder(
    id: Long? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "folder_type")
    val folderType: DocumentFolderType,
    @Column(name = "folder_label") var folderLabel: String,
    @Column(name = "folder_id") var folderId: String,
    @Column(name = "is_active") var isActive: Boolean = true,
    @ManyToOne(cascade = [CascadeType.ALL], fetch = FetchType.LAZY)
    @JoinColumn(name = "integration_id")
    val companyIntegration: JpaCompanyIntegration,
) : AuditableBaseEntity(id)
