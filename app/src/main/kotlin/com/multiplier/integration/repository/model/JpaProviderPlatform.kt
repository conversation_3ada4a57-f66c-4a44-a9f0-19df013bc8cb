package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.repository.type.ProviderPlatformStatus
import jakarta.persistence.*

@Entity
@Table(name = "provider_platform", schema = "customer_integration")
class JpaProviderPlatform(
        id: Long? = null,

        @ManyToOne(fetch = FetchType.LAZY)
        @JoinColumn(name = "provider_id", nullable = false)
        val provider: JpaProvider,

        @ManyToOne(fetch = FetchType.LAZY)
        @JoinColumn(name = "platform_id", nullable = false)
        val platform: JpaPlatform,

        @Column(name = "status", nullable = false)
        @Enumerated(EnumType.STRING)
        val status: ProviderPlatformStatus,
) : AuditableBaseEntity(id)
