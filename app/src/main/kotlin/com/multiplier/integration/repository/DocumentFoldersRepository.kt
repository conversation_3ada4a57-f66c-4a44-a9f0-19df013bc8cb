package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaDocumentFolder
import com.multiplier.integration.types.DocumentFolderType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface DocumentFoldersRepository :
    JpaRepository<JpaDocumentFolder, Long> {

    @Query("SELECT df FROM JpaDocumentFolder df JOIN FETCH df.companyIntegration WHERE df.folderType = :folderType AND df.companyIntegration.id = :integrationId AND df.isActive = TRUE")
    fun findByFolderTypeAndIntegrationId(
        @Param("integrationId") integrationId: Long,
        @Param("folderType") folderType: DocumentFolderType,
    ): JpaDocumentFolder?
}
