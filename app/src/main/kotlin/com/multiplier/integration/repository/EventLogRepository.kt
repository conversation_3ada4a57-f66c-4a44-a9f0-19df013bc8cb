package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*


@Repository
@JvmDefaultWithCompatibility
interface EventLogRepository : JpaRepository<JpaEventLog, Long> {

    @Query("SELECT e FROM JpaEventLog e WHERE e.status IN :statuses AND e.nextAttempt < :time ORDER BY e.createdOn ASC")
    fun findByStatusInAndNextAttemptBeforeOrdered(
        @Param("statuses") statuses: Collection<EventStatus>,
        @Param("time") time: LocalDateTime
    ): List<JpaEventLog>

    fun findByEventId(eventId: String): Optional<JpaEventLog>

    fun findBySyncId(syncId: String): List<JpaEventLog>

    @Query("SELECT e FROM JpaEventLog e WHERE e.status IN :statuses AND e.eventType IN :eventTypes AND e.nextAttempt < :time ORDER BY e.createdOn ASC")
    fun findByStatusInAndEventTypeInAndNextAttemptBeforeOrdered(
        @Param("statuses") statuses: Collection<EventStatus>,
        @Param("eventTypes") eventTypes: Collection<EventType>,
        @Param("time") time: LocalDateTime
    ): List<JpaEventLog>

    @Query("SELECT e FROM JpaEventLog e WHERE e.contractId = :contractId AND e.createdOn BETWEEN :startTime AND :endTime AND e.eventType IN :eventTypes AND e.syncId IS NULL AND e.status = :status")
    fun findByContractIdAndEventTimeRange(
        @Param("contractId") contractId: Long?,
        @Param("startTime") startTime: LocalDateTime?,
        @Param("endTime") endTime: LocalDateTime?,
        @Param("eventTypes") eventTypes: List<EventType?>?,
        @Param("status") status: EventStatus?
    ): List<JpaEventLog?>?

    @Query("SELECT e FROM JpaEventLog e WHERE e.companyId = :companyId AND e.createdOn BETWEEN :startTime AND :endTime AND e.eventType IN :eventTypes AND e.syncId IS NULL")
    fun findByCompanyIdAndEventTimeRange(
        @Param("companyId") companyId: Long?,
        @Param("startTime") startTime: LocalDateTime?,
        @Param("endTime") endTime: LocalDateTime?,
        @Param("eventTypes") eventTypes: List<EventType?>?
    ): List<JpaEventLog>?

    @Modifying
    @Transactional
    @Query("DELETE FROM JpaEventLog e WHERE e.contractId = :contractId AND e.companyId = :companyId")
    fun deleteByContractIdAndCompanyId(
        @Param("contractId") contractId: Long,
        @Param("companyId") companyId: Long
    ): Int

    fun findByStatusIn(
        statuses: Collection<EventStatus>
    ): List<JpaEventLog>
}
