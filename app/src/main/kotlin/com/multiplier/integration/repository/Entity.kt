package com.multiplier.integration.repository

import jakarta.persistence.*
import java.time.LocalDateTime
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@MappedSuperclass
abstract class BaseEntity(
    @GeneratedValue(strategy = GenerationType.IDENTITY) @Id open var id: Long? = null
)

@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class AuditableBaseEntity(
    id: Long? = null,
    @Column(name = "created_by") @CreatedBy var createdBy: Long? = null,
    @Column(name = "created_on") @CreatedDate var createdOn: LocalDateTime? = null,
    @Column(name = "updated_by") @LastModifiedBy var updatedBy: Long? = null,
    @Column(name = "updated_on") @LastModifiedDate var updatedOn: LocalDateTime? = null,
) : BaseEntity(id)
