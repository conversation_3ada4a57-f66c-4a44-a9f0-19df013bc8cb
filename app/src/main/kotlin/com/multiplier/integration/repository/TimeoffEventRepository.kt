package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaTimeoffEvent
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface TimeoffEventRepository : JpaRepository<JpaTimeoffEvent, String> {
    fun findAllByExternalIdAndIntegrationId(externalId: String, integrationId: Long): List<JpaTimeoffEvent>
    fun findAllByShouldProcess(shouldProcess: Boolean): List<JpaTimeoffEvent>

}