package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaFieldsMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
@JvmDefaultWithCompatibility
interface FieldsMappingRepository : JpaRepository<JpaFieldsMapping, Long> {

    @Query("""
        SELECT DISTINCT m FROM JpaFieldsMapping m
        LEFT JOIN FETCH m.children
        WHERE m.entityId = :entityId 
        AND m.integrationId = :integrationId 
        AND m.parent.id IS NULL
        AND m.isActive = TRUE
    """)
    fun findByEntityIdAndIntegrationIdAndParentIdIsNull(entityId: Long, integrationId: Long): List<JpaFieldsMapping>

    fun findByEntityIdAndIntegrationIdAndOriginFieldAndIsActive(entityId: Long, integrationId: Long, originField: String, isActive: Boolean = true): JpaFieldsMapping?

    @Query("SELECT COUNT(f), COALESCE(SUM(CASE WHEN f.mappedField IS NULL THEN 1 ELSE 0 END), 0) " +
            "FROM JpaFieldsMapping f " +
            "WHERE f.entityId = :entityId AND f.integrationId = :integrationId AND f.isRequired = TRUE AND f.isActive = TRUE")
    fun getFieldMappingCounts(@Param("entityId") entityId: Long, @Param("integrationId") integrationId: Long): List<Array<Any>>

    fun findByIntegrationIdAndIsActive(integrationId: Long, isActive: Boolean = true): List<JpaFieldsMapping>

    fun findByEntityId(entityId: Long): List<JpaFieldsMapping>
}
