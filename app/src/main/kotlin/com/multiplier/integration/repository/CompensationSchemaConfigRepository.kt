package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaCompensationSchemaConfig
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface CompensationSchemaConfigRepository : JpaRepository<JpaCompensationSchemaConfig, Long> {

    fun findByEntityId(entityId: Long): JpaCompensationSchemaConfig?
}