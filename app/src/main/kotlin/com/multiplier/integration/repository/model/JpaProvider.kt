package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.repository.type.ProviderName
import jakarta.persistence.*

@Entity
@Table(
    name = "provider",
    schema = "customer_integration",
)
class JpaProvider(
    id: Long? = null,
    @Column(name = "name") @Enumerated(EnumType.STRING) val name: ProviderName,
) : AuditableBaseEntity(id)
