package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.*

@Entity
@Table(
    name = "legal_entity_mapping",
    schema = "customer_integration"
)
class JpaLegalEntityMapping(
    @Column(name = "entity_id")
    val entityId: Long,
    @Column(name = "integration_id")
    val integrationId: Long,
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: LegalMappingStatus,
    @Column(name = "entity_name")
    val entityName: String?,
    @Column(name = "is_enabled")
    var isEnabled: Boolean = false,
    @Column(name = "company_id")
    var companyId: Long,
    @Column(name = "entity_country")
    var entityCountry: String,
    id: Long? = null,
): AuditableBaseEntity(id)


enum class LegalMappingStatus {
    FULLY_MAPPED,
    UNMAPPED,
    PARTIALLY_MAPPED
}
