package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.types.SyncStatus
import com.multiplier.integration.types.SyncType
import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(
    name = "manual_sync",
    schema = "customer_integration"
)
class JpaManualSync(
    @Column(name = "sync_id")
    var syncId: String,
    @Column(name = "integration_id")
    var integrationId: Long,
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    var status: SyncStatus,
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    var type: SyncType,
    @Column(name = "started_On")
    var startedOn: LocalDateTime,
    @Column(name = "completed_On")
    var completedOn: LocalDateTime?,
    @Column(name = "dismissed_on")
    var dismissedOn: LocalDateTime?,
    id: Long? = null,
    ): AuditableBaseEntity(id)
