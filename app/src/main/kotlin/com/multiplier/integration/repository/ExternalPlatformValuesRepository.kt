package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaExternalPlatformValues
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface ExternalPlatformValuesRepository : JpaRepository<JpaExternalPlatformValues, Long> {
    fun findByIntegrationIdAndIsDeleted(integrationId: Long, isDeleted: Boolean = false): List<JpaExternalPlatformValues>
}