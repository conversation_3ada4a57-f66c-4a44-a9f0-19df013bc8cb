package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.annotations.Type
import org.hibernate.type.SqlTypes

@Entity
@Table(
    name = "field_mapping_configuration",
    schema = "customer_integration"
)
class JpaFieldMappingConfiguration(
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    var type: FieldMappingConfigurationType,
    @Column(name = "key")
    val key: String,
    @Column(name = "value")
    var value: String,
    @Column(name = "is_deleted")
    var isDeleted: Boolean = false,
    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType::class)
    @Column(name = "enum_mappings", columnDefinition = "jsonb")
    var enumMappings: String? = null,
    @Column(name = "platform_id")
    val platformId: Long? = null,
    id: Long? = null,
): AuditableBaseEntity(id)


enum class FieldMappingConfigurationType {
    DEFAULT,
    CALCULATED,
    ENUM
}
