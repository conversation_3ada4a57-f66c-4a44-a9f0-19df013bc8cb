package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaEntityIntegration
import jakarta.transaction.Transactional
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface EntityIntegrationRepository :
    JpaRepository<JpaEntityIntegration, Long> {

    fun findFirstByEntityTypeAndInternalId(entityType: String, internalId: Long): JpaEntityIntegration?

    fun findByExternalIdAndPaid(externalId: String, paid: Boolean): List<JpaEntityIntegration>?

    @Transactional
    @Modifying
    @Query(
        nativeQuery = true,
        value = "UPDATE customer_integration.entities_integration e SET e.processed = true WHERE e.externalId = :externalId")
    fun updateProcessedStatusByExternalId(externalId: String)
}
