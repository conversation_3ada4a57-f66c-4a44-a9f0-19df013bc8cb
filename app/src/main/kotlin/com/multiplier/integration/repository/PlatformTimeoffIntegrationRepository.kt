package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface PlatformTimeoffIntegrationRepository : JpaRepository<JpaPlatformTimeoffIntegration, Long> {
    fun existsByInternalTimeoffId(@Param("internalTimeoffId") internalTimeoffId: Long): Boolean
    fun existsByExternalTimeoffIdAndIntegrationId(@Param("externalTimeoffId") externalTimeoffId: String, @Param("integrationId") integrationId: Long): Boolean

    fun findByInternalTimeoffId(@Param("internalTimeoffId") internalTimeoffId: Long): JpaPlatformTimeoffIntegration?
    fun findByExternalTimeoffId(@Param("externalTimeoffId") externalTimeoffId: String): JpaPlatformTimeoffIntegration?
}
