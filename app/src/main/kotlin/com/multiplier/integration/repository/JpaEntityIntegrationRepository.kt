package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaEntityIntegration
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
interface JpaEntityIntegrationRepository : JpaRepository<JpaEntityIntegration, Long> {
    fun findByExternalId(@Param("externalId") externalId: String): List<JpaEntityIntegration>
}
