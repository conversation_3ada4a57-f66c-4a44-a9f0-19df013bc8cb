package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.*

@Entity
@Table(
    name = "entities_integration",
    schema = "customer_integration",
)
class JpaEntityIntegration(
    id: Long? = null,

    @Column(name = "entity_type") val entityType: String,
    @Column(name = "internal_id") val internalId: Long,
    @Column(name = "external_id") val externalId: String,
    @Column(name = "contract_id") val contractId: Long,
    @Column(name = "integration_id") val integrationId: String?,
    @Column(name = "paid") var paid: <PERSON><PERSON>an,
    @Column(name = "processed") var processed: Boolean,
@ManyToOne(cascade = [CascadeType.MERGE], fetch = FetchType.LAZY)
    @JoinColumn(name = "company_integration_id", nullable = true)
    val companyIntegration: JpaCompanyIntegration? = null,
) : AuditableBaseEntity(id)
