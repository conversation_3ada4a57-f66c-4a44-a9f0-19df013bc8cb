package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaLeaveTypesMapping
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
@JvmDefaultWithCompatibility
interface LeaveTypeMappingRepository : JpaRepository<JpaLeaveTypesMapping, Long> {

    fun findByCompanyIdAndIntegrationId(companyId: Long, integrationId: Long): List<JpaLeaveTypesMapping>

    fun findByIntegrationIdAndExternalTypeId(integrationId: Long, externalTypeId: String): List<JpaLeaveTypesMapping>?
}