package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.BulkUploadModule
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface SFTPAccessRequestRepository : JpaRepository<JpaSFTPAccessRequest, Long> {

    /**
     * Find an SFTP access request by company ID, entity ID, and bulk module.
     *
     * @param companyId The ID of the company
     * @param entityId The ID of the entity
     * @param bulkModule The bulk module type
     * @return The matching SFTP access request, or null if none is found
     */
    fun findByCompanyIdAndEntityIdAndBulkModule(
        companyId: Long,
        entityId: Long,
        bulkModule: BulkUploadModule
    ): JpaSFTPAccessRequest?

    /**
     * Find an SFTP access request by status and main SFTP directory.
     *
     * @param status The status of the SFTP access request
     * @param mainSFTPDirectory The main SFTP directory to search for
     * @return The matching SFTP access request, or null if none is found
     */
    fun findByStatusAndMainSFTPDirectory(
        status: SftpAccessRequestStatus,
        mainSFTPDirectory: String
    ): JpaSFTPAccessRequest?

    /**
     * Find an SFTP access request by the main SFTP directory.
     *
     * @param mainSFTPDirectory The main SFTP directory to search for
     * @return The matching SFTP access request, or null if none is found
     */
    fun findByMainSFTPDirectory(mainSFTPDirectory: String): JpaSFTPAccessRequest?
}
