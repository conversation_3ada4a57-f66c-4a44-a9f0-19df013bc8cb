package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table

@Entity
@Table(
    name = "sent_message",
    schema = "customer_integration"
)
class JpaSentMessage(

    @Column(name = "integration_id")
    val integrationId: String?,
    @Column(name = "sync_id")
    val syncId: String?,
    @Column(name = "data", columnDefinition = "jsonb")
    val data: String?
) : AuditableBaseEntity(null)
