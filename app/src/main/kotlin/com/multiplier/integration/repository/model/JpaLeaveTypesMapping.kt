package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table

@Entity
@Table(
    name = "leave_types_mapping",
    schema = "customer_integration"
)
class JpaLeaveTypesMapping(
    @Column(name = "internal_type_id")
    val internalTypeId: String?,
    @Column(name = "internal_type_name")
    val internalTypeName: String?,
    @Column(name = "external_type_id")
    val externalTypeId: String?,
    @Column(name = "integration_id")
    val integrationId: Long?,
    @Column(name = "company_id")
    var companyId: Long,
    id: Long? = null,
) : AuditableBaseEntity(id)