package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.annotations.Type
import org.hibernate.type.SqlTypes

@Entity
@Table(
    name = "timeoff_events",
    schema = "customer_integration"
)
class JpaTimeoffEvent(
    @Column(name = "employee_id")
    val employeeId: String,
    @Column(name = "timeoff_type")
    val timeoffType: String,
    @Column(name = "external_id")
    val externalId: String,
    @Column(name = "internal_id")
    var internalId: String?,
    @Column(name = "integration_id")
    val integrationId: Long,
    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType::class)
    @Column(name = "timeoff_data", columnDefinition = "jsonb")
    val timeoffData: String,
    @Column(name = "should_process")
    var shouldProcess: Boolean? = false,
    @Column(name = "processed")
    var processed: Boolean? = false,
    id: Long? = null,
): AuditableBaseEntity(id) {
    override fun toString(): String {
        return "JpaTimeoffEvent(timeoffType='$timeoffType', externalId='$externalId', timeoffData=$timeoffData, createdOn=$createdOn, updatedOn=$updatedOn, createdBy=$createdBy, updatedBy=$updatedBy)"
    }
}
