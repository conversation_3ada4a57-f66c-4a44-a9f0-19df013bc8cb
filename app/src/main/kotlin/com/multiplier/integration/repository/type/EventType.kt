package com.multiplier.integration.repository.type

enum class EventType {
    INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
    INCOMING_MEMBER_ADDRESS_UPDATED,
    INCOMING_MEMBER_LEGAL_DATA_UPDATED,
    INCOMING_ONBOARDING_STATUS_UPDATE,
    INCOMING_SALARY_REVIEW_SIGNED,
    INCOMING_SALARY_REVIEW_ACTIVATED,
    INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE,
    INCOMING_PAYROLL_PAYSLIP_UPLOADED,
    INCOMING_PAYROLL_PAYSLIP_PUBLISHED,
    INCOMING_OFFBOARDING_STATUS_UPDATE,
    INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
    INCOMING_PAYABLE_UPDATE,

    SERVICE_INTERNAL_CREATE_COMPENSATION,
    SERVICE_INTERNAL_CREATE_INSURANCE_FACTSHEET,
    SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT,
    SERVICE_INTERNAL_CREATE_CONTRACT
}
