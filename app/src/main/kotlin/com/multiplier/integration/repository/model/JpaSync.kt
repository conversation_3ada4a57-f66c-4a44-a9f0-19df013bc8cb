package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import java.time.LocalDateTime

@Entity
@Table(
    name = "sync",
    schema = "customer_integration"
)
class JpaSync(
    @Column(name = "sync_id")
    var syncId: String,
    @Column(name = "integration_id")
    var integrationId: String?,
    @Column(name = "start_time")
    var startTime: LocalDateTime,
    @Column(name = "end_time")
    var endTime: LocalDateTime?,
    @Column(name = "client_sync_id")
    var clientSyncId: String?,
    @Column(name = "in_progress")
    var inProgress: Boolean?,
    id: Long? = null,
    ): AuditableBaseEntity(id)
