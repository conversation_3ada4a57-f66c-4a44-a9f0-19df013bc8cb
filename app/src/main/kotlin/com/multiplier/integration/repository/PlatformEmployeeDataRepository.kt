package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
@JvmDefaultWithCompatibility
interface PlatformEmployeeDataRepository : JpaRepository<JpaPlatformEmployeeData, Long> {

    fun findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
        integrationId: Long,
        employeeId: String,
    ): List<JpaPlatformEmployeeData>

    fun findByEmployeeId(employeeId: String?): List<JpaPlatformEmployeeData>

    fun findFirstById(id: Long): JpaPlatformEmployeeData?

    @Modifying
    @Query("DELETE FROM JpaPlatformEmployeeData ped WHERE ped.integrationId = :integrationId")
    fun deleteByIntegrationId(@Param("integrationId") integrationId: Long): Int


    @Query("SELECT ped.employeeId FROM JpaPlatformEmployeeData ped WHERE ped.integrationId = :integrationId")
    fun findPlatformEmployeeIdsByIntegrationId(@Param("integrationId") integrationId: Long): List<String>

    @Query("""
        SELECT * 
        FROM customer_integration.platform_employee_data ped 
        WHERE 
        (
            ped.employee_data -> 'profile' ->> 'workEmail' ILIKE :workEmail 
            OR (
                jsonb_typeof(ped.employee_data -> 'contactInfo' -> 'personalEmails') = 'array' AND 
                EXISTS (
                    SELECT 1 
                    FROM jsonb_array_elements_text(ped.employee_data -> 'contactInfo' -> 'personalEmails') AS personalEmail
                    WHERE personalEmail ILIKE :email
                )
            )
        )
        AND ped.integration_id = :integrationId
    """, nativeQuery = true)
    fun findByIntegrationIdAndWorkEmailOrPersonalEmail(
        @Param("email") email: String?,
        @Param("workEmail") workEmail: String?,
        @Param("integrationId") integrationId: Long,
    ): List<JpaPlatformEmployeeData>

}
