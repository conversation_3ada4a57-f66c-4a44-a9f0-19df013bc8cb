package com.multiplier.integration.repository.model

import com.vladmihalcea.hibernate.type.json.JsonType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.Type
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import java.time.LocalDate
import java.time.LocalDateTime

data class EmployeeConfig(
    val employeeId: String,
    val payScheduleName: String,
    val compensationStartDate: LocalDate,
)

data class CompensationSchemaConfig(
    val defaultPayScheduleName: String,
    val employeeConfigs: List<EmployeeConfig>,
)

@Entity
@Table(name = "compensation_schema_config", schema = "customer_integration")
class JpaCompensationSchemaConfig(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    var id: Long? = null,

    @Column(name = "entity_id", nullable = false)
    var entityId: Long,

    @Type(JsonType::class)
    @Column(name = "config", nullable = false, columnDefinition = "jsonb")
    var config: CompensationSchemaConfig,

    @Column(name = "created_on", nullable = false)
    @CreatedDate
    var createdOn: LocalDateTime = LocalDateTime.now(),

    @Column(name = "updated_on", nullable = false)
    @LastModifiedDate
    var updatedOn: LocalDateTime = LocalDateTime.now()
)