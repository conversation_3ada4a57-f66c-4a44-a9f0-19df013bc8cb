package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.BaseEntity
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.annotations.Type
import org.hibernate.envers.Audited
import org.hibernate.type.SqlTypes
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import java.time.LocalDateTime

@Entity
@Table(
    name = "bulk_job_tracker",
    schema = "customer_integration",
)
@EntityListeners(AuditingEntityListener::class)
@Audited
class JpaBulkJobTracker (
    id: Long? = null,

    @Column(name = "job_id")
    val jobId: Long,

    @Enumerated(EnumType.STRING)
    @Column(name = "job_status")
    var jobStatus: BulkJobStatus,

    @Column(name = "company_id")
    val companyId: Long,

    @Column(name = "entity_id")
    val entityId: Long,

    @Column(name = "group_name")
    val groupName: String,

    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType::class)
    @Column(name = "module_names")
    val moduleNames: Set<String>,

    @Column(name = "original_file_uri")
    var originalFileURI: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "original_uri_type")
    val originalURIType: URIType? = null,

    @Column(name = "report_file_uri")
    var reportFileURI: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "report_uri_type")
    var reportURIType: URIType? = null,

    @Column(name = "created_by", updatable = false)
    @CreatedBy
    var createdBy: Long? = null,

    @Column(name = "created_on", updatable = false)
    @CreatedDate
    var createdOn: LocalDateTime? = null,

    @Column(name = "updated_by", updatable = true)
    @LastModifiedBy
    var updatedBy: Long? = null,

    @Column(name = "updated_on", updatable = true)
    @LastModifiedDate
    var updatedOn: LocalDateTime? = null,
): BaseEntity(id) {

    fun copy(modify: JpaBulkJobTracker.() -> Unit): JpaBulkJobTracker {
        return JpaBulkJobTracker(
            id = this.id,
            jobId = this.jobId,
            jobStatus = this.jobStatus,
            companyId = this.companyId,
            entityId = this.entityId,
            groupName = this.groupName,
            moduleNames = this.moduleNames,
            originalFileURI = this.originalFileURI,
            originalURIType = this.originalURIType,
            reportFileURI = this.reportFileURI,
            reportURIType = this.reportURIType,
        ).apply(modify)
    }
}

enum class BulkJobStatus {
    VALIDATION_IN_PROGRESS,
    VALIDATION_FAILED,
    UPSERT_IN_PROGRESS,
    UPSERT_SUCCESSFUL,
}

enum class URIType {
    SFTP,
}
