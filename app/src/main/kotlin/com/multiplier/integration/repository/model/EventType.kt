package com.multiplier.integration.repository.model

enum class EventType {
    RECORD_NEW,
    RECORD_UPDATE,
    RECORD_DELETE,
    SYNC_END,
    SYNC_HEARTBEAT,
    UNKNOWN;

    companion object {
        fun fromString(value: String?): EventType? {
            if ("record.new" == value) {
                return EventType.RECORD_NEW
            }
            if ("record.modified" == value) {
                return EventType.RECORD_UPDATE
            }
            if ("record.deleted" == value) {
                return EventType.RECORD_DELETE
            }
            if ("sync.events.processed" == value) {
                return EventType.SYNC_END
            }
            if ("sync.heartbeat" == value) {
                return EventType.SYNC_HEARTBEAT
            }
            return EventType.UNKNOWN
        }
    }
}