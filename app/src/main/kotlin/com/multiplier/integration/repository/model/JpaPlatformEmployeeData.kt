package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.type.SqlTypes

@Entity
@Table(
    name = "platform_employee_data",
    schema = "customer_integration",
)
class JpaPlatformEmployeeData(
    id: Long? = null,

    @Column(name = "integration_id")
    val integrationId: Long,

    @Column(name = "employee_id")
    val employeeId: String,

    @JdbcTypeCode(SqlTypes.JSON)
    var employeeData: String?,

    @Column(name = "origin")
    var origin: String?,

    @Column(name = "is_deleted")
    var isDeleted: Boolean = false,
) : AuditableBaseEntity(id)