package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.repository.model.JpaReceivedEvent
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Repository
interface ReceivedEventRepository : JpaRepository<JpaReceivedEvent, String> {

    fun findByEventId(eventId: String): JpaReceivedEvent

    fun findBySyncIdAndAndProcessed(syncId: String?, processed: Boolean?): List<JpaReceivedEvent>

    fun findByEventTypeAndConfirmedByUserAndProcessed(
        eventType: EventType?,
        confirmedByUser: Boolean?,
        processed: Boolean?
    ): List<JpaReceivedEvent>

    fun findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
        eventType: EventType?,
        syncDataType: String?,
        confirmedByUser: Boolean?,
        processed: Boolean?
    ): List<JpaReceivedEvent>

    fun findByEventTypeAndProcessed(
        eventType: EventType?,
        processed: Boolean?
    ): List<JpaReceivedEvent>

    fun findByEventTypeAndProcessedAndSyncId(
        eventType: EventType?,
        processed: Boolean?,
        syncId: String?
    ): List<JpaReceivedEvent>

    @Modifying
    @Transactional
    @Query(
        nativeQuery = true,
        value = "UPDATE customer_integration.received_events SET sync_id = :newSyncId WHERE integration_id = :integrationId AND processed = false"
    )
    fun setMatchingSyncToProvidedSyncForUnprocessedEventsForGivenIntegration(integrationId: String, newSyncId: String): Int

    @Modifying
    @Transactional
    @Query(
        value = "DELETE FROM JpaReceivedEvent re WHERE re.integrationId = :integrationId")
    fun deleteByIntegrationId(@Param("integrationId") integrationId: String): Int

    fun findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(integrationId: String, entityCountry: String, isEntityEnabled: Boolean, processed: Boolean): List<JpaReceivedEvent>

    fun findByEventTypeAndSyncId(
        eventType: EventType?,
        syncId: String?
    ): List<JpaReceivedEvent>

    @Query(
        nativeQuery = true,
        value = """
            select distinct entity_country from customer_integration.received_events 
	        where integration_id = :integrationId and entity_country is not null and is_entity_enabled = false
        """
    )
    fun getUnsyncedEntityCountryListByIntegrationId(integrationId: String): List<String>

    fun findByProcessed(
        processed: Boolean?
    ): List<JpaReceivedEvent>

    fun findByEventIdAndAndProcessed(eventId: String?, processed: Boolean?): Optional<JpaReceivedEvent>

    fun findByIntegrationIdAndSyncIdOrderByCreatedOn(integrationId: String, syncId: String, pageable: Pageable): List<JpaReceivedEvent>

    @jakarta.transaction.Transactional
    @Modifying
    @Query(
        nativeQuery = true,
        value = "UPDATE customer_integration.received_events SET processed = :processed WHERE sync_id = :syncId")
    fun updateProcessedStatusByExternalId(syncId: String, processed: Boolean): Int

    fun findBySyncId(syncId: String): List<JpaReceivedEvent>


    @Query("SELECT * FROM customer_integration.received_events WHERE integration_id = :integrationId AND sync_id = :syncId AND data::text LIKE %:searchTerm% ORDER BY created_on", nativeQuery = true)
    fun findByIntegrationIdAndSyncIdAndSearchTermOrderByCreatedOn(integrationId: String, syncId: String, searchTerm: String): List<JpaReceivedEvent>
}
