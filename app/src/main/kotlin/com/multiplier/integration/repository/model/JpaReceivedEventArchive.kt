package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.*
import org.hibernate.annotations.JdbcTypeCode
import org.hibernate.annotations.Type
import org.hibernate.type.SqlTypes
import java.time.LocalDateTime

@Entity
@Table(
    name = "received_events_archive",
    schema = "customer_integration"
)
class JpaReceivedEventArchive(
    @Column(name = "event_id")
    val eventId: String,
    @Column(name = "sync_id")
    val syncId: String?,
    @Column(name = "integration_id")
    val integrationId: String?,
    @Enumerated(EnumType.STRING)
    @Column(name = "event_type")
    var eventType: EventType?,
    @Column(name="sync_data_type")
    val syncDataType: String?,
    @Column(name="errors")
    var errors: String?,
    @Column(name = "identifier_value")
    val identifiervalue: String?,
    @Column(name = "received_time")
    val receivedTime: LocalDateTime?,
    @JdbcTypeCode(SqlTypes.JSON)
    @Type(JsonBinaryType::class)
    @Column(name = "data", columnDefinition = "jsonb")
    val data: String?,
    @Column(name = "confirmed_by_user")
    val confirmedByUser: Boolean?,
    @Column(name = "processed")
    var processed: Boolean?,
    @Column(name = "is_entity_enabled")
    var isEntityEnabled: Boolean = true,
    @Column(name = "entity_id")
    var entityId: Long? = null,
    @Column(name = "entity_country")
    var entityCountry: String? = null,
    id: Long? = null,
    ): AuditableBaseEntity(id) {
    override fun toString(): String {
        return "JpaReceivedEventArchive(eventId='$eventId', syncId=$syncId, integrationId=$integrationId, receivedTime=$receivedTime, data=$data, confirmedByUser=$confirmedByUser, processed=$processed)"
    }
}
