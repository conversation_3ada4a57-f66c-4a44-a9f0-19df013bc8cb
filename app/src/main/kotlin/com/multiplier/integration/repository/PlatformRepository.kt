package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.types.PlatformCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository

@Repository
@JvmDefaultWithCompatibility
interface PlatformRepository : JpaRepository<JpaPlatform, Long> {

    fun findFirstByCategoryAndName(category: PlatformCategory, name: String): JpaPlatform?

    fun findByNameIn(names: Set<String>): List<JpaPlatform>

    @Query("SELECT p FROM JpaPlatform p WHERE p.category = :category AND LOWER(p.name) = LOWER(:name)")
    fun findFirstByCategoryAndNameWithCaseInsensitive(@Param("category") category: PlatformCategory, @Param("name") name: String): JpaPlatform?
}
