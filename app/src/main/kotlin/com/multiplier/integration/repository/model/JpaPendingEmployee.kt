package com.multiplier.integration.repository.model

import com.multiplier.integration.repository.AuditableBaseEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table

@Entity
@Table(
    name = "pending_employee",
    schema = "customer_integration"
)
class JpaPendingEmployee(
    @Column(name = "integration_id")
    val integrationId: String?,
    @Column(name = "sync_id")
    val syncId: String?,
    @Column(name = "identifier_value")
    val identifier: String?,
    @Column(name = "first_name")
    val firstName: String?,
    @Column(name = "last_name")
    val lastName: String?,
    @Column(name = "pending_import") //can be ignored, will be dropped
    val pendingImport: Boolean?,
    @Column(name = "pending_invite") //insert with false
    val pendingInvite: Boolean?,
    @Column(name = "contract_id") //insert with false
    val contractId: Long?,
    id: Long? = null,
    @Column(name = "invite_requested")
    var inviteRequested: Boolean?
): AuditableBaseEntity(id)

