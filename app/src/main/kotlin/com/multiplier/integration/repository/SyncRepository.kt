package com.multiplier.integration.repository

import com.multiplier.integration.repository.model.JpaSync
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.*

@Repository
interface SyncRepository : JpaRepository<JpaSync, String> {
    @Query("SELECT s FROM JpaSync s WHERE s.integrationId = :integrationId")
    fun findByIntegrationId(@Param("integrationId") integrationId: String): List<JpaSync>

    fun findBySyncId(@Param("syncId") syncId: String): Optional<JpaSync>

    @Query("SELECT s FROM JpaSync s WHERE s.integrationId = :integrationId AND s.inProgress = :inProgress")
    fun findByIntegrationIdAndInProgress(@Param("integrationId") integrationId: String,
                                         @Param("inProgress") inProgress: Boolean): List<JpaSync>

    @Query("SELECT s FROM JpaSync s WHERE s.integrationId = :integrationId " +
            " ORDER BY s.startTime DESC")
    fun findSyncOrderByStartTime(
        @Param("integrationId") integrationId: String): List<JpaSync>

    @Modifying
    @Transactional
    @Query(
        value = "DELETE FROM JpaSync s WHERE s.integrationId = :integrationId")
    fun deleteByIntegrationId(@Param("integrationId") integrationId: String): Int

    @Query("SELECT s FROM JpaSync s WHERE s.inProgress = TRUE AND s.startTime < :thresholdTime")
    fun findInProgressSyncsOlderThanThresholdTime(thresholdTime: LocalDateTime): List<JpaSync>

}