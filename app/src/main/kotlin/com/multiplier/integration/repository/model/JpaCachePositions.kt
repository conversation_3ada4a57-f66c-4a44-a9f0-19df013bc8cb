package com.multiplier.integration.repository.model

import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.vladmihalcea.hibernate.type.json.JsonBinaryType
import jakarta.persistence.*
import org.hibernate.annotations.Type
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedDate
import java.time.LocalDateTime

@Embeddable
data class CachePositionsId(
        @Column(name = "company_id")
        val companyId: Long,

        @Column(name = "platform_id")
        val platformId: Long
)

@Entity
@Table(name = "cache_positions", schema = "customer_integration")
class JpaCachePositions(
        @EmbeddedId
        var id: CachePositionsId,

        @Type(JsonBinaryType::class)
        @Column(name = "data", nullable = false, columnDefinition = "jsonb")
        var data: GetPositionDetailResponse,

        @Column(name = "created_on", nullable = false)
        @CreatedDate
        var createdOn: LocalDateTime = LocalDateTime.now(),

        @Column(name = "updated_on", nullable = false)
        @LastModifiedDate
        var updatedOn: LocalDateTime = LocalDateTime.now()

)
