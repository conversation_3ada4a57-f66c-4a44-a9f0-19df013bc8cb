package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.DocumentFoldersService
import com.multiplier.integration.types.DocumentFolder
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsQuery
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class DocumentFoldersDataFetcher(
    private val currentUser: CurrentUser,
    private val documentFoldersService: DocumentFoldersService
) {

    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery(field = DgsConstants.QUERY.GetDocumentFolders)
    fun getDocumentFolders(
        integrationId: Long,
        type: DocumentFolderType
    ): DocumentFolder {
        log.info("[getDocumentFolders] integrationId $integrationId - type ${type.name}")
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return documentFoldersService.getDocumentFolders(currentUserCompanyId, integrationId, type, isOpsUser(currentUser))
    }
}