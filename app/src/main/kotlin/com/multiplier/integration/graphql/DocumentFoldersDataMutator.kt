package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.DocumentFoldersService
import com.multiplier.integration.types.DocumentFolder
import com.multiplier.integration.types.UpsertDocumentFolderInput
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class DocumentFoldersDataMutator(
    private val currentUser: CurrentUser,
    private val documentFoldersService: DocumentFoldersService
) {

    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsMutation(field = DgsConstants.MUTATION.UpsertDocumentFolder)
    fun upsertDocumentFolder(
        @InputArgument("input") input: UpsertDocumentFolderInput,
    ): DocumentFolder {
        log.info("[upsertDocumentFolder] with $input")
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return documentFoldersService.upsertDocumentFolder(currentUserCompanyId, input, isOpsUser(currentUser))
    }
}