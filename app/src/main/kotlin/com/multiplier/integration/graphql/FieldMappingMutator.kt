package com.multiplier.integration.graphql

import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.FieldMappingService
import com.multiplier.integration.types.SaveIntegrationFieldsMappingInput
import com.multiplier.integration.types.TaskResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class FieldMappingMutator(
    private val fieldMappingService: FieldMappingService
) {

    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('fields-mapping.operations.company.gp') || @me.allowed('view.company.company.has-integration')")
    @DgsMutation(field = DgsConstants.MUTATION.SaveIntegrationFieldsMapping)
    fun saveIntegrationFieldsMapping(
        @InputArgument("input") input: SaveIntegrationFieldsMappingInput,
    ): TaskResponse {
        log.info("Save integration fields mapping entity id ${input.entityId} - integration id ${input.integrationId}")
        return fieldMappingService.saveIntegrationFieldsMapping(input)
    }

    @PreAuthorize("@me.allowed('fields-mapping.operations.company.gp') || @me.allowed('view.company.company.has-integration')")
    @DgsMutation(field = DgsConstants.MUTATION.SaveIntegrationEntityMappingStatus)
    fun saveIntegrationEntityMappingStatus(
        @InputArgument("entityMappingId") entityMappingId: Long,
        @InputArgument("enableDataSync") enableDataSync: Boolean
    ): TaskResponse {
        log.info("[saveIntegrationEntityMappingStatus] entityMappingId: $entityMappingId")
        return fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId, enableDataSync)
    }
}
