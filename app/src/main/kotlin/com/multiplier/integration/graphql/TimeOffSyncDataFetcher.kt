package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.types.ExternalLeaveType
import com.multiplier.integration.types.LeaveTypeMappingDefinition
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsQuery
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class TimeOffSyncDataFetcher(
    private val currentUser: CurrentUser,
    private val timeOffSyncService: TimeOffSyncService
) {

    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery
    fun getExternalLeaveTypes(
        integrationId: Long
    ): List<ExternalLeaveType> {
        log.info("[getExternalLeaveTypes] integrationId $integrationId")
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return timeOffSyncService.getExternalLeaveTypes(currentUserCompanyId, integrationId, isOpsUser(currentUser))
    }

    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery
    fun getInternalLeaveTypes(
        companyId: Long
    ): List<ExternalLeaveType> {
        log.info("[getInternalLeaveTypes] companyId $companyId")
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return timeOffSyncService.getInternalLeaveTypes(currentUserCompanyId!!)
    }

    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsQuery
    fun getLeaveTypeMappingDefinition(
        integrationId: Long
    ): List<LeaveTypeMappingDefinition> {
        log.info("[getLeaveTypeMappingDefinition] integrationId $integrationId")
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return timeOffSyncService.getLeaveTypeMappingDefinition(currentUserCompanyId!!, integrationId)
    }
}