package com.multiplier.integration.graphql

import com.multiplier.integration.accounting.domain.FinancialTransactionAmountUpdatedHandlerService
import com.multiplier.integration.accounting.domain.FinancialTransactionHandlerService
import com.multiplier.integration.types.*
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging

@DgsComponent
class AccountingCustomerIntegrationDataMutator(
    private val financialTransactionHandlerService: FinancialTransactionHandlerService,
    private val financialTransactionAmountUpdatedHandlerService: FinancialTransactionAmountUpdatedHandlerService,
) {

    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    @DgsMutation
    fun accountingSyncCompanyPayables(
        @InputArgument accountingSyncCompanyPayablesInput: AccountingSyncCompanyPayablesInput,
    ): List<AccountingSyncCompanyPayablesResponse> {
        val companyPayableIds = accountingSyncCompanyPayablesInput.companyPayableIds
        return companyPayableIds.map {
            logger.info { "Syncing company payable ID: $it" }
            try {
                financialTransactionHandlerService.upsertHandler(it)
                AccountingSyncCompanyPayablesResponse.newBuilder()
                    .companyPayableId(it)
                    .taskResponse(TaskResponse.newBuilder()
                        .success(true)
                        .message("successfully synced company payable id = $it")
                        .build()
                    )
                    .build()
            } catch (e: Exception) {
                logger.error(e) { "Error syncing company payable ID: $it" }

                AccountingSyncCompanyPayablesResponse.newBuilder()
                    .companyPayableId(it)
                    .taskResponse(TaskResponse.newBuilder()
                        .success(false)
                        .message(e.message ?: "Unknown error")
                        .build()
                    )
                    .build()
            }
        }
    }

    @DgsMutation
    fun accountingCreatePaymentForCompanyPayable(
        @InputArgument accountingCreatePaymentForCompanyPayableInput: AccountingCreatePaymentForCompanyPayableInput
    ): AccountingCreatePaymentForCompanyPayableResponse {
        logger.info { "Creating payment for company payable ID: ${accountingCreatePaymentForCompanyPayableInput.companyPayableId}" }
        return try {
            financialTransactionAmountUpdatedHandlerService.amountUpdateHandler(accountingCreatePaymentForCompanyPayableInput.companyPayableId)
            AccountingCreatePaymentForCompanyPayableResponse(
                accountingCreatePaymentForCompanyPayableInput.companyPayableId,
                TaskResponse
                    .newBuilder()
                    .success(true)
                    .message("successfully created payment for company payable id = ${accountingCreatePaymentForCompanyPayableInput.companyPayableId}").build()
            )
        } catch (e: Exception) {
            logger.error(e) { "Error creating payment for company payable ID: ${accountingCreatePaymentForCompanyPayableInput.companyPayableId}." }
            AccountingCreatePaymentForCompanyPayableResponse(
                accountingCreatePaymentForCompanyPayableInput.companyPayableId,
                TaskResponse.newBuilder().success(false).message(e.message ?: "Unknown error").build()
            )
        }

    }
}