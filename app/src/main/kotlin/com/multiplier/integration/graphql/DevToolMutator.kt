package com.multiplier.integration.graphql

import com.multiplier.integration.DgsConstants
import com.multiplier.integration.scheduler.EORTimeOffScheduler
import com.multiplier.integration.service.DevMigrationService
import com.multiplier.integration.types.TaskResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class DevToolMutator(
    private val devMigrationService: DevMigrationService,
    private val eorTimeOffScheduler: EORTimeOffScheduler,
) {
    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('update.operations.company.integration')")
    @DgsMutation(field = DgsConstants.MUTATION.MigrateIntegrationIdToContractIntegrationTable)
    fun migrateIntegrationIdToContractIntegrationTable(
        @InputArgument("batchSize") batchSize: Int,
    ): TaskResponse {
        log.info("[migrateIntegrationIdForContractIntegrationTable] with batch size: $batchSize")
        return try {
            devMigrationService.migrateIntegrationIdForContractIntegrationTable(batchSize)
            TaskResponse.newBuilder()
                .message("Successfully migrate integration id for contract integration table")
                .success(true)
                .build()
        } catch (e: Exception) {
            log.error("migrateIntegrationIdForContractIntegrationTable failed")
            TaskResponse.newBuilder()
                .success(false)
                .message(if (e.message == null) e.toString() else e.message)
                .build()
        }
    }

    @PreAuthorize("@me.allowed('update.operations.company.integration')")
    @DgsMutation(field = DgsConstants.MUTATION.TriggerEORTimeoffSyncManually)
    fun triggerEORTimeoffSyncManually(
        @InputArgument("integrationId") integrationId: Long? = null,
    ): TaskResponse {
        log.info("[triggerEORTimeoffSyncManually] with integrationId: $integrationId")
        return try {
            eorTimeOffScheduler.processEORTimeOffs(integrationId)
            TaskResponse.newBuilder()
                .message("Successfully run timeoff EOR for integrationId: $integrationId")
                .success(true)
                .build()
        } catch (e: Exception) {
            log.error("triggerEORTimeoffSyncManually for integrationId: $integrationId failed: ${e.message}")
            TaskResponse.newBuilder()
                .success(false)
                .message(if (e.message == null) e.toString() else e.message)
                .build()
        }
    }
}