package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.types.SaveLeaveTypesMappingInput
import com.multiplier.integration.types.TaskResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class TimeOffSyncDataMutator(
    private val currentUser: CurrentUser,
    private val timeOffSyncService: TimeOffSyncService
) {

    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('view.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    @DgsMutation
    fun saveLeaveTypesMapping(
        @InputArgument("input") input: SaveLeaveTypesMappingInput,
    ): TaskResponse {
        log.info("[saveLeaveTypesMapping] with $input")
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return timeOffSyncService.saveLeaveTypesMapping(currentUserCompanyId!!, input)
    }
}