package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.TokenGeneratorService
import com.multiplier.integration.types.MergeDevError
import com.multiplier.integration.types.MergeDevPublicTokenErrorResponse
import com.multiplier.integration.types.MergeDevPublicTokenInput
import com.multiplier.integration.types.MergeDevPublicTokenResponse
import com.multiplier.integration.types.MergeDevPublicTokenSuccessResponse
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class TokenDataMutator(
    private val tokenGeneratorService: TokenGeneratorService,
    private val currentUser: CurrentUser,
) {

    private val log = KotlinLogging.logger {}

    @DgsMutation(field = "platformCompanyTokenUpdate")
    @PreAuthorize(
            """
            (@me.allowed('update.operations.platform.account-token') ||
            (@me.allowed('view.company.company.has-integration') &&
            @me.allowed('update.company.platform.account-token') &&
            @me.isCompanyUserForCompanyId(#companyId)))
        """)
    fun createCompanyPlatformIntegration(
            @InputArgument("companyId") companyId: Long,
            @InputArgument("platformId") platformId: Long,
            @InputArgument("token") token: String,
    ): TaskResponse {
        return try {
            tokenGeneratorService.createCompanyPlatformIntegration(companyId, platformId, token, isOpsUser(currentUser))
            TaskResponse.newBuilder()
                    .message("Successfully updated account token")
                    .success(true)
                    .build()
        } catch (e: Exception) {
            log.error("saveAccountToken failed for companyID $companyId")
            TaskResponse.newBuilder()
                    .success(false)
                    .message(if (e.message == null) e.toString() else e.message)
                    .build()
        }
    }


    @DgsMutation(field = DgsConstants.MUTATION.PlatformMergeDevPublicToken)
    fun createCompanyPlatformIntegrationForMergeDev(
       input: MergeDevPublicTokenInput
    ): MergeDevPublicTokenResponse {
        log.info("merge dev createCompanyPlatformIntegrationForMergeDev for companyId: ${input.companyId} and platformId: ${input.platformId}")
        return try {
            tokenGeneratorService.createOrUpdateMergeDevToken(input.companyId, input.platformId, input.publicToken, isOpsUser(currentUser))
            MergeDevPublicTokenSuccessResponse.newBuilder()
                .msg("Successfully updated account token")
                .build()
        } catch (e: Exception) {
            log.error("failed to create account token for companyID ${input.companyId}")
            MergeDevPublicTokenErrorResponse.newBuilder()
                .error(
                    MergeDevError.Builder()
                    .msg(if (e.message == null) e.toString() else e.message)
                    .build()
                )
                .build()
        }
    }
}
