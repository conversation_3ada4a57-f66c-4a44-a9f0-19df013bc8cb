package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.TokenGeneratorService
import com.multiplier.integration.types.KnitAuthResponse
import com.multiplier.integration.types.MergeDevLinkTokenInput
import com.multiplier.integration.types.MergeDevLinkTokenResponse
import com.multiplier.integration.types.TrinetCredentialInputV2
import com.multiplier.integration.types.TrinetCredentialResult
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.DgsQuery
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class TokenDataFetcher(private val tokenGeneratorService: TokenGeneratorService, private val currentUser: CurrentUser) {

    private val log = KotlinLogging.logger {}

    @DgsMutation(field = "providerKnitAuthToken")
    @PreAuthorize(
            "(@me.allowed('generate.company.platform.link-token') || @me.allowed('generate.operations.platform.link-token')) ")
    fun getKnitAuthToken(
            @InputArgument("originOrgId") companyId: String,
            @InputArgument("originOrgName") companyName: String,
            @InputArgument("originUserEmail") userEmail: String,
            @InputArgument("originUserName") userName: String,
            @InputArgument("platformId") platformId: String,
            @InputArgument("clearErrors") clearErrors: Boolean?
    ): KnitAuthResponse {
        val currentUserId = currentUser.context?.id;
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        log.info { "Requesting authToken from Knit for companyId=$companyId, companyName=$companyName, " +
                "platformId=$platformId, userId:$currentUserId, clearErrors: $clearErrors" }
        val effectiveClearErrors = clearErrors ?: true
        return tokenGeneratorService.getAuthTokenFromKnit(companyId, companyName, userEmail, userName, platformId,
            currentUserCompanyId, effectiveClearErrors, isOpsUser(currentUser))
    }

    @PreAuthorize(
        "(@me.allowed('generate.company.platform.link-token') || @me.allowed('generate.operations.platform.link-token')) ")
    @DgsMutation(field = DgsConstants.MUTATION.VerifyTriNetCredential)
    fun verifyTriNetCredential(
        input: TrinetCredentialInputV2,
    ): TrinetCredentialResult {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        log.info("Get TriNet Credential for companyId $currentUserCompanyId")
        return tokenGeneratorService.getTriNetCredential(currentUserCompanyId, input.externalCompanyId, input.companyUserName, input.companyUserPassword, isOpsUser(currentUser))
    }

    @DgsQuery(field = DgsConstants.QUERY.PlatformMergeDevLinkTokenV2)
    fun getMergeLinkToken(
       input: MergeDevLinkTokenInput
    ): MergeDevLinkTokenResponse {
        log.info { "Requesting authToken from mergeDev for companyId=${input.companyId}, platformId=${input.platformId}"}
        return tokenGeneratorService.getLinkTokenFromMergeDev(input.companyId, input.companyName, input.companyUserEmail, input.platformId)
    }
}
