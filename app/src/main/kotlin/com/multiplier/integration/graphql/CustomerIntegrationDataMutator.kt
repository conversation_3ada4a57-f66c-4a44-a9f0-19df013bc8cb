package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.FieldMappingConfigurationService
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.types.CustomerIntegration
import com.multiplier.integration.types.FetchEmployeesResult
import com.multiplier.integration.types.FieldMappingConfiguration
import com.multiplier.integration.types.FieldMappingConfigurationInput
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.types.SyncEORManuallyResult
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.utils.isOpsUser
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class CustomerIntegrationDataMutator(
    private val customerIntegrationService: CustomerIntegrationService,
    private val currentUser: CurrentUser,
    private val syncService: SyncService,
    private val fieldMappingConfigurationService: FieldMappingConfigurationService
) {

    private val log = KotlinLogging.logger {}

    @DgsMutation(field = "customerIntegrationDisconnect")
    @PreAuthorize("@me.allowed('disconnect.operations.company.integration') || @me.allowed('view.company.company.has-integration')")
    fun disconnectCompanyIntegration(
        @InputArgument("id") integrationId: Long,
    ): CustomerIntegration {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        val currentUserExperience = currentUser.context?.experience
        val currentUserId = currentUser.context?.id

        return customerIntegrationService.disconnectCompanyIntegration(
            integrationId, currentUserCompanyId, currentUserExperience, currentUserId)
    }

    @DgsMutation(field = "changeSyncState")
    @PreAuthorize("@me.allowed('change.operations.company.sync-state') || @me.allowed('view.company.company.has-integration')")
    fun changeSyncState(
        @InputArgument("integrationId") integrationId: Long,
        @InputArgument("syncType") syncType : SyncType,
        @InputArgument("on") on : Boolean
    ): TaskResponse {
        //TODO - add timeoff sync to SyncType, add necessary logic
        log.info("Received request to change sync state. Integration ID: {}, Sync Type: {}, State: {}", integrationId, syncType, on)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return customerIntegrationService.changeSyncState(integrationId, syncType, on, currentUserCompanyId, isOpsUser(currentUser))
    }

    @DgsMutation(field = "importEmployees")
    @PreAuthorize("@me.allowed('import.operations.company.employees') || @me.allowed('view.company.company.has-integration')")
    fun importEmployees(
        @InputArgument("syncId") syncId: String,
        @InputArgument("employees") employees: List<String>,
    ): TaskResponse {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        syncService.manuallyImportEmployees(syncId, currentUserCompanyId, isOpsUser(currentUser))
        return TaskResponse(true, "Successfully synced employees")
    }

    @DgsMutation(field = "sendImportedMemberInvites")
    @PreAuthorize("@me.allowed('send.operations.company.member-invitations') || @me.allowed('view.company.company.has-integration')")
    fun sendImportedMemberInvites(
        @InputArgument("syncId") syncId: String,
        @InputArgument("employees") employees: List<String>,
    ): TaskResponse {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        syncService.sendInvites(syncId, currentUserCompanyId, isOpsUser(currentUser))
        return TaskResponse(true, "Successfully sent invites")
    }

    @DgsMutation(field = "fetchLatestEmployeesFromPlatform")
    @PreAuthorize("@me.allowed('view.operations.company.external') || @me.allowed('view.company.company.has-integration')")
    fun fetchLatestEmployeesFromPlatform(
        @InputArgument("integrationId") integrationId: Long
    ): FetchEmployeesResult {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return syncService.getEmployeesToSync(integrationId, currentUserCompanyId, isOpsUser(currentUser))
    }

    @DgsMutation(field = "startIncomingSync")
    @PreAuthorize("@me.allowed('manual-sync.operations.company.gp') || @me.allowed('view.company.company.has-integration')")
    fun startIncomingSync(
        @InputArgument("integrationId") integrationId: Long
    ): TaskResponse {
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        syncService.startManualSync(integrationId, currentUserCompanyId, PlatformCategory.HRIS, isOpsUser(currentUser))
        return TaskResponse(true, "Successfully started sync")

    }

    @DgsMutation(field = "syncEORManually")
    @PreAuthorize("@me.allowed('manual-sync.operations.company.eor') || @me.allowed('view.company.company.has-integration')")
    fun syncEORManually(
        @InputArgument("integrationId") integrationId: Long
    ): SyncEORManuallyResult {
        log.info("Sync EOR manually for integrationId: {}", integrationId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return customerIntegrationService.syncEORManually(integrationId, currentUserCompanyId, isOpsUser(currentUser))
    }

    @DgsMutation(field = "dismissSyncResult")
    @PreAuthorize("@me.allowed('manual-sync.operations.company.eor') || @me.allowed('view.company.company.has-integration')")
    fun dismissSyncResult(
        @InputArgument("syncId") syncId: String
    ): TaskResponse {
        log.info("Dismiss sync result for syncId: {}", syncId)
        val currentUserCompanyId = currentUser.context?.scopes?.companyId
        return customerIntegrationService.dismissSyncResult(syncId, currentUserCompanyId, isOpsUser(currentUser))
    }

    @DgsMutation(field = DgsConstants.MUTATION.ClearGPSyncFailedEventsV2)
    @PreAuthorize("@me.allowed('clear-events.operations.company.gp') || @me.allowed('view.company.company.has-integration')")
    fun clearGPSyncFailedEvents(
        @InputArgument("companyId") companyId: Long,
        @InputArgument("platformID") platformID: Long,
    ): TaskResponse {
        log.info("Clear GP sync failed events for companyId: {}", companyId)
        return customerIntegrationService.clearGPSyncFailedEvents(companyId, platformID)
    }

    @PreAuthorize("@me.allowed('fields-mapping.operations.company.gp') || @me.allowed('view.company.company.has-integration')")
    @DgsMutation(field = DgsConstants.MUTATION.UpsertFieldMappingConfiguration)
    fun upsertFieldMappingConfiguration(
        @InputArgument("input") input: List<FieldMappingConfigurationInput>,
        @InputArgument("platformId") platformId: Long?
    ): List<FieldMappingConfiguration> {
        log.info("[upsertFieldMappingConfiguration] request: $input")
        return fieldMappingConfigurationService.upsertFieldMappingConfiguration(input, platformId)
    }

    @PreAuthorize("@me.allowed('fields-mapping.operations.company.gp') || @me.allowed('view.company.company.has-integration')")
    @DgsMutation(field = DgsConstants.MUTATION.DeleteFieldMappingConfiguration)
    fun deleteFieldMappingConfiguration(
        @InputArgument("keys") keys: List<String>,
        @InputArgument("platformId") platformId: Long?
        ): TaskResponse {
        log.info("[deleteFieldMappingConfiguration] request: $keys, platformId: $platformId")
        return fieldMappingConfigurationService.deleteFieldMappingConfigs(keys, platformId)
    }

    @DgsMutation(field = DgsConstants.MUTATION.TriggerIntegrationSync)
    fun triggerIntegrationSync(
        syncId: String?,
        eventId: String?,
        syncType: SyncType
    ): TaskResponse {
        log.info("[triggerIntegrationSync] request: $syncType, eventId: $eventId, syncId: $syncId")
        return customerIntegrationService.triggerIntegrationSync(syncId, eventId, syncType, isOpsUser(currentUser))
    }
}
