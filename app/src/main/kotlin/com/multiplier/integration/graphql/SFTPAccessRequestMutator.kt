package com.multiplier.integration.graphql

import com.multiplier.integration.DgsConstants
import com.multiplier.integration.service.AuthorizationService
import com.multiplier.integration.service.SFTPAccessRequestService
import com.multiplier.integration.types.SftpAccessRequest
import com.multiplier.integration.types.SftpAccessRequestInput
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import mu.KotlinLogging

@DgsComponent
class SFTPAccessRequestMutator(
    private val sftpAccessRequestService: SFTPAccessRequestService,
    private val authorizationService: AuthorizationService,
) {
    private val log = KotlinLogging.logger {}

    @DgsMutation(field = DgsConstants.MUTATION.CreateSftpAccessRequest)
    fun createSftpAccessRequest(
        @InputArgument("input") input: SftpAccessRequestInput,
        dfe: DgsDataFetchingEnvironment,
    ): SftpAccessRequest {
        log.info("[createSftpAccessRequest] with $input")
        authorizationService.checkCompanyAccessOrThrow(dfe, input.companyId)
        authorizationService.checkEntityAccessOrThrow(dfe, input.entityId)
        return sftpAccessRequestService.createSftpAccessRequest(input)
    }
}
