package com.multiplier.integration

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.ComponentScan
import org.springframework.scheduling.annotation.EnableAsync

@EnableFeignClients(basePackages = ["com.multiplier"])
@ConfigurationPropertiesScan
@SpringBootApplication
@ComponentScan("com.multiplier")
@EnableAsync
class CustomerIntegrationApplication

fun main(args: Array<String>) {
    runApplication<CustomerIntegrationApplication>(*args)
}
