package com.multiplier.integration.listeners

import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventType
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventType.CONTRACT_WORK_EMAIL_CHANGED
import com.multiplier.integration.core.model.ContractEvent
import com.multiplier.integration.core.model.ContractWorkEmailChangedEvent
import com.multiplier.integration.listeners.validators.EventListenerValidator
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.utils.ListenerUtil
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import java.util.*

@Component
class ContractEventListener(
    private val eventPublisher: ApplicationEventPublisher,
    private val eventLogService: EventLogService,
    private val eventListenerValidator: EventListenerValidator,
    ) {

    private val log = KotlinLogging.logger {}

    private val messageHandlers = mapOf<ContractEventType, (String) -> ContractEvent>(
      CONTRACT_WORK_EMAIL_CHANGED to { eventLogId -> ContractWorkEmailChangedEvent(eventLogId = eventLogId) }
    )

    @KafkaListener(
        topics = ["topic.internal.v1.contract"], containerFactory = "contractEventConsumerFactory")
    fun onExternalEventFromContractTopic(
        @Payload message: ContractEventMessage,
        @Headers headers: MessageHeaders
    ) {
        val eventId = UUID.randomUUID().toString()
        withLoggingContext(
            "eventId" to eventId,
            "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
            "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}") {

            log.info("Received contract event. Type: ${message.eventType}, Event ID: $eventId")

            if (!messageHandlers.containsKey(message.eventType)) {
                log.warn("Unsupported event type {}", message.eventType)
                return
            }

            val payload = message.toString()
            val type = EventType.valueOf("INCOMING_${message.eventType.name}")

            if (!eventListenerValidator.validateContractRelatedEvent(payload, type))
                return

            log.info("Creating event log for event ID: $eventId")
            eventLogService.createEventLog(
                type = EventType.valueOf("INCOMING_${message.eventType.name}"),
                eventId = eventId,
                status = EventStatus.TO_BE_PROCESSED,
                payload = message.toString(),
                contractId = ListenerUtil.parseContractId(payload, type)
            )

            log.info("Finished processing contract event. Type: ${message.eventType}, Event ID: $eventId")
        }
    }
}
