package com.multiplier.integration.listeners

import com.multiplier.contract.kafka.contract.ContractDocumentEventMessageOuterClass.ContractDocumentEventMessage
import com.multiplier.contract.kafka.contract.ContractDocumentEventMessageOuterClass.ContractDocumentEventType
import com.multiplier.contract.kafka.contract.ContractDocumentEventMessageOuterClass.ContractDocumentEventType.CONTRACT_DOCUMENT_STATUS_UPDATE
import com.multiplier.integration.core.model.ContractDocumentUpdateEvent
import com.multiplier.integration.core.model.ContractEvent
import com.multiplier.integration.listeners.validators.EventListenerValidator
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.utils.ListenerUtil
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import java.util.*

@Component
class ContractDocumentEventListener(
    private val eventPublisher: ApplicationEventPublisher,
    private val eventLogService: EventLogService,
    private val eventListenerValidator: EventListenerValidator,
) {

    private val log = KotlinLogging.logger {}

    private val messageHandlers = mapOf<ContractDocumentEventType, (String) -> ContractEvent>(
        CONTRACT_DOCUMENT_STATUS_UPDATE to { eventLogId -> ContractDocumentUpdateEvent(eventLogId = eventLogId) }
    )

    @KafkaListener(
        topics = ["topic.internal.v1.contract-document"], containerFactory = "contractDocumentEventConsumerFactory")
    fun onExternalEventFromContractTopic(
        @Payload message: ContractDocumentEventMessage,
        @Headers headers: MessageHeaders
    ) {
        val eventId = UUID.randomUUID().toString()
        withLoggingContext(
            "eventId" to eventId,
            "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
            "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}") {

            log.info("Received contract document event. Type: ${message.eventType}, Event ID: $eventId")

            if (!messageHandlers.containsKey(message.eventType)) {
                log.warn("Unsupported event type {}", message.eventType)
                return
            }

            val payload = message.toString()
            val type = EventType.valueOf("INCOMING_${message.eventType.name}")
            if (!eventListenerValidator.validateContractRelatedEvent(payload, type))
                return

            log.info("Creating event log for event ID: $eventId")
            eventLogService.createEventLog(
                type = type,
                eventId = eventId,
                status = EventStatus.TO_BE_PROCESSED,
                payload = payload,
                contractId = ListenerUtil.parseContractId(payload, type)
            )

            log.info("Finished processing contract document event. Type: ${message.eventType}, Event ID: $eventId")
        }
    }
}
