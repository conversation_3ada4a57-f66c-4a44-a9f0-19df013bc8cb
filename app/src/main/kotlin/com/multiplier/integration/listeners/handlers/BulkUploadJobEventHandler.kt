package com.multiplier.integration.listeners.handlers

import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobEventType
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobMessage
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobStatus
import com.multiplier.integration.adapter.api.CUSTOMER_INTEGRATION_SERVICE_GROUP
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.service.BulkJobTrackerService
import com.multiplier.integration.service.IntegrationOrchestrator
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkUploadJobEventHandler(
    private val integrationOrchestrator: IntegrationOrchestrator,
    private val bulkJobTrackerService: BulkJobTrackerService,
) {

    fun handle(message: BulkUploadJobMessage) {
        if (!isMessageValid(message)) {
            return
        }

        when (message.payload.status) {
            BulkUploadJobStatus.VALIDATION_FAILED -> handleBulkDataValidationFailure(message)

            BulkUploadJobStatus.VALIDATION_SUCCESS -> handleBulkDataValidationSuccess(message)

            BulkUploadJobStatus.DATA_CREATION_SUCCESS -> handleBulkDataCreationSuccess(message)

            else -> log.info { "Unsupported bulk upload job status: ${message.payload.status}" }
        }
    }

    private fun handleBulkDataValidationFailure(message: BulkUploadJobMessage) {
        handleBulkJobEvent(
            message = message,
            expectedJobTrackerStatus = BulkJobStatus.VALIDATION_IN_PROGRESS,
            logMessage = "handle bulk data validation failure",
            action = integrationOrchestrator::handleBulkUploadJobValidationFailure,
        )
    }

    private fun handleBulkDataValidationSuccess(message: BulkUploadJobMessage) {
        handleBulkJobEvent(
            message = message,
            expectedJobTrackerStatus = BulkJobStatus.VALIDATION_IN_PROGRESS,
            logMessage = "handle bulk data validation success",
            action = integrationOrchestrator::handleBulkUploadJobValidationSuccess,
        )
    }

    private fun handleBulkDataCreationSuccess(message: BulkUploadJobMessage) {
        handleBulkJobEvent(
            message = message,
            expectedJobTrackerStatus = BulkJobStatus.UPSERT_IN_PROGRESS,
            logMessage = "handle bulk data creation success",
            action = integrationOrchestrator::handleBulkUploadJobDataCreationSuccess,
        )
    }

    private fun handleBulkJobEvent(
        message: BulkUploadJobMessage,
        expectedJobTrackerStatus: BulkJobStatus,
        logMessage: String,
        action: (Long, URIType) -> Unit,
    ) {
        val bulkJobTracker = runCatching {
            bulkJobTrackerService.findByJobIdOrThrow(message.payload.id)
        }.getOrElse { e ->
            log.error(e) { "Failed to find bulk job tracker for jobId: ${message.payload.id}" }
            return
        }
        if (bulkJobTracker.jobStatus != expectedJobTrackerStatus) {
            log.info { "Skipped $logMessage since jobStatus is ${bulkJobTracker.jobStatus}" }
            return
        }
        bulkJobTracker.originalURIType?.let { uriType ->
            action(bulkJobTracker.id!!, uriType)
        } ?: log.info { "Skipped message since originalURIType is null" }
    }

    private fun isMessageValid(message: BulkUploadJobMessage): Boolean {
        // TODO: remove this log after some time (few successful iteration)
        log.info("Message group: ${message.payload.group}, event type: ${message.eventType}")
        if (message.payload.group != CUSTOMER_INTEGRATION_SERVICE_GROUP) {
            return false
        }

        log.info { "Received bulkUploadJobTopic message: $message" }
        return message.eventType == BulkUploadJobEventType.BULK_UPLOAD_JOB_UPDATED
    }
}