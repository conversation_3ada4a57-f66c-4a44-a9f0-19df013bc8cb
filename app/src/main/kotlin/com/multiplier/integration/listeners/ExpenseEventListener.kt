package com.multiplier.integration.listeners

import com.multiplier.expense.schema.ExpenseEventMessageOuterClass
import com.multiplier.integration.service.ExpenseProcessorService
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component

@Component
class ExpenseEventListener(
    private val expenseProcessorService: ExpenseProcessorService
) {
    private val log = KotlinLogging.logger {}

    @KafkaListener(
        topics = ["topic.internal.v1.expense"],
        containerFactory = "expenseEventConsumerFactory")
    fun onExpenseStatusChange(
        @Payload message: ExpenseEventMessageOuterClass.ExpenseEventMessage,
        @Headers headers: MessageHeaders
    ) {
        withLoggingContext(
            "method" to "onExpenseStatusChange",
            "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
            "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}"
        ) {
            val expenseId = message.event.expenseId
            val expenseStatus = message.eventType
            log.info("Received expense status update event. Expense ID: $expenseId - Expense Status: $expenseStatus")

            // filter paid expense events to process
            if (expenseStatus != ExpenseEventMessageOuterClass.ExpenseEventType.EXPENSE_PAID) {
                log.warn("Not process expense with status $expenseStatus")
                return
            }
            try {
                expenseProcessorService.processPaidExpenseById(expenseId)
                log.info("Finished processing expense event. Type: ${message.eventType}")
            }
            catch (e: Exception) {
                log.warn("Error processing paid expense with expense id $expenseId", e)
                return
            }
        }

    }
}