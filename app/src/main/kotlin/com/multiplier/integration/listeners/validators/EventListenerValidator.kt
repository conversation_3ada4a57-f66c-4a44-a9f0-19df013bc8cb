package com.multiplier.integration.listeners.validators

import com.multiplier.contract.schema.onboarding.Onboarding
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.utils.ListenerUtil
import com.multiplier.integration.utils.parseContractOnboardingEventMessageFromString
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class EventListenerValidator(
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
) {
    private val log = KotlinLogging.logger {}

    fun validateContractRelatedEvent(payload: String, type: EventType): Bo<PERSON>an {
        try {
            val contractId = ListenerUtil.parseContractId(payload, type)
            val contractIntegration = platformContractIntegrationRepository
                .findFirstByContractIdOrderByCreatedOnDesc(contractId)
            if (contractIntegration == null) {
                log.info("ContractIntegration not found for contractId=$contractId, skipping event")
                return false
            }
            return isOutgoingSyncEnabled(contractId)
        }
        catch (e: Exception) {
            log.error(e.message, e)
            return false
        }
    }

    fun validateOnboardingEvent(payload: String, type: EventType): Boolean {
        try {
            val contractOnboardingEventMessage = parseContractOnboardingEventMessageFromString(payload)

            val contractOnboardingEvent = contractOnboardingEventMessage.event
            val contractId = contractOnboardingEvent.contractId

            val experience = contractOnboardingEvent.experience
            if (!listOf("company").contains(experience)) {
                log.info("Ignoring event for contract ID: $contractId because experience: $experience is not supported.")
                return false
            }
            // Won't accept onboarding event with status not in
            if (!listOf(
                    Onboarding.ContractOnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                    Onboarding.ContractOnboardingStatus.ACTIVE,
                    Onboarding.ContractOnboardingStatus.ORDER_FORM_SENT_TO_MULTIPLIER).contains(contractOnboardingEvent.onboardingStatus)) {
                log.info("Status ${contractOnboardingEvent.onboardingStatus} for contract ID: $contractId is not yet supported, skipping event")
                return false
            }
            return isOutgoingSyncEnabled(contractId)
        }
        catch (e: Exception) {
            log.error(e.message, e)
            return false
        }
    }

    private fun isOutgoingSyncEnabled(contractId: Long): Boolean {
        val companyId = contractServiceAdapter.findContractByContractId(contractId).companyId
        val companyIntegrations = companyIntegrationRepository.findByCompanyIdIn(setOf(companyId))
        if (companyIntegrations.isEmpty()) {
            log.info("No integrations found for companyId=$companyId")
            return false
        } else {
            val activeIntegration = companyIntegrations.firstOrNull { it.outgoingSyncEnabled }
            if (activeIntegration == null) {
                log.info("No active outgoing integration found for company id = $companyId")
                return false
            }
            return true
        }
    }
}