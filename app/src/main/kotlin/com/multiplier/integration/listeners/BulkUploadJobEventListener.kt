package com.multiplier.integration.listeners

import com.multiplier.integration.listeners.handlers.BulkUploadJobEventHandler
import com.multiplier.messaging.api.consumer.registerConsumers
import com.multiplier.messaging.bulkuploadservice.BulkUploadServiceTopics
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class BulkUploadJobEventListener(
    private val bulkJobJobEventHandler: BulkUploadJobEventHandler,
) {

    @Bean
    fun kafkaConsumer() = registerConsumers {
        onMessage(BulkUploadServiceTopics.bulkUploadJobTopic) { message ->
            bulkJobJobEventHandler.handle(message)
        }
    }
}