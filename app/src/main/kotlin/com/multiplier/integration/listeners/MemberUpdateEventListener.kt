package com.multiplier.integration.listeners

import com.multiplier.integration.config.getStringValue
import com.multiplier.integration.core.model.*
import com.multiplier.integration.listeners.validators.EventListenerValidator
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.utils.ListenerUtil
import com.multiplier.member.kafka.MemberEventMessageOuterClass.MemberEventMessage
import com.multiplier.member.kafka.MemberEventMessageOuterClass.MemberEventType
import com.multiplier.member.kafka.MemberEventMessageOuterClass.MemberEventType.*
//import com.multiplier.member.schema.MemberUpdateMessage.MemberUpdateEventType
//import com.multiplier.member.schema.MemberUpdateMessage
//import com.multiplier.member.schema.MemberUpdateMessage.MemberUpdateEventType.*
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class MemberUpdateEventListener(
    private val eventPublisher: ApplicationEventPublisher,
    private val eventLogService: EventLogService,
    private val eventListenerValidator: EventListenerValidator,
) {

    private val log = KotlinLogging.logger {}

    private val messageHandlers =
        mapOf<MemberEventType, (String) -> MemberEvent>(
            MEMBER_BASIC_DETAILS_UPDATED to { eventLogId -> MemberBasicDetailUpdateEvent(eventLogId = eventLogId) },
            MEMBER_ADDRESS_UPDATED to { eventLogId -> MemberBasicDetailUpdateEvent(eventLogId = eventLogId) },
            MEMBER_LEGAL_DATA_UPDATED to { eventLogId -> MemberBasicDetailUpdateEvent(eventLogId = eventLogId) }
        )

    @KafkaListener(
        topics = ["topic.external.v1.member"], containerFactory = "memberUpdateConsumerFactory")
    fun onExternalEventFromMemberTopic(
        @Payload message: MemberEventMessage,
        @Headers headers: MessageHeaders
    ) {
        val eventId = UUID.randomUUID().toString()
        withLoggingContext(
            "eventId" to "${headers.getStringValue(eventId)}",
            "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
            "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}") {

            log.info("Received member update event. Type: ${message.eventType}, Event ID: $eventId")

            if (!messageHandlers.containsKey(message.eventType)) {
                log.warn("Unsupported message type {}", message.eventType)
                return
            }

            val payload = message.toString()
            val type = EventType.valueOf("INCOMING_${message.eventType.name}")

            if (!eventListenerValidator.validateContractRelatedEvent(payload, type))
                return

            log.info("Creating event log for event ID: $eventId")
            eventLogService.createEventLog(
                type = type,
                eventId = eventId,
                status = EventStatus.TO_BE_PROCESSED,
                payload = payload,
                contractId = ListenerUtil.parseContractId(payload, type)
            )

            log.info("Finished processing member update event. Type: ${message.eventType}, Event ID: $eventId")
        }
    }
}
