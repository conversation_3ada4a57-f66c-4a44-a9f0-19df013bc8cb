package com.multiplier.integration.listeners

import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewEventType
import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage
import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewEventType.SALARY_REVIEW_ACTIVATED
import com.multiplier.integration.core.model.ContractCompensationUpdateEvent
import com.multiplier.integration.core.model.ContractEvent
import com.multiplier.integration.listeners.validators.EventListenerValidator
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.utils.ListenerUtil
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class SalaryReviewEventListener(
    private val eventPublisher: ApplicationEventPublisher,
    private val eventLogService: EventLogService,
    private val eventListenerValidator: EventListenerValidator,
) {

    private val log = KotlinLogging.logger {}

    private val messageHandlers =
        mapOf<
                SalaryReviewEventType,
                    (String) -> ContractEvent>(
            SALARY_REVIEW_ACTIVATED to { eventLogId -> ContractCompensationUpdateEvent(eventLogId = eventLogId) }
        )

    @KafkaListener(
        topics = ["topic.internal.v1.performance"],
        containerFactory = "salaryReviewEventConsumerFactory")
    fun onExternalEventFromSalaryReviewTopic(
        @Payload message: SalaryReviewUpdateEventMessage,
        @Headers headers: MessageHeaders
    ) {
        val eventId = UUID.randomUUID().toString()
        withLoggingContext(
            "eventId" to eventId,
            "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
            "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}") {

            log.info("Received salary review event. Type: ${message.type}, Event ID: $eventId")

            if (!messageHandlers.containsKey(message.type)) {
                log.warn("Unsupported event type {}", message.type)
                return
            }

            val payload = message.toString()
            val type = EventType.valueOf("INCOMING_${message.type.name}")

            if (!eventListenerValidator.validateContractRelatedEvent(payload, type))
                return

            log.info("Creating event log for event ID: $eventId")
            eventLogService.createEventLog(
                type = type,
                eventId = eventId,
                status = EventStatus.TO_BE_PROCESSED,
                payload = payload,
                contractId = ListenerUtil.parseContractId(payload, type)
            )

            log.info("Finished processing salary review event. Type: ${message.type}, Event ID: $eventId")
        }
    }
}
