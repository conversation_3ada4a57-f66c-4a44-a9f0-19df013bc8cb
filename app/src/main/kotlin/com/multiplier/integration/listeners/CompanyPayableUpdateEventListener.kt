package com.multiplier.integration.listeners

import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.payable.kafka.schema.CompanyPayableEvent
import com.multiplier.payable.kafka.schema.CompanyPayableStatus
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import java.util.*

@Component
class CompanyPayableUpdateEventListener(
    private val eventLogService: EventLogService,
    private val featureFlagService: FeatureFlagService,
) {
    private companion object {
        private val logger = KotlinLogging.logger { }
    }

    @KafkaListener(topics = ["topic.internal.v1.payable"], containerFactory = "companyPayableConsumerFactory")
    fun onCompanyPayableUpdateEvent(
        @Payload message: CompanyPayableEvent,
        @Headers headers: MessageHeaders,
    ) {
        if (!isAccountingIntegrationEnabled(message.companyId)) {
            logger.info { "Accounting integration is disabled for company ${message.companyId}" }
            return
        }

        try {
            val eventId = UUID.randomUUID().toString()

            withLoggingContext(
                "eventId" to eventId,
                "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
                "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}",
            ) {
                logger.info {
                    "Received company payable event. Type: ${message.eventType}, Event ID: ${message.eventId}, Company Payable ID: ${message.companyPayableId}, Company Payable Status: ${message.status}"
                }

                if (message.status != CompanyPayableStatus.AUTHORIZED && message.status != CompanyPayableStatus.PAID) return

                val payload = message.toString()

                logger.info("Creating event log for event ID: $eventId")
                eventLogService.createPayableEventLog(
                    type = EventType.INCOMING_PAYABLE_UPDATE,
                    eventId = eventId,
                    status = EventStatus.TO_BE_PROCESSED,
                    payload = payload,
                    retries = 1,
                )

                logger.info("Finished creating payable event. Type: ${message.eventType}, Event ID: $eventId")
            }
        } catch (e: Exception) {
            logger.error(e) { "Failed to create company payable event. Event ID: ${message.eventId}" }
        }
    }

    private fun isAccountingIntegrationEnabled(companyId: Long): Boolean =
        featureFlagService.isOn(FeatureFlag.ACCOUNTING_INTEGRATION_ENABLED, mapOf("company" to companyId))
}
