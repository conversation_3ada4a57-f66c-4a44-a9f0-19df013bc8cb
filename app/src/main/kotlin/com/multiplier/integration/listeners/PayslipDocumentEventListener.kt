package com.multiplier.integration.listeners

import com.multiplier.integration.core.model.PayrollEvent
import com.multiplier.integration.core.model.PayrollPayslipDocumentPublishedEvent
import com.multiplier.integration.core.model.PayrollPayslipDocumentUploadedEvent
import com.multiplier.integration.listeners.validators.EventListenerValidator
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.utils.ListenerUtil
import com.multiplier.schema.kafka.proto.PayrollDocumentEventMessage
import com.multiplier.schema.kafka.proto.PayrollDocumentEventType
import com.multiplier.schema.kafka.proto.PayrollDocumentEventType.PAYROLL_PAYSLIP_PUBLISHED
import com.multiplier.schema.kafka.proto.PayrollDocumentEventType.PAYROLL_PAYSLIP_UPLOADED
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import java.util.*

@Component
class PayslipDocumentEventListener(
    private val eventPublisher: ApplicationEventPublisher,
    private val eventLogService: EventLogService,
    private val eventListenerValidator: EventListenerValidator,
    ) {

    private val log = KotlinLogging.logger {}

    private val messageHandlers = mapOf<PayrollDocumentEventType, (String) -> PayrollEvent>(
        PAYROLL_PAYSLIP_UPLOADED to { eventLogId -> PayrollPayslipDocumentUploadedEvent(eventLogId = eventLogId) },
        PAYROLL_PAYSLIP_PUBLISHED to { eventLogId -> PayrollPayslipDocumentPublishedEvent(eventLogId = eventLogId) }
    )

    @KafkaListener(
        topics = ["topic.internal.v1.payroll-document"], containerFactory = "payrollDocumentEventConsumerFactory")
    fun onExternalEventFromContractTopic(
        @Payload message: PayrollDocumentEventMessage,
        @Headers headers: MessageHeaders
    ) {
        val eventId = UUID.randomUUID().toString()
        withLoggingContext(
            "eventId" to eventId,
            "key" to "${headers[KafkaHeaders.RECEIVED_KEY]}",
            "topic" to "${headers[KafkaHeaders.RECEIVED_TOPIC]}") {

            log.info("Received payslip document event. Type: ${message.eventType}, Event ID: $eventId")

            if (!messageHandlers.containsKey(message.eventType)) {
                log.warn("Unsupported event type {}", message.eventType)
                return
            }

            val payload = message.toString()
            val type = EventType.valueOf("INCOMING_${message.eventType.name}")
            if (!eventListenerValidator.validateContractRelatedEvent(payload, type))
                return

            log.info("Creating event log for event ID: $eventId")
            eventLogService.createEventLog(
                type = type,
                eventId = eventId,
                status = EventStatus.TO_BE_PROCESSED,
                payload = payload,
                contractId = ListenerUtil.parseContractId(payload, type)
            )

            log.info("Finished processing payslip document event. Type: ${message.eventType}, Event ID: $eventId")
        }
    }
}
