package com.multiplier.integration.config

import com.multiplier.common.transport.http.HttpUserContextFilter
import com.multiplier.integration.rest.RestEndpoint
import com.multiplier.integration.config.security.WebhookApiKeyFilter
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport

@Configuration
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true)
@Import(SecurityProblemSupport::class)
class SecurityConfig(private val apiKeyConfig: ApiKeyConfig) {
    @Bean
    fun filterChain(
        http: HttpSecurity,
        httpUserContextFilter: HttpUserContextFilter,
    ): SecurityFilterChain = http.authorizeHttpRequests {
        it.requestMatchers("/graphql").authenticated()
        it.requestMatchers("/actuator/health").permitAll()
        // SFTP WebHook is handled authentication by WebhookApiKeyFilter
        it.requestMatchers(RestEndpoint.WEBHOOK_SFTP_V1 + "/**").permitAll()
        it.requestMatchers("/actuator/**").hasRole("MULTIPLIER_ADMIN")
        it.requestMatchers("/health").permitAll()
        it.requestMatchers("/sync/**").permitAll()

    }.csrf {
        it.disable()
    }
    .addFilterBefore(WebhookApiKeyFilter(apiKeyConfig.apiKey, apiKeyConfig.headerName), UsernamePasswordAuthenticationFilter::class.java)
    .addFilterBefore(httpUserContextFilter, UsernamePasswordAuthenticationFilter::class.java)
    .build()
}
