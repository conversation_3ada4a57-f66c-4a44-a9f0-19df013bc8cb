package com.multiplier.integration.config

import com.multiplier.common.transport.user.CurrentUser
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.domain.AuditorAware
import org.springframework.data.jpa.repository.config.EnableJpaAuditing
import org.springframework.stereotype.Component
import java.util.*

interface PlatformUser {
    val id: Long
    val userId: Long
}

@Configuration
class CurrentUserWiring {
    @Bean
    fun platformUser(currentUser: CurrentUser?): PlatformUser = object : PlatformUser {
        override val id: Long
            get() = currentUser?.context?.scopes?.operationsUserId ?: currentUser?.context?.scopes?.companyUserId ?: -1

        override val userId: Long
            get() = currentUser?.context?.id ?: -1
    }
}

@Component
class AuditorAwareImpl(
    private val currentUser: PlatformUser,
) : AuditorAware<Long> {
    override fun getCurrentAuditor(): Optional<Long> =
        Optional.ofNullable(currentUser).map { it.userId }
}

@Configuration @EnableJpaAuditing(auditorAwareRef = "auditorAware") class AuditConfig {

    @Bean
    fun auditorAware(auditorAwareImpl: AuditorAwareImpl): AuditorAware<Long> = auditorAwareImpl
}
