package com.multiplier.integration.config

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.multiplier.integration.service.exception.KnitIntegrationException
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.resources.*
import io.ktor.client.request.*
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class HttpClientConfig {

    @Value("\${platform.knit.api-url}") private lateinit var knitApiUrl: String
    @Value("\${platform.knit.api-key}") private lateinit var knitApiKey: String
    @Value("\${platform.merge-dev.api-key}") private lateinit var mergeDevApiKey: String
    @Value("\${platform.merge-dev.api-url}") private lateinit var mergeDevApiUrl: String

    private val log = KotlinLogging.logger {}

    companion object {
        const val DEFAULT_REQUEST_TIMEOUT: Long = 120_000
        private const val DEFAULT_CONNECT_TIMEOUT: Long = 120_000
        private const val DEFAULT_SOCKET_TIMEOUT: Long = 120_000
        const val FE_APPROPRIATE_REQUEST_TIMEOUT: Long = 45_000
    }

    @Bean("knitHttpClient")
    fun knitHttpClient(): HttpClient {
        log.info { "Creating Knit HTTP Client with ${knitApiUrl}, checking if knitApiKey is NULL, knitApiKey.isNullOrEmpty()=${knitApiKey.isNullOrEmpty()}" }
        if (knitApiKey.isBlank()) throw KnitIntegrationException("Failed to fetch Knit API key from SSM")
        return HttpClient(OkHttp) {
            install(Resources)
            defaultRequest {
                url(knitApiUrl)
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
                headers {
                    append(HttpHeaders.Authorization, "Bearer $knitApiKey")
                }
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(HttpTimeout) {
                requestTimeoutMillis = FE_APPROPRIATE_REQUEST_TIMEOUT
                connectTimeoutMillis = DEFAULT_CONNECT_TIMEOUT
                socketTimeoutMillis = DEFAULT_SOCKET_TIMEOUT
            }
        }
    }

    @Bean("mergeDevHttpClient")
    fun mergeDevHttpClient(): HttpClient =
        HttpClient(OkHttp) {
            install(Resources)
            defaultRequest {
                url(mergeDevApiUrl)
                contentType(ContentType.Application.Json)
                accept(ContentType.Application.Json)
                headers {
                    append(HttpHeaders.Authorization, "Bearer $mergeDevApiKey")
                }
            }
            install(ContentNegotiation) {
                jackson {
                    registerModule(JavaTimeModule())
                    findAndRegisterModules()
                    setConfig(deserializationConfig.with(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS))
                    setConfig(serializationConfig.with(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS))
                    enable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_USING_DEFAULT_VALUE)
                    disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                }
            }
            install(HttpTimeout) {
                requestTimeoutMillis = 60_000 // 1 minute
                connectTimeoutMillis = 60_000 // 1 minute
                socketTimeoutMillis = 60_000 // 1 minute
            }
        }
}
