package com.multiplier.integration.config.security

import com.multiplier.integration.rest.RestEndpoint
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import mu.KotlinLogging
import org.springframework.web.filter.OncePerRequestFilter

private val log = KotlinLogging.logger {}

/**
 * Filter for API key authentication for webhook endpoints.
 * This filter checks for a valid API key in the request header for the /api/v1/webhook/sftp endpoints.
 *
 * @property apiKey The expected API key value
 * @property headerName The name of the header containing the API key (default: "x-api-key")
 */
open class WebhookApiKeyFilter(
    private val apiKey: String,
    private val headerName: String = "x-api-key"
) : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        // Only apply to SFTP webhook path
        if (!request.requestURI.startsWith(RestEndpoint.WEBHOOK_SFTP_V1)) {
            filterChain.doFilter(request, response)
            return
        }

        val requestApiKey = request.getHeader(headerName)

        if (apiKey == requestApiKey) {
            // If API key is valid, allow calling endpoints
            runCatching { filterChain.doFilter(request, response) }
                .getOrElse { e ->
                    log.error(e) { "Error processing webhook request" }
                    response.status = HttpServletResponse.SC_INTERNAL_SERVER_ERROR
                }
        } else {
            // If API key is invalid, return 401 Unauthorized
            response.status = HttpServletResponse.SC_UNAUTHORIZED
        }
    }
}
