 package com.multiplier.integration.config

 import com.multiplier.integration.Constants
 import net.javacrumbs.shedlock.core.LockProvider
 import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
 import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
 import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
 import org.springframework.context.annotation.Bean
 import org.springframework.context.annotation.Configuration
 import org.springframework.jdbc.core.JdbcTemplate
 import org.springframework.scheduling.annotation.EnableScheduling
 import javax.sql.DataSource

 @ConditionalOnProperty(
     value = ["scheduler.shedlock.enabled"],
     matchIfMissing = true,
     havingValue = "true"
 )
 @Configuration
 @EnableScheduling
 @EnableSchedulerLock(
     defaultLockAtMostFor = Constants.ShedlockTimeConfiguration.DEFAULT_LOCK_AT_MOST_FOR,
     defaultLockAtLeastFor = Constants.ShedlockTimeConfiguration.DEFAULT_LOCK_AT_LEAST_FOR,
 ) // lock for at most for 40 minutes by default; we can override at individual task
 class ShedlockConfiguration {
     @Bean
     fun lockProvider(dataSource: DataSource): LockProvider = JdbcTemplateLockProvider(
         JdbcTemplateLockProvider.Configuration.builder()
             .withJdbcTemplate(JdbcTemplate(dataSource))
             .withTableName("customer_integration.shedlock")
             .build(),
     )
 }
