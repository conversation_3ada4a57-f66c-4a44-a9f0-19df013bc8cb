package com.multiplier.integration.config

import com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufDeserializer
import com.google.protobuf.MessageLite
import com.google.protobuf.Parser
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobMessage
import com.multiplier.contract.kafka.compensation.SalaryReviewDocumentMessage.SalaryReviewDocumentEventMessage
import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage
import com.multiplier.contract.kafka.contract.ContractDocumentEventMessageOuterClass.ContractDocumentEventMessage
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.contract.kafka.onboarding.ContractOnboardingEventMessageOuterClass.ContractOnboardingEventMessage
import com.multiplier.contract.offboarding.schema.ContractOffboardingEventMessageOuterClass
import com.multiplier.core.kafka.proto.benefit.BenefitDocumentUpdateMessage.BenefitDocumentUpdateEventMessage
import com.multiplier.expense.schema.ExpenseEventMessageOuterClass
import com.multiplier.member.kafka.MemberEventMessageOuterClass.MemberEventMessage
import com.multiplier.payable.kafka.schema.CompanyPayableEvent
import com.multiplier.schema.kafka.proto.PayrollDocumentEventMessage
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.StringDeserializer
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.annotation.EnableKafka
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory
import org.springframework.messaging.MessageHeaders

const val EVENT_ID_KEY: String = "eventId"

@EnableKafka
@Configuration
class KafkaConfig {

    @Value("\${platform.kafka.group-id}") private lateinit var kafkaGroupID: String

    @Value("\${platform.kafka.bootstrap-servers}")
    private lateinit var kafkaBootstrapServers: String

    @Value("\${platform.kafka.auto-startup}") private val autoStartup: Boolean = false

    @Bean
    fun contractEventConsumerFactory():
        ConcurrentKafkaListenerContainerFactory<String, ContractEventMessage> =
        getDefaultConsumerFactory(ContractEventMessage.parser())

    @Bean
    fun contractOnboardingEventConsumerFactory():
        ConcurrentKafkaListenerContainerFactory<String, ContractOnboardingEventMessage> =
        getDefaultConsumerFactory(ContractOnboardingEventMessage.parser())

    @Bean
    fun memberUpdateConsumerFactory():
        ConcurrentKafkaListenerContainerFactory<String, MemberEventMessage> =
        getDefaultConsumerFactory(MemberEventMessage.parser())

    @Bean
    fun salaryReviewEventConsumerFactory():
        ConcurrentKafkaListenerContainerFactory<String, SalaryReviewUpdateEventMessage> =
        getDefaultConsumerFactory(SalaryReviewUpdateEventMessage.parser())

    @Bean
    fun contractDocumentEventConsumerFactory():
            ConcurrentKafkaListenerContainerFactory<String, ContractDocumentEventMessage> =
        getDefaultConsumerFactory(ContractDocumentEventMessage.parser())

    @Bean
    fun salaryReviewDocumentEventConsumerFactory():
            ConcurrentKafkaListenerContainerFactory<String, SalaryReviewDocumentEventMessage> =
        getDefaultConsumerFactory(SalaryReviewDocumentEventMessage.parser())

    @Bean
    fun payrollDocumentEventConsumerFactory():
            ConcurrentKafkaListenerContainerFactory<String, PayrollDocumentEventMessage> =
        getDefaultConsumerFactory(PayrollDocumentEventMessage.parser())

    @Bean
    fun benefitDocumentEventConsumerFactory():
            ConcurrentKafkaListenerContainerFactory<String, BenefitDocumentUpdateEventMessage> =
        getDefaultConsumerFactory(BenefitDocumentUpdateEventMessage.parser())

    @Bean
    fun contractOffboardingEventConsumerFactory():
            ConcurrentKafkaListenerContainerFactory<String, ContractOffboardingEventMessageOuterClass.ContractOffboardingEventMessage> =
        getDefaultConsumerFactory(ContractOffboardingEventMessageOuterClass.ContractOffboardingEventMessage.parser())


    @Bean
    fun expenseEventConsumerFactory():
            ConcurrentKafkaListenerContainerFactory<String, ExpenseEventMessageOuterClass.ExpenseEventMessage> =
        getDefaultConsumerFactory(ExpenseEventMessageOuterClass.ExpenseEventMessage.parser())

    @Bean
    fun companyPayableConsumerFactory(): ConcurrentKafkaListenerContainerFactory<String, CompanyPayableEvent> =
        getDefaultConsumerFactory(CompanyPayableEvent.parser())

    @Bean
    fun bulkUploadConsumerFactory(): ConcurrentKafkaListenerContainerFactory<String, BulkUploadJobMessage> =
        getDefaultConsumerFactory(BulkUploadJobMessage.parser())


    private fun <T : MessageLite> getDefaultConsumerFactory(
        valueDeserializer: Parser<T>
    ): ConcurrentKafkaListenerContainerFactory<String, T> =
        ConcurrentKafkaListenerContainerFactory<String, T>().apply {
            this.setConcurrency(1)
            this.setAutoStartup(autoStartup)
            this.consumerFactory =
                DefaultKafkaConsumerFactory(
                    mapOf(
                        ConsumerConfig.GROUP_ID_CONFIG to kafkaGroupID,
                        ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafkaBootstrapServers,
                    ),
                    StringDeserializer(),
                    KafkaProtobufDeserializer(valueDeserializer),
                )
        }
}

/**
 * Attempts to retrieve and convert a kafka header specified by the given key as a string. Returns
 * `null` if a header with given key doesn't exist.
 *
 * This method is useful for retrieving custom kafka header. Built-in kafka header such as
 * `RECEIVE_TOPIC` has already had its value as a string.
 */
fun MessageHeaders.getStringValue(key: String): String? {
    return when (val value = this[key]) {
        is String -> value
        is ByteArray -> String(value)
        else -> value?.toString()
    }
}
