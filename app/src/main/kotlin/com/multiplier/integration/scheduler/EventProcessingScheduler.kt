package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants.ScheduledJobName.RETRY_FAILED_EVENTS
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.EVENT_PROCESSING_LOCK_AT_LEAST
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.EVENT_PROCESSING_LOCK_AT_MOST
import com.multiplier.integration.accounting.domain.FinancialTransactionAmountUpdatedHandlerService
import com.multiplier.integration.accounting.domain.FinancialTransactionHandlerService
import com.multiplier.integration.adapter.api.BenefitServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.core.model.*
import com.multiplier.integration.handlers.company.PayableEventHandler
import com.multiplier.integration.handlers.contract.*
import com.multiplier.integration.handlers.member.MemberDetailUpdateHandler
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.EventProcessingScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class EventProcessingScheduler(
    private val eventLogService: EventLogService,
    private val employeeService: EmployeeService,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val benefitServiceAdapter: BenefitServiceAdapter,
    private val docgenServiceAdapter: DocgenServiceAdapter,
    private val notificationsService: NotificationsService,
    private val eventLogRepository: EventLogRepository,
    private val financialTransactionHandlerService: FinancialTransactionHandlerService,
    private val financialTransactionAmountUpdatedHandlerService: FinancialTransactionAmountUpdatedHandlerService,
) {
    private val logger = LoggerFactory.getLogger(EventProcessingScheduler::class.java)

    // Instantiate handlers
    private val contractOnboardingStatusUpdateHandler = ContractOnboardingStatusUpdateEventHandler(
        contractServiceAdapter,
        memberServiceAdapter,
        employeeService,
        eventLogService,
        notificationsService,
        eventLogRepository
    )
    private val memberDetailUpdateHandler = MemberDetailUpdateHandler(
        employeeService,
        contractServiceAdapter,
        memberServiceAdapter,
        eventLogService,
        notificationsService,
        eventLogRepository
    )
    private val contractDocumentUpdateHandler = ContractDocumentUpdateEventHandler(
        contractServiceAdapter,
        docgenServiceAdapter,
        employeeService,
        eventLogService
    )
    private val payrollDocumentEventHandler =
        PayrollDocumentEventHandler(contractServiceAdapter, employeeService, eventLogService, docgenServiceAdapter)
    private val salaryReviewDocumentEventHandler =
        SalaryReviewDocumentEventHandler(contractServiceAdapter, employeeService, eventLogService, docgenServiceAdapter)
    private val insuranceOnboardingKitUpdateEventHandler = InsuranceOnboardingKitUpdateEventHandler(
        benefitServiceAdapter,
        contractServiceAdapter,
        docgenServiceAdapter,
        employeeService,
        eventLogService
    )
    private val insuranceFactsheetKitUpdateEventHandler = InsuranceFactsheetKitUpdateEventHandler(
        benefitServiceAdapter,
        contractServiceAdapter,
        docgenServiceAdapter,
        employeeService,
        eventLogService
    )
    private val compensationCreationEventHandler =
        CompensationCreationEventHandler(contractServiceAdapter, employeeService, eventLogService)
    private val contractOffboardingStatusUpdateHandler =
        ContractOffboardingStatusUpdateEventHandler(contractServiceAdapter, employeeService, eventLogService)
    private val contractCreationEventHandler = ContractCreationEventHandler(
        contractServiceAdapter,
        memberServiceAdapter,
        employeeService,
        eventLogService,
        notificationsService,
        eventLogRepository
    )
    private val payableEventHandler = PayableEventHandler(
        financialTransactionHandlerService,
        financialTransactionAmountUpdatedHandlerService,
        eventLogService
    )

    @Scheduled(cron = "0 */5 * * * *")
    @SchedulerLock(
        name = RETRY_FAILED_EVENTS,
        lockAtLeastFor = EVENT_PROCESSING_LOCK_AT_LEAST,
        lockAtMostFor = EVENT_PROCESSING_LOCK_AT_MOST
    )
    fun processEvents() {
        logger.info("Starting the processEvents refactored scheduler.")
        val toBeProcessed: List<JpaEventLog> = eventLogService.fetchToBeProcessedEvents()
        for (event in toBeProcessed) {
            val eventLogId = event.eventId
            logger.info("Processing event with ID: $eventLogId of type: ${event.eventType}")

            // Determine which handler to call based on the event type
            when (event.eventType) {
                EventType.INCOMING_ONBOARDING_STATUS_UPDATE -> {
                    contractOnboardingStatusUpdateHandler.handleContractOnboardingStatusUpdateEvent(
                        ContractOnboardingStatusUpdateEvent(eventLogId)
                    )
                }

                EventType.INCOMING_OFFBOARDING_STATUS_UPDATE -> {
                    contractOffboardingStatusUpdateHandler.handleContractOffboardingStatusUpdateEvent(
                        ContractOffboardingStatusUpdateEvent(eventLogId)
                    )
                }
                // Member details update
                EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
                EventType.INCOMING_MEMBER_ADDRESS_UPDATED,
                EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED,
                EventType.INCOMING_MEMBER_LEGAL_DATA_UPDATED -> {
                    memberDetailUpdateHandler.handleBasicDetailsUpdate(MemberBasicDetailUpdateEvent(eventLogId))
                }

                // Compensation
                EventType.INCOMING_SALARY_REVIEW_ACTIVATED -> {
                    compensationCreationEventHandler.handleCompensationUpdationEvent(
                        ContractCompensationUpdateEvent(
                            eventLogId
                        )
                    )
                }

                EventType.SERVICE_INTERNAL_CREATE_COMPENSATION -> {
                    compensationCreationEventHandler.handleCompensationCreationEvent(eventLogId)
                }

                EventType.INCOMING_CONTRACT_DOCUMENT_STATUS_UPDATE -> {
                    contractDocumentUpdateHandler.handleContractDocumentUpdateEvent(
                        ContractDocumentUpdateEvent(
                            eventLogId
                        )
                    )
                }

                EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED -> {
                    payrollDocumentEventHandler.handleUploadPayslipEvent(PayrollPayslipDocumentUploadedEvent(eventLogId))
                }

                EventType.INCOMING_PAYROLL_PAYSLIP_PUBLISHED -> {
                    payrollDocumentEventHandler.handlePublishPayslipEvent(
                        PayrollPayslipDocumentPublishedEvent(
                            eventLogId
                        )
                    )
                }

                EventType.INCOMING_SALARY_REVIEW_SIGNED -> {
                    salaryReviewDocumentEventHandler.handleSalaryReviewDocumentUpdateEvent(
                        ContractSalaryDocumentUpdateEvent(eventLogId)
                    )
                }

                // Insurance Documents
                EventType.SERVICE_INTERNAL_CREATE_INSURANCE_FACTSHEET -> {
                    insuranceFactsheetKitUpdateEventHandler.handleInsuranceFactsheetUpdateEvent(eventLogId)
                }

                EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT -> {
                    insuranceOnboardingKitUpdateEventHandler.handleInsuranceOnboardingKitUpdateEvent(eventLogId)
                }

                EventType.SERVICE_INTERNAL_CREATE_CONTRACT -> {
                    contractCreationEventHandler.handleContractCreationEventHandler(eventLogId)
                }

                EventType.INCOMING_PAYABLE_UPDATE -> {
                    payableEventHandler.handlePayableEvent(eventLogId)
                }
            }
        }
        logger.info("Finished processing events in EventProcessingScheduler")
    }
}
