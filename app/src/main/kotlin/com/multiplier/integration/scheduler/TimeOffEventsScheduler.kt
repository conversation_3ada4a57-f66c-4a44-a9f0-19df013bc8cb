package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants
import com.multiplier.integration.service.TimeoffService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.timeOffEventsScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class TimeOffEventsScheduler (
    private val timeOffService : TimeoffService
) {
    private val logger = LoggerFactory.getLogger(TimeOffEventsScheduler::class.java)

    @Scheduled(fixedDelay = 2000)
    @SchedulerLock(
        name = Constants.ScheduledJobName.TIMEOFF_EVENT_SCHEDULER,
        lockAtLeastFor = Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING,
    )
    fun processPendingTimeoffEvents() {
        timeOffService.processPendingTimeoffEvents()
    }
}