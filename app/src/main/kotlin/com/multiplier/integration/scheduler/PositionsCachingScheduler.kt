package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants.ScheduledJobName.POSITIONS_CACHING_SCHEDULER
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING
import com.multiplier.integration.platforms.model.Platforms
import com.multiplier.integration.repository.*
import com.multiplier.integration.service.PositionsService
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["positions.scheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class PositionsCachingScheduler(
    private val positionsService: PositionsService,
    private val platformRepository: PlatformRepository
) {
    private val log = KotlinLogging.logger {}

    @Scheduled(cron = "\${positions.scheduler.cron}")
    @SchedulerLock(
        name = POSITIONS_CACHING_SCHEDULER,
        lockAtLeastFor = LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = LOCK_AT_MOST_FOR_STRING
    )
    fun cachePositions() {
        log.info("Starting PositionsCachingScheduler scheduler.")

        val platformId = platformRepository.findByNameIn(setOf(Platforms.WORKDAY.platformName)).firstOrNull()?.id
        if(platformId == null) {
            log.error { "PlatformID for Workday not found" }
            return
        }

        try {
            runBlocking { positionsService.cachePositions(platformId) }
        } catch (e: Exception) {
            log.error(e) { "PositionsCachingScheduler failed to cache positions, error: ${e.message}" }
        }

        log.info("Finished PositionsCachingScheduler scheduler.")
    }

}
