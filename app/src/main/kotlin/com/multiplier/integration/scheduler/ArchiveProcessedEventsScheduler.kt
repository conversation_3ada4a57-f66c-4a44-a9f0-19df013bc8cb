package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants.ScheduledJobName.ARCHIVE_PROCESSED_EVENTS_SCHEDULER
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING
import com.multiplier.integration.repository.EventLogArchiveRepository
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaEventLogArchive
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.model.JpaReceivedEventArchive
import com.multiplier.integration.repository.type.EventStatus
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.archiveProcessedEventsScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class ArchiveProcessedEventsScheduler(
    private val eventLogArchiveRepository: EventLogArchiveRepository,
    private val eventLogRepository: EventLogRepository,
    private val receivedEventsArchiveRepository: ReceivedEventsArchiveRepository,
    private val receivedEventsRepository: ReceivedEventRepository,
) {
    private val log = KotlinLogging.logger {}

    @Scheduled(cron = "0 0 2 * * *")  // Run once every day at 2:00 AM
    @SchedulerLock(
        name = ARCHIVE_PROCESSED_EVENTS_SCHEDULER,
        lockAtLeastFor = LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = LOCK_AT_MOST_FOR_STRING
    )
    fun archiveProcessedEvents() {
        log.info("Starting ArchiveProcessedEventsScheduler scheduler.")

        // Process outgoing events
        processOutgoingEvents()

        // Process incoming events
        processIncomingEvents()

        log.info("Finished ArchiveProcessedEventsScheduler scheduler.")
    }

    private fun processOutgoingEvents() {
        log.info("Processing outgoing event logs...")

        val toBeArchivedOutgoing = eventLogRepository.findByStatusIn(
            listOf(EventStatus.FAILED, EventStatus.IGNORED, EventStatus.INVALID, EventStatus.SUCCESS)
        )
        val eventMap = convertEventLogToArchive(toBeArchivedOutgoing).associateBy { it.eventId }

        var successCount = 0
        var errorCount = 0

        toBeArchivedOutgoing.forEach { event ->
            try {
                // Archive the event one by one
                val archiveEvent = eventMap[event.eventId]!!
                eventLogArchiveRepository.save(archiveEvent)

                // Delete the processed event
                eventLogRepository.delete(event)
                successCount++
            } catch (e: Exception) {
                log.error("Error archiving event with ID: ${event.eventId}, Error: ${e.message}", e)
                errorCount++
            }
        }

        log.info("Outgoing events processed. Success: $successCount, Errors: $errorCount")
    }

    private fun processIncomingEvents() {
        log.info("Processing incoming received events...")

        val toBeArchivedIncoming = receivedEventsRepository.findByProcessed(true)
        val eventMap = convertReceivedEventToArchive(toBeArchivedIncoming).associateBy { it.eventId }

        var successCount = 0
        var errorCount = 0

        toBeArchivedIncoming.forEach { event ->
            try {
                // Archive the event one by one
                val archiveEvent = eventMap[event.eventId]!!
                receivedEventsArchiveRepository.save(archiveEvent)

                // Delete the processed event
                receivedEventsRepository.delete(event)
                successCount++
            } catch (e: Exception) {
                log.error("Error archiving incoming event with ID: ${event.eventId}, Error: ${e.message}", e)
                errorCount++
            }
        }

        log.info("Incoming events processed. Success: $successCount, Errors: $errorCount")
    }

    fun convertEventLogToArchive(eventLogs: List<JpaEventLog>): List<JpaEventLogArchive> {
        return eventLogs.map { eventLog ->
            JpaEventLogArchive(
                eventId = eventLog.eventId,
                eventType = eventLog.eventType,
                eventPayload = eventLog.eventPayload,
                status = eventLog.status,
                retriesDone = eventLog.retriesDone,
                retriesLeft = eventLog.retriesLeft,
                errorMessage = eventLog.errorMessage,
                lastAttempt = eventLog.lastAttempt,
                nextAttempt = eventLog.nextAttempt,
                contractId = eventLog.contractId,
                syncId = eventLog.syncId,
                companyId = eventLog.companyId
            )
        }
    }

    fun convertReceivedEventToArchive(receivedEvents: List<JpaReceivedEvent>): List<JpaReceivedEventArchive> {
        return receivedEvents.map { event ->
            JpaReceivedEventArchive(
                eventId = event.eventId,
                syncId = event.syncId,
                integrationId = event.integrationId,
                eventType = event.eventType,
                syncDataType = event.syncDataType,
                errors = event.errors,
                identifiervalue = event.identifiervalue,
                receivedTime = event.receivedTime,
                data = event.data,
                confirmedByUser = event.confirmedByUser,
                processed = event.processed,
                isEntityEnabled = event.isEntityEnabled,
                entityId = event.entityId,
                entityCountry = event.entityCountry
            )
        }
    }
}
