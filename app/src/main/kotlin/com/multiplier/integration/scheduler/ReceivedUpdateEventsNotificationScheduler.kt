package com.multiplier.integration.scheduler

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.google.common.annotations.VisibleForTesting
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.model.AdditionalPay
import com.multiplier.integration.adapter.model.PerformanceReviewRequest
import com.multiplier.integration.adapter.model.knit.BankAccountDetails
import com.multiplier.integration.adapter.model.knit.CompensationType
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.adapter.model.knit.EmployeeLocation
import com.multiplier.integration.adapter.model.knit.Frequency
import com.multiplier.integration.adapter.model.knit.Locations
import com.multiplier.integration.handlers.contract.ContractOffboardingInitialisationEventHandler
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.sync.model.EventData
import com.multiplier.integration.sync.model.RoutingType
import com.multiplier.integration.utils.toEmployeeCompensationData
import com.multiplier.integration.utils.toGrpcTimestamp
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.integration.utils.toMemberStatus
import com.multiplier.member.schema.Address
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member.MaritalStatus
import com.multiplier.member.schema.Member.MemberStatus
import com.multiplier.member.schema.UpdateMemberFieldsRequest
import com.multiplier.member.schema.UpsertMemberAddressDetailRequest
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDate

// todo FND-2288 - centralise
val objectMapperForIncomingUpdates: ObjectMapper = ObjectMapper().registerKotlinModule()
    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    .enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)

@ConditionalOnProperty(
    value = ["scheduler.receivedUpdateEventsNotificationScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class ReceivedUpdateEventsNotificationScheduler(
    private val jpaReceivedEventRepository: ReceivedEventRepository,

    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val notificationsService: NotificationsService,
    private val customerIntegrationService: CustomerIntegrationService,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    private val employeeService: EmployeeService,
    private val contractOffboardingInitialisationEventHandler: ContractOffboardingInitialisationEventHandler,
    private val syncService: SyncService,
    private val featureFlagService: FeatureFlagService
) {

    private val logger = LoggerFactory.getLogger(ReceivedUpdateEventsNotificationScheduler::class.java)

    @Scheduled(fixedDelay = 5000)
    @SchedulerLock(
        name = Constants.ScheduledJobName.PROCESS_RECEIVED_UPDATE_EVENTS,
        lockAtLeastFor = Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING,
    )
    fun fetchAndProcessUpdateEvents() {
        logger.info("Starting scheduler to fetch update events from Knit and process")
        //TODO: change confirmedByUser to true and do proper checking when connecting with FE flow
        val employeeUpdateEvents =
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                syncDataType = Constants.SyncDataType.EMPLOYEE,
                eventType = EventType.RECORD_UPDATE,
                confirmedByUser = true, processed = false
            )

        if (employeeUpdateEvents.isEmpty()) {
            logger.info("No pending update events to process at this time.")
        }
        for (event in employeeUpdateEvents) {
            try {
                logger.info("Start processing event for eventId: ${event.eventId}")
                processEvent(event)
            } catch (e: IntegrationDownstreamException) {
                logger.warn("Error processing event for eventId: ${event.eventId}", e)
            } catch (e: Exception) {
                logger.error("Error processing event for eventId: ${event.eventId}", e)
            } finally {
                event.processed = true
                jpaReceivedEventRepository.save(event)
                logger.info("Finalizing processing event for eventId: ${event.eventId}")
            }
        }

        syncService.processExpenseUpdateEvents()
        logger.info("Finished processing update events.")
    }

    private fun processEvent(event: JpaReceivedEvent) {
        logger.info("Processing event with eventId=${event.eventId} and integrationId=${event.integrationId}")
        val receivedEvent = objectMapperForIncomingUpdates.readTree(event.data)
        val eventDataJsonNode: JsonNode = receivedEvent.get("eventData")
        val eventDataJsonString: String = objectMapperForIncomingUpdates.writeValueAsString(eventDataJsonNode)
        val eventData: EventData = objectMapperForIncomingUpdates.readValue(eventDataJsonString, EventData::class.java)

        logger.info("Finding contractId for platformEmployeeId=${eventData.profile?.id}")

        val (companyIntegration, platformContractIntegration) = customerIntegrationService.findPlatformContractIntegrationFromEvent(
            event,
            eventData.profile?.id!!
        )
        logger.info("Fetching cached employee data for platformEmployeeId=${eventData.profile.id}")
        val jpaPlatformEmployeeData = platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
            companyIntegration.id!!,
            eventData.profile.id
        )
        if (platformContractIntegration == null || jpaPlatformEmployeeData.isNullOrEmpty()) {
            event.eventType = EventType.RECORD_NEW
            syncService.processEmployeeEvent(event, false, mutableListOf())
            return
        }

        val contractId = platformContractIntegration.contractId
        val origin = employeeService.getEmployeeDataOrigin(contractId)
        if (origin == Constants.EmployeeOrigin.INTERNAL) {
            logger.info("Employee origin is INTERNAL, skipping update to Multiplier")
            return
        }

        logger.info("Fetching contract by ID: {}", contractId)
        val contract = contractServiceAdapter.findContractByContractId(contractId)
        val internalEmployeeData = jpaPlatformEmployeeData.first()

        val employeeData = objectMapperForIncomingUpdates.readValue(internalEmployeeData.employeeData, EmployeeData::class.java)

        logger.info("Checking if there is any change(s) in basic member details")
        var shouldUpdateCache = false
        val memberUpdateBuilder = UpdateMemberFieldsRequest.newBuilder()
        if (checkIfMemberDetailsChanged(employeeData, eventData, memberUpdateBuilder)) {
            logger.info("Change(s) found in member details")
            shouldUpdateCache = true
            // Update member basic details
            logger.info("Update member details with memberId=${contract.memberId}")
            memberUpdateBuilder.setMemberId(contract.memberId)
            val updateMemberRequest = memberUpdateBuilder.build()
            memberServiceAdapter.updateMemberFields(updateMemberRequest)
        }

        val eventPresentAddress = eventData.locations?.presentAddress

        logger.info("Checking if there is any change(s) in member address details")
        if (checkIfMemberAddressChanged(employeeData, eventData) && contract.type == ContractOuterClass.ContractType.HR_MEMBER) {
            logger.info("Change(s) found, sending email to notify user")
            shouldUpdateCache = true
            val updatedCurrentAddressBuilder = Address.newBuilder()
                .setLine1(eventPresentAddress?.addressLine1)
                .setLine2(eventPresentAddress?.addressLine2 ?: "")
                .setCity(eventPresentAddress?.city)
                .setZipcode(eventPresentAddress?.zipCode ?: "")
                .setState(eventPresentAddress?.state ?: "")

            if (eventPresentAddress?.addressLine2 == null) updatedCurrentAddressBuilder.clearLine2()
            if (eventPresentAddress?.state == null) updatedCurrentAddressBuilder.clearState()
            if (eventPresentAddress?.zipCode == null) updatedCurrentAddressBuilder.clearZipcode()
            val upsertMemberAddressDetailRequest = UpsertMemberAddressDetailRequest
                .newBuilder()
                .setMemberId(contract.memberId)
                .setCurrentAddress(updatedCurrentAddressBuilder.build())
                .build()

            memberServiceAdapter.upsertMemberAddressDetails(upsertMemberAddressDetailRequest)
            logger.info("Updated member address with memberId=${contract.memberId} and new address ${eventData.locations?.presentAddress}")
        }

        logger.info("Checking if there is any change(s) in bank details")

        if (checkIfBankDetailsChanged(employeeData, eventData)) {
            logger.info("Change(s) found, sending email to notify user")

            syncService.handleBankUpdate(contract.memberId, eventData, contract.country)
//            val platformName = platformContractIntegration.platform.name
//            notificationsService.sendMemberToUpdateBankDetailEmailNotification(contract, platformName)
            shouldUpdateCache = true
        }

        if (checkIfTerminationDateChanged(employeeData, eventData)) {
            contractOffboardingInitialisationEventHandler.handleContractOffboardingInitialisation(eventData, contractId)
            shouldUpdateCache = true
        }

        logger.info("Checking if there is any change(s) in compensation")
        val compensationUpdated = checkIfCompensationChanged(employeeData, eventData, contractId)
        if (compensationUpdated.isUpdated && compensationUpdated.compensationUpdateRequest != null) {
            logger.info("Change(s) found, update compensation for user")

            // performanceReviewBulkCreateAsApproved is being fixed by Giga Squad, context: https://multiplier-group.slack.com/archives/C079DAXSXCM/p1744634656805379
            logger.info("Ignoring requests for performanceReviewBulkCreateAsApproved as it is currently not supported in this version.")
            if (featureFlagService.isSyncCompensationUpdate(contract.companyId)) {
                val compensationUpdateRequest = compensationUpdated.compensationUpdateRequest
                contractServiceAdapter.performanceReviewBulkCreateAsApproved(compensationUpdateRequest)
                shouldUpdateCache = true
            }
        }

        if (shouldUpdateCache) {
            logger.info("Updating cache employee data with new data from eventId=${event.eventId} for employeeId=${internalEmployeeData.employeeId}")
            val eventEmployeeProfile = eventData.profile
            val internalEmployeeProfile = employeeData.profile
            if (internalEmployeeProfile == null) {
                return
            } else {
                val newInternalEmployeeDataProfile = internalEmployeeProfile.copy(
                    firstName = eventEmployeeProfile.firstName,
                    lastName = eventEmployeeProfile.lastName,
                    workEmail = eventEmployeeProfile.workEmail,
                    birthDate = eventEmployeeProfile.birthDate.toString(),
                    maritalStatus = eventEmployeeProfile.maritalStatus.toString(),
                    employmentStatus = eventEmployeeProfile.employmentStatus.toString(),
                    gender = eventEmployeeProfile.gender.toString(),
                    terminationDate = eventEmployeeProfile.terminationDate.toString()
                )
                val newBankDetails = eventData.bankAccounts?.get(0)?.copy()
                val newBankAccountList = if (newBankDetails != null) listOf(
                    BankAccountDetails(
                        accountNumber = newBankDetails.accountNumber,
                        accountType = newBankDetails.accountType?.name,
                        bankName = newBankDetails.bankName
                    )
                ) else null
                val newCompensation = eventData.compensation?.toEmployeeCompensationData()
                val newInternalEmployeeData =
                    employeeData.copy(
                        profile = newInternalEmployeeDataProfile,
                        bankAccounts = newBankAccountList,
                        compensation = newCompensation,
                        locations = Locations(
                            // setting these two to null since we are not tracking them now
                            workAddress = null,
                            permanentAddress = null,
                            presentAddress = if (eventPresentAddress != null) EmployeeLocation(
                                addressLine1 = eventPresentAddress?.addressLine1,
                                addressLine2 = eventPresentAddress?.addressLine2,
                                city = eventPresentAddress?.city,
                                state = eventPresentAddress?.state,
                                country = eventPresentAddress?.country,
                                zipCode = eventPresentAddress?.zipCode,
                            ) else null
                        )
                    )
                internalEmployeeData.employeeData = objectMapperForIncomingUpdates.writeValueAsString(newInternalEmployeeData)
                platformEmployeeDataRepository.save(internalEmployeeData)
                logger.info("Success updating cache employee data with new data from eventId=${event.eventId} for employeeId=${internalEmployeeData.employeeId}")
            }
        }
    }

    private fun checkIfMemberDetailsChanged(
        employeeData: EmployeeData,
        eventData: EventData,
        memberUpdateBuilder: UpdateMemberFieldsRequest.Builder,
    ): Boolean {
        val eventEmployeeProfile = eventData.profile ?: return false
        val internalEmployeeProfile = employeeData.profile ?: return false
        var isChanged = false
        if (internalEmployeeProfile.firstName != eventEmployeeProfile.firstName && eventEmployeeProfile.firstName != null) {
            isChanged = true
            memberUpdateBuilder.setFirstName(eventEmployeeProfile.firstName)
        }
        if (internalEmployeeProfile.lastName != eventEmployeeProfile.lastName && eventEmployeeProfile.lastName != null) {
            isChanged = true
            memberUpdateBuilder.setLastName(eventEmployeeProfile.lastName)
        }
        if (internalEmployeeProfile.gender != eventEmployeeProfile.gender.toString() && eventEmployeeProfile.gender != null) {
            isChanged = true
            val gender = enumValues<Gender>().find { it.name == eventEmployeeProfile.gender.name }
                ?: Gender.UNSPECIFIED
            memberUpdateBuilder.setGender(gender)
        }
        if (internalEmployeeProfile.birthDate != eventEmployeeProfile.birthDate.toString() && eventEmployeeProfile.birthDate != null) {
            isChanged = true
            memberUpdateBuilder.setDateOfBirth(eventEmployeeProfile.birthDate.toGrpcTimestamp())
        }
        if (internalEmployeeProfile.maritalStatus != eventEmployeeProfile.maritalStatus.toString() && eventEmployeeProfile.maritalStatus != null) {
            isChanged = true
            val maritalStatus = enumValues<MaritalStatus>().find { it.name == eventEmployeeProfile.maritalStatus.name }
                ?: MaritalStatus.UNSPECIFIED
            memberUpdateBuilder.setMaritalStatus(maritalStatus)
        }
        if (internalEmployeeProfile.employmentStatus != eventEmployeeProfile.employmentStatus.toString() && eventEmployeeProfile.employmentStatus != null) {
            isChanged = true
            val employmentStatus = eventEmployeeProfile.employmentStatus.toMemberStatus()
            if (employmentStatus != MemberStatus.UNRECOGNIZED) {
                memberUpdateBuilder.setStatus(employmentStatus)
            }
        }

        return isChanged
    }

    private fun checkIfMemberAddressChanged(
        employeeData: EmployeeData,
        eventData: EventData,
    ): Boolean {
        val eventPresentAddress = eventData.locations?.presentAddress ?: return false
        // if cache was empty and we got address from eventData now, proceed to update
        val cachePresentAddress = employeeData.locations?.presentAddress ?: return true

        if (cachePresentAddress.addressLine1 != eventPresentAddress.addressLine1 ||
            cachePresentAddress.addressLine2 != eventPresentAddress.addressLine2 ||
            cachePresentAddress.city != eventPresentAddress.city ||
            cachePresentAddress.state != eventPresentAddress.state ||
            cachePresentAddress.country != eventPresentAddress.country ||
            cachePresentAddress.zipCode != eventPresentAddress.zipCode)
        {
            return true
        }

        return false
    }

    private fun checkIfBankDetailsChanged(employeeData: EmployeeData, eventData: EventData): Boolean {
        val eventBankDetails = eventData.bankAccounts?.getOrNull(0) ?: return false
        val internalBankDetails = employeeData.bankAccounts?.getOrNull(0) ?: return true

        return !(eventBankDetails.bankName == internalBankDetails.bankName &&
                eventBankDetails.accountNumber == internalBankDetails.accountNumber &&
                eventBankDetails.accountType?.toString() == internalBankDetails.accountType &&
                eventBankDetails.routingInfo?.any { it.type == RoutingType.BRANCH_CODE && it.number == internalBankDetails.branchCode } == true &&
                eventBankDetails.routingInfo?.any { it.type == RoutingType.IFSC_CODE && it.number == internalBankDetails.ifscCode } == true &&
                eventBankDetails.routingInfo?.any { it.type == RoutingType.SWIFT_CODE && it.number == internalBankDetails.swiftCode } == true &&
                eventBankDetails.routingInfo?.any { it.type == RoutingType.BANK_IDENTIFICATION_CODE && it.number == internalBankDetails.bankIdentificationCode } == true &&
                eventBankDetails.routingInfo?.any { it.type == RoutingType.IBAN && it.number == internalBankDetails.iban } == true &&
                eventBankDetails.routingInfo?.any { it.type == RoutingType.ROUTING_NUMBER && it.number == internalBankDetails.routingNumber } == true)
    }

    private fun checkIfTerminationDateChanged(employeeData: EmployeeData, eventData: EventData): Boolean {
        val eventEmployeeProfile = eventData.profile ?: return false
        val internalEmployeeProfile = employeeData.profile ?: return false
        if (eventEmployeeProfile.terminationDate == null && internalEmployeeProfile.terminationDate == null) return false
        return (eventEmployeeProfile.terminationDate.toString() != internalEmployeeProfile.terminationDate)
    }

    @VisibleForTesting
    fun checkIfCompensationChanged(
        employeeData: EmployeeData,
        eventData: EventData,
        contractId: Long
    ): CheckCompensationUpdateResponse {
        var isUpdated = false
        val eventCompensation = eventData.compensation ?: return CheckCompensationUpdateResponse(
            isUpdated = false,
            compensationUpdateRequest = null
        )
        val internalCompensation = employeeData.compensation
        // check for fixed base salary amount updated
        val existedSalary = internalCompensation?.fixed?.find { it.type == CompensationType.SALARY.name }
        val updatedSalary = eventCompensation.fixed?.find { it.type == CompensationType.SALARY.name }
        var updatedBaseAmount: Double? = null
        var updatedEffectiveDate: LocalDate? = null
        if (updatedSalary != null && existedSalary?.amount != updatedSalary.amount?.toString()) {
            updatedBaseAmount = updatedSalary.amount
            updatedEffectiveDate = updatedSalary.startDate.toLocalDate()
            isUpdated = true
        }
        // check for variable compensation updated
        val updatedVariables = eventCompensation.variable ?: return CheckCompensationUpdateResponse(
            isUpdated = isUpdated,
            compensationUpdateRequest = PerformanceReviewRequest(
                contractId = contractId,
                effectiveDate = updatedEffectiveDate,
                amount = updatedBaseAmount,
                additionalPays = null
            )
        )
        val additionalPays = mutableListOf<AdditionalPay>()
        for (variable in updatedVariables) {
            val matchingItem = internalCompensation?.variable?.find { it.type == variable.type }
            var updatedAdditionalAmount: Double? = null
            var updatedAdditionalFrequency: Frequency? = null
            if (matchingItem?.amount != variable.amount?.toString()) {
                updatedAdditionalAmount = variable.amount
                isUpdated = true
            }
            if (matchingItem?.frequency != variable.frequency?.toString()) {
                updatedAdditionalFrequency = variable.frequency
                isUpdated = true
            }
            if (isUpdated) {
                additionalPays.add(
                    AdditionalPay(
                        type = variable.type.toString(),
                        amount = updatedAdditionalAmount,
                        frequency = updatedAdditionalFrequency
                    )
                )
            }
        }
        val compensationUpdateRequest = PerformanceReviewRequest(
            contractId = contractId,
            effectiveDate = updatedEffectiveDate,
            amount = updatedBaseAmount,
            additionalPays = additionalPays
        )
        return CheckCompensationUpdateResponse(
            isUpdated = isUpdated,
            compensationUpdateRequest = compensationUpdateRequest
        )
    }

    data class CheckCompensationUpdateResponse(
        val isUpdated: Boolean,
        val compensationUpdateRequest: PerformanceReviewRequest?,
    )
}