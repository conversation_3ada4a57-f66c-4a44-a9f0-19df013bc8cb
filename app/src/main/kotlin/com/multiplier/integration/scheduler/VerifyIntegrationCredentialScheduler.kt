package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants.ScheduledJobName.VERIFY_INTEGRATION_CREDENTIALS_EXPIRY
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.types.PlatformCategory
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.verifyIntegrationCredentialScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class VerifyIntegrationCredentialScheduler(
    private val customerIntegrationService: CustomerIntegrationService,
    private val platformRepository: PlatformRepository,
    private val companyIntegrationRepository: CompanyIntegrationRepository
) {
    private val log = LoggerFactory.getLogger(VerifyIntegrationCredentialScheduler::class.java)

    @Scheduled(cron = "0 0 5 * * *")  // Run once every day at 5:00 AM
    @SchedulerLock(
        name = VERIFY_INTEGRATION_CREDENTIALS_EXPIRY,
        lockAtLeastFor = LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = LOCK_AT_MOST_FOR_STRING
    )
    fun verifyIntegrationCredentialsExpiry() {
        log.info("Starting verifyIntegrationCredentialsExpiry scheduler.")
        try {
            val platform = platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet")
            if (platform == null || platform.id == null) {
                log.error("No platform found for category HRIS and name TriNet")
                return
            }
            log.info("Platform ID: {} found for TRINET.", platform.id)

            val integrations = companyIntegrationRepository.findEnabledIntegrationsByPlatformId(platform.id!!)
            log.info("Found {} enabled integrations for platform ID: {}", integrations.size, platform.id)

            integrations.forEach { integration ->
                log.info("Validating credentials for integration ID: {}", integration.id)
                customerIntegrationService.validateIntegrationCredentials(integration.id!!, null, false)
            }
            log.info("All integrations processed.")
        } catch (e: Exception) {
            log.error("An error occurred during the verification of integration credentials: {}", e.message, e)
        }
        log.info("Finished verifyIntegrationCredentialsExpiry scheduler.")
    }
}
