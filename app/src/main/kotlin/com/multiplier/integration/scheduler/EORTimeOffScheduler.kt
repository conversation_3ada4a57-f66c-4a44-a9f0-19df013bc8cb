package com.multiplier.integration.scheduler

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.platforms.BambooHRPlatformStrategy
import com.multiplier.integration.platforms.HibobHRPlatformStrategy
import com.multiplier.integration.platforms.KekaHRPlatformStrategy
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.service.TimeoffService
import com.multiplier.integration.service.exception.IntegrationNotFoundException
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class EORTimeOffScheduler(
    private val integrationRepository: CompanyIntegrationRepository,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val timeOffServiceAdapter: TimeoffServiceAdapter,
    private val knitAdapter: KnitAdapter,
    private val platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository,
    private val timeOffService: TimeoffService,
    private val timeOffSyncService: TimeOffSyncService,
    private val bambooHRPlatformStrategy: BambooHRPlatformStrategy,
    private val kekaHRPlatformStrategy: KekaHRPlatformStrategy,
    private val hibobHRPlatformStrategy: HibobHRPlatformStrategy,
) {
    private val log = KotlinLogging.logger {}
    private val ALLOWED_PLATFORMS = listOf("BambooHR", "KekaHR", "Hibob")

//    @Scheduled(cron = "0 0 * * * ?") // Runs every hour
//    @SchedulerLock(
//        name = EOR_TIMEOFF_SCHEDULER,
//        lockAtLeastFor = LOCK_AT_LEAST_FOR_STRING,
//        lockAtMostFor = LOCK_AT_MOST_FOR_STRING
//    )
    fun triggerScheduledEORTimeOffsSync() {
//        processEORTimeOffs(null)
    }

    fun processEORTimeOffs(integrationId: Long? = null) {
        runBlocking {
            log.info("Starting EOR Time Off processing")
            var eorEnabledIntegrations = integrationRepository.findEORTimeoffEnabledIntegration().filter {
                ALLOWED_PLATFORMS.contains(it.platform.name)
            }
            if (integrationId != null) {
                eorEnabledIntegrations = eorEnabledIntegrations.filter { it.id == integrationId }
                if (eorEnabledIntegrations.isEmpty()) {
                    throw IntegrationNotFoundException("Timeoff outbound enabled integration with id $integrationId not found")
                }
            }
            log.info("Found ${eorEnabledIntegrations.size} EOR-enabled integrations")

            for (integration in eorEnabledIntegrations) {
                if (integration.platform.name == "BambooHR")
                    bambooHRPlatformStrategy.processTimeOff(integration)
                if (integration.platform.name == "KekaHR")
                    kekaHRPlatformStrategy.processTimeOff(integration)
                if (integration.platform.name == "Hibob")
                    hibobHRPlatformStrategy.processTimeOff(integration)

            }
        }
        log.info("Completed EOR Time Off processing")
    }
}