package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants
import com.multiplier.integration.service.SyncService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.receivedCreateEventsScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class ReceivedCreateEventsScheduler (
    private val syncService : SyncService
) {
    private val logger = LoggerFactory.getLogger(ReceivedCreateEventsScheduler::class.java)

    @Scheduled(fixedDelay = 2000)
    @SchedulerLock(
        name = Constants.ScheduledJobName.PROCESS_RECEIVED_EVENTS,
        lockAtLeastFor = Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING,
    )
    fun processApprovedCreateEvents() {
        syncService.processApprovedCreateEvents()
    }

}