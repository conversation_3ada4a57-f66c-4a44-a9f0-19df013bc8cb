package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants.ScheduledJobName.UPDATE_STALE_GP_SYNC
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.service.NotificationsService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@ConditionalOnProperty(
    value = ["scheduler.gpSyncScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class GPSyncScheduler(
    private val syncRepository: SyncRepository,
    private val notificationsService: NotificationsService,
    private val companyIntegrationRepository: CompanyIntegrationRepository,
) {
    private val log = LoggerFactory.getLogger(GPSyncScheduler::class.java)
    private val expiredMinutes = 5L

    @Scheduled(cron = "0 */5 * * * *")  // Run every 5 minutes
    @SchedulerLock(
        name = UPDATE_STALE_GP_SYNC,
        lockAtLeastFor = LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = LOCK_AT_MOST_FOR_STRING
    )
    fun disableStaleGPSync() {
        log.info("Starting updateStaleGPSync scheduler.")
        try {
            val thresholdTime = LocalDateTime.now().minusMinutes(expiredMinutes)
            val openSyncs = syncRepository.findInProgressSyncsOlderThanThresholdTime(thresholdTime)

            log.info("Found {} expired gp sync(s) in progress.", openSyncs.size)

            openSyncs.forEach { sync ->
                try {
                    val integration = companyIntegrationRepository.findByAccountToken(sync.integrationId!!)

                    // Close the sync
                    sync.inProgress = false
                    sync.endTime = LocalDateTime.now()
                    integration!!.incomingSyncInProgress = false
                    // Save the changes
                    syncRepository.save(sync)
                    companyIntegrationRepository.save(integration)

                    // Notify about the issue
                    // currently disabled as the message's content is inappropriate, will be fixed later
//                    notificationsService.sendAdminGPSyncClosure(sync.syncId, integration?.companyId!!, integration.platform?.name!!)

                    // This is not an error but worth sending an alert to the dev team for further investigation
                    log.error("[ALERT] Closed gp sync with ID: ${sync.syncId} for company: ${integration.companyId} that was open for more than $expiredMinutes minutes.")
                } catch (e: Exception) {
                    log.error("[ALERT] Error closing gp sync with ID: ${sync.syncId}, Error: ${e.message}", e)
                }
            }
            log.info("All stale gp syncs processed.")
        } catch (e: Exception) {
            log.error("An error occurred during the stale gp syncs processing: {}", e.message, e)
        }
        log.info("Finished updateStaleGPSync scheduler.")
    }
}

