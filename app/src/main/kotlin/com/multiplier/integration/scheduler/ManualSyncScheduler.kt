package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants.ScheduledJobName.UPDATE_IN_PROGRESS_MANUAL_SYNC
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING
import com.multiplier.integration.Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING
import com.multiplier.integration.repository.ManualSyncRepository
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.types.SyncStatus
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.manualSyncScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class ManualSyncScheduler(
    private val customerIntegrationService: CustomerIntegrationService,
    private val manualSyncRepository: ManualSyncRepository,
) {
    private val log = LoggerFactory.getLogger(ManualSyncScheduler::class.java)

    @Scheduled(cron = "0 */30 * * * *")  // Run every 30 minutes
    @SchedulerLock(
        name = UPDATE_IN_PROGRESS_MANUAL_SYNC,
        lockAtLeastFor = LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = LOCK_AT_MOST_FOR_STRING
    )
    fun updateInProgressManualSync() {
        log.info("Starting updateInProgressManualSync scheduler.")
        try {
            val manualSyncInProgress = manualSyncRepository.findByStatus(SyncStatus.IN_PROGRESS)

            log.info("Found {} manual sync(s) in progress.", manualSyncInProgress.size)

            manualSyncInProgress.forEach { sync ->
                log.info("Processing manual sync with ID: {}", sync.syncId)
                customerIntegrationService.getLatestSyncResultForIntegrationInternal(sync.integrationId)
            }
            log.info("All manual sync(s) processed.")
        } catch (e: Exception) {
            log.error("An error occurred during the manual sync processing: {}", e.message, e)
        }
        log.info("Finished updateInProgressManualSync scheduler.")
    }
}
