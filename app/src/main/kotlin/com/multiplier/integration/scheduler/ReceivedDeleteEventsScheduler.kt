package com.multiplier.integration.scheduler

import com.multiplier.expense.schema.BulkDeleteExpensesRequest
import com.multiplier.integration.Constants
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.service.ExpenseProcessorService
import com.multiplier.integration.service.SyncService
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@ConditionalOnProperty(
    value = ["scheduler.receivedDeleteEventsScheduler.enabled"],
    matchIfMissing = true,
    havingValue = "true"
)
@Component
class ReceivedDeleteEventsScheduler(
    private val jpaReceivedEventRepository: ReceivedEventRepository,
    private val syncService: SyncService,
    private val expenseProcessorService: ExpenseProcessorService
) {

    private val logger = KotlinLogging.logger {}

    @Scheduled(fixedDelay = 5000)
    @SchedulerLock(
        name = Constants.ScheduledJobName.PROCESS_RECEIVED_DELETE_EVENTS,
        lockAtLeastFor = Constants.ShedlockTimeConfiguration.LOCK_AT_LEAST_FOR_STRING,
        lockAtMostFor = Constants.ShedlockTimeConfiguration.LOCK_AT_MOST_FOR_STRING,
    )
    fun fetchAndProcessDeleteEvents() {
        logger.info("Fetching delete events from KNIT.")
        val events = jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
            eventType = EventType.RECORD_DELETE,
            syncDataType = Constants.SyncDataType.EMPLOYEE,
            confirmedByUser = true,
            processed = false
        )

        if (events.isEmpty()) {
            logger.info("No incoming delete events to process.")
        }

        for (event in events) {
            try {
                logger.info("Start processing event for eventId: ${event.eventId}")
                syncService.processDeletedEvent(event)
            } catch (e: Exception) {
                logger.error("Error processing event for eventId: ${event.eventId}", e)
            } finally {
                event.processed = true
                jpaReceivedEventRepository.save(event)
                logger.info("Finalizing processing event for eventId: ${event.eventId}")
            }
        }


        // code below this should be removed as delete events are processed in Update events as it's being sent in Update by knit
        val expenseDeletedEvents = jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
            eventType = EventType.RECORD_DELETE,
            syncDataType = Constants.SyncDataType.EXPENSE,
            confirmedByUser = true,
            processed = false
        )
        if (expenseDeletedEvents.isEmpty()) {
            logger.info("No incoming expense delete events to process.")
            return
        }

        val deleteExpenseIds = mutableListOf<Long>()
        for (event in expenseDeletedEvents) {
            var shouldUpdateReceivedEvent = true
            try {
                logger.info("Start processing event for eventId: ${event.eventId}")
                val syncId = event.syncId
                val syncEndEvent = jpaReceivedEventRepository.findByEventTypeAndSyncId(eventType = EventType.SYNC_END, syncId = syncId).firstOrNull()
                if (syncEndEvent == null) {
                    logger.warn("Skipping events as syncId $syncId has not finished processing")
                    shouldUpdateReceivedEvent = false
                    continue
                }
                val internalExpenseId = expenseProcessorService.getExpenseIdFromEventData(event)
                if (internalExpenseId != null) {
                    deleteExpenseIds.add(internalExpenseId)
                }
            } catch (e: Exception) {
                logger.error("Error processing event for eventId: ${event.eventId}", e)
            } finally {
                if (shouldUpdateReceivedEvent) {
                    event.processed = true
                    jpaReceivedEventRepository.save(event)
                    logger.info("Finalizing processing event for eventId: ${event.eventId}")
                }
            }
        }
        if (deleteExpenseIds.isNotEmpty()) {
            val bulkDeleteExpensesRequest = BulkDeleteExpensesRequest.newBuilder()
                .addAllExpenseIds(deleteExpenseIds)
                .build()
            expenseProcessorService.processBulkRevokeExpenseRequest(bulkDeleteExpensesRequest)
        }
        logger.info("Finished processing delete events.")
    }
}