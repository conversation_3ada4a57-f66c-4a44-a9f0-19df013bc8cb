package utils

import com.multiplier.integration.utils.convertDateToLocalDate
import org.apache.commons.lang3.time.DateUtils
import java.time.format.DateTimeFormatter

class DateUtils {

    companion object {

        private val knownDateTimePatterns = arrayOf(
            "yyyy-M-d'T'HH:mm:ss+HH:mm",
            "yyyy-M-dd'T'H:mm:ss'Z'",
            "yyyy-MM-d'T'HH:mm'Z'"
        )

        fun parseToFormattedDateString(input: String, dateFormat: String): String {
            try {
                val date = DateUtils.parseDate(input, *knownDateTimePatterns)
                return date.convertDateToLocalDate().format(DateTimeFormatter.ofPattern(dateFormat))
            } catch (e: Exception) {
                return input
            }
        }
    }
}