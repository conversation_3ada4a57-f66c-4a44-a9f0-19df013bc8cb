package utils

class StringUtils {

    companion object {

        fun replaceAllNonAlphanumericCharacters(input: String, replaceWith: String): String {
           return input.replace(("[^\\w\\d ]").toRegex(), replaceWith)
        }

        /**
         * Masks an email address by hiding most of the username part while keeping the domain visible
         * Example: "<EMAIL>" becomes "jo****<EMAIL>"
         * 
         * @param email The email address to mask
         * @return The masked email address, or original email if invalid format or too short
         */
        fun maskEmail(email: String): String {
            val parts = email.split("@")
            if (parts.size != 2) return email  // Return original if not a valid email

            val username = parts[0]
            val domain = parts[1]

            return if (username.length > 4) {
                val maskedPart = "*".repeat(username.length - 4)
                "${username.take(2)}$maskedPart${username.takeLast(2)}@$domain"
            } else {
                email  // Return original if too short to mask properly
            }
        }
    }
}