package utils

import com.multiplier.company.schema.grpc.CompanyOuterClass as NewCompanyOuterClass

fun NewCompanyOuterClass.Company.getCompanyCountryCode(): String? {
    val legalEntity: NewCompanyOuterClass.LegalEntity? = this.primaryEntity
    return legalEntity?.address?.country
}

fun NewCompanyOuterClass.CompanyUser.getPrimaryOrDefaultEmailOrThrow(): String {
    val primaryEmail = this.emailsList?.firstOrNull {
        it.type.equals("primary", ignoreCase = true) && it.email.isNotBlank()
    }
    if (primaryEmail != null) {
        return primaryEmail.email
    }
    val defaultEmail = this.emailsList?.firstOrNull {
        it.type.equals("default", ignoreCase = true) && it.email.isNotBlank()
    }
    if (defaultEmail != null) {
        return defaultEmail.email
    }
    throw NoSuchElementException("No emails found for member id = ${this.id}")
}

fun NewCompanyOuterClass.CompanyUser.getCompanyUserEmail(): String? {
    return this.emailsList
        .stream()
        .filter { emailAddress ->
            emailAddress.type.equals("primary", ignoreCase = true)
                    || emailAddress.type.equals("default", ignoreCase = true)
        }
        .findFirst()
        .orElseThrow()
        .email
}

fun NewCompanyOuterClass.CompanyUser.getFullName(): String {
    return listOfNotNull(this.firstName, this.lastName)
        .filter { it.isNotBlank() }
        .joinToString(" ")
        .trim()
}