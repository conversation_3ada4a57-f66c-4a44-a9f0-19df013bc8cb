package utils

import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.EmailAddress
import com.multiplier.member.schema.Member

fun Member.getPrimaryOrDefaultEmailOrThrow(): String {
    return getPrimaryEmail(this)?.email
        ?: getDefaultEmail(this)?.email
            ?: throw NoSuchElementException("No emails found for member id = ${this.id}")
}

fun Member.getPrimaryOrDefaultEmail(): String? {
    return getPrimaryEmail(this)?.email ?: getDefaultEmail(this)?.email
}

fun Member.getCountryOrDefault(): CountryCode {
    if (nationalitiesCount > 0) {
        return nationalitiesList.first().country
    }

    return CountryCode.NULL_COUNTRY
}

private fun getPrimaryEmail(member: Member?): EmailAddress? {
    return member?.emailsList?.firstOrNull {
        it.type.equals("primary", ignoreCase = true) && it.email.isNotBlank()
    }
}

private fun getDefaultEmail(member: Member?): EmailAddress? {
    return member?.emailsList?.firstOrNull {
        it.type.equals("default", ignoreCase = true) && it.email.isNotBlank()
    }
}
