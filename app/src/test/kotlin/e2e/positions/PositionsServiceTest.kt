package e2e.positions

import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.repository.CachePositionsRepository
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.service.PositionsService
import e2e.PostgreSQLContainerInitializer
import e2e.TestDataService
import e2e.resourceAsString
import io.kotest.matchers.shouldBe
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.util.ReflectionTestUtils
import kotlin.test.assertNotNull

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestDataService::class,
    ],
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class])
@ActiveProfiles("test")
@Disabled("Temporarily disable this since it causing out of memory after implementing platform kafka lib")
class PositionsServiceTest {
    @Autowired
    private lateinit var cachePositionsRepository: CachePositionsRepository
    
    @Autowired
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository
    
    private val knitAdapter = mockk<KnitAdapter>()
    private val json = Json { ignoreUnknownKeys = true }
    
    private lateinit var positionsService: PositionsService
    
    @BeforeEach
    fun setup() {
        positionsService = PositionsService(
            cachePositionsRepository = cachePositionsRepository,
            knitAdapter = knitAdapter,
            companyIntegrationRepository = companyIntegrationRepository
        )
        // Set cache TTL to 24 hours
        ReflectionTestUtils.setField(positionsService, "cacheTtlSeconds", 86400L)
    }
    
    @Test
    fun `test getPositions writes correct data to cache`(): Unit = runBlocking {
        // Given
        val companyId = 123L
        val platformId = 456L
        val expectedResponse = json.decodeFromString<GetPositionDetailResponse>(
            resourceAsString("get_positions/workday.json")
        )
        
        coEvery { 
            knitAdapter.getPositionsDetails(companyId, platformId) 
        } returns expectedResponse
        
        // When
        val result = positionsService.getPositions(companyId, platformId, ignoreCache = false)
        
        // Then
        result shouldBe expectedResponse
        
        // Verify it was cached
        val cachedData = cachePositionsRepository.findCachedPositions(companyId, platformId)
        cachedData?.first shouldBe expectedResponse
    }

    @Test
    fun `test getPositions updates and returns correct data when cache already exists`(): Unit = runBlocking {
        // Given
        val companyId = 123L
        val platformId = 456L
        val initialResponse = json.decodeFromString<GetPositionDetailResponse>(
            resourceAsString("get_positions/workday.json")
        )

        // Ensure we have non-null data before proceeding
        assertNotNull(initialResponse.data) { "Initial response data should not be null" }

        // First call to populate cache
        coEvery {
            knitAdapter.getPositionsDetails(companyId, platformId)
        } returns initialResponse

        positionsService.getPositions(companyId, platformId, ignoreCache = false)

        // Verify initial cache state
        val initialCachedData = cachePositionsRepository.findCachedPositions(companyId, platformId)
        initialCachedData?.first shouldBe initialResponse

        // Create updated response with safe null handling
        val updatedResponse = initialResponse.copy(
            data = initialResponse.data?.let { originalData ->
                originalData.copy(
                    positions = originalData.positions?.map {
                        it.copy(designation = "${it.designation}-UPDATED")
                    },
                    workShifts = originalData.workShifts
                )
            }
        )

        // Manually update the cache with new data
        cachePositionsRepository.upsertCachedPosition(
            companyId = companyId,
            platformId = platformId,
            data = updatedResponse
        )

        // Clear all recorded calls to the adapter before our second get
        clearMocks(knitAdapter)

        // When - Get positions (should return from cache)
        val result = positionsService.getPositions(companyId, platformId, ignoreCache = false)

        // Then - Verify adapter was not called
        coVerify(exactly = 0) { knitAdapter.getPositionsDetails(companyId, platformId) }

        // Then - Verify the returned result matches expected
        result shouldBe updatedResponse

        // Verify cache was updated with the new data
        val cachedData = cachePositionsRepository.findCachedPositions(companyId, platformId)
        requireNotNull(cachedData) { "Cached data should not be null" }
        cachedData.first shouldBe updatedResponse
    }

}
