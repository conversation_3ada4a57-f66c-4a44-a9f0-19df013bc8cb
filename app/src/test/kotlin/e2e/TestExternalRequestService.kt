package e2e

import com.multiplier.integration.aRandomEmail
import com.multiplier.integration.aRandomString
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.adapter.api.resources.knit.bamboo.*
import com.multiplier.integration.adapter.api.resources.knit.hibob.*
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaAddressDetails
import com.multiplier.integration.adapter.api.resources.knit.oracle.LegalEntityItem
import com.multiplier.integration.adapter.api.resources.knit.oracle.GetLegalEntitiesResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.*
import com.multiplier.integration.sync.model.IsPaid
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.sync.model.Unit
import com.multiplier.integration.utils.convertDateToLocalDate
import com.multiplier.integration.adapter.api.resources.knit.oracle.LegalEntityBodyWrapper
import com.multiplier.integration.adapter.api.resources.knit.oracle.LegalEntityResponseWrapper
import com.ninjasquad.springmockk.SpykBean
import io.mockk.*
import org.springframework.stereotype.Service
import java.sql.Timestamp
import java.time.format.DateTimeFormatter

@Service
class TestExternalRequestService {
    @SpykBean
    lateinit var knitAdapter: KnitAdapter

    fun recordKnitCreateEmployeeRecord(): CapturingSlot<CreateEmployeeRequest> {
        val slot = slot<CreateEmployeeRequest>()
        coEvery {
            knitAdapter.createEmployeeRecord(any(), any(), capture(slot))
        } returns CreateEmployeeResponse(success = true)
        return slot
    }

    fun recordKnitUpdateEmployeeRecord(): CapturingSlot<UpdateEmployeeDetailsRequest> {
        val slot = slot<UpdateEmployeeDetailsRequest>()
        coEvery {
            knitAdapter.updateEmployeeDetails(any(), any(), any(), capture(slot))
        } returns UpdateEmployeeDetailsResponse(success = true)
        return slot
    }

    fun useRealKnitCreateEmployee(): CapturingSlot<CreateEmployeeRequest> {
        val slot = slot<CreateEmployeeRequest>()
        coEvery { knitAdapter.createEmployeeRecord(any(), any(), capture(slot)) } answers { callOriginal() }
        return slot
    }

    fun mockKnitEmployeeDirectory(workEmail: String = aRandomEmail()) {
        coEvery {
            knitAdapter.getEmployeeDirectory(any(), any())
        } returns GetEmployeeDirectoryResponse(
            success = true,
            employees = listOf(
                Employee(
                    id = aRandomString(),
                    workEmail = workEmail
                )
            )
        )
    }

    fun mockKnitBambooWorkLocationPassthrough(name: String = aRandomString()): OptionData {
        val location = OptionData(name = name)
        coEvery {
            knitAdapter.getBambooWorkLocations(any(), any())
        } returns GetWorkLocationsResponse(
            success = true,
            data = WorkLocationResponse(
                parsedData = Lists(
                    listOf(
                        ListData(
                            alias = "location",
                            options = Options(listOf(location))
                        )
                    )
                ),
                response = WorkLocationResponseBody(
                    body = "asdf"
                )
            )
        )
        return location
    }

    fun mockKnitHibobWorkLocationPassthrough(name: String = aRandomString()): OptionData {
        val location = OptionData(name = name)
        coEvery {
            knitAdapter.getWorksitesResponse(any(), any())
        } returns GetWorksitesResponse(
            success = true,
            data = WorkSiteResponse(
                responseJson = WorkSiteResponseBodySerialized(
                    body = WorkSiteData(
                        name = name,
                        values = listOf(
                            WorkSite(id = 1, value = name, name = name, archived = false)
                        )
                    )
                )
            )
        )
        return location
    }

    fun mockKnitKekaWorkLocationPassthrough(name: String = aRandomString()): OptionData {
        val location = OptionData(name = name)
        coEvery {
            knitAdapter.getAllLocationsKekaHR(any(), any())
        } returns KekaGetAllLocationsResponse(
            succeeded = true,
            data = listOf(
                KekaLocationResponse(
                    id = location.name,
                    name = name,
                    address = KekaAddressDetails(
                        addressLine1 = aRandomString(),
                        addressLine2 = aRandomString(),
                        city = aRandomString(),
                        state = aRandomString(),
                        zip = aRandomString(),
                        countryCode = aRandomString()
                    )
                )
            )
        )
        return location
    }

    fun mockKnitSuccessFactorsWorkLocationPassthrough(name: String = aRandomString()): OptionData {
        val location = OptionData(name = name)
        coEvery {
            knitAdapter.getSuccessFactorsWorkLocations(any(), any())
        } returns GetSAPWorkLocationsResponse(
            success = true,
            data = SAPWorkLocationResponse(
                response = SAPWorkLocationResponseBody(
                    body = SAPWorkLocationNestedResult(
                        d = SAPWorkLocationResponseData(
                            results = listOf(WorkLocationData(
                                name = name,
                                externalCode = "externalCode",
                                timezone = "timezone",
                                locationGroup = "locationGroup",
                                internalCode = "internalCode",
                            ))
                        )
                    )
                )
            )
        )
        return location
    }

    fun mockKnitSuccessFactorsWorkBusinessUnitsPassthrough(name: String = aRandomString()): OptionData {
        val businessUnit = OptionData(name = name)
        coEvery {
            knitAdapter.getSuccessFactorsBusinessUnits(any(), any())
        } returns GetSAPBusinessUnitsResponse(
            success = true,
            data = SAPBusinessUnitsResponse(
                response = SAPBusinessUnitsResponseBody(
                    body = SAPBusinessUnitsNestedResult(
                        d = SAPBusinessUnitsResponseData(
                            results = listOf(BusinessUnitData(
                                name = name,
                                externalCode = "externalCode",
                                description = "description",
                            ))
                        )
                    )
                )
            )
        )
        return businessUnit
    }

    fun mockKnitPosition(position: String = aRandomString()): Position {
        val p = Position(
            positionId = aRandomString(),
            designation = aRandomString(),
            department = aRandomString(),
        )
        coEvery {
            knitAdapter.getPositionsDetails(any(), any())
        } returns GetPositionDetailResponse(
            success = true,
            data = Data(
                workShifts = null,
                positions = listOf(p)
            )
        )
        return p
    }

    fun captureKnitCreateTimeOffRequest(externalTimeoffId: String): CapturingSlot<LeaveCreateRequest> {
        val slot = slot<LeaveCreateRequest>()
        coEvery {
            knitAdapter.createLeaveRequest(any(), any(), any(), capture(slot))
        } returns LeaveCreateRequestResponse(
            success = true,
            data = mockk<LeaveCreateRequestResponseData>(relaxed = true) {
                every { leaveRequestId } returns externalTimeoffId
            },
            responseCode = 200
        )
        return slot
    }

    fun captureKnitRevokeTimeOffRequest(): Pair<CapturingSlot<String>, CapturingSlot<String>> {
        val timeoffId = slot<String>()
        val status = slot<String>()
        coEvery {
            knitAdapter.approveBambooHrTimeOffRequest(any(), any(), capture(timeoffId), capture(status), any())
        } returns BambooApproveTimeOffResponse(
            success = true,
            responseCode = 200
        )
        return Pair(timeoffId, status)
    }

    fun mockKnitCreateTimeOffApproveRequest() {
        coEvery {
            knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any())
        } returns BambooApproveTimeOffResponse(
            success = true,
            responseCode = 200
        )
    }

    fun mockKnitGetEmployeeLeaveRequest(externalTimeoffId: String, employeeId: String) {
        val startDate = Timestamp(0)
        val endDate = Timestamp(0)
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
        coEvery {
            knitAdapter.getEmployeeLeaveRequests(any(), any(), any())
        } returns LeaveRequestResponse(
            success = true,
            data = LeaveRequestListResponse(
                requests = listOf(
                    LeaveRequest(
                        employeeId = employeeId ,
                        id = externalTimeoffId,
                        startDate = startDate.convertDateToLocalDate().format(formatter),
                        endDate = endDate.convertDateToLocalDate().format(formatter),
                        requestedOn = "2021-01-01",
                        note = "note",
                        status = Status.APPROVED,
                        unit = Unit.DAYS,
                        amount = 1.0,
                        isPaid = IsPaid.TRUE
                    )
                )
            ),
            responseCode = 200
        )
    }

    fun mockKnitFields(vararg fields: FieldData) {
        every {
            knitAdapter.getAllFields(any(), any(), any())
        } returns GetAllFieldsResponse(
            success = true,
            data = FieldDataList(default = fields.toList())
        )
    }

    fun mockKnitOracleLegalEntities(
        organizationId: Long = 300100037952498,
        name: String = "Test Legal Entity",
        legislationCode: String = "US"
    ): LegalEntityItem {
        val legalEntityJson = """
        {
            "items": [
                {
                    "OrganizationId": $organizationId,
                    "Name": "$name",
                    "LegislationCode": "$legislationCode"
                }
            ]
        }
        """.trimIndent()

        coEvery {
            knitAdapter.getLegalEntitiesOracleHCM(any(), any())
        } returns GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(
                response = LegalEntityBodyWrapper(
                    body = legalEntityJson
                )
            )
        )

        return LegalEntityItem(
            OrganizationId = organizationId,
            Name = name,
            LegislationCode = legislationCode
        )
    }
}
