package e2e.inbound

import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.service.SyncService
import e2e.*
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.web.reactive.function.client.WebClient

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestDataService::class,
    ]
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class InboundEmployeeDeleteTest {
    @LocalServerPort
    private var port: Int = 0

    @Value("\${platform.knit.api-key}")
    private lateinit var knitApiKey: String

    @Autowired
    private lateinit var webClientBuilder: WebClient.Builder

    private lateinit var webClient: WebClient

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var receivedEventRepository: ReceivedEventRepository

    @Autowired
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @Autowired
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @Autowired
    private lateinit var syncService: SyncService

    val accountToken = "qwerty"
    val companyId: Long = 12345
    val contractId: Long = 12345
    val offboardingId: Long = 1
    val employeeId = "1043"
    val syncId = "sr_AUf0AaPGV3NvHeM1vENIRS"

    @BeforeEach
    fun bfe() {
        receivedEventRepository.deleteAll()
        platformContractIntegrationRepository.deleteAll()
        platformEmployeeDataRepository.deleteAll()

        // prepare test data
        val integrationId = testDataService.insertCompanyIntegration(accountToken, companyId).id
        testDataService.insertPlatformContractIntegration(integrationId!!, contractId, employeeId)
        testInternalRequestService.mockValidOffboardingRequest(offboardingId)
        testInternalRequestService.mockIsMtmIntegration()
        testInternalRequestService.mockMtmSupportedCountries("MYS")
        testInternalRequestService.mockMtmKnitIntegrationId("multiplier-integration-id")
        testDataService.insertSync(accountToken, syncId)
        testDataService.insertPlatformEmployeeData(resourceAsString("sync_events/bamboo_new_employee.json"), integrationId, employeeId)

        webClient = webClientBuilder.baseUrl("http://localhost:$port").build()
    }

    @Test
    fun `should offboard an employee after a completed RECORD_DELETE sync`() {
        val payload = resourceAsString("sync_events/delete_employee.json")
        val accountToken = "qwerty"

        val slot = testInternalRequestService.recordDeletedEvents()

        postSyncEvent(accountToken, payload, webClient, knitApiKey)

        assertSingleReceivedEvent(receivedEventRepository) { event ->
                event.integrationId shouldBe accountToken
                event.syncDataType shouldBe "employee"
                event.eventType shouldBe EventType.RECORD_DELETE
        }

        val event = receivedEventRepository.findAll().first()

        syncService.processDeletedEvent(event)

        val deletedEmployeeData = platformEmployeeDataRepository.findByEmployeeId(employeeId).first()

        slot.captured shouldBe offboardingId
        deletedEmployeeData.isDeleted.shouldBeTrue()
    }

}