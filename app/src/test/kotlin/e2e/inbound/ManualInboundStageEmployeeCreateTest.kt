package e2e.inbound

import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.model.JpaReceivedEventArchive
import com.multiplier.integration.service.FieldMappingService
import com.multiplier.integration.service.SyncService
import com.ninjasquad.springmockk.SpykBean
import e2e.assertSingleCreatedContract
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.CapturingSlot
import io.mockk.every
import io.mockk.slot
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@Tag("manual")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
    ]
)
@ActiveProfiles("test", "test-stage")
class ManualInboundStageEmployeeCreateTest {

    @Autowired
    private lateinit var jpaReceivedEventRepository: ReceivedEventRepository

    @Autowired
    private lateinit var jpaReceivedEventArchiveRepository: ReceivedEventsArchiveRepository

    @Autowired
    private lateinit var fieldMappingService: FieldMappingService

    @Autowired
    private lateinit var syncService: SyncService

    @SpykBean
    private lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @Autowired
    private lateinit var knitAdapter: KnitAdapter

    @Test
    fun `should get knit fields`() {
        val fields = knitAdapter.getAllFields(
            companyId = 866846,
            platformId = 1L,
            platformName = "bamboohr"
        )

        fields.shouldNotBeNull()
    }

    @Test
    fun `should fetch field mappings`() {
        fieldMappingService.getIntegrationLegalEntityMappings( 247)

        val mappings = fieldMappingService.getIntegrationFieldsMapping(
            entityId = 11314633,
            integrationId = 247
        )

        mappings.shouldNotBeNull()
    }

    @Test
    fun `should fetch onboarding specs`() {
        val request = BulkContractOnboardingRequest(
            // todd
//            companyId = 1518335,
//            entityId = 11314633,
            companyId = 1518335,
            entityId = 11268403,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.valueOf("COUNTRY_CODE_CHE"),
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(emptyMap())
        )

        val response = contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(request)

        response.shouldNotBeNull()
    }

    @Test
    fun `should create an employee after a completed RECORD_NEW sync`() {
        val slot = captureCreatedContracts()

        processEventById("ev_GH7EzMmABUpt5acS8s012C")

        assertSingleCreatedContract(slot) { contract ->
            contract.data.all["countryCode"] shouldBe "COUNTRY_CODE_MYS"
            contract.data.all["templateVersion"] shouldBe "1.0.2"
            contract.data.all["firstName"] shouldBe "Kristin"
            contract.data.all["gender"] shouldBe "FEMALE"
        }
    }

    private fun processEventById(eventId: String) {
        val event = jpaReceivedEventRepository.findByEventId(eventId)
        syncService.processEmployeeEvent(event, false, mutableListOf())
    }

    private fun processArchivedEvent(eventId: String) {
        val event = jpaReceivedEventArchiveRepository.findByEventId(eventId)
        val unarchived =  event.unArchive()
        syncService.processEmployeeEvent(unarchived, false, mutableListOf())
    }

    private fun captureCreatedContracts(): CapturingSlot<BulkContractOnboardingRequest> {
        val slot = slot<BulkContractOnboardingRequest>()
        every {
            contractOnboardingServiceAdapter.bulkOnboarding(capture(slot))
        } answers { callOriginal() }
        return slot
    }
}

private fun JpaReceivedEventArchive.unArchive() =
    JpaReceivedEvent(
        id = id,
        eventId = eventId,
        syncId = syncId,
        integrationId = integrationId,
        eventType = eventType,
        syncDataType = syncDataType,
        errors = errors,
        identifiervalue = identifiervalue,
        receivedTime = receivedTime,
        data = data,
        confirmedByUser = confirmedByUser,
        processed = false,
        isEntityEnabled = isEntityEnabled,
        entityId = entityId,
        entityCountry = entityCountry,
    )

