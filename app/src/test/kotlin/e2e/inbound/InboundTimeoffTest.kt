package e2e.inbound

import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomInt
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.aRandomString
import com.multiplier.integration.repository.*
import com.multiplier.integration.service.TimeoffService
import e2e.*
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.web.reactive.function.client.WebClient

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestDataService::class,
    ]
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class InboundTimeoffTest {

    @LocalServerPort
    private var port: Int = 0

    @Value("\${platform.knit.api-key}")
    private lateinit var knitApiKey: String

    @Autowired
    private lateinit var webClientBuilder: WebClient.Builder

    private lateinit var webClient: WebClient

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var jpaReceivedEventRepository: ReceivedEventRepository

    @Autowired
    private lateinit var timeoffEventRepository: TimeoffEventRepository

    @Autowired
    private lateinit var timeoffService: TimeoffService

    @Autowired
    private lateinit var platformTimeoffIntegrationRepository: PlatformTimeoffIntegrationRepository

    @Autowired
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @Autowired
    private lateinit var leaveTypeMappingRepository: LeaveTypeMappingRepository

    @Autowired
    private lateinit var companyIntegrationRepository: PlatformTimeoffIntegrationRepository


    @BeforeEach
    fun bfe() {
        companyIntegrationRepository.deleteAll()
        jpaReceivedEventRepository.deleteAll()
        timeoffEventRepository.deleteAll()
        platformContractIntegrationRepository.deleteAll()
        platformTimeoffIntegrationRepository.deleteAll()
        leaveTypeMappingRepository.deleteAll()

        webClient = webClientBuilder.baseUrl("http://localhost:$port").build()
    }

    // Disable because of timeoff sync is temporarily turned off
    //    @Test
    fun `should create timeoff internally after a completed RECORD_NEW sync with leaveRequests`() {
        val contractId = aRandomLong()
        val companyId = aRandomLong()
        val employeeId = aRandomString()
        val internalTimeoffTypeId = aRandomString()
        val externalTimeoffTypeId = aRandomInt()
        val externalTimeoffId = aRandomString()
        val payload = interpolate(
            "sync_events/event_with_timeoffs.json",
            mapOf(
                "employeeId" to employeeId,
                "timeoffStatus" to "APPROVED",
                "externalTimeoffId" to externalTimeoffId,
                "externalTimeoffTypeId" to externalTimeoffTypeId.toString(),
            )
        )
        val integration = testDataService.insertCompanyIntegration(companyId = companyId)

        testInternalRequestService.mockCreateInboundTimeOffAdapterRequest(internalTimeoffTypeId)
        testDataService.insertTimeoffTypeMapping(integration.id!!, companyId, internalTimeoffTypeId, externalTimeoffTypeId)
        testDataService.insertPlatformContractIntegration(integration.id!!, contractId, employeeId)

        postSyncEvent(integration.accountToken, payload, webClient, knitApiKey)

        timeoffService.processPendingTimeoffEvents()

        val timeOffIntegrations = platformTimeoffIntegrationRepository.findAll()

        timeOffIntegrations shouldHaveSize 1
        timeOffIntegrations.first().employeeId shouldBe employeeId
        timeOffIntegrations.first().contractId shouldBe contractId
        timeOffIntegrations.first().employeeId shouldBe employeeId

        timeoffEventRepository.findAll() shouldHaveSize 1
    }

}

