package e2e.inbound

import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomInt
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.aRandomString
import com.multiplier.integration.repository.*
import e2e.*
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.web.reactive.function.client.WebClient

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestDataService::class,
    ]
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
class InboundTimeoffRevokeTest {

    @LocalServerPort
    private var port: Int = 0

    @Value("\${platform.knit.api-key}")
    private lateinit var knitApiKey: String

    @Autowired
    private lateinit var webClientBuilder: WebClient.Builder

    private lateinit var webClient: WebClient

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var receivedEventRepository: ReceivedEventRepository

    @Autowired
    private lateinit var timeoffEventRepository: TimeoffEventRepository

    @Autowired
    private lateinit var platformTimeoffIntegrationRepository: PlatformTimeoffIntegrationRepository

    @Autowired
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @Autowired
    private lateinit var leaveTypeMappingRepository: LeaveTypeMappingRepository

    @Autowired
    private lateinit var companyIntegrationRepository: PlatformTimeoffIntegrationRepository

    @BeforeEach
    fun bfe() {
        companyIntegrationRepository.deleteAll()
        receivedEventRepository.deleteAll()
        timeoffEventRepository.deleteAll()
        platformContractIntegrationRepository.deleteAll()
        platformTimeoffIntegrationRepository.deleteAll()
        leaveTypeMappingRepository.deleteAll()

        webClient = webClientBuilder.baseUrl("http://localhost:$port").build()
    }

    // Disable because of timeoff sync is temporarily turned off
    //    @Test
    fun `should revoke timeoff internally after a completed RECORD_NEW sync with revoked leaveRequests`() {
        val contractId = aRandomLong()
        val companyId = aRandomLong()
        val employeeId = aRandomString()
        val internalTimeoffTypeId = aRandomString()
        val externalTimeoffTypeId = aRandomInt()
        val externalTimeoffId = aRandomString()
        val internalTimeoffId = aRandomLong()
        val payload = interpolate("sync_events/event_with_timeoffs.json",
            mapOf(
                "employeeId" to employeeId,
                "timeoffStatus" to "DRAFT",
                "externalTimeoffId" to externalTimeoffId,
                "externalTimeoffTypeId" to externalTimeoffTypeId.toString(),
            )
        )
        val integration = testDataService.insertCompanyIntegration(companyId = companyId)

        val slot = testInternalRequestService.mockRevokeTimeOffAdapterRequest()
        testDataService.insertTimeoffIntegration(integration.id!!, contractId, internalTimeoffId, externalTimeoffId, employeeId)
        testDataService.insertTimeoffTypeMapping(integration.id!!, companyId, internalTimeoffTypeId, externalTimeoffTypeId)
        testDataService.insertPlatformContractIntegration(integration.id!!, contractId, employeeId)
        testDataService.insertTimeoffEvent(integration.id!!, employeeId, externalId = externalTimeoffId)

        val beforeTestTimeoff = platformTimeoffIntegrationRepository.findByInternalTimeoffId(internalTimeoffId)
        beforeTestTimeoff.shouldNotBeNull()

        postSyncEvent(integration.accountToken, payload, webClient, knitApiKey)

        slot.captured.companyId shouldBe companyId
        slot.captured.externalTimeOffIdsList shouldHaveSize 1
        slot.captured.externalTimeOffIdsList shouldContain externalTimeoffId
        val timeOffIntegration = platformTimeoffIntegrationRepository.findByInternalTimeoffId(internalTimeoffId)
        timeOffIntegration.shouldBeNull()
    }

}

