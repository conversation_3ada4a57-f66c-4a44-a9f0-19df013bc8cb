package e2e.fieldmappings

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.model.JpaFieldsMapping
import com.multiplier.integration.repository.model.LegalMappingStatus
import com.multiplier.integration.service.FieldMappingService
import com.multiplier.integration.service.fieldmappings.EmployeeDataFieldMapper
import e2e.KafkaContainerInitializer
import e2e.PostgreSQLContainerInitializer
import e2e.TestDataService
import e2e.TestExternalRequestService
import e2e.TestInternalRequestService
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestExternalRequestService::class,
        TestDataService::class,
    ]
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class FieldMappingsTest {

    @Autowired
    private lateinit var fieldMappingService: FieldMappingService

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var internalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var externalRequestService: TestExternalRequestService

    @Autowired
    private lateinit var fieldsMappingRepository: FieldsMappingRepository

    @Autowired
    private lateinit var employeeDataFieldMapper: EmployeeDataFieldMapper

    @Test
    fun `should get an unmapped field`() {
        val data = setup()

        internalRequestService.mockDataSpecs(
            BulkOnboardDataSpec.newBuilder()
                .setKey("someCustomField")
                .setLabel("Some Custom Field")
                .setRequired(true)
                .build()
        )

        val mappings = fieldMappingService.getIntegrationFieldsMapping(
            entityId = data.entityId,
            integrationId = data.integrationId,
        )

        mappings.fieldsMapping shouldHaveSize 1
        mappings.fieldsMapping.first().originField shouldBe "someCustomField"
        mappings.fieldsMapping.first().mappedField.shouldBeNull()
    }

    private operator fun List<JpaFieldsMapping>.get(key: String): JpaFieldsMapping =
        this.find { it.originField == key } ?: throw IllegalArgumentException("No field mapping with key $key exists")

    private operator fun List<JpaFieldsMapping>.set(key: String, keyAndValue: Pair<String, String>) {
        this[key].mappedField = keyAndValue.first
        this[key].mappedFieldLabel = keyAndValue.second
    }

    private data class TestData(
        val companyId: Long,
        val entityId: Long,
        val integrationId: Long,
    )

    private fun setup(): TestData {
        val companyId = aRandomLong()
        val entityId = aRandomLong()

        val integration = testDataService.insertCompanyIntegration(
            companyId = companyId
        )

        externalRequestService.mockKnitFields(FieldData())

        testDataService.insertLegalEntityMapping(
            integrationId = integration.id!!,
            legalEntityId = entityId,
            companyId = companyId,
            legalMappingStatus = LegalMappingStatus.UNMAPPED
        )

        internalRequestService.mockLegalEntity(
            companyId = companyId,
            country = "CHE",
            entityId = entityId
        )

        return TestData(
            companyId = companyId,
            entityId = entityId,
            integrationId = integration.id!!
        )
    }


}