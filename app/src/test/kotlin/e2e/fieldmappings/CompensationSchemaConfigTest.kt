package e2e.fieldmappings

import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.repository.CompensationSchemaConfigRepository
import com.multiplier.integration.repository.model.CompensationSchemaConfig
import com.multiplier.integration.repository.model.EmployeeConfig
import com.multiplier.integration.repository.model.JpaCompensationSchemaConfig
import com.multiplier.integration.service.fieldmappings.CompensationSchemaConfigService
import e2e.KafkaContainerInitializer
import e2e.PostgreSQLContainerInitializer
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [CustomerIntegrationApplication::class]
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class CompensationSchemaConfigTest {

    @Autowired
    private lateinit var repository: CompensationSchemaConfigRepository

    @Autowired
    private lateinit var service: CompensationSchemaConfigService

    @BeforeEach
    fun setup() {
        repository.deleteAll()
    }

    @Test
    @Transactional
    fun `should apply the compensation schema config`() {
        val entityId = 123L
        val today = LocalDate.now()

        val config = CompensationSchemaConfig(
            defaultPayScheduleName = "Default-Schedule",
            employeeConfigs = listOf(
                EmployeeConfig(
                    employeeId = "emp1",
                    payScheduleName = "Employee-Schedule",
                    compensationStartDate = today
                )
            )
        )

        repository.save(
            JpaCompensationSchemaConfig(entityId = entityId, config = config)
        )

        val withEmployeeConfig = service.groupEmployeeData(
            entityId = entityId,
            input = mapOf(
                "employeeId" to "emp1",
                "name" to "John",
                "COMP_ITEM:BASE_SALARY" to "10000",
                "COMP_ATTR:BILLING_FREQUENCY" to "ANNUALLY",
                "COMP_ATTR:CURRENCY" to "USD",
            )
        )

        val withoutEmployeeConfig = service.groupEmployeeData(
            entityId = entityId,
            input = mapOf(
                "employeeId" to "emp2",
                "name" to "John",
                "COMP_ITEM:BASE_SALARY" to "10000",
                "COMP_ATTR:BILLING_FREQUENCY" to "ANNUALLY",
                "COMP_ATTR:CURRENCY" to "USD",
            )
        )

        withEmployeeConfig.all["PAY_SCHEDULE_NAME"] shouldBe "Employee-Schedule"
        withEmployeeConfig.all["START_DATE"] shouldBe today.format(DateTimeFormatter.ISO_DATE)

        withoutEmployeeConfig.all["PAY_SCHEDULE_NAME"] shouldBe "Default-Schedule"
    }
}