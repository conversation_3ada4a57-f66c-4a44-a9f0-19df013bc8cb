package e2e

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.JpaReceivedEvent
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldHaveSize
import io.mockk.CapturingSlot
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.WebClient
import java.time.LocalDate
import java.util.*
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

fun resourceAsString(fileName: String) = object {}.javaClass.classLoader.getResource(fileName)?.readText()
    ?: throw IllegalArgumentException("File not found")


fun postSyncEvent(
    integrationId: String,
    payload: String,
    webClient: WebClient,
    knitApiKey: String,
) {
    val objectMapper = jacksonObjectMapper()
    webClient.post()
        .uri("/sync/consume-sync-event")
        .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        .header("X-Knit-Signature", objectMapper.readTree(payload).toString().sign(knitApiKey))
        .header("X-Knit-Integration-Id", integrationId)
        .bodyValue(payload)
        .retrieve()
        .bodyToMono(String::class.java)
        .block()
}

private fun String.sign(knitApiKey: String): String {
    val mac = Mac.getInstance("HmacSHA256")
    val secretKeySpec = SecretKeySpec(knitApiKey.toByteArray(Charsets.UTF_8), "HmacSHA256")
    mac.init(secretKeySpec)

    val signatureBytes = mac.doFinal(this.toByteArray(Charsets.UTF_8))
    return Base64.getUrlEncoder().withoutPadding().encodeToString(signatureBytes)
}

fun LocalDate.toGrpcDate(): com.google.type.Date {
    return com.google.type.Date.newBuilder()
        .setYear(this.year)
        .setMonth(this.monthValue)
        .setDay(this.dayOfMonth)
        .build()
}

inline fun <reified T> T.toJson(): String {
    val mapper = jacksonObjectMapper()
    return mapper.writeValueAsString(this)
}

fun interpolate(file: String, values: Map<String, String>): String {
    var text = resourceAsString(file)

    values.forEach { (key, value) ->
        text = text.replace("{$key}", value)
    }

    return text.replace(Regex("\"\\{[^{}]+}\""), "null")
}


fun assertSingleReceivedEvent(
    receivedEventRepository: ReceivedEventRepository,
    fn: (event: JpaReceivedEvent) -> Unit
) {
    val events = receivedEventRepository.findAll()
    events shouldHaveSize 1
    fn(events.first())
}

fun assertSingleCreatedContract(
    slot: CapturingSlot<BulkContractOnboardingRequest>,
    fn: (event: BulkContractOnboardingRequest) -> Unit
) {
    slot.isCaptured.shouldBeTrue()
    fn(slot.captured)
}