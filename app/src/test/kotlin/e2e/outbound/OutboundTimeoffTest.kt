package e2e.outbound

import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomInt
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.aRandomString
import com.multiplier.integration.repository.LeaveTypeMappingRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.scheduler.EORTimeOffScheduler
import com.multiplier.timeoff.schema.GrpcTimeOffStatus
import e2e.KafkaContainerInitializer
import e2e.PostgreSQLContainerInitializer
import e2e.TestDataService
import e2e.TestExternalRequestService
import e2e.TestInternalRequestService
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestExternalRequestService::class,
        TestDataService::class,
    ],
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class OutboundTimeoffTest {
    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var testExternalRequestService: TestExternalRequestService

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @Autowired
    private lateinit var platformTimeoffIntegrationRepository: PlatformTimeoffIntegrationRepository

    @Autowired
    private lateinit var eorTimeOffScheduler: EORTimeOffScheduler

    @Autowired
    private lateinit var leaveTypeMappingRepository: LeaveTypeMappingRepository

    @BeforeEach
    fun bfe() {
        platformContractIntegrationRepository.deleteAll()
        platformTimeoffIntegrationRepository.deleteAll()
        leaveTypeMappingRepository.deleteAll()
    }

    @Test
    fun `should create timeoff and add to timeoff integration`() {
        runBlocking {
            val contractId = aRandomLong()
            val companyId = aRandomLong()
            val employeeId = aRandomString()
            val internalTimeoffTypeId = aRandomString()
            val externalTimeoffTypeId = aRandomInt()
            val externalTimeoffId = aRandomString()
            val internalTimeoffId = aRandomLong()

            val integration = testDataService.insertCompanyIntegration(companyId = companyId)
            testDataService.insertTimeoffTypeMapping(integration.id!!, companyId, internalTimeoffTypeId, externalTimeoffTypeId)
            testDataService.insertPlatformContractIntegration(integration.id!!, contractId, employeeId)
            testInternalRequestService.mockInternalTimeoff(internalTimeoffTypeId, internalTimeoffId, contractId, GrpcTimeOffStatus.APPROVED)
            val slot = testExternalRequestService.captureKnitCreateTimeOffRequest(externalTimeoffId)
            testExternalRequestService.mockKnitCreateTimeOffApproveRequest()
            testExternalRequestService.mockKnitGetEmployeeLeaveRequest(externalTimeoffId, employeeId)

            eorTimeOffScheduler.processEORTimeOffs()

            val timeOffIntegration = platformTimeoffIntegrationRepository.findByInternalTimeoffId(internalTimeoffId)

            slot.captured.leaveTypeId shouldBe externalTimeoffTypeId.toString()
            timeOffIntegration!!.externalTimeoffId shouldBe externalTimeoffId
            timeOffIntegration.integrationId shouldBe integration.id
            timeOffIntegration.contractId shouldBe contractId
            timeOffIntegration.employeeId shouldBe employeeId
        }
    }

    @Test
    fun `should revoke timeoff and remove from timeoff integration`() = runBlocking {
        val contractId = aRandomLong()
        val companyId = aRandomLong()
        val employeeId = aRandomString()
        val internalTimeoffTypeId = aRandomString()
        val externalTimeoffId = aRandomString()
        val internalTimeoffId = aRandomLong()

        val integration = testDataService.insertCompanyIntegration(companyId = companyId)
        testDataService.insertTimeoffIntegration(integration.id!!, contractId, internalTimeoffId, externalTimeoffId, employeeId)
        testDataService.insertPlatformContractIntegration(integration.id!!, contractId, employeeId)
        testInternalRequestService.mockInternalTimeoff(internalTimeoffTypeId, internalTimeoffId, contractId, GrpcTimeOffStatus.DRAFT)
        val slots = testExternalRequestService.captureKnitRevokeTimeOffRequest()

        val beforeTestTimeoff = platformTimeoffIntegrationRepository.findByInternalTimeoffId(internalTimeoffId)
        beforeTestTimeoff.shouldNotBeNull()

        eorTimeOffScheduler.processEORTimeOffs()

        slots.first.captured shouldBe externalTimeoffId
        slots.second.captured shouldBe "canceled"
        val timeOffIntegration = platformTimeoffIntegrationRepository.findByInternalTimeoffId(internalTimeoffId)
        timeOffIntegration.shouldBeNull()
    }
}