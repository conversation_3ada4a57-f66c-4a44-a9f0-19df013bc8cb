package e2e.outbound

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.grpc.common.toLocalDate
import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomEmail
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.WorkAddress
import com.multiplier.integration.adapter.api.resources.knit.Address
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.scheduler.EventProcessingScheduler
import com.multiplier.integration.utils.mapToGoogleDate
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.integration.utils.toYYYYMMDD
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.dateOfBirthOrNull
import e2e.*
import com.neovisionaries.i18n.CountryCode
import e2e.PostgreSQLContainerInitializer
import e2e.TestDataService
import e2e.TestExternalRequestService
import e2e.TestInternalRequestService
import e2e.TestPlatform
import e2e.anEventLog
import e2e.toJson
import io.kotest.assertions.json.shouldEqualJson
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.nulls.shouldNotBeNull
import io.mockk.CapturingSlot
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration
import java.time.LocalTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestExternalRequestService::class,
        TestDataService::class,
    ],
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class OutboundEmployeeCreateTest {
    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var testExternalRequestService: TestExternalRequestService

    @Autowired
    private lateinit var eventLogRepository: EventLogRepository

    @Autowired
    private lateinit var eventProcessingScheduler: EventProcessingScheduler

    @Autowired
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @BeforeEach
    fun bfe() {
        testInternalRequestService.mockKnitV2DataModelEnabled()
        every {
            testInternalRequestService.contractServiceAdapter.updateContractEmployeeId(any(), any())
        } returns ContractOuterClass.Contract.newBuilder().build()

        eventLogRepository.deleteAll()
    }

    @Test
    fun `should create a new external employee on bamboo`() {
        val bambooLocation = testExternalRequestService.mockKnitBambooWorkLocationPassthrough()
        val data = commonSetup(TestPlatform.BAMBOO)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.contract.workEmail,
            personalEmails = listOf(data.member.emailsList.first().email),
            employment = Employment(
                positionId = data.position.positionId!!,
                designation = data.position.designation!!,
            ),
            workAddress = WorkAddress(
                id = bambooLocation.name
            ),
            startDate = data.contract.startOn.toYYYYMMDD(),
            employmentType = "Full-Time",
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on zoho`() {
        val data = commonSetup(TestPlatform.ZOHO)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.contract.workEmail,
            personalEmails = listOf(data.member.emailsList.first().email),
            employment = Employment(
                positionId = data.position.positionId!!,
                designation = data.position.designation!!,
            ),
            startDate = data.contract.startOn.toYYYYMMDD(),
            employmentType = "Full-Time",
            maritalStatus = "SINGLE",
            gender = "FEMALE",
            metadata = mapOf(
                "employeeId" to data.contract.employeeId
            )
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    fun com.google.type.Date.toKekaDate(): String {
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00.00000000'Z'")
        return this.toLocalDate().format(formatter)
    }

    @Test
    fun `should create a new external employee on KekaHR`() {
        val kekaLocation = testExternalRequestService.mockKnitKekaWorkLocationPassthrough()
        val data = commonSetup(TestPlatform.KEKA)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.contract.workEmail,
            personalEmails = listOf(data.member.emailsList.first().email),
            workAddress = WorkAddress(
                id = kekaLocation.name
            ),
            startDate = data.contract.startOn.toKekaDate(),
            birthDate = data.member.dateOfBirth.toLocalDate().mapToGoogleDate().toKekaDate(),
            gender = data.member.genderValue.toString(),
            employeeNumber = data.contract.id.toString(),
            location = kekaLocation.name
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on Personio`() {
        val data = commonSetup(TestPlatform.PERSONIO)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.member.emailsList.firstOrNull { it.type == "primary" }?.email,
            employment = Employment(
                positionId = data.position.designation!!,
                designation = data.position.designation!!,
            ),
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on Hibob`() {
        val locations = testExternalRequestService.mockKnitHibobWorkLocationPassthrough("Test")
        val data = commonSetup(TestPlatform.HIBOB)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.contract.workEmail,
            personalEmails = listOf(data.member.emailsList.first().email),
            employeeNumber = null,
            employment = Employment(
                positionId = data.position.positionId!!,
                designation = data.position.designation!!,
            ),
            workAddress = WorkAddress(
                id = locations.name
            ),
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on Successfactors`() {
        val locations = testExternalRequestService.mockKnitSuccessFactorsWorkLocationPassthrough()
        val businessUnits = testExternalRequestService.mockKnitSuccessFactorsWorkBusinessUnitsPassthrough()
        val data = commonSetup(TestPlatform.SUCCESSFACTORS)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.member.emailsList.first().email,
            employment = Employment(
                positionId = data.position.positionId!!,
                designation = data.position.designation!!,
            ),
            workAddress = WorkAddress(
                id = locations.name
            ),
            startDate = "/Date(${data.contract.startOn.toLocalDate().toEpochSecond(LocalTime.MIN, ZoneOffset.UTC)})",
            employeeNumber = data.member.id.toString(),
            businessUnit = businessUnits.name
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on ADP WorkForceNow`() {
        val data = commonSetup(TestPlatform.WORKFORCENOW)

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            employment = Employment(
                positionId = data.position.positionId,
                designation = data.position.designation
            ),
            workEmail = data.contract.workEmail,
            personalEmails = listOf(data.member.emailsList
                .firstOrNull { it.type == "primary" }?.email.toString()),
            startDate = data.contract.startOn.toYYYYMMDD(),
            birthDate = data.member.dateOfBirthOrNull?.toString(),
            maritalStatus = data.member.martialStatus?.toString(),
            gender = data.member.gender.toString(),
            employmentType = data.contract.type.toString(),
            workAddress = WorkAddress(
                country = data.contract.country?.toString(),
            ),
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on Oracle HCM`() {
        val legalEntity = testExternalRequestService.mockKnitOracleLegalEntities()
        val data = commonSetup(TestPlatform.ORACLE)

        val firstMemberAddress = data.member.addressesList.firstOrNull()

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.contract.workEmail,
            startDate = data.contract.startOn.toYYYYMMDD(),
            birthDate = data.member.dateOfBirth.toLocalDate().toString(),
            employeeNumber = data.contract.id.toString(),
            employment = Employment(
                positionId = data.position.positionId!!,
            ),
            companyId = legalEntity.OrganizationId.toString(),
            presentAddress = Address(
                addressLine1 = firstMemberAddress?.line1,
                addressLine2 = firstMemberAddress?.line2,
                city = firstMemberAddress?.city,
                state = firstMemberAddress?.state,
                country = CountryCode.getByAlpha3Code(firstMemberAddress?.country?.name)?.alpha2,
                zipCode = firstMemberAddress?.postalCode
            )
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should create a new external employee on Paychex`() {
        val data = commonSetup(TestPlatform.PAYCHEX)

        val firstMemberAddress = data.member.addressesList.firstOrNull()

        val expected = CreateEmployeeRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            workEmail = data.contract.workEmail,
            personalEmails = listOf(data.member.emailsList.firstOrNull { it.type == "primary" }?.email ?: ""),
            employment = Employment(
                positionId = data.position.positionId!!,
                designation = data.position.designation!!
            ),
            startDate = data.contract.startOn.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")),
            birthDate = data.member.dateOfBirth.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")),
            maritalStatus = data.member.martialStatus?.toString(),
            gender = data.member.gender.toString(),
            workAddress = WorkAddress(
                addressLine1 = firstMemberAddress?.line1 ?: "",
                city = firstMemberAddress?.city ?: "",
                state = data.contract.countryStateCode,
                country = data.contract.country,
                zipCode = firstMemberAddress?.postalCode ?: ""
            ),
            presentAddress = if (firstMemberAddress != null) Address(
                addressLine1 = firstMemberAddress.line1 ?: "",
                addressLine2 = firstMemberAddress.line2,
                state = null,
                country = CountryCode.getByAlpha3Code(firstMemberAddress.country?.name).alpha2,
                zipCode = firstMemberAddress.postalCode,
                city = firstMemberAddress.city,
            ) else null
        ).toJson()

        assertOutgoingPayload(data.createdEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should map to external employee if present`() {
        val contractId = aRandomLong()
        val memberId = aRandomLong()
        val companyId = aRandomLong()
        val workEmail = aRandomEmail()

        testDataService.insertCompanyIntegration(companyId = companyId)
        testExternalRequestService.mockKnitEmployeeDirectory(workEmail = workEmail)
        val slot = testExternalRequestService.recordKnitCreateEmployeeRecord()

        val event = anEventLog(
            contractId = contractId,
            eventType = EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
            status = EventStatus.TO_BE_PROCESSED,
        )

        testInternalRequestService.mockGetMultiplierContract(
            contractId = contractId,
            companyId = companyId,
            memberId = memberId,
            status = ContractOuterClass.ContractStatus.ACTIVE,
            workEmail = workEmail
        )

        testInternalRequestService.mockGetMultiplierMember(
            memberId = memberId,
        )

        eventLogRepository.save(event)
        eventProcessingScheduler.processEvents()

        val contractIntegration = platformContractIntegrationRepository.findByContractId(contractId)

        slot.isCaptured.shouldBeFalse()
        contractIntegration.shouldNotBeNull()
    }

    fun assertOutgoingPayload(slot: CapturingSlot<CreateEmployeeRequest>, fn: (payload: String) -> Unit) {
        val mapper = jacksonObjectMapper()
        val actualAsString = mapper.writeValueAsString(slot.captured)

        fn(actualAsString)
    }

    data class OutboundTestData(
        val position: Position,
        val createdEmployee: CapturingSlot<CreateEmployeeRequest>,
        val contract: ContractOuterClass.Contract,
        val member: Member,
    )

    private fun commonSetup(platform: TestPlatform): OutboundTestData {
        val contractId = aRandomLong()
        val memberId = if (platform == TestPlatform.SUCCESSFACTORS) 50000L else aRandomLong()
        val companyId = aRandomLong()

        testDataService.insertCompanyIntegration(companyId = companyId, testPlatform = platform)
        val knitPosition = testExternalRequestService.mockKnitPosition()
        val slot = testExternalRequestService.recordKnitCreateEmployeeRecord()

        val event = anEventLog(
            contractId = contractId,
            eventType = EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
            status = EventStatus.TO_BE_PROCESSED,
        )

        val contract = testInternalRequestService.mockGetMultiplierContract(
            contractId = contractId,
            companyId = companyId,
            memberId = memberId,
            status = ContractOuterClass.ContractStatus.ACTIVE,
            position = knitPosition.designation!!,
        )

        val member = testInternalRequestService.mockGetMultiplierMember(
            memberId = memberId,
        )

        eventLogRepository.save(event)
        eventProcessingScheduler.processEvents()

        return OutboundTestData(
            position = knitPosition,
            createdEmployee = slot,
            contract = contract,
            member = member
        )
    }
}

