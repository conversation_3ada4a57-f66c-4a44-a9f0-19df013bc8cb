package e2e.outbound

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.Constants
import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.adapter.api.resources.knit.Employment
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsRequest
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.scheduler.EventProcessingScheduler
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.member.schema.Member
import e2e.KafkaContainerInitializer
import e2e.PostgreSQLContainerInitializer
import e2e.TestDataService
import e2e.TestExternalRequestService
import e2e.TestInternalRequestService
import e2e.TestPlatform
import e2e.anEventLog
import e2e.resourceAsString
import e2e.toJson
import io.kotest.assertions.json.shouldEqualJson
import io.mockk.CapturingSlot
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration


@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestExternalRequestService::class,
        TestDataService::class,
    ],
)
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class, KafkaContainerInitializer::class])
@ActiveProfiles("test")
class OutboundEmployeeUpdateTest {

    @Autowired
    private lateinit var testDataService: TestDataService

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var testExternalRequestService: TestExternalRequestService

    @Autowired
    private lateinit var eventLogRepository: EventLogRepository

    @Autowired
    private lateinit var eventProcessingScheduler: EventProcessingScheduler

    @BeforeEach
    fun bfe() {
        testInternalRequestService.mockKnitV2DataModelEnabled() //can be changed to beforeAll test instead of beforeEach
        every {
            testInternalRequestService.contractServiceAdapter.updateContractEmployeeId(any(), any())
        } returns ContractOuterClass.Contract.newBuilder().build()

        eventLogRepository.deleteAll()
    }

    fun assertOutgoingPayload(slot: CapturingSlot<UpdateEmployeeDetailsRequest>, fn: (payload: String) -> Unit) {
        val mapper = jacksonObjectMapper()
        val actualAsString = mapper.writeValueAsString(slot.captured)

        fn(actualAsString)
    }

    data class OutboundTestData(
        val employeeId: String,
        val position: Position,
        val updatedEmployee: CapturingSlot<UpdateEmployeeDetailsRequest>,
        val contract: ContractOuterClass.Contract,
        val member: Member,
    )

    private fun commonSetup(platform: TestPlatform): OutboundTestData {
        val contractId = aRandomLong()
        val memberId = aRandomLong()
        val companyId = aRandomLong()
        val employeeId = aRandomLong()

        val integrationId = testDataService.insertCompanyIntegration(testPlatform = platform, companyId = companyId).id
        testDataService.insertPlatformContractIntegration(integrationId!!, contractId, employeeId.toString(), platform)
        testDataService.insertPlatformEmployeeData(
            resourceAsString("outbound/create_employee.json"),
            integrationId,
            employeeId.toString(),
            Constants.EmployeeOrigin.INTERNAL
        )

        testDataService.insertCompanyIntegration(companyId = companyId, testPlatform = platform)
        val knitPosition = testExternalRequestService.mockKnitPosition()
        val slot = testExternalRequestService.recordKnitUpdateEmployeeRecord()


        val payload = "event {\n" +
                "  member_id: $memberId\n" +
                "  contract_id: $contractId\n" +
                "  status: ACTIVE\n" +
                "}\n"

        val event = anEventLog(
            contractId = contractId,
            eventType = EventType.INCOMING_MEMBER_BASIC_DETAILS_UPDATED,
            status = EventStatus.TO_BE_PROCESSED,
            eventPayload = payload
        )

        val contract = testInternalRequestService.mockGetMultiplierContract(
            contractId = contractId,
            companyId = companyId,
            memberId = memberId,
            status = ContractOuterClass.ContractStatus.ACTIVE,
            position = knitPosition.designation!!,
        )

        val member = testInternalRequestService.mockGetMultiplierMember(
            memberId = memberId,
        )

        eventLogRepository.save(event)
        eventProcessingScheduler.processEvents()

        return OutboundTestData(
            employeeId = employeeId.toString(),
            position = knitPosition,
            updatedEmployee = slot,
            contract = contract,
            member = member
        )
    }

    @Test
    fun `should update a internal employee on Personio`() {
        val data = commonSetup(TestPlatform.PERSONIO)

        val expected = UpdateEmployeeDetailsRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            employeeId = data.employeeId,
            employment = Employment(
                positionId = data.position.designation!!,
                designation = data.position.designation!!,
            ),
        ).toJson()

        assertOutgoingPayload(data.updatedEmployee) {
            it shouldEqualJson expected
        }
    }

    @Test
    fun `should update a internal employee on ADP WorkForceNow`() {
        val data = commonSetup(TestPlatform.WORKFORCENOW)
        val primaryEmail = data.member.emailsList
            .firstOrNull { it.type == "primary" }
            ?.email ?: throw IntegrationIllegalStateException("Primary email not found for member.")

        val expected = UpdateEmployeeDetailsRequest(
            firstName = data.member.firstName,
            lastName = data.member.lastName,
            employeeId = data.employeeId,
            employment = Employment(
                positionId = data.position.positionId!!,
                designation = data.position.designation!!,
            ),
            workEmail = data.contract.workEmail.ifBlank { primaryEmail },
            personalEmails = listOf(primaryEmail)
        ).toJson()

        assertOutgoingPayload(data.updatedEmployee) {
            it shouldEqualJson expected
        }
    }
}