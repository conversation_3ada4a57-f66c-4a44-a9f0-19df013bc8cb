package e2e.outbound

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.CustomerIntegrationApplication
import com.multiplier.integration.aRandomLong
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.scheduler.EventProcessingScheduler
import com.multiplier.integration.utils.toYYYYMMDD
import e2e.TestExternalRequestService
import e2e.TestInternalRequestService
import e2e.TestDataService
import e2e.anEventLog
import e2e.interpolate
import io.kotest.assertions.json.shouldEqualJson
import io.mockk.CapturingSlot
import io.mockk.every
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import java.time.LocalDate

@Tag("manual")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [
        CustomerIntegrationApplication::class,
        TestInternalRequestService::class,
        TestExternalRequestService::class,
        TestDataService::class,
    ],
)
@ActiveProfiles("test", "test-stage")
class ManualOutboundStageEmployeeCreateTest {

    @Autowired
    private lateinit var testInternalRequestService: TestInternalRequestService

    @Autowired
    private lateinit var testExternalRequestService: TestExternalRequestService

    @Autowired
    private lateinit var eventLogRepository: EventLogRepository

    @Autowired
    private lateinit var eventProcessingScheduler: EventProcessingScheduler

    @BeforeEach
    fun bfe() {
        testInternalRequestService.mockKnitV2DataModelEnabled()
        every {
            testInternalRequestService.contractServiceAdapter.updateContractEmployeeId(any(), any())
        } returns ContractOuterClass.Contract.newBuilder().build()
    }

    @Test
    fun `should create an employee on some platform`() {
        val contractId = aRandomLong()
        val memberId = aRandomLong()
        val companyId = 502092L

        val slot = testExternalRequestService.useRealKnitCreateEmployee()

        val event = anEventLog(
            contractId = contractId,
            eventType = EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
            status = EventStatus.TO_BE_PROCESSED,
        )

        val contract = testInternalRequestService.mockGetMultiplierContract(
            contractId = contractId,
            companyId = companyId,
            memberId = memberId,
            status = ContractOuterClass.ContractStatus.ACTIVE,
            position = "Manager",
            startOn = LocalDate.now().minusDays(7),
            employeeId = "E12345"
        )

        val member = testInternalRequestService.mockGetMultiplierMember(
            memberId = memberId,
            firstName = "Jon",
            lastName = "Snow",
        )

        eventLogRepository.save(event)
        eventProcessingScheduler.processEvents()

        val expected = interpolate(
            "outbound/create_employee.json", mapOf(
                "firstName" to member.firstName,
                "lastName" to member.lastName,
                "workEmail" to contract.workEmail,
                "personalEmail" to member.emailsList.first().email,
                "positionId" to "todo",
                "designation" to contract.position,
                "startDate" to contract.startOn.toYYYYMMDD(),
                "employmentType" to "Full-Time"
            )
        )

        assertOutgoingPayload(slot) {
            it shouldEqualJson expected
        }
    }

    fun assertOutgoingPayload(slot: CapturingSlot<CreateEmployeeRequest>, fn: (payload: String) -> Unit) {
        val mapper = jacksonObjectMapper()
        val actualAsString = mapper.writeValueAsString(slot.captured)

        fn(actualAsString)
    }

}

