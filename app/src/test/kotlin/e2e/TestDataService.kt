package e2e

import com.fasterxml.jackson.core.type.TypeReference
import com.multiplier.integration.Constants.EmployeeOrigin
import com.multiplier.integration.aRandomString
import com.multiplier.integration.repository.*
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.sync.DataMapper.Companion.objectMapper
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service
import java.time.LocalDateTime

enum class TestPlatform(val id: Long) {
    BAMBOO(1L),
    ZOHO(11L),
    KEKA(19L),
    HIBOB(3L),
    NAMELY(14L),
    PERSONIO(2L),
    SUCCESSFACTORS(5L),
    WORKFORCENOW(22L),
    ORACLE(6L),
    PAYCHEX(17L),
}

@Service
class TestDataService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val platformRepository: PlatformRepository,
    private val providerRepository: ProviderRepository,
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val fieldsMappingRepository: FieldsMappingRepository,
    private val platformContractIntegrationRepository: PlatformContractIntegrationRepository,
    private val syncRepository: SyncRepository,
    private val platformEmployeeDataRepository: PlatformEmployeeDataRepository,
    private val leaveTypeMappingRepository: LeaveTypeMappingRepository,
    private val timeoffIntegrationRepository: PlatformTimeoffIntegrationRepository,
    private val timeoffEventRepository: TimeoffEventRepository,
) {

    @Transactional
    fun insertCompanyIntegration(
        accountToken: String = aRandomString(),
        companyId: Long,
        testPlatform: TestPlatform = TestPlatform.BAMBOO
    ): JpaCompanyIntegration {
        val integration = companyIntegrationRepository.findByAccountToken(accountToken)
        if (integration != null) {
            return integration
        }

        val platform = platformRepository.getReferenceById(testPlatform.id)
        val provider = providerRepository.findFirstByName(ProviderName.KNIT)!!

        return companyIntegrationRepository.save(
            JpaCompanyIntegration(
                companyId = companyId,
                provider = provider,
                platform = platform,
                accountToken = accountToken,
                enabled = true,
                incomingSyncEnabled = true,
                outgoingSyncEnabled = true,
                lastIncomingSyncTime = null,
                lastOutgoingSyncTime = null,
                lastOutgoingSyncTimeToggleOffTime = null,
                lastOutgoingSyncTimeToggleOnTime = null,
                timeOffSyncEnabled = true,
            )
        )
    }

    fun insertTimeoffTypeMapping(integrationId: Long,
                                 companyId: Long,
                                 internalTypeId: String,
                                 externalTypeId: Int,
    ): JpaLeaveTypesMapping {
        return leaveTypeMappingRepository.save(
            JpaLeaveTypesMapping(
                internalTypeId = internalTypeId,
                externalTypeId = externalTypeId.toString(),
                companyId = companyId,
                integrationId = integrationId,
                internalTypeName = aRandomString(),
            )
        )
    }

    fun insertTimeoffIntegration(integrationId: Long,
                                 contractId: Long,
                                 internalId: Long,
                                 externalId: String,
                                 employeeId: String,
    ): JpaPlatformTimeoffIntegration {
        return timeoffIntegrationRepository.save(
            JpaPlatformTimeoffIntegration(
                internalTimeoffId = internalId,
                externalTimeoffId = externalId,
                contractId = contractId,
                integrationId = integrationId,
                employeeId = employeeId,
            )
        )
    }

    fun insertLegalEntityMapping(
        integrationId: Long,
        legalEntityId: Long,
        companyId: Long,
        legalMappingStatus: LegalMappingStatus = LegalMappingStatus.FULLY_MAPPED
    ) {
        legalEntityMappingRepository.save(
            JpaLegalEntityMapping(
                integrationId = integrationId,
                entityId = legalEntityId,
                status = legalMappingStatus,
                isEnabled = true,
                entityName = "Test Entity",
                companyId = companyId,
                entityCountry = "US"
            )
        )
    }

    fun insertFieldMappings(jsonFilePath: String, integrationId: Long, entityId: Long, companyId: Long) {
        val mappingString = resourceAsString(jsonFilePath)
        val mappings = loadMappingsFromJson(mappingString)
        mappings.forEach { mapping ->
            insertFieldMapping(integrationId, entityId, companyId, mapping)
        }
    }

    fun insertPlatformContractIntegration(integrationId: Long, contractId: Long, employeeId: String, testPlatform: TestPlatform = TestPlatform.BAMBOO) {
        val platform = platformRepository.getReferenceById(testPlatform.id)
        val provider = providerRepository.findFirstByName(ProviderName.KNIT)!!
        platformContractIntegrationRepository.save(JpaPlatformContractIntegration(
            contractId = contractId,
            platformEmployeeId = employeeId,
            providerId = provider.id!!,
            integrationId = integrationId,
            platform = platform,
            provider = provider,
            platformId = platform.id!!,
            remoteId = ""
        ))
    }

    fun insertTimeoffEvent(integrationId: Long, employeeId: String, externalId: String) {
        timeoffEventRepository.save(
            JpaTimeoffEvent(
                integrationId = integrationId,
                externalId = externalId,
                employeeId = employeeId,
                timeoffData = "{}",
                timeoffType = "",
                internalId = "0"
            )
        )
    }

    fun insertSync(accountToken: String, syncId: String) {
        syncRepository.save(
            JpaSync(
                integrationId = accountToken,
                syncId = syncId,
                startTime = LocalDateTime.now(),
                endTime = LocalDateTime.now(),
                clientSyncId = "",
                inProgress = false,
            )
        )
    }

    fun insertPlatformEmployeeData(newEmployeePayload: String, integrationId: Long,
                                   employeeId: String,origin: EmployeeOrigin = EmployeeOrigin.EXTERNAL
    ) {
        val newPlatformEmployeeData = JpaPlatformEmployeeData(
            employeeId = employeeId,
            employeeData = newEmployeePayload,
            integrationId = integrationId,
            origin = origin.name,
            isDeleted = false
        )
        platformEmployeeDataRepository.save(newPlatformEmployeeData)
    }

    private fun loadMappingsFromJson(jsonString: String): List<FieldMapping> {
        return objectMapper.readValue(jsonString, object : TypeReference<List<FieldMapping>>() {})
    }

    private fun insertFieldMapping(integrationId: Long, entityId: Long, companyId: Long, mapping: FieldMapping) {
        val children = mapping.target.children?.map { childMapping ->
            createChildrenFieldMapping(integrationId, entityId, companyId, childMapping)
        } ?: emptyList()

        fieldsMappingRepository.save(
            JpaFieldsMapping(
                companyId = companyId,
                entityId = entityId,
                integrationId = integrationId,
                originField = mapping.source.key,
                originFieldLabel = mapping.source.label,
                type = FieldType.valueOf(mapping.target.type ?: "STRING"),
                mappedField = mapping.target.key,
                mappedFieldLabel = mapping.target.label,
                isRequired = true,
                isActive = true,
                isCalculated = false,
                children = children,
            ).also { newDataSpec ->
                children.forEach { it.parent = newDataSpec }
            }
        )
    }

    private fun createChildrenFieldMapping(integrationId: Long, entityId: Long, companyId: Long, childMapping: ChildMapping): JpaFieldsMapping {
        return JpaFieldsMapping(
            companyId = companyId,
            entityId = entityId,
            integrationId = integrationId,
            originField = childMapping.source.key,
            originFieldLabel = childMapping.source.label,
            type = FieldType.STRING,
            mappedField = childMapping.target.key,
            mappedFieldLabel = childMapping.target.label,
            isRequired = true,
            isActive = true,
            isCalculated = false,
        )
    }

    data class FieldMapping(
        val source: MappingField,
        val target: MappingField
    )

    data class MappingField(
        val label: String,
        val key: String,
        val type: String? = null,
        val children: List<ChildMapping>? = null
    )

    data class ChildMapping(
        val source: MappingField,
        val target: MappingField
    )
}