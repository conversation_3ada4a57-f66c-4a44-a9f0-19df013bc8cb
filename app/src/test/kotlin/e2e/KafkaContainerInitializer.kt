package e2e

import org.springframework.boot.test.util.TestPropertyValues
import org.springframework.context.ApplicationContextInitializer
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.kafka.ConfluentKafkaContainer

class KafkaContainerInitializer : ApplicationContextInitializer<ConfigurableApplicationContext> {

    private val kafka = ConfluentKafkaContainer("confluentinc/cp-kafka:7.4.0").withReuse(true)

    init {
        kafka.start()
    }

    override fun initialize(configurableApplicationContext: ConfigurableApplicationContext) {
        TestPropertyValues.of(
            "kafka.bootstrap-servers=" + kafka.bootstrapServers,
            "platform.kafka.bootstrap-servers=" + kafka.bootstrapServers,
            "pigeon.client.kafka.bootstrap-servers=" + kafka.bootstrapServers)
            .applyTo(configurableApplicationContext.environment)
    }
}