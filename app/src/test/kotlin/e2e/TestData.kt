package e2e

import com.multiplier.integration.aRandomLong
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import java.time.LocalDateTime
import java.util.*


fun anEventLog(
    contractId: Long = aRandomLong(),
    eventType: EventType = EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
    status: EventStatus = EventStatus.TO_BE_PROCESSED,
    eventPayload: String = "",
): JpaEventLog =
    JpaEventLog(
        eventType = eventType,
        eventId = UUID.randomUUID().toString(),
        eventPayload = eventPayload,
        status = status,
        retriesLeft = 3,
        retriesDone = 0,
        nextAttempt = LocalDateTime.now(),
        errorMessage = null,
        contractId = contractId,
        syncId = UUID.randomUUID().toString(),
    )

