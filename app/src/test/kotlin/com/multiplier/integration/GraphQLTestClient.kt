package com.multiplier.integration

import com.fasterxml.jackson.databind.ObjectMapper
import com.netflix.graphql.dgs.client.GraphQLResponse
import com.netflix.graphql.dgs.client.MonoGraphQLClient
import java.io.File
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.ResponseEntity
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate
import org.springframework.web.reactive.function.client.WebClient

data class HttpConfig(
    val jwt: String? = null,
)

class HttpClient(val port: Int) {

    val objectMapper = ObjectMapper()

    inline fun <reified T> post(httpConfig: HttpConfig): ResponseEntity<T> {
        val headers = HttpHeaders()
        headers["Authorization"] = listOf("Bearer ${httpConfig.jwt}")

        return RestTemplate()
            .postForEntity(
                "http://localhost:$port/graphql", HttpEntity("body", headers), T::class.java)
    }

    inline fun <reified T> graphQLFileUpload(
        httpConfig: HttpConfig,
        queryName: String? = null,
        query: String,
        file: File,
        variables: Map<String, Any>? = null,
    ): ResponseEntity<T> {
        val combinedVariables =
            variables?.let { it + mapOf("file" to null) } ?: mapOf("file" to null)
        val q = getResourceAsText(query) ?: query

        val params =
            mapOf(
                "variables" to combinedVariables,
                "query" to q,
            )

        val bytes = file.readBytes()

        val resource =
            object : ByteArrayResource(bytes) {
                override fun getFilename(): String = "upload.jpg"
            }

        val formData = LinkedMultiValueMap<String, Any>()
        formData.set("operations", objectMapper.writeValueAsString(params))
        formData.set("map", "{\"file\": [\"variables.file\"]}")
        formData.set("file", resource)

        val headers = HttpHeaders()
        headers["Authorization"] = listOf("Bearer ${httpConfig.jwt}")
        headers["x-apollo-operation-name"] = queryName

        return RestTemplate()
            .postForEntity(
                "http://localhost:$port/graphql", HttpEntity(formData, headers), T::class.java)
    }
}

class GraphQLTestClient(port: Int) {
    private val webClient: WebClient = WebClient.create("http://localhost:$port/graphql")

    fun execute(
        query: String,
        variables: Map<String, Any>? = null,
        config: HttpConfig? = null,
    ): GraphQLResponse {
        val client =
            MonoGraphQLClient.createWithWebClient(webClient) { headers ->
                config?.jwt?.let { headers.add("Authorization", "Bearer $it") }
            }

        val q = getResourceAsText(query) ?: query

        if (variables != null) {
            return client.reactiveExecuteQuery(q, variables).block()!!
        }

        return client.reactiveExecuteQuery(q).block()!!
    }
}
