
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeRequest
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.adapter.model.knit.EmployeeDetailData
import com.multiplier.integration.mock.getEventsWithExperiencePayload
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.platforms.BambooHRPlatformStrategy
import com.multiplier.integration.platforms.HibobHRPlatformStrategy
import com.multiplier.integration.platforms.PlatformStrategy
import com.multiplier.integration.platforms.TriNetPlatformStrategy
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.model.JpaPlatformEmployeeData
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.types.CustomerIntegration
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.core.env.Environment
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)
internal class EmployeeServiceTest {
    @MockK
    private lateinit var platformRepository: PlatformRepository
    @MockK
    private lateinit var platformStrategies: MutableList<out PlatformStrategy>
    @MockK
    private lateinit var customerIntegrationService: CustomerIntegrationService
    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository
    @MockK
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository
    @MockK
    private lateinit var triNetPlatformStrategy: TriNetPlatformStrategy
    @MockK
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository
    @MockK
    private lateinit var hibobHRPlatformStrategy: HibobHRPlatformStrategy
    @MockK
    private lateinit var bambooHRPlatformStrategy: BambooHRPlatformStrategy
    @MockK
    private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK
    private lateinit var environment: Environment

    @InjectMockKs
    private lateinit var employeeService: EmployeeService

    @Test
    fun `createEmployee successfully`() {
        MockKAnnotations.init(this)
        val companyId = 1L
//        val mockTriNetPlatformStrategy = mock<TriNetPlatformStrategy>()
        val employeeData = EmployeeData(
            firstName = "Test",
            lastName = "Test",
            fullName = "Test",
            username = "test123",
            personalEmail = "<EMAIL>",
            id = "test",
            phoneNumber = "test",
            gender = Gender.FEMALE,
            maritalStatus = Member.MaritalStatus.MARRIED,
            employmentActive = true,
            position = "Test",
            contactDetails = null,
            dateOfBirth = null,
            endDate = null,
            startDate = null,
            workEmail = null
        )
        val primaryEmail = "test"
        val member = getMockMember(1L)
        val contract = getMockContract(contractId = 1L, 1L)
        val createEmployeeResp = CreateEmployeePlatformResponse(
            createdEmployeeId = "Test",
            companyIntegrationId = 1L,
            employeeDetailData = EmployeeDetailData(
                firstName = employeeData.firstName,
                lastName = employeeData.lastName,
                addressLine1 = null,
                addressLine2 = null,
                country = null,
                city = null,
                postalCode = null,
                startDate = "2024-04-29",
                emailAddress = employeeData.personalEmail,
                workEmailAddress = employeeData.workEmail,
                contactNumber = null,
                locationId = null,
                designation = null,
                state = null
            ),
            originalRequest = CreateEmployeeRequest(
                firstName = employeeData.firstName,
                lastName = employeeData.lastName,
            )
        )
        val mockPlatformEmployeeData = mock<JpaPlatformEmployeeData>()
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }

        every { customerIntegrationService.getPlatformStrategyByCompanyId(companyId) } returns Pair(triNetPlatformStrategy, mockCustomerIntegration)
        every { platformEmployeeDataRepository.findByIntegrationIdAndWorkEmailOrPersonalEmail(integrationId = 1L, workEmail = primaryEmail, email = primaryEmail) } returns emptyList()
        coEvery { triNetPlatformStrategy.createEmployee(companyId, employeeData, member, contract, null) } returns createEmployeeResp
        every { platformEmployeeDataRepository.save(any()) } returns mockPlatformEmployeeData
        every { contractServiceAdapter.updateContractEmployeeId(any(), any()) } returns ContractOuterClass.Contract.getDefaultInstance()
        every { environment.matchesProfiles("local") } returns false

        employeeService.createEmployee(companyId, employeeData, member, contract)


        verify(exactly = 1) { platformEmployeeDataRepository.save(any()) }
    }

    @Test
    fun `updateEmployee successfully`() {
        val mockCompanyId = 100L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockEmployeeData = mockk<EmployeeData>(relaxed = true)
        val member = getMockMember(1L)
        val contract = getMockContract(contractId = 1L, 1L)
        val eventLogs = getEventsWithExperiencePayload(EventType.INCOMING_ONBOARDING_STATUS_UPDATE, EventStatus.TO_BE_PROCESSED, contract.id)

        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(hibobHRPlatformStrategy, mockCustomerIntegration)
        coEvery { hibobHRPlatformStrategy.updateEmployee(mockCompanyId, mockEmployeeData, member, contract, eventLogs[0]) } returns Unit

        employeeService.updateEmployee(mockCompanyId, mockEmployeeData, member, contract, eventLogs[0])

        coVerify(exactly = 1) { hibobHRPlatformStrategy.updateEmployee(mockCompanyId, mockEmployeeData, member, contract, eventLogs[0]) }
    }

    @Test
    fun `updateEmployeeCompensation successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockCompensationDetails = mockk<CompensationData>(relaxed = true)
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(hibobHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            hibobHRPlatformStrategy.updateEmployeeCompensation(
                companyId = any(),
                contractId = any(),
                compensationDetails = any()
            )
        } returns Unit
        employeeService.updateEmployeeCompensation(mockCompanyId, mockContractId, mockCompensationDetails)

        coVerify(exactly = 1) { hibobHRPlatformStrategy.updateEmployeeCompensation(mockCompanyId, mockContractId, mockCompensationDetails ) }
    }

    @Test
    fun `updateFactsheetDocument successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockDocument = mockk<DocumentResponse>(relaxed = true)
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(bambooHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            bambooHRPlatformStrategy.updateFactsheetDocument(
                companyId = any(),
                contractId = any(),
                document = any()
            )
        } returns Unit
        employeeService.updateFactsheetDocument(mockCompanyId, mockContractId, mockDocument)

        coVerify(exactly = 1) { bambooHRPlatformStrategy.updateFactsheetDocument(mockCompanyId, mockContractId, mockDocument) }
    }

    @Test
    fun `updateOnboardingKitDocument successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockDocument = mockk<DocumentResponse>(relaxed = true)
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(bambooHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            bambooHRPlatformStrategy.updateOnboardingKitDocument(
                companyId = any(),
                contractId = any(),
                document = any()
            )
        } returns Unit
        employeeService.updateOnboardingKitDocument(mockCompanyId, mockContractId, mockDocument)

        coVerify(exactly = 1) { bambooHRPlatformStrategy.updateOnboardingKitDocument(mockCompanyId, mockContractId, mockDocument) }
    }

    @Test
    fun `updateContractDocument successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockDocument = mockk<DocumentResponse>(relaxed = true)
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(hibobHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            hibobHRPlatformStrategy.updateContractDocument(
                companyId = any(),
                contractId = any(),
                document = any()
            )
        } returns Unit
        employeeService.updateContractDocument(mockCompanyId, mockContractId, mockDocument)

        coVerify(exactly = 1) { hibobHRPlatformStrategy.updateContractDocument(mockCompanyId, mockContractId, mockDocument) }
    }

    @Test
    fun `updateSalaryReviewDocument successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockDocument = mockk<DocumentResponse>(relaxed = true)
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(hibobHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            hibobHRPlatformStrategy.updateSalaryReviewDocument(
                companyId = any(),
                contractId = any(),
                document = any()
            )
        } returns Unit
        employeeService.updateSalaryReviewDocument(mockCompanyId, mockContractId, mockDocument)

        coVerify(exactly = 1) { hibobHRPlatformStrategy.updateSalaryReviewDocument(mockCompanyId, mockContractId, mockDocument) }
    }
    @Test
    fun `uploadPayslipDocument successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val mockDocument = InternalDocument(downloadUrl = "")
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(hibobHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            hibobHRPlatformStrategy.uploadPayslipDocument(
                companyId = any(),
                contractId = any(),
                document = any()
            )
        } returns Unit
        employeeService.uploadPayslipDocument(mockCompanyId, mockContractId, mockDocument)

        coVerify(exactly = 1) { hibobHRPlatformStrategy.uploadPayslipDocument(mockCompanyId, mockContractId, mockDocument) }
    }

    @Test
    fun `terminateEmployee successfully`() {
        val mockCompanyId = 100L
        val mockContractId = 1L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        val eventLogs = getEventsWithExperiencePayload(EventType.INCOMING_ONBOARDING_STATUS_UPDATE, EventStatus.TO_BE_PROCESSED, mockContractId)

        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(hibobHRPlatformStrategy, mockCustomerIntegration)
        coEvery {
            hibobHRPlatformStrategy.terminateEmployee(
                companyId = mockCompanyId,
                contractId = mockContractId,
                terminationDate = LocalDate.now(),
                terminationReason = "Test Reason",
                eventLog = eventLogs[0],
                1L
            )
        } returns Unit
        employeeService.terminateEmployee(mockCompanyId, mockContractId, LocalDate.now(), "Test Reason", eventLogs[0])

        coVerify(exactly = 1) { hibobHRPlatformStrategy.terminateEmployee(any(), any(), any(), any(), any(), 1L) }
    }

    @Test
    fun `isTriNetPlatform successfully`() {
        val mockCompanyId = 100L
        val mockCustomerIntegration = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }
        every { customerIntegrationService.getPlatformStrategyByCompanyId(mockCompanyId) } returns Pair(
            triNetPlatformStrategy,
            mockCustomerIntegration
        )
        every { triNetPlatformStrategy.platformName } returns "TriNet"

        val resp = employeeService.isTriNetPlatform(mockCompanyId)

        assertEquals(true, resp)
    }
}