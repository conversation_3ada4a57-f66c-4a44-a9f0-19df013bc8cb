package com.multiplier.integration.service

import com.multiplier.common.transport.auth.MplAccessDeniedException
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.common.transport.user.attribute.CompanyAccessService
import com.multiplier.common.transport.user.attribute.EntityAccessService
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import graphql.schema.GraphQLFieldDefinition
import graphql.schema.GraphQLObjectType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class AuthorizationServiceTest {

    @MockK
    private lateinit var currentUser: CurrentUser

    @MockK
    private lateinit var entityAccessService: EntityAccessService

    @MockK
    private lateinit var companyAccessService: CompanyAccessService

    @InjectMockKs
    private lateinit var authorizationService: AuthorizationService

    private lateinit var dfe: DgsDataFetchingEnvironment
    private lateinit var parentType: GraphQLObjectType
    private lateinit var fieldDefinition: GraphQLFieldDefinition

    @BeforeEach
    fun setUp() {
        dfe = mockk()
        fieldDefinition = mockk()
        parentType = mockk()

        every { dfe.fieldDefinition } returns fieldDefinition
        every { dfe.parentType } returns parentType
        every { fieldDefinition.name } returns "testField"
        every { parentType.name } returns "Mutation"
    }

    @Nested
    inner class CheckEntityAccessOrThrow {
        @Test
        fun `should not throw exception when user has entity access`() {
            // Given
            val entityId = 123L
            every { entityAccessService.hasEntityAccess(dfe, entityId) } returns true

            // When
            assertDoesNotThrow { authorizationService.checkEntityAccessOrThrow(dfe, entityId) }

            // Then
            verify { entityAccessService.hasEntityAccess(dfe, entityId) }
        }

        @Test
        fun `should throw MplAccessDeniedException when user does not have entity access`() {
            // Given
            val entityId = 123L
            val userContext = UserContext(
                id = 456L,
                scopes = UserScopes(companyId = 789L)
            )
            every { entityAccessService.hasEntityAccess(dfe, entityId) } returns false
            every { currentUser.context } returns userContext

            // When/Then
            assertThatThrownBy { authorizationService.checkEntityAccessOrThrow(dfe, entityId) }
                .isInstanceOf(MplAccessDeniedException::class.java)
                .hasMessage("access denied")

            verify { entityAccessService.hasEntityAccess(dfe, entityId) }
            verify { currentUser.context }
        }
    }

    @Nested
    inner class CheckCompanyAccessOrThrow {
        @Test
        fun `should not throw exception when user has company access`() {
            // Given
            val companyId = 123L
            every { companyAccessService.hasCompanyAccess(dfe, companyId) } returns true

            // When
            assertDoesNotThrow { authorizationService.checkCompanyAccessOrThrow(dfe, companyId) }

            // Then
            verify { companyAccessService.hasCompanyAccess(dfe, companyId) }
        }

        @Test
        fun `should throw MplAccessDeniedException when user does not have company access`() {
            // Given
            val companyId = 123L
            val userContext = UserContext(
                id = 456L,
                scopes = UserScopes(companyId = 789L)
            )
            every { companyAccessService.hasCompanyAccess(dfe, companyId) } returns false
            every { currentUser.context } returns userContext

            // When/Then
            assertThatThrownBy { authorizationService.checkCompanyAccessOrThrow(dfe, companyId) }
                .isInstanceOf(MplAccessDeniedException::class.java)
                .hasMessage("access denied")

            verify { companyAccessService.hasCompanyAccess(dfe, companyId) }
            verify { currentUser.context }
        }
    }
}
