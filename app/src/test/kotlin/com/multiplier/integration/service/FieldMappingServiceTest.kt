package com.multiplier.integration.service

import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.PaymentServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.mock.*
import com.multiplier.integration.repository.*
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.types.FieldMappingInput
import com.multiplier.integration.types.SaveIntegrationFieldsMappingInput
import com.multiplier.integration.utils.mapPlatformIdToKnitAppId
import com.multiplier.integration.utils.toLocalDateTime
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirement
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementField
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExtendWith(SpringExtension::class)
internal class FieldMappingServiceTest {

    @MockK
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var knitAdapter: KnitAdapter

    @MockK
    private lateinit var receivedEventRepository: ReceivedEventRepository

    @MockK
    private lateinit var newCompanyServiceAdapter: NewCompanyServiceAdapter

    @MockK
    private lateinit var fieldsMappingRepository: FieldsMappingRepository

    @MockK
    private lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @MockK
    private lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    private lateinit var externalPlatformValuesRepository: ExternalPlatformValuesRepository

    @InjectMockKs
    private lateinit var fieldMappingService: FieldMappingService

    @MockK
    private lateinit var fieldMappingConfigurationRepository: FieldMappingConfigurationRepository

    @MockK
    private lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockK
    private lateinit var receivedEventsArchiveRepository: ReceivedEventsArchiveRepository

    @MockK
    private lateinit var fieldMappingServiceAdapter: FieldMappingServiceAdapter


    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getIntegrationFieldsMapping successfully`() {
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val fieldMapping = mockk<JpaFieldsMapping>(relaxed = true) {
            every { originField } returns "firstName"
            every { mappedField } returns "firstName"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
        }
        val mockGetOnboardSpecResp = BulkOnboardDataSpec.newBuilder()
            .setKey("firstName")
            .setLabel("First Name")
            .build()

        val knitFields = getMockKnitFields()

        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every {
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(
                entityId,
                integrationId
            )
        } returns listOf(
            fieldMapping
        )
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns listOf(mockGetOnboardSpecResp)
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(
            externalValues
        )
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                entityId
            )
        } returns Optional.of(legalEntityMapping)
        every { fieldsMappingRepository.saveAll(any<List<JpaFieldsMapping>>()) } returns listOf()
        every { fieldsMappingRepository.deleteAll(any()) } returns Unit

        val resp = fieldMappingService.getIntegrationFieldsMapping(entityId, integrationId)

        assertEquals(integrationId, resp.integrationId)
        assertEquals("firstName", resp.fieldsMapping[0].originField)
        assertEquals("firstName", resp.fieldsMapping[0].mappedField)
        assertEquals(2, resp.thirdPartyFields[3].subFields.size)
    }

    @Test
    fun `getIntegrationFieldsMapping successfully with enum values`() {
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val fieldMapping = mockk<JpaFieldsMapping>(relaxed = true) {
            every { originField } returns "firstName"
            every { mappedField } returns "firstName"
        }
        val mockSpecResp = listOf(
            BulkOnboardDataSpec.newBuilder()
                .setKey("firstName")
                .build()
        )
        val knitFields = GetAllFieldsResponse(
            success = true,
            data = FieldDataList(
                default = listOf(
                    FieldData(
                        fieldId = "firstName",
                        fieldFromApp = "firstName",
                        mappedKey = "firstName",
                    ),
                    FieldData(
                        fieldId = "maritalStatus",
                        fieldFromApp = "maritalStatus",
                        mappedKey = "maritalStatus",
                        dataType = "ENUM"
                    )
                )
            )
        )
        val fieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(
                        id = "SINGLE",
                        label = "SINGLE"
                    )
                )
            )
        )
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { values } returns listOf("{\"id\":\"SINGLE\",\"label\":\"SINGLE\"}")
            every { mappedKey } returns "maritalStatus"
            every { fieldId } returns "maritalStatus"
        }


        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every {
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(
                entityId,
                integrationId
            )
        } returns listOf(
            fieldMapping
        )

        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns mockSpecResp
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        coEvery {
            knitAdapter.getFieldValues(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name),
                "maritalStatus"
            )
        } returns fieldValuesResp
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList() andThen listOf(
            externalValues
        )
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                entityId
            )
        } returns Optional.of(legalEntityMapping)
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns listOf(mockk<JpaExternalPlatformValues>())
        every { fieldsMappingRepository.saveAll(any<List<JpaFieldsMapping>>()) } returns listOf()
        every { fieldsMappingRepository.deleteAll(any()) } returns Unit

        val resp = fieldMappingService.getIntegrationFieldsMapping(entityId, integrationId)

        assertEquals(integrationId, resp.integrationId)
        assertEquals("firstName", resp.fieldsMapping[0].originField)
        assertEquals("firstName", resp.fieldsMapping[0].mappedField)
        assertEquals(true, resp.thirdPartyFields[1].isMappedByThirdParty)
        assertEquals("maritalStatus", resp.thirdPartyFields[1].key)
        assertEquals("SINGLE", resp.thirdPartyFields[1].children[0].key)
    }

    @Test
    fun `getIntegrationFieldsMapping successfully with unmapped fields`() {
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val fieldMapping = mockk<JpaFieldsMapping>(relaxed = true) {
            every { originField } returns "firstName"
            every { mappedField } returns "firstName"
            every { createdOn } returns LocalDateTime.now()
            every { updatedOn } returns LocalDateTime.now()
        }
        val mockSpecResp = listOf(
            BulkOnboardDataSpec.newBuilder()
                .setKey("firstName")
                .build(),
            BulkOnboardDataSpec.newBuilder()
                .setKey("lastName")
                .setLabel("Last Name")
                .build(),
            BulkOnboardDataSpec.newBuilder()
                .setKey("gender")
                .setLabel("Gender")
                .addAllValues(listOf("FEMALE", "MALE"))
                .build(),
            BulkOnboardDataSpec.newBuilder()
                .setKey("numberOfDependents")
                .setLabel("Number of Dependents")
                .build()
        )
        val knitFields = GetAllFieldsResponse(
            success = true,
            data = FieldDataList(
                default = listOf(
                    FieldData(
                        fieldId = "firstName",
                        fieldFromApp = "firstName",
                        mappedKey = "firstName",
                    )
                ),
                mapped = listOf(
                    FieldData(
                        fieldId = "lastName",
                        fieldFromApp = "lastName",
                        mappedKey = "lastName",
                    )
                ),
                unmapped = listOf(
                    FieldData(
                        fieldId = "maritalStatus",
                        fieldFromApp = "maritalStatus",
                        mappedKey = null,
                        dataType = "ENUM"
                    )
                )
            )
        )
        val fieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(
                        id = "SINGLE",
                        label = "SINGLE"
                    )
                )
            )
        )
        val mockSaveFieldMappings = listOf(
            JpaFieldsMapping(
                companyId = companyId,
                entityId = entityId,
                integrationId = integrationId,
                originField = "lastName",
                originFieldLabel = "Last Name",
                type = FieldType.STRING,
                mappedField = null,
                mappedFieldLabel = null,
                children = mutableListOf()
            )
        )
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }
        val mockDefaultFieldConfigs = getMockDefaultFieldConfigs()
        val mockCalculatedFieldConfigs = getMockCalculatedFieldConfigs()

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every {
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(
                entityId,
                integrationId
            )
        } returns listOf(
            fieldMapping
        )
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns mockSpecResp
        every { fieldsMappingRepository.saveAll(any<List<JpaFieldsMapping>>()) } returns mockSaveFieldMappings
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        coEvery {
            knitAdapter.getFieldValues(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name),
                "maritalStatus"
            )
        } returns fieldValuesResp
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(
            externalValues
        )
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns listOf(mockk<JpaExternalPlatformValues>())
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                entityId
            )
        } returns Optional.of(legalEntityMapping)
        every { fieldMappingConfigurationRepository.findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type = FieldMappingConfigurationType.DEFAULT) } returns mockDefaultFieldConfigs
        every { fieldMappingConfigurationRepository.findByTypeAndPlatformIdIsNullAndIsDeletedFalse(type = FieldMappingConfigurationType.CALCULATED) } returns mockCalculatedFieldConfigs
        every { fieldsMappingRepository.deleteAll(any()) } returns Unit

        val resp = fieldMappingService.getIntegrationFieldsMapping(entityId, integrationId)

        assertEquals(integrationId, resp.integrationId)
        val firstNameMapping = resp.fieldsMapping.firstOrNull { it.originField == "firstName" }
        val lastNameMapping = resp.fieldsMapping.firstOrNull { it.originField == "lastName" }
        val numberOfDependentsMapping = resp.fieldsMapping.firstOrNull { it.originField == "numberOfDependents" }

        assertNotNull(firstNameMapping)
        assertNotNull(lastNameMapping)
        assertNotNull(numberOfDependentsMapping)
        assertEquals("firstName", firstNameMapping.originField)
        assertEquals("firstName", firstNameMapping.mappedField)
        assertEquals("lastName", lastNameMapping.originField)
        assertEquals("profile.lastName", lastNameMapping.mappedField)
        assertEquals("numberOfDependents", numberOfDependentsMapping.originField)
        assertEquals("dependents", numberOfDependentsMapping.mappedField)
        assertEquals(true, numberOfDependentsMapping.isCalculated)
        assertEquals("customFields.fields.lastName", resp.thirdPartyFields[1].key)
        assertEquals(true, resp.thirdPartyFields[1].isMappedByThirdParty)
        assertEquals(true, resp.thirdPartyFields[0].isMappedByThirdParty)
        assertEquals("firstName", resp.thirdPartyFields[0].key)


    }

    @Test
    fun `saveIntegrationFieldsMapping successfully with unmapped fields`() {
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val mockRequest = getMockSaveIntegrationFieldsMappingInput(integrationId, entityId)
        val mockAddCustomMappingRequest = AddCustomFieldMappingRequest(
            fieldId = "maritalStatus",
            fieldFromApp = "maritalStatus",
            mappedKey = "maritalStatus",
            dataType = "ENUM"
        )
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every {
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndOriginFieldAndIsActive(
                entityId,
                integrationId,
                "maritalStatus"
            )
        } returns null
        every { fieldsMappingRepository.save(any()) } returns mock<JpaFieldsMapping>()
        every { fieldsMappingRepository.getFieldMappingCounts(entityId, integrationId) } returns listOf(arrayOf(2, 0))
        every {
            legalEntityMappingRepository.findByIntegrationIdAndEntityId(
                integrationId,
                entityId
            )
        } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        every {
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndOriginFieldAndIsActive(
                entityId,
                integrationId,
                "gender"
            )
        } returns mock<JpaFieldsMapping>()
        coEvery {
            knitAdapter.addCustomFieldMapping(
                companyId,
                integrationId,
                mockAddCustomMappingRequest
            )
        } returns AddCustomFieldMappingResponse(
            success = true
        )

        val resp = fieldMappingService.saveIntegrationFieldsMapping(mockRequest)

        assertEquals(true, resp.success)
        verify(exactly = 2) { fieldsMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationFieldsMapping successfully with existed fields`() {
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val mockRequest = SaveIntegrationFieldsMappingInput.newBuilder()
            .integrationId(integrationId)
            .entityId(entityId)
            .mapping(
                listOf(
                    FieldMappingInput.newBuilder()
                        .originField("maritalStatus")
                        .originFieldLabel("Marital Status")
                        .mappedField("maritalStatus")
                        .mappedFieldLabel("Marital Status")
                        .type("ENUM")
                        .children(
                            listOf(
                                FieldMappingInput.newBuilder()
                                    .originField("SINGLE")
                                    .originFieldLabel("SINGLE")
                                    .mappedField("SINGLE")
                                    .mappedFieldLabel("SINGLE")
                                    .build()
                            )
                        )
                        .mappedFieldId("maritalStatus")
                        .mappedFieldFromApp("maritalStatus")
                        .isMappedByThirdParty(true)
                        .build()
                )
            )
            .build()
        val existedFieldMapping = mockk<JpaFieldsMapping>(relaxed = true) {
            every { children } returns mutableListOf(
                JpaFieldsMapping(
                    originField = "SINGLE",
                    companyId = companyId,
                    entityId = entityId,
                    integrationId = integrationId,
                    originFieldLabel = "SINGLE",
                    mappedField = "TEST",
                    mappedFieldLabel = "TEST",
                    type = FieldType.STRING
                )
            )
        }

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every {
            fieldsMappingRepository.findByEntityIdAndIntegrationIdAndOriginFieldAndIsActive(
                entityId,
                integrationId,
                "maritalStatus"
            )
        } returns existedFieldMapping
        every { fieldsMappingRepository.save(any()) } returns mock<JpaFieldsMapping>()
        every { fieldsMappingRepository.getFieldMappingCounts(entityId, integrationId) } returns listOf(arrayOf(1, 1))

        val resp = fieldMappingService.saveIntegrationFieldsMapping(mockRequest)

        assertEquals(true, resp.success)
        verify(exactly = 1) { fieldsMappingRepository.save(any()) }

    }

    @Test
    fun `getIntegrationLegalEntityMappings successfully`() {
        val integrationId = 1L
        val mockEntityId = 2L
        val companyId = 100L
        val newEntityId = 4L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
            every { entityId } returns newEntityId
        }
        val legalEntityMapping2 = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
            every { entityId } returns mockEntityId
        }
        val mockLegalEntities = CompanyOuterClass.GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setId(mockEntityId)
                        .setCompanyId(100)
                        .setAddress(
                            CompanyOuterClass.Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .setCurrencyCode("USD")
                        .setStatus(LegalEntity.LegalEntityStatus.ACTIVE)
                        .build(),
                    LegalEntity.newBuilder()
                        .setId(newEntityId)
                        .setCompanyId(100)
                        .setAddress(
                            CompanyOuterClass.Address.newBuilder()
                                .setCountry("VNM")
                                .build()
                        )
                        .setCurrencyCode("USD")
                        .setStatus(LegalEntity.LegalEntityStatus.ACTIVE)
                        .build()
                )
            )
            .build()

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns listOf(
            legalEntityMapping,
            legalEntityMapping2
        )
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) } returns listOf(mockk<JpaLegalEntityMapping>())

        val resp = fieldMappingService.getIntegrationLegalEntityMappings(integrationId)

        assertEquals(2, resp.size)
        assertEquals(mockEntityId, resp[1].legalEntity.id)
        assertEquals(LegalMappingStatus.UNMAPPED.name, resp[1].entityMappingStatus.name)
    }

    @Test
    fun `getIntegrationLegalEntityMappings successfully with unmap entity`() {
        val integrationId = 1L
        val mockEntityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId = mockEntityId, companyId)

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) } returns listOf(mockk<JpaLegalEntityMapping>())

        fieldMappingService.getIntegrationLegalEntityMappings(integrationId)

        verify(exactly = 1) { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) }
        verify(exactly = 2) { legalEntityMappingRepository.findByIntegrationId(integrationId) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus successfully with fully mapped`() {
        val entityMappingId = 1L
        val mockIntegrationId = 2L
        val mockEntityId = 3L
        val mockEntityCountry = "USA"
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.FULLY_MAPPED
            every { integrationId } returns mockIntegrationId
            every { entityId } returns mockEntityId
            every { entityCountry } returns mockEntityCountry
        }
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { enabled } returns true
            every { incomingSyncEnabled } returns true
        }
        val receivedEvent = mockk<JpaReceivedEvent>(relaxed = true)

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns Optional.of(companyIntegration)
        every {
            receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                companyIntegration.accountToken,
                mockEntityCountry,
                isEntityEnabled = false,
                processed = true
            )
        } returns listOf(receivedEvent)
        every { receivedEventRepository.saveAll(any<List<JpaReceivedEvent>>()) } returns listOf(receivedEvent)
        every {
            receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                any(),
                any(),
                any(),
                any()
            )
        } returns listOf()
        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId)

        assertEquals(true, resp.success)
        verify(exactly = 1) { legalEntityMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus successfully with fully mapped - include archived events`() {
        val entityMappingId = 1L
        val mockIntegrationId = 2L
        val mockEntityId = 3L
        val mockEntityCountry = "USA"
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.FULLY_MAPPED
            every { integrationId } returns mockIntegrationId
            every { entityId } returns mockEntityId
            every { entityCountry } returns mockEntityCountry
        }
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { enabled } returns true
            every { incomingSyncEnabled } returns true
        }
        val receivedEvent = mockk<JpaReceivedEvent>(relaxed = true)

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns Optional.of(companyIntegration)
        every {
            receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                companyIntegration.accountToken,
                mockEntityCountry,
                isEntityEnabled = false,
                processed = true
            )
        } returns listOf(receivedEvent)
        every { receivedEventRepository.saveAll(any<List<JpaReceivedEvent>>()) } returns listOf(receivedEvent)
        every {
            receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                any(),
                any(),
                any(),
                any()
            )
        } returns listOf(
            JpaReceivedEventArchive(
                eventId = "event-id",
                syncId = "sync-id",
                integrationId = "integrationId",
                eventType = null,
                syncDataType = null,
                errors = null,
                identifiervalue = null,
                receivedTime = null,
                data = null,
                confirmedByUser = null,
                processed = null,
                isEntityEnabled = false,
                entityId = null,
                entityCountry = null
            )
        )
        justRun { receivedEventsArchiveRepository.deleteAll(any()) }

        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId)

        assertEquals(true, resp.success)
        verify(exactly = 1) { legalEntityMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus failed for not fully mapped`() {
        val entityMappingId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping

        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId)

        assertEquals(false, resp.success)
        verify(exactly = 0) { legalEntityMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus successfully for disable sync`() {
        val entityMappingId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping

        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId, enableDataSync = false)

        assertEquals(true, resp.success)
        verify(exactly = 1) { legalEntityMappingRepository.save(any()) }
    }

    @Nested
    inner class GetBankDetailFromDynamicFieldsWithCustomFields {

        @Test
        fun `return empty map`() {
            val resp = fieldMappingService.getBankDetailFromDynamicFieldsWithCustomFields(
                IntegrationPaymentAccountRequirement.newBuilder().build()
            )

            assertEquals(0, resp.size)
        }

        @Test
        fun `return bank details`() {
            val resp = fieldMappingService.getBankDetailFromDynamicFieldsWithCustomFields(
                IntegrationPaymentAccountRequirement.newBuilder()
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("accountHolderName")
                            .setMappedKey("accountHolderName")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("accountNumber")
                            .setMappedKey("accountNumber")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("bankName")
                            .setMappedKey("bankName")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("branchName")
                            .setMappedKey("branchName")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("swiftCode")
                            .setMappedKey("swiftCode")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("localBankCode")
                            .setMappedKey("localBankCode")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("ifscCode")
                            .setMappedKey("ifscCode")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("iban")
                            .setMappedKey("iban")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("routingNumber")
                            .setMappedKey("routingNumber")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("country")
                            .setMappedKey("country")
                            .build()
                    )
                    .addAccountRequirements(
                        IntegrationPaymentAccountRequirementField.newBuilder()
                            .setRequirementKey("key")
                            .setMappedKey("key")
                            .build()
                    )
                    .build()
            )

            assertEquals(11, resp.size)
            assertEquals("profile.firstName", resp["$BANK_DATA_SPEC_PREFIX.accountHolderName"])
            assertEquals("bankAccounts[0].accountNumber", resp["$BANK_DATA_SPEC_PREFIX.accountNumber"])
            assertEquals("bankAccounts[0].bankName", resp["$BANK_DATA_SPEC_PREFIX.bankName"])
            assertEquals(
                "bankAccounts[0].routingInfo[type=BRANCH_CODE].number",
                resp["$BANK_DATA_SPEC_PREFIX.branchName"]
            )
            assertEquals(
                "bankAccounts[0].routingInfo[type=SWIFT_CODE].number",
                resp["$BANK_DATA_SPEC_PREFIX.swiftCode"]
            )
            assertEquals(
                "bankAccounts[0].routingInfo[type=BANK_IDENTIFICATION_CODE].number",
                resp["$BANK_DATA_SPEC_PREFIX.localBankCode"]
            )
            assertEquals("bankAccounts[0].routingInfo[type=IFSC_CODE].number", resp["$BANK_DATA_SPEC_PREFIX.ifscCode"])
            assertEquals("bankAccounts[0].routingInfo[type=IBAN].number", resp["$BANK_DATA_SPEC_PREFIX.iban"])
            assertEquals(
                "bankAccounts[0].routingInfo[type=ROUTING_NUMBER].number",
                resp["$BANK_DATA_SPEC_PREFIX.routingNumber"]
            )
            assertEquals("locations.workAddress.country", resp["$BANK_DATA_SPEC_PREFIX.country"])
            assertEquals("customFields.fields.key", resp["$BANK_DATA_SPEC_PREFIX.key"])
        }
    }


    @Test
    fun `handleFieldMappingsOnDisconnection successfully`() {
        val mockIntegrationId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
        }
        val fieldMapping = mockk<JpaFieldsMapping>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
        }
        val externalPlatformValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
        }

        every { legalEntityMappingRepository.findByIntegrationId(mockIntegrationId) } returns listOf(legalEntityMapping)
        every { fieldsMappingRepository.findByIntegrationIdAndIsActive(mockIntegrationId) } returns listOf(fieldMapping)
        every { legalEntityMappingRepository.saveAll(listOf(legalEntityMapping)) } returns listOf(legalEntityMapping)
        every { fieldsMappingRepository.saveAll(listOf(fieldMapping)) } returns listOf(fieldMapping)
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalPlatformValues
        )
        every { externalPlatformValuesRepository.saveAll(listOf(externalPlatformValues)) } returns listOf(
            externalPlatformValues
        )

        fieldMappingService.handleFieldMappingsOnDisconnection(mockIntegrationId)

        verify(exactly = 1) { fieldsMappingRepository.saveAll(listOf(fieldMapping)) }
        verify(exactly = 1) { legalEntityMappingRepository.saveAll(listOf(legalEntityMapping)) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(listOf(externalPlatformValues)) }
    }

    @Test
    fun `getExternalEnumValues successfully`() {
        val mockIntegrationId = 1L
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { fieldId } returns "gender"
            every { updatedOn } returns "2024-08-04T18:52:21.145".toLocalDateTime()
        }
        val externalFields = listOf(
            FieldData(
                fieldId = "gender",
                fieldFromApp = "gender",
                mappedKey = "gender",
                label = "Gender",
                dataType = "ENUM"
            ),
            FieldData(
                fieldId = "maritalStatus",
                fieldFromApp = "maritalStatus",
                mappedKey = "maritalStatus",
                label = "Marital Status",
                dataType = "ENUM"
            ),
        )
        val genderFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(
                        label = "FEMALE"
                    )
                )
            )
        )
        val maritalStatusFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(
                        label = "SINGLE"
                    )
                )
            )
        )
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Hibob"
        }

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalValues
        )
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "hibob",
                "maritalStatus"
            )
        } returns maritalStatusFieldValuesResp
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "hibob",
                "gender"
            )
        } returns genderFieldValuesResp
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns listOf(mockk<JpaExternalPlatformValues>())

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues without enum in external fields `() {
        val mockIntegrationId = 1L
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val externalFields = listOf(
            FieldData(
                fieldId = "gender",
                fieldFromApp = "gender",
                mappedKey = "gender",
                label = "Gender",
                dataType = "STRING"
            )
        )
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalValues
        )

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 1) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 0) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues need to update but failed`() {
        val mockIntegrationId = 1L
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { fieldId } returns "gender"
            every { updatedOn } returns "2024-08-04T18:52:21.145".toLocalDateTime()
        }
        val externalFields = listOf(
            FieldData(
                fieldId = "gender",
                fieldFromApp = "gender",
                mappedKey = "gender",
                label = "Gender",
                dataType = "ENUM"
            )
        )
        val genderFieldValuesResp = GetFieldValuesResponse(
            success = false
        )
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Hibob"
        }

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalValues
        )
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "hibob",
                "gender"
            )
        } returns genderFieldValuesResp

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 1) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 0) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues with department field fallback when departments list returns empty`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"

        // Mock company integration
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        // Mock external fields with a department field
        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        // Mock empty field values response
        val emptyFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList())
        )

        // Mock departments list response
        val departmentsListResp = DepartmentsListResponse(
            success = true,
            data = DepartmentsData(
                departments = listOf(
                    Department(id = "dept1", name = "Engineering", companyId = "company1"),
                    Department(id = "dept2", name = "Sales", companyId = "company2")
                )
            )
        )

        // Mock repository responses
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()

        // Capture the argument passed to saveAll
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        // Mock Knit adapter responses
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns emptyFieldValuesResp

        coEvery {
            knitAdapter.getDepartmentsList(
                mockCompanyId,
                mockPlatformId,
                "paychex"
            )
        } returns departmentsListResp

        // Call the method
        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        // Verify the results
        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }

        // Verify the captured saved values
        val savedValues = savedValuesSlot.captured.first()
        assertEquals(departmentFieldId, savedValues.fieldId)
        assertEquals("department", savedValues.mappedKey)

        // Verify the values contain both departments
        val values = savedValues.values
        assertNotNull(values)
        assertTrue(values.any { it.contains("Engineering") })
        assertTrue(values.any { it.contains("Sales") })
    }

    @Test
    fun `getExternalEnumValues with department field fallback when departments list fails`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"
        
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        val emptyFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList())
        )

        val failedDepartmentsListResp = DepartmentsListResponse(
            success = false,
            error = ErrorResponse(msg = "Failed to fetch departments")
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns emptyFieldValuesResp

        coEvery {
            knitAdapter.getDepartmentsList(
                mockCompanyId,
                mockPlatformId,
                "paychex"
            )
        } returns failedDepartmentsListResp

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues with department field fallback when departments list throws exception`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"
        
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        val emptyFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList())
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns emptyFieldValuesResp

        coEvery {
            knitAdapter.getDepartmentsList(
                mockCompanyId,
                mockPlatformId,
                "paychex"
            )
        } throws RuntimeException("Network error")

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues with department field fallback when field values has data`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"
        
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        val fieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(id = "dept1", label = "Engineering"),
                    FieldValues(id = "dept2", label = "Sales")
                )
            )
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns fieldValuesResp

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
        verify(exactly = 0) { knitAdapter.getDepartmentsList(mockCompanyId, mockPlatformId, "paychex") }
        
        val savedValues = savedValuesSlot.captured.first()
        assertEquals(departmentFieldId, savedValues.fieldId)
        assertEquals("department", savedValues.mappedKey)
        
        val values = savedValues.values
        assertNotNull(values)
        assertTrue(values.any { it.contains("Engineering") })
        assertTrue(values.any { it.contains("Sales") })
    }
}
