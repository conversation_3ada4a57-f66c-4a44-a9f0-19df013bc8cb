package com.multiplier.integration.service

import com.google.protobuf.Int64Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyUser
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.offboarding.email.MemberEmails
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.core.schema.common.Common.EmailAddress
import com.multiplier.country.schema.Country
import com.multiplier.integration.adapter.api.BulkUploadServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.CountryServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.adapter.api.JobDetail
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.ValidationSummary
import com.multiplier.integration.adapter.api.ValidationSummaryChunk
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaBulkJobTracker
import com.multiplier.integration.service.email.AdminEmailTemplateGenerator
import com.multiplier.integration.service.email.EmailService
import com.multiplier.integration.types.NotificationType
import com.multiplier.member.schema.Member
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.net.URL
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)

class NotificationsServiceTest {
    @MockK
    lateinit var emailService: EmailService

    @MockK
    lateinit var memberEmailBuilder: MemberEmails

    @MockK
    lateinit var adminEmailTemplateGenerator: AdminEmailTemplateGenerator

    @MockK
    lateinit var memberService: MemberServiceAdapter

    @MockK
    lateinit var countryService: CountryServiceAdapter

    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter

    @MockK
    lateinit var contractService: ContractServiceAdapter

    @MockK
    lateinit var newCompanyServiceAdapter: NewCompanyServiceAdapter

    @MockK
    lateinit var bulkJobTrackerService: BulkJobTrackerService

    @MockK
    lateinit var bulkUploadServiceAdapter: BulkUploadServiceAdapter

    @InjectMockKs
    lateinit var notificationsService: NotificationsService

    private var systemNotificationEmail: String = "<EMAIL>"

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should send email sync result successfully`() {
        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("primary")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()

        every { emailService.sendEmailWithAttachment(any(), any()) } returns Unit
        notificationsService.sendingResultEmail(mockCompanyUsers, listOf())

        verify(exactly = 1) { emailService.sendEmailWithAttachment(any(), any())}
    }

    @Test
    fun `should send email sync result successfully with template params`() {
        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("primary")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val templateParams =
            mutableMapOf(
                "addedEmployees" to "100",
                "updatedEmployees" to "200",
                "failedEmployees" to "300",
                "link" to "test"
            )
        every { emailService.sendEmailWithAttachment(any(), any()) } returns Unit
        notificationsService.sendingResultEmail(mockCompanyUsers, listOf(), templateParams = templateParams)

        verify(exactly = 1) { emailService.sendEmailWithAttachment(any(), any())}
    }

    @Test
    fun `should send email outgoing sync disable successfully with template params`() {
        val companyId = 1L
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("primary")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        every { emailService.sendEmail(any()) } returns Unit
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { adminEmailTemplateGenerator.getEmailTemplateForOutgoingSyncDisabled("<EMAIL>", platformName = "TriNet") } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationEmployeeSyncTurnedOnEmailToAdmin.toString())
            .setSubject("You just turned off employee sync from Multiplier to TriNet")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")

        notificationsService.sendAdminOutgoingSyncDisabled(companyId, platformName = "TriNet", platformId = 1L)

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email integration disconnected successfully with template params`() {
        val companyId = 1L
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        every { emailService.sendEmail(any()) } returns Unit
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { adminEmailTemplateGenerator.getEmailTemplateForIntegrationDisconnected("<EMAIL>", platformName = "TriNet") } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationDisconnectedEmailToAdmin.toString())
            .setSubject("Reconnect TriNet on Multiplier")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")

        notificationsService.sendAdminIntegrationDisconnected(companyId, platformName = "TriNet", companyUserId = 1L, platformId = 1)

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email integration disconnected failed`() {
        val companyId = 1L
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument

        val actualException =
            assertFailsWith<Exception> {
                notificationsService.sendAdminIntegrationDisconnected(companyId, platformName = "TriNet", companyUserId = 2L, platformId = 1)
            }

        Assertions.assertEquals(actualException.message, "Company user not found for companyId: $companyId and userId: 2")
        verify(exactly = 0) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email integration connected successfully with template params`() {
        val companyId = 1L
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        every { emailService.sendEmail(any()) } returns Unit
        every { adminEmailTemplateGenerator.getEmailTemplateForIntegrationConnected("<EMAIL>", platformName = "TriNet") } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationConnectedEmailToAdmin.toString())
            .setSubject("TriNet is now connected.")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")

        notificationsService.sendAdminIntegrationConnected(companyId, platformName = "TriNet")

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email integration credential expired with template params`() {
        val companyId = 1L
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        every { emailService.sendEmail(any()) } returns Unit
        every { adminEmailTemplateGenerator.getEmailTemplateForIntegrationCredentialExpired("<EMAIL>", platformName = "TriNet") } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationConnectedEmailToAdmin.toString())
            .setSubject("Important Note - Auto sync with TriNet failed.")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")

        notificationsService.sendAdminIntegrationCredentialExpired(companyId, platformName = "TriNet")

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email member update bank details`() {
        val companyId = 1L
        val contractId = 1L
        val contract = ContractOuterClass.Contract.newBuilder()
            .setId(contractId)
            .setMemberId(123L)
            .setCountry("VNM")
            .setCompanyId(companyId)
            .build()
        val member = Member.newBuilder()
            .setId(123L)
            .setFirstName("John")
            .addEmails(
                com.multiplier.member.schema.EmailAddress.newBuilder()
                    .setType("primary")
                    .setEmail("test")
                    .build()
            )
            .build()
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()

        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        every { emailService.sendEmail(any()) } returns Unit
        every { memberService.getMember(any()) } returns member
        every { contractService.getContractMemberEmailsByContractIds(any()) } returns mapOf(contractId to "<EMAIL>")
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { memberEmailBuilder.getEmailToUpdateBasicDetail(any()) } returns EmailNotificationBody.newBuilder()
        notificationsService.sendMemberToUpdateBasicDetailEmailNotification(contract, platformName = "TriNet")

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email member update bank details without company logo`() {
        val companyId = 1L
        val contractId = 1L
        val contract = ContractOuterClass.Contract.newBuilder()
            .setId(contractId)
            .setMemberId(123L)
            .setCountry("VNM")
            .setCompanyId(companyId)
            .build()
        val member = Member.newBuilder()
            .setId(123L)
            .setFirstName("John")
            .addEmails(
                com.multiplier.member.schema.EmailAddress.newBuilder()
                    .setType("primary")
                    .setEmail("test")
                    .build()
            )
            .build()
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(-1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()

        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { emailService.sendEmail(any()) } returns Unit
        every { memberService.getMember(any()) } returns member
        every { contractService.getContractMemberEmailsByContractIds(any()) } returns mapOf(contractId to "<EMAIL>")
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { memberEmailBuilder.getEmailToUpdateBasicDetail(any()) } returns EmailNotificationBody.newBuilder()
        notificationsService.sendMemberToUpdateBasicDetailEmailNotification(contract, platformName = "TriNet")

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email member update bank details with error when fetching company logo`() {
        val companyId = 1L
        val contractId = 1L
        val contract = ContractOuterClass.Contract.newBuilder()
            .setId(contractId)
            .setMemberId(123L)
            .setCountry("VNM")
            .setCompanyId(companyId)
            .build()
        val member = Member.newBuilder()
            .setId(123L)
            .setFirstName("John")
            .addEmails(
                com.multiplier.member.schema.EmailAddress.newBuilder()
                    .setType("primary")
                    .setEmail("test")
                    .build()
            )
            .build()
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()

        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } throws RuntimeException("Document not found")
        every { emailService.sendEmail(any()) } returns Unit
        every { memberService.getMember(any()) } returns member
        every { contractService.getContractMemberEmailsByContractIds(any()) } returns mapOf(contractId to "<EMAIL>")
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { memberEmailBuilder.getEmailToUpdateBasicDetail(any()) } returns EmailNotificationBody.newBuilder()
        notificationsService.sendMemberToUpdateBasicDetailEmailNotification(contract, platformName = "TriNet")

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

    @Test
    fun `should send email gp sync closure successfully with template params`() {
        val companyId = 1L
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("test")
                .setLastName("test")
                .build())
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder().addAllUsers(mockListAdmins).build()
        val mockDocument = DocumentResponse()

        mockDocument.setPrivateField("downloadURL",URL("http://127.0.0.1:8080"))
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "VietNam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        every { emailService.sendEmail(any()) } returns Unit
        every { adminEmailTemplateGenerator.getEmailTemplateForStaleGPSyncDeactivated("<EMAIL>", platformName = "TriNet") } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.StaleGPSyncDeactivatedToCompany.toString())
            .setSubject("GP sync is now disabled.")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")

        notificationsService.sendAdminGPSyncClosure("syncId", companyId, platformName = "TriNet")

        verify(exactly = 1) { emailService.sendEmail(any())}
    }

/**
     * Test case for verifying the notification sent to admins upon successful SFTP data creation.
     *
     * The test ensures that:
     * - The email notification is sent exactly once.
     * - The captured email has the correct template type, subject, and sender.
     * - The content variables in the email contain the expected data such as company name, country,
     *   logo link, admin name, bulk module, and file name.
     */
    @Test
    fun `should notify admins for SFTP data creation success`() {
        // Given
        // 1. Set up bulk job tracker data
        val bulkJobTrackerId = 123L
        val jobId = 1234L
        val companyId = 1L
        val originalFileURI = "sftp://server/path/test_file.csv"
        val moduleNames = setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name)

        val mockBulkJobTracker = mockk<JpaBulkJobTracker>()
        every { mockBulkJobTracker.id } returns bulkJobTrackerId
        every { mockBulkJobTracker.jobId } returns jobId
        every { mockBulkJobTracker.companyId } returns companyId
        every { mockBulkJobTracker.originalFileURI } returns originalFileURI
        every { mockBulkJobTracker.moduleNames } returns moduleNames
        every { mockBulkJobTracker.jobStatus } returns BulkJobStatus.UPSERT_SUCCESSFUL

        // 2. Set up validation summary data from bulk upload service
        val mockValidationSummaryChunk = mockk<ValidationSummaryChunk>()
        every { mockValidationSummaryChunk.name } returns "EOR_TIMESHEET"
        every { mockValidationSummaryChunk.identifiedRowCount } returns 10

        val mockValidationSummary = mockk<ValidationSummary>()
        every { mockValidationSummary.validationSummaryChunks } returns listOf(mockValidationSummaryChunk)

        val mockBulkJob = mockk<JobDetail>()
        every { mockBulkJob.validationSummary } returns mockValidationSummary

        // 3. Set up company and admin user data
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test Company")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder()
                .setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build())
                .build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("Admin")
                .setLastName("User")
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder()
            .addAllUsers(mockListAdmins)
            .build()

        // 4. Set up company logo document
        val mockDocument = DocumentResponse()
        mockDocument.setPrivateField("downloadURL", URL("http://127.0.0.1:8080"))

        // 5. Configure mock service behaviors
        every { bulkJobTrackerService.findByIdOrThrow(bulkJobTrackerId) } returns mockBulkJobTracker
        every { bulkUploadServiceAdapter.getJob(jobId) } returns mockBulkJob
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "Vietnam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        val emailCaptor = slot<EmailNotificationBody>()
        every { emailService.sendEmail(capture(emailCaptor)) } returns Unit

        // 6. Configure email template generator
        every {
            adminEmailTemplateGenerator.getEmailTemplateForSFTPIntegrationUpsertSuccess(
                adminEmail = "<EMAIL>",
                entriesCount = 10,
                fileName = "test_file.csv",
                feature = "timesheet"
            )
        } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationSFTPUpsertSuccessEmailToAdmin.toString())
            .setSubject("Timesheet upload: 10 values updated successfully - (test_file.csv)")
            .setFrom(systemNotificationEmail)
            .setTo("<EMAIL>")

        // When: Call the method under test
        notificationsService.notifySFTPUpsertSuccess(bulkJobTrackerId)

        // Then: Verify that an email was sent with the expected parameters
        verify(exactly = 1) { emailService.sendEmail(any()) }


        // Verify the captured email body has the expected properties
        val capturedEmail = emailCaptor.captured
        assertThat(capturedEmail.subject).isEqualTo("Timesheet upload: 10 values updated successfully - (test_file.csv)")
        assertThat(capturedEmail.from).isEqualTo(systemNotificationEmail)
        assertThat(capturedEmail.to).isEqualTo("<EMAIL>")

        // Verify the content variables contain the expected data
        assertThat(capturedEmail.contentVariablesMap)
            .containsEntry("companyName", "Test Company")
            .containsEntry("companyCountry", "Vietnam")
            .containsEntry("companyLogoLink", "http://127.0.0.1:8080")
            .containsEntry("adminName", "Admin User")
            .containsEntry("bulkModule", "timesheet")
            .containsEntry("fileName", "test_file.csv")
            .containsEntry("entriesCount", "10")
    }

    /**
     * Test case for verifying the notification sent to admins upon SFTP validation failure.
     *
     * The test ensures that:
     * - The email notification is sent exactly once.
     * - The captured email has the correct template type, subject, and sender.
     * - The content variables in the email contain the expected data such as company name, country,
     *   logo link, admin name, bulk module, file name, and report link.
     */
    @Test
    fun `should notify admins for SFTP validation failure`() {
        // Given
        // 1. Set up bulk job tracker data
        val bulkJobTrackerId = 123L
        val jobId = 1234L
        val companyId = 1L
        val originalFileURI = "sftp://server/path/test_file.csv"
        val reportFileURI = "sftp://server/path/report/test_file_report.csv"
        val moduleNames = setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name)

        val mockBulkJobTracker = mockk<JpaBulkJobTracker>()
        every { mockBulkJobTracker.id } returns bulkJobTrackerId
        every { mockBulkJobTracker.jobId } returns jobId
        every { mockBulkJobTracker.companyId } returns companyId
        every { mockBulkJobTracker.originalFileURI } returns originalFileURI
        every { mockBulkJobTracker.reportFileURI } returns reportFileURI
        every { mockBulkJobTracker.moduleNames } returns moduleNames
        every { mockBulkJobTracker.jobStatus } returns BulkJobStatus.VALIDATION_FAILED

        // 2. Set up validation summary data from bulk upload service
        val mockValidationSummaryChunk = mockk<ValidationSummaryChunk>()
        every { mockValidationSummaryChunk.name } returns "EOR_TIMESHEET"
        every { mockValidationSummaryChunk.identifiedRowCount } returns 10

        val mockValidationSummary = mockk<ValidationSummary>()
        every { mockValidationSummary.validationSummaryChunks } returns listOf(mockValidationSummaryChunk)

        val mockBulkJob = mockk<JobDetail>()
        every { mockBulkJob.validationSummary } returns mockValidationSummary

        // 3. Set up company and admin user data
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test Company")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder()
                .setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build())
                .build())
            .build()

        val mockListAdmins = listOf(
            CompanyUser.newBuilder()
                .setId(1L)
                .addAllEmails(
                    mutableListOf(EmailAddress.newBuilder()
                        .setType("default")
                        .setEmail("<EMAIL>")
                        .build())
                )
                .setUserId("1")
                .setFirstName("Admin")
                .setLastName("User")
                .build()
        )
        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder()
            .addAllUsers(mockListAdmins)
            .build()

        // 4. Set up company logo document
        val mockDocument = DocumentResponse()
        mockDocument.setPrivateField("downloadURL", URL("http://127.0.0.1:8080"))

        // 5. Configure mock service behaviors
        every { bulkJobTrackerService.findByIdOrThrow(bulkJobTrackerId) } returns mockBulkJobTracker
        every { bulkUploadServiceAdapter.getJob(jobId) } returns mockBulkJob
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "Vietnam"
        every { docgenServiceAdapter.getDocument(1L) } returns mockDocument
        val emailCaptor = slot<EmailNotificationBody>()
        every { emailService.sendEmail(capture(emailCaptor)) } returns Unit

        // 6. Configure email template generator
        every {
            adminEmailTemplateGenerator.getEmailTemplateForSFTPIntegrationValidationFailure(
                adminEmail = "<EMAIL>",
                fileName = "test_file.csv",
                feature = "timesheet"
            )
        } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationSFTPValidationFailureEmailToAdmin.toString())
            .setSubject("Timesheet: Some Entries Could Not Be Processed – (test_file.csv)")
            .setFrom(systemNotificationEmail)
            .setTo("<EMAIL>")

        // When: Call the method under test
        notificationsService.notifySFTPValidationFailure(bulkJobTrackerId)

        // Then: Verify that an email was sent with the expected parameters
        verify(exactly = 1) { emailService.sendEmail(any()) }

        // Verify the captured email body has the expected properties
        val capturedEmail = emailCaptor.captured
        assertThat(capturedEmail.subject).isEqualTo("Timesheet: Some Entries Could Not Be Processed – (test_file.csv)")
        assertThat(capturedEmail.from).isEqualTo(systemNotificationEmail)
        assertThat(capturedEmail.to).isEqualTo("<EMAIL>")

        // Verify the content variables contain the expected data
        assertThat(capturedEmail.contentVariablesMap)
            .containsEntry("companyName", "Test Company")
            .containsEntry("companyCountry", "Vietnam")
            .containsEntry("companyLogoLink", "http://127.0.0.1:8080")
            .containsEntry("adminName", "Admin User")
            .containsEntry("bulkModule", "timesheet")
            .containsEntry("fileName", "test_file.csv")
            .containsEntry("reportURI", reportFileURI)
    }

    /**
     * Test case for verifying the notification sent to ops team when a new SFTP access request is created.
     *
     * The test ensures that:
     * - The email notification is sent exactly once.
     * - The captured email has the correct template type, subject, and sender.
     * - The content variables in the email contain the expected data such as request ID, company ID,
     *   company name, entity ID, bulk module, and status.
     */
    @Test
    fun `should send notification to ops team for SFTP access request creation`() {
        // Given
        val requestId = 123L
        val companyId = 456L
        val entityId = 789L
        val bulkModule = com.multiplier.integration.repository.model.BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
        val status = com.multiplier.integration.repository.model.SftpAccessRequestStatus.PENDING

        val mockRequest = mockk<com.multiplier.integration.repository.model.JpaSFTPAccessRequest>()
        every { mockRequest.id } returns requestId
        every { mockRequest.companyId } returns companyId
        every { mockRequest.entityId } returns entityId
        every { mockRequest.bulkModule } returns bulkModule
        every { mockRequest.status } returns status

        // Set up company details
        val mockCompany = CompanyOuterClass.Company.newBuilder()
            .setDisplayName("Test Company")
            .setCompanyLogoId(Int64Value.newBuilder().setValue(1L).build())
            .setPrimaryEntity(LegalEntity.newBuilder().setAddress(CompanyOuterClass.Address.newBuilder().setCountry("VNM").build()).build())
            .build()

        val mockCompanyUsers = CompanyOuterClass.CompanyUsers.newBuilder()
            .addUsers(CompanyUser.newBuilder()
                .setId(1L)
                .setFirstName("Admin")
                .setLastName("User")
                .addEmails(EmailAddress.newBuilder().setType("primary").setEmail("<EMAIL>").build())
                .build())
            .build()

        // Set the opsSupportEmail field
        notificationsService.setPrivateField("opsSupportEmail", "<EMAIL>")

        // Configure mock services
        every { newCompanyServiceAdapter.getCompanyById(companyId) } returns mockCompany
        every { newCompanyServiceAdapter.getCompanyAdmins(companyId) } returns mockCompanyUsers
        every { countryService.getCountryNameByCode(Country.GrpcCountryCode.VNM) } returns "Vietnam"

        // Configure email template generator
        val emailCaptor = slot<EmailNotificationBody>()
        every {
            adminEmailTemplateGenerator.getEmailTemplateForSftpAccessRequest(
                opsEmail = "<EMAIL>"
            )
        } returns EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.IntegrationSFTPAccessRequestEmailToOps.toString())
            .setSubject("New SFTP Access Request Created")
            .setFrom(systemNotificationEmail)
            .setTo("<EMAIL>")

        every { emailService.sendEmail(capture(emailCaptor)) } returns Unit

        // When
        notificationsService.sendSFTPAccessRequestNotificationToOps(mockRequest)

        // Then
        verify(exactly = 1) { emailService.sendEmail(any()) }

        // Verify the captured email body has the expected properties
        val capturedEmail = emailCaptor.captured
        assertThat(capturedEmail.templateType).isEqualTo(NotificationType.IntegrationSFTPAccessRequestEmailToOps.toString())
        assertThat(capturedEmail.subject).isEqualTo("New SFTP Access Request Created")
        assertThat(capturedEmail.from).isEqualTo(systemNotificationEmail)
        assertThat(capturedEmail.to).isEqualTo("<EMAIL>")

        // Verify the content variables contain the expected data
        assertThat(capturedEmail.contentVariablesMap)
            .containsEntry("requestId", requestId.toString())
            .containsEntry("companyId", companyId.toString())
            .containsEntry("companyName", "Test Company")
            .containsEntry("entityId", entityId.toString())
            .containsEntry("bulkModule", bulkModule.name)
    }
}
