package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategory
import com.multiplier.integration.mock.getDocumentCategoriesResponse
import com.multiplier.integration.mock.getEmployeeDirectoryResponse
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockDocumentFolder
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationNotFoundException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.DocumentFolderDetail
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.UpsertDocumentFolderInput
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)
class DocumentFoldersServiceTest {
    @MockK
    lateinit var documentFoldersRepository: DocumentFoldersRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var dataMapper: DataMapper

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @InjectMockKs
    lateinit var documentFoldersService: DocumentFoldersService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getDocumentFolders successfully`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L
        val mockIntegration = getMockCompanyIntegration()
        val mockDocumentFolder = getMockDocumentFolder(mockIntegration.get())
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )

        coEvery {
            knitAdapter.getDocCategories(
                mockCompanyId,
                mockIntegration.get().platform.id!!
            )
        } returns getDocumentCategoriesResponse(true, documentCatergory)
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration
        every {
            documentFoldersRepository.findByFolderTypeAndIntegrationId(
                mockIntegrationId,
                DocumentFolderType.PAYSLIP
            )
        } returns mockDocumentFolder
        every {
            dataMapper.map(
                mockDocumentFolder.folderId,
                mockDocumentFolder.folderLabel
            )
        } returns DocumentFolderDetail.newBuilder()
            .id(mockDocumentFolder.folderId)
            .label(mockDocumentFolder.folderLabel)
            .build()
        every {
            dataMapper.map(
                documentCatergory.id,
                documentCatergory.name
            )
        } returns DocumentFolderDetail.newBuilder()
            .id(documentCatergory.id)
            .label(documentCatergory.name)
            .build()

        val resp =
            documentFoldersService.getDocumentFolders(mockCompanyId, mockIntegrationId, DocumentFolderType.PAYSLIP)

        assertEquals(mockDocumentFolder.folderLabel, resp.savedFolder.label)
        assertEquals(documentCatergory.name, resp.folders[0].label)
    }

    @Test
    fun `getDocumentFolders successfully with unsupported platform`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L
        val mockIntegration = getMockCompanyIntegration(platformName = "BambooHR")
        val mockEmployeeId = "1"

        coEvery { knitAdapter.getDocumentCategories(mockCompanyId, mockIntegration.get().platform.id!!, employeeId = mockEmployeeId) } returns getDocumentCategoriesResponse(true, "shared")
        coEvery { knitAdapter.getEmployeeDirectory(mockCompanyId, mockIntegration.get().platform.id!!) } returns getEmployeeDirectoryResponse(true, mockEmployeeId)
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration
        every {
            documentFoldersRepository.findByFolderTypeAndIntegrationId(
                mockIntegrationId,
                DocumentFolderType.PAYSLIP
            )
        } returns null
        every {
            dataMapper.map(
                "1",
                "shared"
            )
        } returns DocumentFolderDetail.newBuilder()
            .id("1")
            .label("shared")
            .build()

        val resp =
            documentFoldersService.getDocumentFolders(mockCompanyId, mockIntegrationId, DocumentFolderType.PAYSLIP)

        assertEquals(null, resp.savedFolder)
        assertEquals("shared", resp.folders[0].label)
    }

    @Test
    fun `getDocumentFolders failed not found integration`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L

        every { companyIntegrationRepository.findById(mockIntegrationId) } returns Optional.empty()

        val resp = assertThrows<IntegrationNotFoundException> {
            documentFoldersService.getDocumentFolders(mockCompanyId, mockIntegrationId, DocumentFolderType.PAYSLIP)
        }

        assertEquals("Integration not found for integrationId=$mockIntegrationId", resp.message)
    }

    @Test
    fun `getDocumentFolders failed not match company`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 1L
        val mockIntegration = getMockCompanyIntegration(platformName = "BambooHR")

        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration

        val resp = assertThrows<BadRequestException> {
            documentFoldersService.getDocumentFolders(mockCompanyId, mockIntegrationId, DocumentFolderType.PAYSLIP)
        }

        assertEquals("Integration is not related to company id of current user companyId: $mockCompanyId", resp.message)
    }

    @Test
    fun `upsertDocumentFolder successfully`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L
        val mockIntegration = getMockCompanyIntegration()
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        val mockRequest = UpsertDocumentFolderInput.newBuilder()
            .folderId("1")
            .type(DocumentFolderType.PAYSLIP)
            .integrationId(mockIntegrationId)
            .build()
        val mockDocumentFolder = getMockDocumentFolder(mockIntegration.get())

        coEvery {
            knitAdapter.getDocCategories(
                mockCompanyId,
                mockIntegration.get().platform.id!!
            )
        } returns getDocumentCategoriesResponse(true, documentCatergory)
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration
        every {
            documentFoldersRepository.findByFolderTypeAndIntegrationId(
                mockIntegrationId,
                DocumentFolderType.PAYSLIP
            )
        } returns null
        every {
            dataMapper.map(
                documentCatergory.id,
                documentCatergory.name
            )
        } returns DocumentFolderDetail.newBuilder()
            .id(documentCatergory.id)
            .label(documentCatergory.name)
            .build()
        every { documentFoldersRepository.save(any()) } returns mockDocumentFolder

        val resp = documentFoldersService.upsertDocumentFolder(mockCompanyId, mockRequest)

        assertEquals(documentCatergory.name, resp.savedFolder.label)
        assertEquals(documentCatergory.name, resp.folders[0].label)
    }

    @Test
    fun `upsertDocumentFolder successfully existed folder`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L
        val mockIntegration = getMockCompanyIntegration()
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        val mockRequest = UpsertDocumentFolderInput.newBuilder()
            .folderId("1")
            .type(DocumentFolderType.PAYSLIP)
            .integrationId(mockIntegrationId)
            .build()
        val mockDocumentFolder = getMockDocumentFolder(mockIntegration.get())

        coEvery {
            knitAdapter.getDocCategories(
                mockCompanyId,
                mockIntegration.get().platform.id!!
            )
        } returns getDocumentCategoriesResponse(true, documentCatergory)
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration
        every {
            documentFoldersRepository.findByFolderTypeAndIntegrationId(
                mockIntegrationId,
                DocumentFolderType.PAYSLIP
            )
        } returns mockDocumentFolder
        every {
            dataMapper.map(
                documentCatergory.id,
                documentCatergory.name
            )
        } returns DocumentFolderDetail.newBuilder()
            .id(documentCatergory.id)
            .label(documentCatergory.name)
            .build()
        every { documentFoldersRepository.save(any()) } returns mockDocumentFolder

        val resp = documentFoldersService.upsertDocumentFolder(mockCompanyId, mockRequest)

        assertEquals(documentCatergory.name, resp.savedFolder.label)
        assertEquals(documentCatergory.name, resp.folders[0].label)
    }

    @Test
    fun `upsertDocumentFolder failed not found integration`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L
        val mockRequest = UpsertDocumentFolderInput.newBuilder()
            .folderId("1")
            .type(DocumentFolderType.PAYSLIP)
            .integrationId(mockIntegrationId)
            .build()

        every { companyIntegrationRepository.findById(mockIntegrationId) } returns Optional.empty()

        val resp = assertThrows<IntegrationNotFoundException> {
            documentFoldersService.upsertDocumentFolder(mockCompanyId, mockRequest)
        }

        assertEquals("Integration not found for integrationId=$mockIntegrationId", resp.message)
    }

    @Test
    fun `upsertDocumentFolder failed not match company`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 1L
        val mockIntegration = getMockCompanyIntegration(platformName = "BambooHR")
        val mockRequest = UpsertDocumentFolderInput.newBuilder()
            .folderId("1")
            .type(DocumentFolderType.PAYSLIP)
            .integrationId(mockIntegrationId)
            .build()

        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration

        val resp = assertThrows<BadRequestException> {
            documentFoldersService.upsertDocumentFolder(mockCompanyId, mockRequest)
        }

        assertEquals("Integration is not related to company id of current user companyId: $mockCompanyId", resp.message)
    }

    @Test
    fun `upsertDocumentFolder failed not found target folder`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 100L
        val mockIntegration = getMockCompanyIntegration()
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        val mockRequest = UpsertDocumentFolderInput.newBuilder()
            .folderId("2")
            .type(DocumentFolderType.PAYSLIP)
            .integrationId(mockIntegrationId)
            .build()

        coEvery {
            knitAdapter.getDocCategories(
                mockCompanyId,
                mockIntegration.get().platform.id!!
            )
        } returns getDocumentCategoriesResponse(true, documentCatergory)
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns mockIntegration
        every {
            dataMapper.map(
                documentCatergory.id,
                documentCatergory.name
            )
        } returns DocumentFolderDetail.newBuilder()
            .id(documentCatergory.id)
            .label(documentCatergory.name)
            .build()

        val resp = assertThrows<BadRequestException> {
            documentFoldersService.upsertDocumentFolder(mockCompanyId, mockRequest)
        }

        assertEquals("Not found folderId 2 from existed folders", resp.message)
    }
}