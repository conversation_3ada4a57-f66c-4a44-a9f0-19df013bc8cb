package com.multiplier.integration.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.currency.Currency
import com.multiplier.core.schema.currency.Currency.CurrencyCode
import com.multiplier.expense.schema.BulkDeleteExpensesRequest
import com.multiplier.expense.schema.GrpcBulkDeleteExpensesResponse
import com.multiplier.expense.schema.GrpcExpense
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.CurrencyServiceAdapter
import com.multiplier.integration.adapter.api.ExpenseServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.mock.createMockBulkCreateExpensesResponse
import com.multiplier.integration.mock.createMockCreateExpensesRequestWithReportId
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockEntityIntegration
import com.multiplier.integration.mock.getMockExpenseData
import com.multiplier.integration.mock.getMockExpenseDataReceivedEvents
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.repository.EntityIntegrationRepository
import com.multiplier.integration.repository.JpaEntityIntegrationRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.repository.model.JpaEntityIntegration
import com.multiplier.integration.repository.type.EntityType
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.KnitIntegrationException
import com.multiplier.integration.sync.model.ExpenseDetails
import com.multiplier.integration.sync.model.ExpenseStatus
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class ExpenseProcessorServiceTest {
    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK
    lateinit var expenseServiceAdapter: ExpenseServiceAdapter

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var jpaEntityIntegrationRepository: JpaEntityIntegrationRepository

    @MockK
    lateinit var entityIntegrationRepository: EntityIntegrationRepository

    @MockK
    lateinit var currencyServiceAdapter: CurrencyServiceAdapter

    @InjectMockKs
    lateinit var expenseProcessorService: ExpenseProcessorService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    // Placeholder values for IDs
    val expenseId = 1234L
    val contractId = 123L
    val externalId = "externalId123"

    @Test
    fun `should process paid expense event successfully`() = testScope.runTest {
        val expenseId = 1L
        val reportId = "Test"
        val mockEntityIntegraion =
            getMockEntityIntegration(entityType = EntityType.EXPENSE, internalId = expenseId, externalId = reportId)

        every {
            entityIntegrationRepository.findFirstByEntityTypeAndInternalId(
                entityType = EntityType.EXPENSE.name,
                internalId = expenseId
            )
        } returns mockEntityIntegraion
        coEvery {
            knitAdapter.updateExpenseReport(
                mockEntityIntegraion.integrationId!!,
                mockEntityIntegraion.externalId
            )
        } returns true

        val mockUpdatedEntityIntegration = getMockEntityIntegration(
            entityType = EntityType.EXPENSE,
            internalId = expenseId,
            externalId = reportId,
            process = true,
            paid = true
        )
        every { entityIntegrationRepository.save(any()) } returns mockUpdatedEntityIntegration
        every { entityIntegrationRepository.findByExternalIdAndPaid(reportId, paid = false) } returns emptyList()
        every { entityIntegrationRepository.updateProcessedStatusByExternalId(reportId) } returns Unit

        expenseProcessorService.processPaidExpenseById(expenseId)

        verify(exactly = 1) { entityIntegrationRepository.save(mockEntityIntegraion) }
        coVerify(exactly = 1) { knitAdapter.updateExpenseReport(any(), any()) }
        verify(exactly = 1) { entityIntegrationRepository.updateProcessedStatusByExternalId(reportId) }
    }

    @Test
    fun `should throw not found integration id when processing paid expense event`() = testScope.runTest {
        val expenseId = 1L
        val mockEntityIntegraion = getMockEntityIntegration(entityType = EntityType.EXPENSE, internalId = expenseId, externalId = "Test", integrationId = null)

        every {
            entityIntegrationRepository.findFirstByEntityTypeAndInternalId(
                entityType = EntityType.EXPENSE.name,
                internalId = expenseId
            )
        } returns mockEntityIntegraion

        val actualException =
            assertFailsWith<EntityNotFoundException> {
                expenseProcessorService.processPaidExpenseById(expenseId)
            }

        Assertions.assertEquals(actualException.message, "Not found integration id with expense id $expenseId")
    }

    @Test
    fun `should not process if expense had already been processed`() = testScope.runTest {
        val expenseId = 1L
        val reportId = "Test"
        val mockEntityIntegraion = getMockEntityIntegration(
            entityType = EntityType.EXPENSE,
            internalId = expenseId,
            externalId = reportId,
            paid = true,
            process = true
        )

        every {
            entityIntegrationRepository.findFirstByEntityTypeAndInternalId(
                entityType = EntityType.EXPENSE.name,
                internalId = expenseId
            )
        } returns mockEntityIntegraion

        expenseProcessorService.processPaidExpenseById(expenseId)

        verify(exactly = 0) { entityIntegrationRepository.save(mockEntityIntegraion) }
        coVerify(exactly = 0) { knitAdapter.updateExpenseReport(any(), any()) }
    }

    @Test
    fun `should process paid expense event and not update report to be reimbursed`() = testScope.runTest {
        val expenseId = 1L
        val reportId = "Test"
        val mockEntityIntegraion =
            getMockEntityIntegration(entityType = EntityType.EXPENSE, internalId = expenseId, externalId = reportId)

        val mockOtherEntityIntegraion =
            getMockEntityIntegration(entityType = EntityType.EXPENSE, internalId = 2L, externalId = reportId, id = 2L)

        every {
            entityIntegrationRepository.findFirstByEntityTypeAndInternalId(
                entityType = EntityType.EXPENSE.name,
                internalId = expenseId
            )
        } returns mockEntityIntegraion

        val mockUpdatedEntityIntegration = getMockEntityIntegration(
            entityType = EntityType.EXPENSE,
            internalId = expenseId,
            externalId = reportId,
            process = true,
            paid = true
        )
        every { entityIntegrationRepository.save(any()) } returns mockUpdatedEntityIntegration
        every { entityIntegrationRepository.findByExternalIdAndPaid(reportId, paid = false) } returns listOf(mockOtherEntityIntegraion)

        expenseProcessorService.processPaidExpenseById(expenseId)

        verify(exactly = 1) { entityIntegrationRepository.save(mockEntityIntegraion) }
        coVerify(exactly = 0) { knitAdapter.updateExpenseReport(any(), any()) }
        verify(exactly = 0) { entityIntegrationRepository.updateProcessedStatusByExternalId(reportId) }
    }

    @Test
    fun `should update last expense status to paid but not update all expenses in the same reportId to processed due to knit update failed `() = testScope.runTest {
        val expenseId = 1L
        val reportId = "Test"
        val mockEntityIntegraion =
            getMockEntityIntegration(entityType = EntityType.EXPENSE, internalId = expenseId, externalId = reportId)

        every {
            entityIntegrationRepository.findFirstByEntityTypeAndInternalId(
                entityType = EntityType.EXPENSE.name,
                internalId = expenseId
            )
        } returns mockEntityIntegraion
        coEvery {
            knitAdapter.updateExpenseReport(
                mockEntityIntegraion.integrationId!!,
                mockEntityIntegraion.externalId
            )
        } returns false

        val mockUpdatedEntityIntegration = getMockEntityIntegration(
            entityType = EntityType.EXPENSE,
            internalId = expenseId,
            externalId = reportId,
            process = true,
            paid = true
        )
        every { entityIntegrationRepository.save(any()) } returns mockUpdatedEntityIntegration
        every { entityIntegrationRepository.findByExternalIdAndPaid(reportId, paid = false) } returns emptyList()

        val actualException =
            assertFailsWith<KnitIntegrationException> {
                expenseProcessorService.processPaidExpenseById(expenseId)
            }

        Assertions.assertEquals(actualException.message, "Fail update expense report to Knit")

        verify(exactly = 1) { entityIntegrationRepository.save(mockEntityIntegraion) }
        coVerify(exactly = 1) { knitAdapter.updateExpenseReport(any(), any()) }
        verify(exactly = 0) { entityIntegrationRepository.updateProcessedStatusByExternalId(reportId) }
    }

    @Test
    fun `should generate expense with different expense currency`() {
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)
        val convertedAmount = 15000.0
        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { jpaEntityIntegrationRepository.findByExternalId(any()) } returns emptyList()
        every { currencyServiceAdapter.getCurrencyExchangeAmount(CurrencyCode.INR, CurrencyCode.VND, 50.0) } returns convertedAmount

        val response = expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(mockReceivedEvent), EventType.RECORD_NEW)

        verify(exactly = 1) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals("R00DfWoZDDwb", response.first)
        Assertions.assertEquals(contractId, response.second.contractId)
        Assertions.assertEquals(1, response.second.itemsList.size)
        Assertions.assertEquals(convertedAmount, response.second.itemsList[0].amount)
        Assertions.assertEquals(true, response.second.itemsList[0].hasAmountInForeignCurrency())
    }

    @Test
    fun `should generate expense without converting expense currency`() {
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId, currency = Currency.CurrencyCode.INR)
        val originalAmount = 50.0
        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { jpaEntityIntegrationRepository.findByExternalId(any()) } returns emptyList()

        val response = expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(mockReceivedEvent), EventType.RECORD_NEW)

        verify(exactly = 0) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals("R00DfWoZDDwb", response.first)
        Assertions.assertEquals(contractId, response.second.contractId)
        Assertions.assertEquals(1, response.second.itemsList.size)
        Assertions.assertEquals(originalAmount, response.second.itemsList[0].amount)
        Assertions.assertEquals(false, response.second.itemsList[0].hasAmountInForeignCurrency())
    }

    @Test
    fun `should handleExpenseEvent successfully`() {
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData()
        val convertedAmount = 15000.0

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { currencyServiceAdapter.getCurrencyExchangeAmount(CurrencyCode.INR, CurrencyCode.VND, 50.0) } returns convertedAmount
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val response = expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_NEW)

        verify(exactly = 1) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals(mockContract.id, response)
        Assertions.assertEquals(mockExpenseData.details?.amount, expenseDetailsList[0].amount)
    }

    @Test
    fun `should handleExpenseEvent successfully for update event`() {
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData()
        val convertedAmount = 15000.0

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { currencyServiceAdapter.getCurrencyExchangeAmount(CurrencyCode.INR, CurrencyCode.VND, 50.0) } returns convertedAmount

        val response = expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_UPDATE)

        verify(exactly = 1) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals(mockContract.id, response)
        Assertions.assertEquals(mockExpenseData.details?.amount, expenseDetailsList[0].amount)
    }

    @Test
    fun `should handleExpenseEvent successfully with get contract by email null`() {
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(-1L, memberId)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData()
        val mockMember = getMockMember(memberId)
        val mockValidContract = getMockContract(contractId, memberId, currency = Currency.CurrencyCode.INR)

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { memberServiceAdapter.findMemberByEmailAddress(mockExpenseData.details!!.creatorEmail!!) } returns mockMember
        every { contractServiceAdapter.findContractByMemberId(memberId) } returns mockValidContract
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val response = expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_NEW)

        verify(exactly = 0) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals(mockValidContract.id, response)
        Assertions.assertEquals(mockExpenseData.details?.amount, expenseDetailsList[0].amount)
    }

    @Test
    fun `should handleExpenseEvent failed with not found member`() {
        val memberId = 2L
        val mockContract = getMockContract(-1L, memberId)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData()
        val mockMember = getMockMember(-1L)

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { memberServiceAdapter.findMemberByEmailAddress(mockExpenseData.details!!.creatorEmail!!) } returns mockMember
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val actualException =
            assertFailsWith<EntityNotFoundException> {
                expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_NEW)
            }

        Assertions.assertEquals(actualException.message, "Member not found for email ${mockExpenseData.details?.creatorEmail}")
    }

    @Test
    fun `should handleExpenseEvent failed with not found contract`() {
        val memberId = 2L
        val mockContract = getMockContract(-1L, memberId)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData()
        val mockMember = getMockMember(memberId)

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { memberServiceAdapter.findMemberByEmailAddress(mockExpenseData.details!!.creatorEmail!!) } returns mockMember
        every { contractServiceAdapter.findContractByMemberId(memberId) } returns mockContract
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val actualException =
            assertFailsWith<EntityNotFoundException> {
                expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_NEW)
            }

        Assertions.assertEquals(actualException.message, "Contract not found for member ID $memberId")
    }

    @Test
    fun `should handleExpenseEvent failed with inactive contract`() {
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId, status=ContractOuterClass.ContractStatus.DELETED)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData()

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val actualException =
            assertFailsWith<IntegrationIllegalStateException> {
                expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_NEW)
            }

        Assertions.assertEquals(actualException.message, "Ignoring event with eventId=${mockReceivedEvent.eventId} as the contract status is ${mockContract.status}")
    }

    @Test
    fun `should handleExpenseEvent failed with unapproved expense`() {
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId)
        val mockReceivedEvent = getMockExpenseDataReceivedEvents()
        val expenseDetailsList = mutableListOf<ExpenseDetails>()
        val mapExpenseIdToExpenseAmount: MutableMap<String?, Double?> = mutableMapOf()
        val mockExpenseData = getMockExpenseData(status = ExpenseStatus.ARCHIVED)

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val actualException =
            assertFailsWith<IntegrationIllegalStateException> {
                expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, expenseDetailsList, mapExpenseIdToExpenseAmount, EventType.RECORD_NEW)
            }

        Assertions.assertEquals(actualException.message, "Ignoring event with eventId=${mockReceivedEvent.eventId} as the expense status is ${mockExpenseData.details?.status}")
    }

    @Test
    fun `should process bulk revoke request with partial success`() {
        val successfulExpenseId = 1L
        val failedExpenseId = 2L
        val request = BulkDeleteExpensesRequest.newBuilder()
            .addExpenseIds(successfulExpenseId)
            .addExpenseIds(failedExpenseId)
            .build()

        every { expenseServiceAdapter.bulkRevokeExpensesNonTransactional(request) } returns GrpcBulkDeleteExpensesResponse.newBuilder()
            .addItems(GrpcExpense.newBuilder().setId(successfulExpenseId).build())
            .build()

        expenseProcessorService.processBulkRevokeExpenseRequest(request)

        verify { expenseServiceAdapter.bulkRevokeExpensesNonTransactional(request) }
    }

    @Test
    fun `should return allApproved as false when status is not approved for update`() {
        val mockReceivedEvent = getMockExpenseDataReceivedEvents(status = "OPEN")
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId, currency = Currency.CurrencyCode.INR)
        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract

        val response = expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(mockReceivedEvent), EventType.RECORD_UPDATE)

        verify(exactly = 0) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals("R00DfWoZDDwb", response.first)
        Assertions.assertEquals(contractId, response.second.contractId)
        Assertions.assertEquals(false, response.third)
    }

    @Test
    fun `should return allApproved as true when status is approved for update`() {
        val mockReceivedEvent = getMockExpenseDataReceivedEvents(status = "APPROVED")
        val contractId = 1L
        val memberId = 2L
        val mockContract = getMockContract(contractId, memberId, currency = Currency.CurrencyCode.INR)
        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockContract

        val response = expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(mockReceivedEvent), EventType.RECORD_UPDATE)

        verify(exactly = 0) { currencyServiceAdapter.getCurrencyExchangeAmount(any(), any(), any()) }
        Assertions.assertEquals("R00DfWoZDDwb", response.first)
        Assertions.assertEquals(contractId, response.second.contractId)
        Assertions.assertEquals(true, response.third)
    }

    @Test
    fun `processBulkCreateExpenseRequest should handle creation and update correctly`() {
        val expenseId1 = 100L
        val contractId1 = 200L
        val externalId1 = "externalId1"
        val eventTypeRecordNew = EventType.RECORD_NEW
        val eventTypeRecordUpdate = EventType.RECORD_UPDATE

        val createExpensesRequestWithReportId1 = createMockCreateExpensesRequestWithReportId(expenseId1, contractId1, externalId1)
        val createExpensesRequestList = mutableListOf(createExpensesRequestWithReportId1)

        // Mocking the responses
        every { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) } returns createMockBulkCreateExpensesResponse(listOf(expenseId1))
        every { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) } returns createMockBulkCreateExpensesResponse(listOf(expenseId1))
        every { jpaEntityIntegrationRepository.findByExternalId(any()) } returns listOf()
        every { jpaEntityIntegrationRepository.save(any()) } returnsArgument 0

        // Test for RECORD_NEW event type
        expenseProcessorService.processBulkCreateExpenseRequest(createExpensesRequestList, eventTypeRecordNew)
        verify(exactly = 1) { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) }
        verify(exactly = 0) { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) }

        // Test for RECORD_UPDATE event type
        expenseProcessorService.processBulkCreateExpenseRequest(createExpensesRequestList, eventTypeRecordUpdate)
        verify(exactly = 1) { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) }

        // Verifying JpaEntityIntegrationRepository interactions
        verify(atLeast = 2) { jpaEntityIntegrationRepository.save(any()) }
    }

    @Test
    fun `processBulkCreateExpenseRequest should handle empty request list gracefully`() {
        val emptyList = mutableListOf<CreateExpensesRequestWithReportId>()
        expenseProcessorService.processBulkCreateExpenseRequest(emptyList, EventType.RECORD_NEW)

        verify(exactly = 0) { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) }
        verify(exactly = 0) { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) }
    }

    @Test
    fun `should throw exception when expense status is not APPROVED and event type is RECORD_NEW`() {
        // Setup
        val mockReceivedEvent = getMockExpenseDataReceivedEvents(eventType = EventType.RECORD_NEW, status = "OPEN")
        val mockExpenseData = getMockExpenseData(status = ExpenseStatus.SUBMITTED)

        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns getMockContract(1L, 2L, currency = Currency.CurrencyCode.INR)
        every { memberServiceAdapter.findMemberByEmailAddress(any()) } returns getMockMember(1L)
        every { contractServiceAdapter.findContractByMemberId(any()) } returns getMockContract(1L, 2L, currency = Currency.CurrencyCode.INR)
        every { jpaEntityIntegrationRepository.findByExternalId(mockExpenseData.details?.reportId!!) } returns emptyList()

        val exception = assertFailsWith<IntegrationIllegalStateException> {
            // Call the method with the mocked data
            expenseProcessorService.handleExpenseEvent(mockReceivedEvent, mockExpenseData, mutableListOf(), mutableMapOf(), EventType.RECORD_NEW)
        }

        // Assert that the exception message is as expected
        Assertions.assertEquals("Ignoring event with eventId=${mockReceivedEvent.eventId} as the expense status is ${mockExpenseData.details?.status}", exception.message)
    }

    @Test
    fun `processBulkCreateExpenseRequest should call bulkUpsert for RECORD_UPDATE event`() {
        // Setup
        val mockCreateExpensesRequestWithReportId = createMockCreateExpensesRequestWithReportId(expenseId, contractId, externalId)
        val createExpensesRequestList = mutableListOf(mockCreateExpensesRequestWithReportId)

        every { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) } returns createMockBulkCreateExpensesResponse(listOf(expenseId))
        every { jpaEntityIntegrationRepository.findByExternalId(externalId) } returns emptyList()
        every { jpaEntityIntegrationRepository.save(any()) } answers { arg<JpaEntityIntegration>(0) }

        // Call
        expenseProcessorService.processBulkCreateExpenseRequest(createExpensesRequestList, EventType.RECORD_UPDATE)

        // Verify
        verify(exactly = 1) { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) }
        verify(exactly = 0) { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) }
    }

    @Test
    fun `processBulkCreateExpenseRequest should call bulkCreate for non-RECORD_UPDATE event`() {
        // Setup
        val mockCreateExpensesRequestWithReportId = createMockCreateExpensesRequestWithReportId(expenseId, contractId, externalId)
        val createExpensesRequestList = mutableListOf(mockCreateExpensesRequestWithReportId)

        every { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) } returns createMockBulkCreateExpensesResponse(listOf(expenseId))
        every { jpaEntityIntegrationRepository.save(any()) } answers { arg<JpaEntityIntegration>(0) }
        // Call for a different eventType
        expenseProcessorService.processBulkCreateExpenseRequest(createExpensesRequestList, EventType.RECORD_NEW)

        // Verify
        verify(exactly = 1) { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) }
        verify(exactly = 0) { expenseServiceAdapter.bulkUpsertExpensesNonTransactional(any()) }
    }

    @Test
    fun `should ignore event when expense status is REIMBURSED`() {
        // Setup
        val mockReceivedEvent = getMockExpenseDataReceivedEvents(status = ExpenseStatus.REIMBURSED.name)
        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockk(relaxed = true)

        // Execute & Verify
        val exception = Assertions.assertThrows(IntegrationIllegalStateException::class.java) {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(mockReceivedEvent), EventType.RECORD_NEW)
        }

        verify(exactly = 0) { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) }
        exception.message?.let { assert(it.contains("Ignoring event with eventId=${mockReceivedEvent.eventId} as the expense status is REIMBURSED")) }
    }

    @Test
    fun `should ignore event when expense status is ARCHIVED`() {
        // Setup
        val mockReceivedEvent = getMockExpenseDataReceivedEvents(status = ExpenseStatus.ARCHIVED.name)
        every { contractServiceAdapter.findContractByWorkEmail(any()) } returns mockk(relaxed = true)

        // Execute & Verify
        val exception = Assertions.assertThrows(IntegrationIllegalStateException::class.java) {
            expenseProcessorService.generateExpenseRequestFromExpenseEvents(listOf(mockReceivedEvent), EventType.RECORD_NEW)
        }

        verify(exactly = 0) { expenseServiceAdapter.bulkCreateExpensesNonTransactional(any()) }
        exception.message?.let { assert(it.contains("Ignoring event with eventId=${mockReceivedEvent.eventId} as the expense status is ARCHIVED")) }
    }
}