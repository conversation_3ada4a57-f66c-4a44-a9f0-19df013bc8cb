package com.multiplier.integration.service

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.integration.repository.model.URIType
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class FileExtractorFactoryTest {

    @Nested
    inner class GetExtractor {

        @Test
        fun `should return UKGTimeSheetFileExtractor for UKG xlsx file`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "c_aira/2253166/ukg/timesheets/upload/file.xlsx",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When
            val extractor = FileExtractorFactory.getExtractor(input)

            // Then
            assertThat(extractor).isEqualTo(UKGTimeSheetFileExtractor)
        }

        @Test
        fun `should return CsvTimeSheetFileExtractor for Hubstaff csv file`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "c_aira/2253166/hubstaff/timesheets/upload/file.csv",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When
            val extractor = FileExtractorFactory.getExtractor(input)

            // Then
            assertThat(extractor).isEqualTo(CsvTimeSheetFileExtractor)
        }

        @Test
        fun `should throw MplBusinessException for unsupported file format in UKG path`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "c_aira/2253166/ukg/timesheets/upload/file.pdf",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When & Then
            assertThatThrownBy { FileExtractorFactory.getExtractor(input) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("Unsupported file format or source in URI")
        }

        @Test
        fun `should throw MplBusinessException for unsupported file format in Hubstaff path`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "c_aira/2253166/hubstaff/timesheets/upload/file.xlsx",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When & Then
            assertThatThrownBy { FileExtractorFactory.getExtractor(input) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("Unsupported file format or source in URI")
        }

        @Test
        fun `should throw MplBusinessException for unsupported source path`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "c_aira/2253166/unknown_source/timesheets/upload/file.xlsx",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When & Then
            assertThatThrownBy { FileExtractorFactory.getExtractor(input) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("Unsupported file format or source in URI")
        }

        @Test
        fun `should handle UKG path with different directory structure`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "different/path/ukg/data/file.xlsx",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When
            val extractor = FileExtractorFactory.getExtractor(input)

            // Then
            assertThat(extractor).isEqualTo(UKGTimeSheetFileExtractor)
        }

        @Test
        fun `should handle Hubstaff path with different directory structure`() {
            // Given
            val input = IntegrationInput(
                type = URIType.SFTP,
                uri = "different/path/hubstaff/data/file.csv",
                companyId = 123L,
                module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                entityId = 456L
            )

            // When
            val extractor = FileExtractorFactory.getExtractor(input)

            // Then
            assertThat(extractor).isEqualTo(CsvTimeSheetFileExtractor)
        }
    }
}