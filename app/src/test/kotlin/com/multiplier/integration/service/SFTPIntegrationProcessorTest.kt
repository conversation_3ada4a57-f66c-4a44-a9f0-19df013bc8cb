package com.multiplier.integration.service

import com.multiplier.common.concurrent.MplExecutor
import com.multiplier.integration.adapter.api.BulkUploadServiceAdapter
import com.multiplier.integration.adapter.api.CUSTOMER_INTEGRATION_SERVICE_GROUP
import com.multiplier.integration.adapter.api.GetValidationReportInput
import com.multiplier.integration.adapter.api.Job
import com.multiplier.integration.adapter.api.ValidationReport
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaBulkJobTracker
import com.multiplier.integration.repository.model.URIType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.io.InputStream
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.function.Supplier

@ExtendWith(MockKExtension::class)
class SFTPIntegrationProcessorTest {

    @MockK
    private lateinit var mockSFTPDirectoryService: SFTPDirectoryService

    @MockK
    private lateinit var mockBulkUploadServiceAdapter: BulkUploadServiceAdapter

    @MockK
    private lateinit var mockBulkJobTrackerService: BulkJobTrackerService

    @MockK
    private lateinit var mockNotificationService: NotificationsService

    @InjectMockKs
    private lateinit var sftpIntegrationProcessor: SFTPIntegrationProcessor

    @BeforeEach
    fun mockMplExecutor() {
        mockkObject(MplExecutor)
        val testExecutor = Executors.newSingleThreadExecutor()
        MplExecutor::class.java.getDeclaredField("globalExecutor").apply {
            isAccessible = true
            set(null, testExecutor)
        }
        every { MplExecutor.supplyAsync(any(), any<Supplier<*>>()) } answers {
            val supplier = secondArg<Supplier<*>>()
            CompletableFuture.supplyAsync {
                supplier.get()
            }
        }
    }

    @Nested
    inner class StartIntegration {

        @Test
        fun `should start integration successfully`() {
            // Given
            val mockInputStream = mockk<InputStream>()
            val mockDataEntries = listOf(mapOf("key" to "value"))
            val mockedProcessingURI = "/test/processing/file.xlsx"
            val mockJobId = 100L
            val mockJob = Job(id = mockJobId)

            // Mock responses
            mockkObject(FileExtractorFactory)
            mockkObject(UKGTimeSheetFileExtractor)
            every { FileExtractorFactory.getExtractor(any()) } returns UKGTimeSheetFileExtractor
            every { mockSFTPDirectoryService.downloadFile(any()) } returns mockInputStream
            every { mockSFTPDirectoryService.moveFileToProcessingDir(any()) } returns mockedProcessingURI
            every { UKGTimeSheetFileExtractor.extractEntries(mockInputStream) } returns mockDataEntries
            every { mockBulkUploadServiceAdapter.createJob(any()) } returns mockJob
            every { mockBulkUploadServiceAdapter.validateJob(any()) } returns mockJob
            every { mockBulkJobTrackerService.createBulkJobTracker(any()) } returns mockk()

            // When
            sftpIntegrationProcessor.startIntegration(integrationInput)

            // Then
            verify { mockSFTPDirectoryService.downloadFile(integrationInput.uri) }
            verify { mockSFTPDirectoryService.moveFileToProcessingDir(integrationInput.uri) }
            verify {
                mockBulkUploadServiceAdapter.createJob(
                    match { jobInput ->
                        jobInput.companyId == integrationInput.companyId &&
                        jobInput.entityId == integrationInput.entityId &&
                        jobInput.groupName == CUSTOMER_INTEGRATION_SERVICE_GROUP &&
                        jobInput.moduleNames == setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name) &&
                        jobInput.jobData.size == 1 &&
                        jobInput.jobData[0].name == "EOR_TIMESHEET" &&
                        jobInput.jobData[0].moduleName == BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name &&
                        jobInput.jobData[0].rows == mockDataEntries
                    }
                )
            }
            verify { mockBulkUploadServiceAdapter.validateJob(mockJobId) }
            verify {
                mockBulkJobTrackerService.createBulkJobTracker(
                    match { jobTracker ->
                        jobTracker.jobId == mockJobId &&
                        jobTracker.jobStatus == BulkJobStatus.VALIDATION_IN_PROGRESS &&
                        jobTracker.companyId == integrationInput.companyId &&
                        jobTracker.entityId == integrationInput.entityId &&
                        jobTracker.groupName == CUSTOMER_INTEGRATION_SERVICE_GROUP &&
                        jobTracker.moduleNames == setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name) &&
                        jobTracker.originalFileURI == mockedProcessingURI &&
                        jobTracker.originalURIType == URIType.SFTP
                    }
                )
            }
        }

        private val integrationInput = IntegrationInput(
            companyId = 1L,
            entityId = 2L,
            uri = "c_test/123/ukg/timesheets/upload/file.xlsx",
            type = URIType.SFTP,
            module = BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
        )
    }

    @Nested
    inner class HandleValidationFailure {

        @Test
        fun `should handle validation failure`() {
            // Given
            val mockJobId = 100L
            val mockBulkJobTracker = createBulkJobTracker(mockJobId)
            val mockGetValidationReportInput = GetValidationReportInput(mockJobId)
            val mockReportURL = "http://localhost/test/report/file.xlsx"
            val mockReportInputStream = mockk<InputStream>()
            val mockSFTPReportURI = "/test/report/file.xlsx"
            val mockArchiveURI = "/test/archive/file.xlsx"

            // Mock responses
            mockkObject(WebDownloader)
            every { mockBulkJobTrackerService.findByIdOrThrow(mockJobId) } returns mockBulkJobTracker
            every { WebDownloader.downloadAsInputStream(mockReportURL) } returns mockReportInputStream
            every {
                mockBulkUploadServiceAdapter.getValidationReport(mockGetValidationReportInput)
            } returns ValidationReport(mockReportURL)
            every {
                mockSFTPDirectoryService.uploadReportFile(mockReportInputStream, mockBulkJobTracker.originalFileURI!!)
            } returns mockSFTPReportURI
            every {
                mockSFTPDirectoryService.moveFileToArchive(mockBulkJobTracker.originalFileURI!!)
            } returns mockArchiveURI
            every { mockBulkUploadServiceAdapter.cancelJob(mockJobId) } just runs
            every { mockBulkJobTrackerService.updateBulkJobTracker(any()) } returns mockk()
            every { mockNotificationService.notifySFTPValidationFailure(mockJobId) } just runs

            // When
            sftpIntegrationProcessor.handleValidationFailure(mockJobId)

            // Then
            verify { mockBulkJobTrackerService.findByIdOrThrow(mockJobId) }
            verify { mockBulkUploadServiceAdapter.getValidationReport(mockGetValidationReportInput) }
            verify { WebDownloader.downloadAsInputStream(mockReportURL) }
            verify { mockSFTPDirectoryService.uploadReportFile(mockReportInputStream, mockBulkJobTracker.originalFileURI!!) }
            verify { mockSFTPDirectoryService.moveFileToArchive(mockBulkJobTracker.originalFileURI!!) }
            verify { mockBulkUploadServiceAdapter.cancelJob(mockJobId) }
            verify {
                mockBulkJobTrackerService.updateBulkJobTracker(
                    match { jobTrackerDTO ->
                        jobTrackerDTO.id == mockJobId &&
                        jobTrackerDTO.jobStatus == BulkJobStatus.VALIDATION_FAILED &&
                        jobTrackerDTO.originalFileURI == mockArchiveURI &&
                        jobTrackerDTO.reportFileURI == mockSFTPReportURI &&
                        jobTrackerDTO.reportURIType == URIType.SFTP
                    }
                )
            }
            verify { mockNotificationService.notifySFTPValidationFailure(mockJobId) }
        }
    }

    @Nested
    inner class HandleValidationSuccess {

        @Test
        fun `should handle validation success`() {
            // Given
            val mockJobId = 200L
            val mockBulkUploadTracker = createBulkJobTracker(mockJobId)

            // Mock responses
            every { mockBulkJobTrackerService.findByIdOrThrow(mockJobId) } returns mockBulkUploadTracker
            every { mockBulkUploadServiceAdapter.commitJob(mockJobId) } returns mockk()
            every { mockBulkJobTrackerService.updateBulkJobTracker(any()) } returns mockk()
            every { mockNotificationService.notifySFTPUpsertSuccess(mockJobId) } just runs

            // When
            sftpIntegrationProcessor.handleValidationSuccess(mockJobId)

            // Then
            verify { mockBulkJobTrackerService.findByIdOrThrow(mockJobId) }
            verify { mockBulkUploadServiceAdapter.commitJob(mockJobId) }
            verify {
                mockBulkJobTrackerService.updateBulkJobTracker(
                    match { input: BulkJobTrackerDTO ->
                        input.id == mockJobId &&
                        input.jobStatus == BulkJobStatus.UPSERT_IN_PROGRESS &&
                        input.originalFileURI == mockBulkUploadTracker.originalFileURI &&
                        input.reportFileURI == null &&
                        input.reportURIType == null
                    }
                )
            }
        }
    }

    @Nested
    inner class HandleDataCreationSuccess {

        @Test
        fun `should handle data creation success`() {
            // Given
            val mockJobId = 300L
            val mockBulkUploadTracker = createBulkJobTracker(mockJobId)
            val mockArchiveURI = "/test/archive/file.xlsx"

            // Mock responses
            every { mockBulkJobTrackerService.findByIdOrThrow(mockJobId) } returns mockBulkUploadTracker
            every { mockSFTPDirectoryService.moveFileToArchive(mockBulkUploadTracker.originalFileURI!!) } returns mockArchiveURI
            every { mockBulkJobTrackerService.updateBulkJobTracker(any()) } returns mockk()
            every { mockNotificationService.notifySFTPUpsertSuccess(any()) } just runs

            // When
            sftpIntegrationProcessor.handleDataCreationSuccess(mockJobId)

            // Then
            verify { mockBulkJobTrackerService.findByIdOrThrow(mockJobId) }
            verify { mockSFTPDirectoryService.moveFileToArchive(mockBulkUploadTracker.originalFileURI!!) }
            verify {
                mockBulkJobTrackerService.updateBulkJobTracker(
                    match { input: BulkJobTrackerDTO ->
                        input.id == mockJobId &&
                        input.jobStatus == BulkJobStatus.UPSERT_SUCCESSFUL &&
                        input.originalFileURI == mockArchiveURI &&
                        input.reportFileURI == null &&
                        input.reportURIType == null
                    }
                )
            }
            verify { mockNotificationService.notifySFTPUpsertSuccess(any()) }
        }
    }

    private fun createBulkJobTracker(jobId: Long) = JpaBulkJobTracker(
        id = jobId,
        companyId = 1L,
        entityId = 2L,
        groupName = CUSTOMER_INTEGRATION_SERVICE_GROUP,
        moduleNames = setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name),
        jobId = jobId,
        jobStatus = BulkJobStatus.VALIDATION_IN_PROGRESS,
        originalFileURI = "/test/processing/file.xlsx",
        originalURIType = URIType.SFTP,
    )
}
