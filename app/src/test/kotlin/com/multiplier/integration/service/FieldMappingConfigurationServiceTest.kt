package com.multiplier.integration.service

import com.multiplier.integration.mock.getMockDefaultFieldConfigs
import com.multiplier.integration.repository.FieldMappingConfigurationRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.FieldMappingConfigurationType
import com.multiplier.integration.repository.model.JpaExternalPlatformValues
import com.multiplier.integration.repository.model.JpaFieldMappingConfiguration
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.types.FieldMappingConfiguration
import com.multiplier.integration.types.FieldMappingConfigurationInput
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)

class FieldMappingConfigurationServiceTest {
    @MockK
    lateinit var fieldMappingConfigurationRepository: FieldMappingConfigurationRepository

    @MockK
    lateinit var dataMapper: DataMapper

    @InjectMockKs
    lateinit var fieldMappingConfigurationService: FieldMappingConfigurationService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getFieldMappingConfiguration successfully`() {
        val mockFieldConfigs = getMockDefaultFieldConfigs()

        every { fieldMappingConfigurationRepository.findByTypeAndIsDeletedFalse(type=FieldMappingConfigurationType.DEFAULT) } returns mockFieldConfigs
        every { dataMapper.map(mockFieldConfigs[0]) } returns FieldMappingConfiguration.newBuilder()
            .key(mockFieldConfigs[0].key)
            .value(mockFieldConfigs[0].value)
            .build()
        every { dataMapper.map(mockFieldConfigs[1]) } returns FieldMappingConfiguration.newBuilder()
            .key(mockFieldConfigs[1].key)
            .value(mockFieldConfigs[1].value)
            .build()

        val resp = fieldMappingConfigurationService.getFieldMappingConfiguration(type="DEFAULT")

        assertEquals("firstName", resp[0].key)
        assertEquals("profile.firstName", resp[0].value)
    }

    @Test
    fun `upsertFieldMappingConfiguration update existed config successfully`() {
        val mockFieldConfigs = getMockDefaultFieldConfigs()
        val input = listOf(
            FieldMappingConfigurationInput.newBuilder()
                .key("firstName")
                .value("testFirstName")
                .build()
        )
        val updateFieldConfig = mockk<JpaFieldMappingConfiguration>(relaxed = true) {
            every { key } returns "firstName"
            every { value } returns "testFirstName"
        }

        every { fieldMappingConfigurationRepository.findByKeyInAndIsDeletedFalse(listOf("firstName")) } returns listOf(mockFieldConfigs[0]) andThen listOf(updateFieldConfig)
        every { fieldMappingConfigurationRepository.saveAll(any<List<JpaFieldMappingConfiguration>>()) } returns listOf(mockk<JpaFieldMappingConfiguration>())
        every { dataMapper.map(updateFieldConfig) } returns FieldMappingConfiguration.newBuilder()
            .key(updateFieldConfig.key)
            .value(updateFieldConfig.value)
            .build()

        val resp = fieldMappingConfigurationService.upsertFieldMappingConfiguration(input)

        assertEquals("testFirstName", resp[0].value)
    }

    @Test
    fun `upsertFieldMappingConfiguration insert new config successfully`() {
        val input = listOf(
            FieldMappingConfigurationInput.newBuilder()
                .key("firstName")
                .value("testFirstName")
                .build()
        )
        val updateFieldConfig = mockk<JpaFieldMappingConfiguration>(relaxed = true) {
            every { key } returns "firstName"
            every { value } returns "testFirstName"
        }

        every { fieldMappingConfigurationRepository.findByKeyInAndIsDeletedFalse(listOf("firstName")) } returns emptyList() andThen listOf(updateFieldConfig)
        every { fieldMappingConfigurationRepository.saveAll(any<List<JpaFieldMappingConfiguration>>()) } returns listOf(mockk<JpaFieldMappingConfiguration>())
        every { dataMapper.map(updateFieldConfig) } returns FieldMappingConfiguration.newBuilder()
            .key(updateFieldConfig.key)
            .value(updateFieldConfig.value)
            .build()

        val resp = fieldMappingConfigurationService.upsertFieldMappingConfiguration(input)

        assertEquals("testFirstName", resp[0].value)
    }

    @Test
    fun `deleteFieldMappingConfigs successfully`() {
        val mockFieldConfigs = getMockDefaultFieldConfigs()
        val input = listOf("firstName", "lastName")

        every { fieldMappingConfigurationRepository.findByKeyInAndIsDeletedFalse(keys = input) } returns mockFieldConfigs
        every { fieldMappingConfigurationRepository.saveAll(any<List<JpaFieldMappingConfiguration>>()) } returns listOf(mockk<JpaFieldMappingConfiguration>())

        val resp = fieldMappingConfigurationService.deleteFieldMappingConfigs(input)

        assertEquals(true, resp.success)
    }
}