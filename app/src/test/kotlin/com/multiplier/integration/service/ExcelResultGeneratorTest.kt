package com.multiplier.integration.service

import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

class ExcelResultGeneratorTest
{
    @Test
    fun `should add errors to the appropriate rows while retaining the original column and value order, including empty cells`() {
        val input =
            getFileFromResources("/templates/error-report-file.xlsx")

        val row2Error1 = "this is a very long error message and the sheet should update the width"
        val row2Error2 = "first name is missing"
        val row4Error1 = "salary is negative"

        val resultByteArray =
            ExcelResultGenerator.addValidationErrorsToInputSheet(
                inputSheet = input,
                validationErrors =
                listOf(
                    EmployeeValidationError(
                        error = row2Error1,
                        rowNumber = 3,
                        columnName = "",
                        employeeName = "",
                        integrationId = "",
                    ),
                    EmployeeValidationError(
                        error = row2Error2,
                        rowNumber = 3,
                        columnName = "",
                        employeeName = "",
                        integrationId = "",
                    ),
                    EmployeeValidationError(
                        error = row4Error1,
                        rowNumber = 5,
                        columnName = "",
                        employeeName = "",
                        integrationId = "",
                    )))

        Assertions.assertNotNull(resultByteArray)
    }

    @Test
    fun `addSyncSummaryResultToInputSheet successfully`() {
        val input = getResourceAsStream("/templates/sync-summary-file.xlsx")

        val resp =
            ExcelResultGenerator.addSyncSummaryResultToInputSheet(
                inputSheet = input,
                results = listOf(
                    SyncSummaryResult(
                        status = EventStatus.SUCCESS.name,
                        contractId = 1L,
                        eventType = EventType.INCOMING_ONBOARDING_STATUS_UPDATE,
                        error = "Test error",
                        employeeInfo = null
                    )
                ),
            )

        Assertions.assertNotNull(resp)
    }
}