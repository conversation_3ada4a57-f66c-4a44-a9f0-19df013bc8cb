package com.multiplier.integration.service

import com.multiplier.integration.service.InboundDataExtractionService.BaseExtractor.Companion.extractDataFromPath
import com.multiplier.integration.service.InboundDataExtractionService.BaseExtractor.Companion.extractFromListWithConditions
import com.multiplier.integration.service.InboundDataExtractionService.BaseExtractor.Companion.extractFromListWithIndex
import com.multiplier.integration.service.InboundDataExtractionService.CountryExtractor.Companion.extractCountry
import com.multiplier.integration.service.InboundDataExtractionService.CountryExtractor.Companion.extractState
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class InboundDataExtractionServiceTest {

    @Nested
    inner class BaseExtractor {
        @Test
        fun `should extract data from list with index`() {
            Assertions.assertNull(extractFromListWithIndex(listOf(0, 1), "invalidIndex"))
            Assertions.assertEquals(0, extractFromListWithIndex(listOf(0, 1), "0"))
            Assertions.assertEquals(1, extractFromListWithIndex(listOf(0, 1), "1"))
            Assertions.assertNull(extractFromListWithIndex(listOf(0, 1), "2"))
        }

        @Test
        fun `should extract data from list with conditions`() {
            Assertions.assertNull(
                extractFromListWithConditions(
                    listOf(mapOf("type" to "IBAN")),
                    "type=BANK_IDENTIFICATION_CODE"
                )
            )
            Assertions.assertNotNull(
                extractFromListWithConditions(
                    listOf(
                        mapOf("type" to "IBAN"),
                        mapOf("type" to "BANK_IDENTIFICATION_CODE")
                    ),
                    "type=BANK_IDENTIFICATION_CODE"
                )
            )
            Assertions.assertNull(
                extractFromListWithConditions(
                    listOf(mapOf("type" to "BANK_IDENTIFICATION_CODE")),
                    "type=BANK_IDENTIFICATION_CODE,foo=bar"
                )
            )
            Assertions.assertNotNull(
                extractFromListWithConditions(
                    listOf(mapOf("type" to "BANK_IDENTIFICATION_CODE", "foo" to "bar")),
                    "type=BANK_IDENTIFICATION_CODE,foo=bar"
                )
            )
        }

        @Test
        fun `should extract data from path`() {
            Assertions.assertNull(
                extractDataFromPath(
                    mapOf(),
                    "bankAccounts[0].routingInfo[type=SWIFT_CODE].number"
                )
            )
            Assertions.assertEquals(
                "123",
                extractDataFromPath(
                    mapOf(
                        "bankAccounts" to listOf(
                            mapOf(
                                "routingInfo" to listOf(
                                    mapOf(
                                        "type" to "SWIFT_CODE",
                                        "number" to "123"
                                    )
                                )
                            )
                        )
                    ),
                    "bankAccounts[0].routingInfo[type=SWIFT_CODE].number"
                )
            )
        }
    }

    @Nested
    inner class CountryExtractor {
        @Test
        fun `should extract country`() {
            Assertions.assertNull(extractCountry(mapOf()))
            Assertions.assertEquals(
                "CAN",
                extractCountry(
                    mapOf(
                        "locations" to mapOf(
                            "workAddress" to mapOf(
                                "country" to "CAN"
                            )
                        )
                    )
                )
            )
            Assertions.assertEquals(
                "CAN",
                extractCountry(
                    mapOf(
                        "locations" to mapOf(
                            "presentAddress" to mapOf(
                                "country" to "CAN"
                            )
                        )
                    )
                )
            )
            Assertions.assertEquals(
                "CAN",
                extractCountry(
                    mapOf(
                        "locations" to mapOf(
                            "permanentAddress" to mapOf(
                                "country" to "CAN"
                            )
                        )
                    )
                )
            )
            Assertions.assertEquals(
                "CAN",
                extractCountry(
                    mapOf(
                        "customFields" to mapOf(
                            "fields" to mapOf(
                                "country" to "CAN"
                            )
                        )
                    )
                )
            )
        }

        @Test
        fun `should extract state`() {
            Assertions.assertNull(extractState(mapOf()))
            Assertions.assertEquals(
                "BC",
                extractState(
                    mapOf(
                        "locations" to mapOf(
                            "workAddress" to mapOf(
                                "state" to "BC"
                            )
                        )
                    )
                )
            )
            Assertions.assertEquals(
                "BC",
                extractState(
                    mapOf(
                        "locations" to mapOf(
                            "presentAddress" to mapOf(
                                "state" to "BC"
                            )
                        )
                    )
                )
            )
            Assertions.assertEquals(
                "BC",
                extractState(
                    mapOf(
                        "locations" to mapOf(
                            "permanentAddress" to mapOf(
                                "state" to "BC"
                            )
                        )
                    )
                )
            )
            Assertions.assertEquals(
                "BC",
                extractState(
                    mapOf(
                        "customFields" to mapOf(
                            "fields" to mapOf(
                                "state" to "BC"
                            )
                        )
                    )
                )
            )
        }
    }
}