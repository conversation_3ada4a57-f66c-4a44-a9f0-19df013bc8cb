package com.multiplier.integration.service

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.integration.repository.SFTPAccessRequestRepository
import com.multiplier.integration.repository.model.BulkUploadModule
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import com.multiplier.integration.types.BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
import com.multiplier.integration.types.SftpAccessRequestInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class SFTPAccessRequestServiceTest {

    @MockK
    private lateinit var sftpAccessRequestRepository: SFTPAccessRequestRepository

    @MockK
    private lateinit var notificationsService: NotificationsService

    @InjectMockKs
    private lateinit var sftpAccessRequestService: SFTPAccessRequestService

    @BeforeEach
    fun setUp() {
        // Mock notifications service
        every { notificationsService.sendSFTPAccessRequestNotificationToOps(any()) } returns Unit
    }

    @Nested
    inner class CreateSftpAccessRequest {

        @Test
        fun `should create a new request successfully and send email notification`() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val bulkModule = TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA

            val input = SftpAccessRequestInput.newBuilder()
                .companyId(companyId)
                .entityId(entityId)
                .bulkModule(bulkModule)
                .build()

            val savedJpaRequest = JpaSFTPAccessRequest(
                id = 1L,
                companyId = companyId,
                entityId = entityId,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.PENDING,
                mainSFTPDirectory = null
            )

            // Mock repository behavior
            every {
                sftpAccessRequestRepository.findByCompanyIdAndEntityIdAndBulkModule(
                    companyId = companyId,
                    entityId = entityId,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
                )
            } returns null

            val capturedRequest = slot<JpaSFTPAccessRequest>()
            every { sftpAccessRequestRepository.save(capture(capturedRequest)) } returns savedJpaRequest

            // Mock the notification service
            every { notificationsService.sendSFTPAccessRequestNotificationToOps(any()) } returns Unit

            // When
            val result = sftpAccessRequestService.createSftpAccessRequest(input)

            // Then
            verify { sftpAccessRequestRepository.save(any()) }
            verify { notificationsService.sendSFTPAccessRequestNotificationToOps(savedJpaRequest) }

            // Verify the saved entity has the correct properties
            assertThat(capturedRequest.captured.companyId).isEqualTo(companyId)
            assertThat(capturedRequest.captured.entityId).isEqualTo(entityId)
            assertThat(capturedRequest.captured.bulkModule).isEqualTo(BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA)
            assertThat(capturedRequest.captured.status).isEqualTo(SftpAccessRequestStatus.PENDING)
            assertThat(capturedRequest.captured.mainSFTPDirectory).isNull()

            // Verify the returned object has the correct properties
            assertThat(result.id).isEqualTo(1L)
            assertThat(result.companyId).isEqualTo(companyId)
            assertThat(result.entityId).isEqualTo(entityId)
            assertThat(result.bulkModule).isEqualTo(bulkModule)
            assertThat(result.status).isEqualTo(com.multiplier.integration.types.SftpAccessRequestStatus.PENDING)
            assertThat(result.mainSftpDirectory).isNull()
        }

        @Test
        fun `should throw exception when request already exists`() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val bulkModule = TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA

            val input = SftpAccessRequestInput.newBuilder()
                .companyId(companyId)
                .entityId(entityId)
                .bulkModule(bulkModule)
                .build()

            val existingRequest = JpaSFTPAccessRequest(
                id = 1L,
                companyId = companyId,
                entityId = entityId,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.PENDING,
                mainSFTPDirectory = null
            )

            // Mock repository behavior
            every {
                sftpAccessRequestRepository.findByCompanyIdAndEntityIdAndBulkModule(
                    companyId = companyId,
                    entityId = entityId,
                    bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
                )
            } returns existingRequest

            // When/Then
            assertThatThrownBy { sftpAccessRequestService.createSftpAccessRequest(input) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessage("SFTP access request already exists for this company, entity, and module combination")
        }
    }
}
