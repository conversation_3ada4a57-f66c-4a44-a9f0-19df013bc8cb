package com.multiplier.integration.service

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.mockPlatformContractIntegration
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.types.PlatformCategory
import io.mockk.*
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class DevMigrationServiceTest {

    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository
    private lateinit var contractServiceAdapter: ContractServiceAdapter
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository
    private lateinit var devMigrationService: DevMigrationService

    @BeforeEach
    fun setup() {
        platformContractIntegrationRepository = mockk()
        contractServiceAdapter = mockk()
        companyIntegrationRepository = mockk()
        devMigrationService = DevMigrationService(
            platformContractIntegrationRepository,
            contractServiceAdapter,
            companyIntegrationRepository
        )
    }

    @Test
    fun `migrateIntegrationIdForContractIntegrationTable processes all batches successfully`() = runBlocking {
        // Mock data
        val contractIds = listOf(1L, 2L, 3L)
        val contracts = listOf(
            mockk<ContractOuterClass.Contract> { every { id } returns 1L; every { companyId } returns 100L },
            mockk<ContractOuterClass.Contract> { every { id } returns 2L; every { companyId } returns 100L },
            mockk<ContractOuterClass.Contract> { every { id } returns 3L; every { companyId } returns 200L }
        )
        val integrations = listOf(
            mockk<JpaCompanyIntegration> {
                every { companyId } returns 100L
                every { id } returns 1000L
                every { enabled } returns true
                every { platform.category } returns PlatformCategory.HRIS
            },
            mockk<JpaCompanyIntegration> {
                every { companyId } returns 200L
                every { id } returns 2000L
                every { enabled } returns true
                every { platform.category } returns PlatformCategory.HRIS
            }
        )
        val contractIntegration1 = mockPlatformContractIntegration(id=1L, contractId = 1L, platformEmployeeId = "employee")
        val contractIntegration2 = mockPlatformContractIntegration(id=2L, contractId = 2L, platformEmployeeId = "employee")
        val contractIntegration3 = mockPlatformContractIntegration(id=3L, contractId = 3L, platformEmployeeId = "employee")
        val contractIntegrations = listOf(
            contractIntegration1, contractIntegration2, contractIntegration3
        )

        // Setup mocks
        every { platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull() } returns contractIds.toSet()
        coEvery { contractServiceAdapter.getContractsByContractIds(any()) } returns contracts
        every { companyIntegrationRepository.findByCompanyIdIn(any()) } returns integrations
        every { platformContractIntegrationRepository.findAllByContractIdIn(any()) } returns contractIntegrations
        every { platformContractIntegrationRepository.saveAll(any<List<JpaPlatformContractIntegration>>()) } returns contractIntegrations

        // Execute
        devMigrationService.migrateIntegrationIdForContractIntegrationTable(2)

        // Verify
        verify { platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull() }
        coVerify { contractServiceAdapter.getContractsByContractIds(any()) }
        verify { companyIntegrationRepository.findByCompanyIdIn(setOf(100L, 200L)) }
        verify { platformContractIntegrationRepository.findAllByContractIdIn(any()) }
        verify { platformContractIntegrationRepository.saveAll(any<List<JpaPlatformContractIntegration>>()) }

        // Verify that integrationId is set correctly for each contract integration
        contractIntegrations.forEach { integration ->
            when (integration.contractId) {
                1L, 2L -> assert(integration.integrationId == 1000L)
                3L -> assert(integration.integrationId == 2000L)
            }
        }
    }

    @Test
    fun `migrateIntegrationIdForContractIntegrationTable handles empty contract list`() = runBlocking {
        every { platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull() } returns emptySet()

        devMigrationService.migrateIntegrationIdForContractIntegrationTable(2)

        verify { platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull() }
        coVerify(exactly = 0) { contractServiceAdapter.getContractsByContractIds(any()) }
    }

    @Test
    fun `migrateIntegrationIdForContractIntegrationTable handles no matching integrations`() = runBlocking {
        val contractIds = listOf(1L)
        val contracts = listOf(mockk<ContractOuterClass.Contract> { every { id } returns 1L; every { companyId } returns 100L })

        every { platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull() } returns contractIds.toSet()
        coEvery { contractServiceAdapter.getContractsByContractIds(any()) } returns contracts
        every { companyIntegrationRepository.findByCompanyIdIn(any()) } returns emptyList()
        every { platformContractIntegrationRepository.findAllByContractIdIn(any()) } returns emptyList()
        every { platformContractIntegrationRepository.saveAll(any<List<JpaPlatformContractIntegration>>()) } returns emptyList()


        devMigrationService.migrateIntegrationIdForContractIntegrationTable(2)

        verify { platformContractIntegrationRepository.findDistinctContractIdByIntegrationIdIsNull() }
        coVerify { contractServiceAdapter.getContractsByContractIds(any()) }
        verify { companyIntegrationRepository.findByCompanyIdIn(setOf(100L)) }
        verify { platformContractIntegrationRepository.findAllByContractIdIn(any()) }
        verify { platformContractIntegrationRepository.saveAll(emptyList()) }
    }
}