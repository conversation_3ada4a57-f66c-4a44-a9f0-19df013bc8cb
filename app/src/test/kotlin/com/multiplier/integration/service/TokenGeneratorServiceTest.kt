package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.DefaultTriNetAPIAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.MergeDevAdapter
import com.multiplier.integration.adapter.api.resources.financial.Error
import com.multiplier.integration.adapter.api.resources.financial.MergeDevAccountTokenRetrieveMessage
import com.multiplier.integration.adapter.api.resources.financial.MergeDevLinkTokenMessage
import com.multiplier.integration.adapter.api.resources.financial.MergeDevResponse
import com.multiplier.integration.adapter.api.resources.trinet.GetAccessTokenResponse
import com.multiplier.integration.adapter.api.resources.trinet.GetLocationByNameResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderPlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.model.JpaProviderPlatform
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.repository.type.ProviderPlatformStatus
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.types.MergeDevLinkTokenErrorResponse
import com.multiplier.integration.types.MergeDevLinkTokenSuccessResponse
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.types.SyncType
import com.multiplier.integration.types.TaskResponse
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.test.runBlockingTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.core.env.Environment
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class TokenGeneratorServiceTest {

    @Mock
    private lateinit var environment: Environment

    @Mock
    private lateinit var knitAdapter: KnitAdapter

    @InjectMocks
    private lateinit var service: TokenGeneratorService


    @Mock
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @Mock
    private lateinit var providerRepository: ProviderRepository

    @Mock
    private lateinit var platformRepository: PlatformRepository

    @Mock
    private lateinit var providerPlatformRepository: ProviderPlatformRepository

    @Mock
    private lateinit var triNetAPIAdapter: DefaultTriNetAPIAdapter

    @Mock
    private lateinit var syncService: SyncService

    @Mock
    private lateinit var notificaionService: NotificationsService

    @Mock
    private lateinit var customerIntegrationService: CustomerIntegrationService

    @Mock
    private lateinit var mergeDevAdapter: MergeDevAdapter

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    // Happy Path Tests
    @Test
    fun testCreateCompanyPlatformIntegrationHappyPath1() = runBlockingTest {
        // Arrange
        val companyId = 1L
        val platformId = 2L
        val token = "token"
        val platform = mock<JpaPlatform>()
        val platformProvider = mock<JpaProviderPlatform>()
        val provider = mock<JpaProvider>()
        val companyIntegration = mock<JpaCompanyIntegration>()

        whenever(platformRepository.findById(platformId)).thenReturn(Optional.of(platform))
        whenever(provider.id).thenReturn(123L)
        whenever(providerRepository.findById(any())).thenReturn(Optional.of(provider))
        whenever(providerPlatformRepository.findByPlatformIdAndStatus(platformId, ProviderPlatformStatus.ACTIVE))
            .thenReturn(listOf(platformProvider))
        whenever(platformProvider.provider).thenReturn(provider)
        whenever(companyIntegrationRepository.save(any<JpaCompanyIntegration>())).thenReturn(companyIntegration)

        // Act
        service.createCompanyPlatformIntegration(companyId, platformId, token)

        // Assert
        verify(companyIntegrationRepository).save(any<JpaCompanyIntegration>())
    }

    // Non-Happy Path Tests
    @Test
    fun testCreateCompanyPlatformIntegrationWithBlankToken() = runBlockingTest {
        // Arrange
        val companyId = 1L
        val platformId = 2L

        // Act & Assert
        assertFailsWith<IntegrationIllegalArgumentException> {
            service.createCompanyPlatformIntegration(companyId, platformId, "")
        }
    }

    @Test
    fun testCreateCompanyPlatformIntegrationWithPlatformNotFound() = runBlockingTest {
        // Arrange
        val companyId = 1L
        val platformId = 2L
        val token = "token"

        whenever(platformRepository.findById(platformId)).thenReturn(Optional.empty())

        // Act & Assert
        assertFailsWith<IntegrationIllegalArgumentException> {
            service.createCompanyPlatformIntegration(companyId, platformId, token)
        }
    }

    @Test
    fun testCreateCompanyPlatformIntegrationWithNoActiveProviders() = runBlockingTest {
        // Arrange
        val companyId = 1L
        val platformId = 2L
        val token = "token"
        val platform = mock<JpaPlatform>()

        whenever(platformRepository.findById(platformId)).thenReturn(Optional.of(platform))
        whenever(providerPlatformRepository.findByPlatformIdAndStatus(platformId, ProviderPlatformStatus.ACTIVE))
            .thenReturn(emptyList())

        // Act & Assert
        assertFailsWith<IntegrationIllegalArgumentException> {
            service.createCompanyPlatformIntegration(companyId, platformId, token)
        }
    }

    @Test
    fun testCreateCompanyPlatformIntegrationWithIntegrationAlreadyExists() = runBlockingTest {
        // Arrange
        val companyId = 1L
        val platformId = 2L
        val token = "token"
        val platform = mock<JpaPlatform>()
        val platformProvider = mock<JpaProviderPlatform>()
        val provider = mock<JpaProvider>()

        whenever(platformRepository.findById(platformId)).thenReturn(Optional.of(platform))
        whenever(providerPlatformRepository.findByPlatformIdAndStatus(platformId, ProviderPlatformStatus.ACTIVE))
            .thenReturn(listOf(platformProvider))
        whenever(platformProvider.provider).thenReturn(provider)

        // Act & Assert
        assertFailsWith<IntegrationIllegalArgumentException> {
            service.createCompanyPlatformIntegration(companyId, platformId, token)
        }
    }

    @Test
    fun `should get TriNet credential successfully`() {
        val companyUserName = "test"
        val companyUserPassword = "test"
        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.HRIS,
            name = "TriNet",
            isPositionDropdownEnabled = false
        )
        val provider = JpaProvider(
            id = 1L,
            name = ProviderName.TRINET
        )
        val mockCompanyId = 1L
        val externalCompanyId = "test"
        val mockGetAccessTokenResponse = GetAccessTokenResponse(
            tokenType = "BearerToken",
            apiProductList = "[API-Multiplier-qen1]",
            accessToken = "",
            expiresIn = ""
        )
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { enabled } returns true
            every { companyId } returns mockCompanyId
        }

        whenever(triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)).thenReturn(
            mockGetAccessTokenResponse
        )
        whenever(
            platformRepository.findFirstByCategoryAndName(
                PlatformCategory.HRIS,
                "TriNet"
            )
        ).thenReturn(mockPlatform)
        whenever(providerRepository.findFirstByName(ProviderName.TRINET)).thenReturn(provider)
        whenever(companyIntegrationRepository.findIntegration(mockCompanyId, provider, mockPlatform)).thenReturn(null)
        whenever(companyIntegrationRepository.save(any<JpaCompanyIntegration>())).thenReturn(companyIntegration)
        whenever(customerIntegrationService.changeSyncState(integrationId = companyIntegration.id!!, syncType = SyncType.OUTGOING, companyIntegration.enabled, companyIntegration.companyId)).thenReturn(
            TaskResponse.newBuilder().success(true).build()
        )
        whenever(triNetAPIAdapter.getLocationByName(any(), any())).thenReturn(GetLocationByNameResponse())

        val resp = service.getTriNetCredential(mockCompanyId, externalCompanyId, companyUserName, companyUserPassword)

        assertEquals(true, resp.success)
        verify(companyIntegrationRepository).save(any<JpaCompanyIntegration>())
    }

    @Test
    fun `should get TriNet credential fail for empty input`() {
        val resp = service.getTriNetCredential(null, null, null, null)

        assertEquals(false, resp.success)
        assertEquals("Not externalCompanyId or companyUserName or companyUserPassword can be empty", resp.message)
    }

    @Test
    fun `should get TriNet credential fail for trinet adapter throw errors`() {
        val companyUserName = "test"
        val companyUserPassword = "test"
        val companyId = 1L
        val externalCompanyId = "test"

        whenever(triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)).thenReturn(null)

        val resp = service.getTriNetCredential(companyId, externalCompanyId, companyUserName, companyUserPassword)

        assertEquals(false, resp.success)
        assertEquals("Invalid TriNet credential", resp.message)
    }

    @Test
    fun `should get TriNet credential fail for not found platform`() {
        val companyUserName = "test"
        val companyUserPassword = "test"
        val companyId = 1L
        val externalCompanyId = "test"
        val mockGetAccessTokenResponse = GetAccessTokenResponse(
            tokenType = "BearerToken",
            apiProductList = "[API-Multiplier-qen1]",
            accessToken = "",
            expiresIn = ""
        )

        whenever(triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)).thenReturn(
            mockGetAccessTokenResponse
        )
        whenever(platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet")).thenReturn(null)

        val resp = service.getTriNetCredential(companyId, externalCompanyId, companyUserName, companyUserPassword)

        assertEquals(false, resp.success)
        assertEquals("Not found platform TriNet", resp.message)
    }

    @Test
    fun `should get TriNet credential fail for not found provider`() {
        val companyUserName = "test"
        val companyUserPassword = "test"
        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.HRIS,
            name = "TriNet",
            isPositionDropdownEnabled = false
        )
        val companyId = 1L
        val externalCompanyId = "test"
        val mockGetAccessTokenResponse = GetAccessTokenResponse(
            tokenType = "BearerToken",
            apiProductList = "[API-Multiplier-qen1]",
            accessToken = "",
            expiresIn = ""
        )

        whenever(triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)).thenReturn(
            mockGetAccessTokenResponse
        )
        whenever(
            platformRepository.findFirstByCategoryAndName(
                PlatformCategory.HRIS,
                "TriNet"
            )
        ).thenReturn(mockPlatform)
        whenever(providerRepository.findFirstByName(ProviderName.TRINET)).thenReturn(null)

        val resp = service.getTriNetCredential(companyId, externalCompanyId, companyUserName, companyUserPassword)

        assertEquals(false, resp.success)
        assertEquals("Not found  provider ${ProviderName.TRINET}", resp.message)
    }

    @Test
    fun `should get TriNet credential successfully with existed integration`() {
        val companyUserName = "test"
        val companyUserPassword = "test"
        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.HRIS,
            name = "TriNet",
            isPositionDropdownEnabled = false
        )
        val provider = JpaProvider(
            id = 1L,
            name = ProviderName.TRINET
        )
        val mockCompanyId = 1L
        val externalCompanyId = "test"
        val mockGetAccessTokenResponse = GetAccessTokenResponse(
            tokenType = "BearerToken",
            apiProductList = "[API-Multiplier-qen1]",
            accessToken = "",
            expiresIn = ""
        )
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { enabled } returns true
            every { companyId } returns mockCompanyId
        }
        whenever(triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)).thenReturn(
            mockGetAccessTokenResponse
        )
        whenever(
            platformRepository.findFirstByCategoryAndName(
                PlatformCategory.HRIS,
                "TriNet"
            )
        ).thenReturn(mockPlatform)
        whenever(providerRepository.findFirstByName(ProviderName.TRINET)).thenReturn(provider)
        whenever(companyIntegrationRepository.findIntegration(mockCompanyId, provider, mockPlatform)).thenReturn(companyIntegration)
        whenever(companyIntegrationRepository.save(any<JpaCompanyIntegration>())).thenReturn(companyIntegration)
        whenever(customerIntegrationService.changeSyncState(integrationId = companyIntegration.id!!, syncType = SyncType.OUTGOING, companyIntegration.enabled, companyIntegration.companyId)).thenReturn(
            TaskResponse.newBuilder().success(true).build()
        )
        whenever(triNetAPIAdapter.getLocationByName(any(), any())).thenReturn(GetLocationByNameResponse())

        val resp = service.getTriNetCredential(mockCompanyId, externalCompanyId, companyUserName, companyUserPassword)

        assertEquals(true, resp.success)
        verify(companyIntegrationRepository).save(any<JpaCompanyIntegration>())
    }

    @Test
    fun `should get TriNet credential failed with error update sync state`() {
        val companyUserName = "test"
        val companyUserPassword = "test"
        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.HRIS,
            name = "TriNet",
            isPositionDropdownEnabled = false
        )
        val provider = JpaProvider(
            id = 1L,
            name = ProviderName.TRINET
        )
        val mockCompanyId = 1L
        val externalCompanyId = "test"
        val mockGetAccessTokenResponse = GetAccessTokenResponse(
            tokenType = "BearerToken",
            apiProductList = "[API-Multiplier-qen1]",
            accessToken = "",
            expiresIn = ""
        )
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns 1L
            every { enabled } returns true
            every { companyId } returns mockCompanyId
        }
        whenever(triNetAPIAdapter.getAccessToken(companyUserName, companyUserPassword)).thenReturn(
            mockGetAccessTokenResponse
        )
        whenever(
            platformRepository.findFirstByCategoryAndName(
                PlatformCategory.HRIS,
                "TriNet"
            )
        ).thenReturn(mockPlatform)
        whenever(providerRepository.findFirstByName(ProviderName.TRINET)).thenReturn(provider)
        whenever(companyIntegrationRepository.findIntegration(mockCompanyId, provider, mockPlatform)).thenReturn(companyIntegration)
        whenever(companyIntegrationRepository.save(any<JpaCompanyIntegration>())).thenReturn(companyIntegration)
        whenever(customerIntegrationService.changeSyncState(integrationId = companyIntegration.id!!, syncType = SyncType.OUTGOING, companyIntegration.enabled, companyIntegration.companyId)).thenReturn(
            TaskResponse.newBuilder().success(false).message("Failed updated sync state").build()
        )
        whenever(triNetAPIAdapter.getLocationByName(any(), any())).thenReturn(GetLocationByNameResponse())

        val resp = service.getTriNetCredential(mockCompanyId, externalCompanyId, companyUserName, companyUserPassword)

        assertEquals(false, resp.success)
        assertEquals("Failed updated sync state", resp.message)
    }

    @Test
    fun `should get mergeDev link token fails with illegal argument exception`() {
        val companyName = ""
        val mockCompanyId = 1L
        val companyEmail = "test"

        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.ACCOUNTING,
            name = "XERO",
            isPositionDropdownEnabled = false
        )

        assertFailsWith<IntegrationIllegalArgumentException> {
            mockPlatform.id?.let {
                service.getLinkTokenFromMergeDev(mockCompanyId, companyEmail, companyName,
                    it
                )
            }
        }
    }


    @Test
    fun `should get mergeDev link token fails with illegal argument exception platform does not exist`() {
        val companyName = "test"
        val mockCompanyId = 1L
        val companyEmail = "test"

        whenever(platformRepository.findById(any())).thenReturn(Optional.empty())

        assertFailsWith<IntegrationIllegalArgumentException> {
            service.getLinkTokenFromMergeDev(mockCompanyId, companyEmail, companyName, 1L)
        }
    }

    @Test
    fun `should get mergeDev link token fails with illegal argument exception platform name does not exist`() {
        val companyName = "test"
        val mockCompanyId = 1L
        val companyEmail = "test"

        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.ACCOUNTING,
            name = "",
            isPositionDropdownEnabled = false
        )

        whenever(platformRepository.findById(any())).thenReturn(Optional.of(mockPlatform))

        assertFailsWith<IntegrationIllegalArgumentException> {
            service.getLinkTokenFromMergeDev(mockCompanyId, companyEmail, companyName, mockPlatform.id!!)
        }
    }

    @Test
    fun `should get mergeDev link token fail response`() {
        val companyName = "test"
        val mockCompanyId = 1L
        val companyEmail = "test"

        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.ACCOUNTING,
            name = "XERO",
            isPositionDropdownEnabled = false
        )

        whenever(platformRepository.findById(any())).thenReturn(Optional.of(mockPlatform))

        val mergeDevResponse = MergeDevResponse(
            success = false,
            message = null,
            error = Error(
                errorMessage = "Unknown error",
                errorCode = null
            )
        )

        whenever(mergeDevAdapter.createLinkToken(any())).thenReturn(mergeDevResponse)

        val linkTokenFromMergeDev =
            service.getLinkTokenFromMergeDev(mockCompanyId, companyEmail, companyName, mockPlatform.id!!)

        val mergeDevLinkTokenErrorResponse = linkTokenFromMergeDev as MergeDevLinkTokenErrorResponse
        assertEquals(mergeDevResponse.error?.errorMessage, mergeDevLinkTokenErrorResponse.error.msg)
    }


    @Test
    fun `should get mergeDev link token successfully`() {
        val companyName = "test"
        val mockCompanyId = 1L
        val companyEmail = "test"

        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.ACCOUNTING,
            name = "XERO",
            isPositionDropdownEnabled = false
        )

        whenever(platformRepository.findById(any())).thenReturn(Optional.of(mockPlatform))

        val mergeDevLinkTokenMessage = MergeDevLinkTokenMessage(
            linkToken = "linkToken",
            integrationName = "Xero",
            magicLinkUrl = "magicLinkUrl",
        )

        val mergeDevResponse = MergeDevResponse(
            success = true,
            message = mergeDevLinkTokenMessage,
            error = null,
        )

        whenever(mergeDevAdapter.createLinkToken(any())).thenReturn(mergeDevResponse)

        val linkTokenFromMergeDev =
            service.getLinkTokenFromMergeDev(mockCompanyId, companyEmail, companyName, mockPlatform.id!!)

        val mergeDevLinkTokenSuccessResponse = linkTokenFromMergeDev as MergeDevLinkTokenSuccessResponse
        assertEquals(mergeDevLinkTokenMessage.linkToken , mergeDevLinkTokenSuccessResponse.token.token)
    }

    @Test
    fun `should get mergeDev account token fails with illegal argument exception`() = runBlockingTest {
        val mockCompanyId = 1L
        assertFailsWith<IntegrationIllegalArgumentException>{
            service.createOrUpdateMergeDevToken(mockCompanyId, 1L, "", false)
        }
    }

    @Test
    fun `should get mergeDev account token fails in merge dev api `() {
        val mergeDevResponse = MergeDevResponse(
            success = false,
            message = null,
            error = Error(
                errorMessage = "Unknown error",
                errorCode = null
            )
        )

        whenever(mergeDevAdapter.retrieveAccountToken(any())).thenReturn(mergeDevResponse)
        runBlockingTest {
            assertFailsWith<BadRequestException> {
                service.createOrUpdateMergeDevToken(1L, 1L, "public token", false)
            }
        }
    }

    @Test
    fun `should get mergeDev account token successfully`() {

        val mergeDevAccountTokenRetrieveMessage = MergeDevAccountTokenRetrieveMessage(
            accountToken = "account token",
            integrationName = "Xero",
            integrationCategories = emptyList()
        )

        val mergeDevResponse = MergeDevResponse(
            success = true,
            message = mergeDevAccountTokenRetrieveMessage,
            error = null
        )

        val mockPlatform = JpaPlatform(
            id = 1L,
            category = PlatformCategory.ACCOUNTING,
            name = "XERO",
            isPositionDropdownEnabled = false
        )

        val platform = mockPlatform
        val platformProvider = mock<JpaProviderPlatform>()
        val provider = mock<JpaProvider>()
        val companyIntegration = mock<JpaCompanyIntegration>()

        whenever(platformRepository.findById(1L)).thenReturn(Optional.of(platform))
        whenever(provider.id).thenReturn(1L)
        whenever(providerRepository.findById(any())).thenReturn(Optional.of(provider))
        whenever(providerPlatformRepository.findByPlatformIdAndStatus(1L, ProviderPlatformStatus.ACTIVE))
            .thenReturn(listOf(platformProvider))
        whenever(platformProvider.provider).thenReturn(provider)
        whenever(companyIntegrationRepository.save(any<JpaCompanyIntegration>())).thenReturn(companyIntegration)
        whenever(mergeDevAdapter.retrieveAccountToken(any())).thenReturn(mergeDevResponse)

        runBlockingTest {
            service.createOrUpdateMergeDevToken(1L, 1L, "public token", false)
            verify(companyIntegrationRepository).save(any<JpaCompanyIntegration>())
        }
    }

}
