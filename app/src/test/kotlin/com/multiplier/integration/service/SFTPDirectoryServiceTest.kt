package com.multiplier.integration.service

import com.multiplier.common.aws.s3.S3Service
import com.multiplier.integration.utils.FTPDirectoryUtil
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.extension.ExtendWith
import java.io.ByteArrayInputStream

@ExtendWith(MockKExtension::class)
class SFTPDirectoryServiceTest {

    @MockK
    private lateinit var s3Service: S3Service

    @InjectMockKs
    private lateinit var sftpDirectoryService: SFTPDirectoryService

    private val mockInputStream = ByteArrayInputStream("test".toByteArray())
    private val sourceURI = "c_aira/ukg/timesheets/upload/test.xlsx"
    private val processingURI = "c_aira/ukg/timesheets/processing/test_2025-04-01T11-16-00.xlsx"

    @Test
    fun `downloadFile should return input stream`() {
        // Given
        every { s3Service.download(any()) } returns mockInputStream

        // When
        val result = sftpDirectoryService.downloadFile(sourceURI)

        // Then
        assertThat(result).isEqualTo(mockInputStream)
        verify {
            s3Service.download(match {
                it.fileName == FTPDirectoryUtil.getFileName(sourceURI) &&
                it.path == FTPDirectoryUtil.getFilePath(sourceURI)
            })
        }
    }

    @Test
    fun `moveFileToProcessingDir should copy file and delete original`() {
        // Given
        every { s3Service.copy(any()) } just runs
        every { s3Service.delete(any()) } just runs

        // When
        val result = sftpDirectoryService.moveFileToProcessingDir(sourceURI)

        // Then
        val expectedProcessingFilePath = FTPDirectoryUtil.generateProcessingFilePath(sourceURI)
        verify {
            s3Service.copy(match {
                it.sourcePath == FTPDirectoryUtil.getFilePath(sourceURI) &&
                it.sourceFileName == FTPDirectoryUtil.getFileName(sourceURI) &&
                it.destinationPath == expectedProcessingFilePath &&
                it.destinationFilename.startsWith("test") &&
                it.destinationFilename.endsWith(".xlsx")
            })
        }
        verify {
            s3Service.delete(match { input ->
                input.filePaths.size == 1 &&
                input.filePaths.first().path == FTPDirectoryUtil.getFilePath(sourceURI) &&
                input.filePaths.first().fileName == FTPDirectoryUtil.getFileName(sourceURI)
            })
        }
        assertThat(result).contains(expectedProcessingFilePath)
        assertThat(result).endsWith(".xlsx")
    }

    @Test
    fun `moveFileToArchive should copy file and delete original`() {
        // Given
        every { s3Service.copy(any()) } just runs
        every { s3Service.delete(any()) } just runs

        // When
        val result = sftpDirectoryService.moveFileToArchive(processingURI)

        // Then
        val expectedArchiveFilePath = FTPDirectoryUtil.generateArchiveFilePath(processingURI)
        val expectedArchiveFileName = FTPDirectoryUtil.getFileName(processingURI)
        verify {
            s3Service.copy(match {
                it.sourceFileName == FTPDirectoryUtil.getFileName(processingURI) &&
                it.sourcePath == FTPDirectoryUtil.getFilePath(processingURI) &&
                it.destinationFilename == expectedArchiveFileName &&
                it.destinationPath == expectedArchiveFilePath
            })
            s3Service.delete(any())
        }
        assertThat(result).contains("$expectedArchiveFilePath/$expectedArchiveFileName")
    }

    @Test
    fun `uploadReportFile should upload file and return URI`() {
        // Given
        every { s3Service.upload(any()) } just runs

        // When
        val result = sftpDirectoryService.uploadReportFile(mockInputStream, processingURI)

        // Then
        val expectedReportFilePath = FTPDirectoryUtil.generateReportFilePath(processingURI)
        val expectedReportFileName = FTPDirectoryUtil.generateReportFileName(processingURI)
        verify {
            s3Service.upload(match {
                it.path == expectedReportFilePath &&
                it.fileName == expectedReportFileName
            })
        }
        assertThat(result).isEqualTo("$expectedReportFilePath/$expectedReportFileName")
    }

    @Test
    fun `deleteFile should handle exceptions gracefully`() {
        // Given
        every { s3Service.delete(any()) } throws RuntimeException("Delete failed")
        every { s3Service.copy(any()) } just runs

        // Then
        assertDoesNotThrow { sftpDirectoryService.moveFileToProcessingDir(sourceURI) }
    }

}