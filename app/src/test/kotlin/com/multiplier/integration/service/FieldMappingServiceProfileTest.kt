package com.multiplier.integration.service

import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.fieldmapping.grpc.schema.ListProfilesResponse
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.fieldmapping.grpc.schema.ProfileResponse
import com.multiplier.fieldmapping.grpc.schema.Rule
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.PaymentServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.adapter.api.resources.knit.FieldDataList
import com.multiplier.integration.adapter.api.resources.knit.GetAllFieldsResponse
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockLegalEntity
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.ExternalPlatformValuesRepository
import com.multiplier.integration.repository.FieldMappingConfigurationRepository
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository
import com.multiplier.integration.repository.model.JpaExternalPlatformValues
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.repository.model.LegalMappingStatus
import com.multiplier.integration.utils.mapPlatformIdToKnitAppId
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.Optional
import java.util.UUID
import kotlin.test.assertNull

@ExtendWith(SpringExtension::class)
class FieldMappingServiceProfileTest {

    @MockK
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var knitAdapter: KnitAdapter

    @MockK
    private lateinit var receivedEventRepository: ReceivedEventRepository

    @MockK
    private lateinit var newCompanyServiceAdapter: NewCompanyServiceAdapter

    @MockK
    private lateinit var fieldsMappingRepository: FieldsMappingRepository

    @MockK
    private lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @MockK
    private lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    private lateinit var externalPlatformValuesRepository: ExternalPlatformValuesRepository

    @MockK
    private lateinit var fieldMappingConfigurationRepository: FieldMappingConfigurationRepository

    @MockK
    private lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockK
    private lateinit var receivedEventsArchiveRepository: ReceivedEventsArchiveRepository

    @MockK
    private lateinit var fieldMappingServiceAdapter: FieldMappingServiceAdapter

    @InjectMockKs
    private lateinit var fieldMappingService: FieldMappingService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should return profile when found`() {
        // Arrange
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val profileId = UUID.randomUUID().toString()
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        // Mock profile with existing configuration
        val configMap = Struct.newBuilder()
            .putFields("entityId", Value.newBuilder().setStringValue(entityId.toString()).build())
            .putFields("integrationId", Value.newBuilder().setStringValue(integrationId.toString()).build())
            .build()

        val profile = Profile.newBuilder()
            .setId(profileId)
            .setName("Test Profile")
            .setDescription("Test Description")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setConfigMap(configMap)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("source_field")
                    .setTargetField("target_field")
                    .setIsRequired(true)
                    .build()
            )
            .build()

        val listProfilesResponse = ListProfilesResponse.newBuilder()
            .addProfiles(profile)
            .build()

        val knitFields = GetAllFieldsResponse(
            success = true,
            data = FieldDataList(
                default = listOf(
                    FieldData(
                        fieldId = "firstName",
                        fieldFromApp = "firstName",
                        mappedKey = "firstName",
                    )
                )
            )
        )

        // Setup mocks
        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns listProfilesResponse
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(
            externalValues
        )
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId,entityId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Act
        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        // Assert
        assertEquals(UUID.fromString(profileId), result.profileId)
        assertEquals(integrationId, result.integrationId)
        assertEquals(companyId, result.companyId)
        assertEquals(LegalMappingStatus.FULLY_MAPPED.name, result.entityMappingStatus.name)

        // Verify
        verify(exactly = 1) { fieldMappingServiceAdapter.listProfiles(companyId) }
        verify(exactly = 0) { fieldMappingServiceAdapter.createProfile(any()) }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should create new profile when none exists`() {
        // Arrange
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val profileId = UUID.randomUUID().toString()
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        // Empty list of profiles
        val emptyListProfilesResponse = ListProfilesResponse.newBuilder().build()

        // Mock the newly created profile
        val configMap = Struct.newBuilder()
            .putFields("entityId", Value.newBuilder().setStringValue(entityId.toString()).build())
            .putFields("integrationId", Value.newBuilder().setStringValue(integrationId.toString()).build())
            .build()

        val newProfile = Profile.newBuilder()
            .setId(profileId)
            .setName("Test Legal Entity - Test Platform")
            .setDescription("Profile for Test Legal Entity - Test Platform")
            .setCompanyId(companyId)
            .setIsActive(false)
            .setConfigMap(configMap)
            .build()

        val profileResponse = ProfileResponse.newBuilder()
            .setProfile(newProfile)
            .build()

        val knitFields = GetAllFieldsResponse(
            success = true,
            data = FieldDataList(
                default = listOf(
                    FieldData(
                        fieldId = "firstName",
                        fieldFromApp = "firstName",
                        mappedKey = "firstName",
                    )
                )
            )
        )

        // Setup mocks
        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns emptyListProfilesResponse
        every { fieldMappingServiceAdapter.createProfile(any()) } returns profileResponse
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(
            externalValues
        )
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId,entityId) } returns Optional.of(legalEntityMapping)
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Act
        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        // Assert
        assertEquals(UUID.fromString(profileId), result.profileId)
        assertEquals(integrationId, result.integrationId)
        assertEquals(companyId, result.companyId)
        assertEquals(LegalMappingStatus.UNMAPPED.name, result.entityMappingStatus.name)

        // Verify
        verify(exactly = 1) { fieldMappingServiceAdapter.listProfiles(companyId) }
        verify(exactly = 1) { fieldMappingServiceAdapter.createProfile(any()) }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle fully mapped status`() {
        // Arrange
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val profileId = UUID.randomUUID().toString()
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        // Mock profile with existing configuration - all required fields are mapped
        val configMap = Struct.newBuilder()
            .putFields("entityId", Value.newBuilder().setStringValue(entityId.toString()).build())
            .putFields("integrationId", Value.newBuilder().setStringValue(integrationId.toString()).build())
            .build()

        val profile = Profile.newBuilder()
            .setId(profileId)
            .setName("Test Profile")
            .setDescription("Test Description")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setConfigMap(configMap)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("source_field")
                    .setTargetField("target_field") // All required fields have target fields
                    .setIsRequired(true)
                    .build()
            )
            .build()

        val listProfilesResponse = ListProfilesResponse.newBuilder()
            .addProfiles(profile)
            .build()

        val knitFields = GetAllFieldsResponse(
            success = true,
            data = FieldDataList(
                default = listOf(
                    FieldData(
                        fieldId = "firstName",
                        fieldFromApp = "firstName",
                        mappedKey = "firstName",
                    )
                )
            )
        )

        // Setup mocks
        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns listProfilesResponse
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId,entityId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(
            externalValues
        )
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Act
        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        // Assert
        assertEquals(UUID.fromString(profileId), result.profileId)
        assertEquals(integrationId, result.integrationId)
        assertEquals(companyId, result.companyId)
        assertEquals(LegalMappingStatus.FULLY_MAPPED.name, result.entityMappingStatus.name)

        // Verify
        verify(exactly = 1) { fieldMappingServiceAdapter.listProfiles(companyId) }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle exception and return empty response`() {
        // Arrange
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)

        // Setup mocks
        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { fieldMappingServiceAdapter.listProfiles(companyId) } throws RuntimeException("Service unavailable")
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Act
        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        // Assert
        assertNull(result.profileId)
        assertNull(result.integrationId)

        // Verify
        verify { fieldMappingServiceAdapter.listProfiles(companyId) }
        verify(exactly = 0) { fieldMappingServiceAdapter.createProfile(any()) }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle unmapped status for inactive profile`() {
        // Arrange
        val integrationId = 1L
        val entityId = 2L
        val companyId = 100L
        val profileId = UUID.randomUUID().toString()
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId, companyId)
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)

        // Mock profile with existing configuration but inactive
        val configMap = Struct.newBuilder()
            .putFields("entityId", Value.newBuilder().setStringValue(entityId.toString()).build())
            .putFields("integrationId", Value.newBuilder().setStringValue(integrationId.toString()).build())
            .build()

        val profile = Profile.newBuilder()
            .setId(profileId)
            .setName("Test Profile")
            .setDescription("Test Description")
            .setCompanyId(companyId)
            .setIsActive(false) // Profile is inactive
            .setConfigMap(configMap)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("source_field")
                    .setTargetField("target_field")
                    .setIsRequired(true)
                    .build()
            )
            .build()

        val listProfilesResponse = ListProfilesResponse.newBuilder()
            .addProfiles(profile)
            .build()

        val knitFields = GetAllFieldsResponse(
            success = true,
            data = FieldDataList(
                default = listOf(
                    FieldData(
                        fieldId = "firstName",
                        fieldFromApp = "firstName",
                        mappedKey = "firstName",
                    )
                )
            )
        )

        // Setup mocks
        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId,entityId) } returns Optional.of(legalEntityMapping)
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns listProfilesResponse
        coEvery {
            knitAdapter.getAllFields(
                companyId,
                integrationId,
                mapPlatformIdToKnitAppId(mockCompanyIntegration.get().platform.name)
            )
        } returns knitFields
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(
            externalValues
        )
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Act
        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        // Assert
        assertEquals(UUID.fromString(profileId), result.profileId)
        assertEquals(integrationId, result.integrationId)
        assertEquals(companyId, result.companyId)
        assertEquals(LegalMappingStatus.UNMAPPED.name, result.entityMappingStatus.name)

        // Verify
        verify(exactly = 1) { fieldMappingServiceAdapter.listProfiles(companyId) }
    }
}
