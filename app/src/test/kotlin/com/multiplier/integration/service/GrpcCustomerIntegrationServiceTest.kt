package com.multiplier.integration.service

import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.schema.CustomerIntegration.GetIntegrationFromExternalCompanyIdAndPlatformNameRequest
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.grpc.CustomerIntegrationGrpcService
import com.multiplier.integration.service.grpc.GrpcExceptionWrapper
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import io.grpc.Status

@ExtendWith(MockKExtension::class)
class GrpcCustomerIntegrationServiceTest {
    @MockK
    lateinit var customerIntegrationService: CustomerIntegrationService

    @InjectMockKs
    lateinit var customerIntegrationGrpcService: CustomerIntegrationGrpcService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should return customer integration info successfully`() =
        runBlocking {
            val externalCompanyId = "Test"
            val platformName = "TriNet"
            val mockCompanyIntegration = getMockCompanyIntegration()
            val request = GetIntegrationFromExternalCompanyIdAndPlatformNameRequest.newBuilder()
                .setPlatformCompanyId(externalCompanyId)
                .setPlatformName(platformName)
                .build()

            every {
                customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                    externalCompanyId,
                    platformName
                )
            } returns mockCompanyIntegration.get()

            val resp = customerIntegrationGrpcService.getIntegrationFromExternalCompanyIdAndPlatformName(request)

            assertEquals(mockCompanyIntegration.get().companyId, resp.companyId)
        }

    @Test
    fun `should get customer integration info failed for null inputs`() {
        val externalCompanyId = ""
        val exception = IntegrationIllegalArgumentException("Neither externalCompanyId nor platformName can be null or blank")

        val request = GetIntegrationFromExternalCompanyIdAndPlatformNameRequest.newBuilder()
            .setPlatformCompanyId(externalCompanyId)
            .setPlatformName("")
            .build()

        every {
            customerIntegrationService.getCompanyIntegrationByExternalCompanyIdAndPlatformName(
                "",
                ""
            )
        } throws exception

        val actualException =
            assertFailsWith<GrpcExceptionWrapper> {
                runBlocking {
                    customerIntegrationGrpcService.getIntegrationFromExternalCompanyIdAndPlatformName(request)
                }
            }

        assertEquals(actualException.status.code, Status.INTERNAL.code)
        assertEquals(actualException.status.cause, exception)
    }
}