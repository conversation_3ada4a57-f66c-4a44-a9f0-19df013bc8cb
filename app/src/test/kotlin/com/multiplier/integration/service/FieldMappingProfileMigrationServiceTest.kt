package com.multiplier.integration.service

import com.google.protobuf.Value
import com.multiplier.fieldmapping.grpc.schema.TransformationType
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.model.FieldType
import com.multiplier.integration.repository.model.JpaFieldsMapping
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.service.fieldmappings.FieldMappingProfileMigrationService
import com.multiplier.integration.service.fieldmappings.MigrationRequest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.*
import kotlin.test.assertFalse

class FieldMappingProfileMigrationServiceTest {

    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter = mockk()
    private val legalEntityMappingRepository: LegalEntityMappingRepository = mockk()
    private val fieldsMappingRepository: FieldsMappingRepository = mockk()
    private lateinit var service: FieldMappingProfileMigrationService

    @BeforeEach
    fun setUp() {
        service = FieldMappingProfileMigrationService(
            fieldMappingServiceAdapter,
            legalEntityMappingRepository,
            fieldsMappingRepository
        )
    }

@Test
fun `migrateFieldMappingsToProfilesByCompanyIdAndEntityIdAndIntegrationId should return success`() {
    val request = MigrationRequest(1L, 2L, 3L)
    val legalEntity = mockk<JpaLegalEntityMapping>(
        relaxed = true
    ) {
        every { entityId } returns 2L
        every { companyId } returns 1L
        every { integrationId } returns 3L
        every { entityName } returns "Test Entity"
    }

    every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(3L, 2L) } returns Optional.of(legalEntity)
    every { fieldMappingServiceAdapter.listProfiles(1L) } returns mockk {
        every { profilesList } returns emptyList()
    }
    every { fieldMappingServiceAdapter.createProfile(any()) } returns mockk {
        every { profile } returns mockk {
            every { rulesCount } returns 0
            every { toBuilder() } returns mockk(relaxed = true)
        }
    }
    every { fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(2L, 3L) } returns emptyList()

    val response = service.migrateFieldMappingsToProfilesByCompanyIdAndEntityIdAndIntegrationId(request)
    assertTrue(response.success)
}

    @Test
    fun `migrateFieldMappingsToProfiles should return success`() {
        val legalEntity = mockk<JpaLegalEntityMapping>()
        every { legalEntityMappingRepository.findAll() } returns listOf(legalEntity)
        every { fieldMappingServiceAdapter.listProfiles(any()) } returns mockk {
            every { profilesList } returns emptyList()
        }
        every { fieldMappingServiceAdapter.createProfile(any()) } returns mockk {
            every { profile } returns mockk {
                every { rulesCount } returns 0
                every { toBuilder() } returns mockk(relaxed = true)
            }
        }
        every { fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(any(), any()) } returns emptyList()

        val response = service.migrateFieldMappingsToProfiles()
        assertTrue(response.success)
    }
    @Test
    fun `migrateFieldMappingsToProfilesByCompanyIdAndEntityIdAndIntegrationId should throw EntityNotFoundException if legal entity is not found`() {
        val request = MigrationRequest(1L, 2L, 3L)
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(3L, 2L) } returns Optional.empty()

        val response = service.migrateFieldMappingsToProfilesByCompanyIdAndEntityIdAndIntegrationId(request)

        assertFalse(response.success)
    }

    @Test
    fun `migrateFieldMappingsToProfiles should handle empty legal entity list gracefully`() {
        every { legalEntityMappingRepository.findAll() } returns emptyList()

        val response = service.migrateFieldMappingsToProfiles()

        assertTrue(response.success)
        assertEquals("Successfully retried received event", response.message)
    }

    @Test
    fun `migrateEntityMapping should create a new profile if no matching profile exists`() {
        val legalEntity = mockk<JpaLegalEntityMapping> {
            every { entityId } returns 2L
            every { companyId } returns 1L
            every { integrationId } returns 3L
            every { entityName } returns "Test Entity"
        }
        every { fieldMappingServiceAdapter.listProfiles(1L) } returns mockk {
            every { profilesList } returns emptyList()
        }
        every { fieldMappingServiceAdapter.createProfile(any()) } returns mockk {
            every { profile } returns mockk {
                every { rulesCount } returns 0
                every { toBuilder() } returns mockk(relaxed = true)
            }
        }
        every { fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(2L, 3L) } returns emptyList()

        service.migrateEntityMapping(legalEntity)

        verify { fieldMappingServiceAdapter.createProfile(any()) }
    }

    @Test
    fun `migrateEntityMapping should not create a new profile if a matching profile exists`() {
        val legalEntity = mockk<JpaLegalEntityMapping> {
            every { entityId } returns 2L
            every { companyId } returns 1L
            every { integrationId } returns 3L
        }
        every { fieldMappingServiceAdapter.listProfiles(1L) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap } returns mapOf(
                    "entityId" to Value.newBuilder().setStringValue("2").build(),
                    "integrationId" to Value.newBuilder().setStringValue("3").build()
                )
                every { rulesCount } returns 1
            })
        }

        service.migrateEntityMapping(legalEntity)

        verify(exactly = 0) { fieldMappingServiceAdapter.createProfile(any()) }
    }

    @Test
    fun `createRulesFromFieldMappings should return empty list if no field mappings are provided`() {
        val result = service.createRulesFromFieldMappings(emptyList())

        assertTrue(result.isEmpty())
    }

    @Test
    fun `createRulesFromFieldMappings should create rules for valid field mappings`() {
        val fieldMappings = listOf(
            mockk<JpaFieldsMapping> {
                every { originField } returns "origin"
                every { mappedField } returns "mapped"
                every { mappedFieldLabel } returns "Mapped Label"
                every { originFieldLabel } returns "Origin Label"
                every { isRequired } returns true
                every { type } returns FieldType.STRING
                every { isCalculated } returns false
                every { id } returns 1L
                every { parent } returns null
            }
        )

        val result = service.createRulesFromFieldMappings(fieldMappings)

        assertEquals(1, result.size)
        assertEquals("origin", result[0].targetField)
        assertEquals("mapped", result[0].sourceField)
        assertEquals(TransformationType.DIRECT_MAPPING, result[0].transformationType)
    }

    @Test
    fun `createRulesFromFieldMappings should create value transformation config for ENUM type`() {
        val fieldMappings = listOf(
            mockk<JpaFieldsMapping> {
                every { id } returns 1L
                every { type } returns FieldType.ENUM
                every { children } returns listOf(
                    mockk {
                        every { mappedField } returns "Mapped1"
                        every { originField } returns "Origin1"
                    },
                    mockk {
                        every { mappedField } returns "Mapped2"
                        every { originField } returns "Origin2"
                    }
                )
                every { originField } returns "origin"
                every { mappedField } returns "mapped"
                every { mappedFieldLabel } returns "Mapped Label"
                every { originFieldLabel } returns "Origin Label"
                every { isRequired } returns true
                every { isCalculated } returns false
                every { parent } returns null
            }
        )

        val result = service.createRulesFromFieldMappings(fieldMappings)

        assertEquals(1, result.size)
        assertEquals(TransformationType.VALUE_TRANSFORMATION, result[0].transformationType)
        assertTrue(result[0].transformationConfig.fieldsMap["mappings"]!!.structValue.fieldsMap.containsKey("Mapped1"))
        assertTrue(result[0].transformationConfig.fieldsMap["mappings"]!!.structValue.fieldsMap.containsKey("Mapped2"))
        assertEquals("Origin1", result[0].transformationConfig.fieldsMap["mappings"]!!.structValue.fieldsMap["Mapped1"]!!.stringValue)
        assertEquals("Origin2", result[0].transformationConfig.fieldsMap["mappings"]!!.structValue.fieldsMap["Mapped2"]!!.stringValue)
    }

    @Test
    fun `createRulesFromFieldMappings should create functional transformation config for calculated fields`() {
        val fieldMappings = listOf(
            mockk<JpaFieldsMapping> {
                every { id } returns 1L
                every { type } returns FieldType.STRING
                every { isCalculated } returns true
                every { originField } returns "origin"
                every { mappedField } returns "mapped"
                every { mappedFieldLabel } returns "Mapped Label"
                every { originFieldLabel } returns "Origin Label"
                every { isRequired } returns true
                every { parent } returns null
            },
            mockk<JpaFieldsMapping> { //duplicate mapping
                every { id } returns 2L
                every { type } returns FieldType.STRING
                every { isCalculated } returns true
                every { originField } returns "origin"
                every { mappedField } returns "mapped"
                every { mappedFieldLabel } returns "Mapped Label"
                every { originFieldLabel } returns "Origin Label"
                every { isRequired } returns true
                every { parent } returns null
            }
        )

        val result = service.createRulesFromFieldMappings(fieldMappings)

        assertEquals(1, result.size)
        assertEquals(TransformationType.FUNCTIONAL_TRANSFORMATION, result[0].transformationType)
        assertEquals("COUNT", result[0].transformationConfig.fieldsMap["function"]!!.stringValue)
    }

    @Test
    fun `createRulesFromFieldMappings should create conditional transformation config for uanNumberExists field`() {
        val fieldMappings = listOf(
            mockk<JpaFieldsMapping> {
                every { id } returns 1L
                every { originField } returns "uanNumberExists"
                every { mappedField } returns "uanField"
                every { type } returns FieldType.STRING
                every { mappedFieldLabel } returns "UAN Field"
                every { originFieldLabel } returns "UAN Exists"
                every { isRequired } returns true
                every { isCalculated } returns false
                every { parent } returns null
            }
        )

        val result = service.createRulesFromFieldMappings(fieldMappings)

        assertEquals(1, result.size)
        assertEquals(TransformationType.CONDITIONAL_MAPPING, result[0].transformationType)
        assertTrue(result[0].transformationConfig.fieldsMap.containsKey("conditions"))
        assertTrue(result[0].transformationConfig.fieldsMap.containsKey("default"))
        assertEquals("false", result[0].transformationConfig.fieldsMap["default"]!!.stringValue)
        val conditions = result[0].transformationConfig.fieldsMap["conditions"]!!.listValue.valuesList
        assertEquals(1, conditions.size)
        val condition = conditions[0].structValue.fieldsMap
        assertEquals("exists", condition["operator"]!!.stringValue)
        assertEquals("true", condition["result"]!!.stringValue)
    }

    @Test
    fun `createRulesFromFieldMappings should create functional transformation config for country fields`() {
        val fieldMappings = listOf(
            mockk<JpaFieldsMapping> {
                every { id } returns 1L
                every { originField } returns "countryCode"
                every { mappedField } returns "countryField"
                every { type } returns FieldType.ENUM
                every { mappedFieldLabel } returns "Country Field"
                every { originFieldLabel } returns "Country Code"
                every { isRequired } returns true
                every { isCalculated } returns false
                every { parent } returns null
            }
        )

        val result = service.createRulesFromFieldMappings(fieldMappings)

        assertEquals(1, result.size)
        assertEquals(TransformationType.FUNCTIONAL_TRANSFORMATION, result[0].transformationType)
        assertTrue(result[0].transformationConfig.fieldsMap.containsKey("function"))
        assertEquals("COUNTRY_ALPHA_3_CODE", result[0].transformationConfig.fieldsMap["function"]!!.stringValue)
    }
}