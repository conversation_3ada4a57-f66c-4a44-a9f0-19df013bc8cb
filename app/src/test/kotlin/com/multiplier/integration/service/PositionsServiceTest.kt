package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.repository.CachePositionsRepository
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.PlatformCategory
import e2e.resourceAsString
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.slot
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit

@ExtendWith(SpringExtension::class)
class PositionsServiceTest {
    @MockK
    lateinit var cachePositionsRepository: CachePositionsRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    private val json = Json { ignoreUnknownKeys = true }

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @InjectMockKs
    lateinit var positionsService: PositionsService

    private val companyId = 123L
    private val platformId = 456L

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        positionsService.setPrivateField("cacheTtlSeconds", 7200L) // 2 hours
        positionsService.setPrivateField("batchSize", 3)
    }

    @Test
    fun `updateCachedPosition should successfully cache position data`() {
        val data = json.decodeFromString<GetPositionDetailResponse>(
            resourceAsString("get_positions/workday.json")
        )
        val timeSlot = slot<LocalDateTime>()

        every { 
            cachePositionsRepository.upsertCachedPosition(companyId, platformId, data, capture(timeSlot))
        } returns Unit

        positionsService.updateCachedPosition(companyId, platformId, data)

        verify(exactly = 1) { 
            cachePositionsRepository.upsertCachedPosition(companyId, platformId, data, any())
        }
    }

    @Test
    fun `updateCachedPosition should throw exception when caching fails`() {
        val data = json.decodeFromString<GetPositionDetailResponse>(
            resourceAsString("get_positions/workday.json")
        )
        
        every { 
            cachePositionsRepository.upsertCachedPosition(any(), any(), any(), any())
        } throws RuntimeException("Database error")

        assertThrows<IntegrationInternalServerException> {
            positionsService.updateCachedPosition(companyId, platformId, data)
        }
    }

    @Test
    fun `getPositions should return cached data when valid`() = runBlocking {
        val cachedData = json.decodeFromString<GetPositionDetailResponse>(
            resourceAsString("get_positions/workday.json")
        )
        val now = LocalDateTime.now()
        
        every { 
            cachePositionsRepository.findCachedPositions(companyId, platformId)
        } returns Pair(cachedData, now)

        val result = positionsService.getPositions(companyId, platformId, ignoreCache = false)

        assertEquals(cachedData, result)
        verify(exactly = 1) { cachePositionsRepository.findCachedPositions(companyId, platformId) }
        coVerify(exactly = 0) { knitAdapter.getPositionsDetails(any(), any()) }
    }

    @Test
    fun `getPositions should fetch fresh data when cache expired`() = runBlocking {
        val cachedData = json.decodeFromString<GetPositionDetailResponse>(
            resourceAsString("get_positions/workday.json")
        )
        val expiredTime = LocalDateTime.now().minus(3, ChronoUnit.HOURS)
        val freshData = cachedData.copy(
            data = cachedData.data?.let { originalData ->
                originalData.copy(
                    positions = originalData.positions?.map {
                        it.copy(designation = "${it.designation}-UPDATED")
                    },
                    workShifts = originalData.workShifts
                )
            }
        )
        
        every { 
            cachePositionsRepository.findCachedPositions(companyId, platformId)
        } returns Pair(cachedData, expiredTime)

        coEvery {
            knitAdapter.getPositionsDetails(companyId, platformId)
        } returns freshData

        every {
            cachePositionsRepository.upsertCachedPosition(any(), any(), any(), any())
        } returns Unit

        val result = positionsService.getPositions(companyId, platformId, ignoreCache = false)

        assertEquals(freshData, result)
        verify(exactly = 1) { cachePositionsRepository.findCachedPositions(companyId, platformId) }
        coVerify(exactly = 1) { knitAdapter.getPositionsDetails(companyId, platformId) }
    }

    @Test
    fun `getPositions should throw exception when knit adapter fails`() {
        runBlocking {
            every { 
                cachePositionsRepository.findCachedPositions(companyId, platformId)
            } returns null

            coEvery {
                knitAdapter.getPositionsDetails(companyId, platformId)
            } returns null

            assertThrows<IntegrationInternalServerException> {
                positionsService.getPositions(companyId, platformId, ignoreCache = false)
            }
        }
    }

    @Test
    fun `cachePositions should process integrations in batches`() = runBlocking {
        val integrations = listOf(
            mockCompanyIntegration(
                id = 1L,
                companyId = 1L,
                platformId = platformId
            ),
            mockCompanyIntegration(
                id = 2L,
                companyId = 2L,
                platformId = platformId
            ),
            mockCompanyIntegration(
                id = 3L,
                companyId = 3L,
                platformId = platformId
            ),
            mockCompanyIntegration(
                id = 4L,
                companyId = 4L,
                platformId = platformId
            )
        )

        coEvery { 
            companyIntegrationRepository.findEnabledIntegrationsByPlatformId(platformId)
        } returns integrations

        coEvery {
            knitAdapter.getPositionsDetails(any(), any())
        } returns GetPositionDetailResponse(success = true)

        every {
            cachePositionsRepository.upsertCachedPosition(any(), any(), any(), any())
        } returns Unit

        positionsService.cachePositions(platformId)

        coVerify(exactly = 1) { 
            companyIntegrationRepository.findEnabledIntegrationsByPlatformId(platformId)
        }
        coVerify(exactly = 4) { 
            knitAdapter.getPositionsDetails(any(), platformId)
        }
    }

    fun mockCompanyIntegration(
        id: Long,
        companyId: Long,
        platformId: Long,
        accountToken: String = "token$id",
        externalCompanyId: String = "ext$id",
        accountName: String = "account$id",
        enabled: Boolean = true,
        outgoingSyncEnabled: Boolean = false,
        incomingSyncEnabled: Boolean = false,
        timeOffSyncEnabled: Boolean = false,
        incomingSyncInProgress: Boolean = false,
        importInProgress: Boolean = false,
        lastOutgoingSyncTime: LocalDateTime? = null,
        lastIncomingSyncTime: LocalDateTime? = null,
        lastOutgoingSyncTimeToggleOnTime: LocalDateTime? = null,
        lastOutgoingSyncTimeToggleOffTime: LocalDateTime? = null,
        initialSyncStarted: Boolean = false
    ) = JpaCompanyIntegration(
        id = id,
        companyId = companyId,
        provider = JpaProvider(id = 1L, name = ProviderName.KNIT),
        platform = JpaPlatform(
            id = platformId,
            name = "Workday",
            category = PlatformCategory.HRIS,
            isPositionDropdownEnabled = true
        ),
        accountToken = accountToken,
        enabled = enabled,
        outgoingSyncEnabled = outgoingSyncEnabled,
        incomingSyncEnabled = incomingSyncEnabled,
        timeOffSyncEnabled = timeOffSyncEnabled,
        incomingSyncInProgress = incomingSyncInProgress,
        importInProgress = importInProgress,
        lastOutgoingSyncTime = lastOutgoingSyncTime,
        lastIncomingSyncTime = lastIncomingSyncTime,
        lastOutgoingSyncTimeToggleOnTime = lastOutgoingSyncTimeToggleOnTime,
        lastOutgoingSyncTimeToggleOffTime = lastOutgoingSyncTimeToggleOffTime,
        externalCompanyId = externalCompanyId,
        accountName = accountName,
        initialSyncStarted = initialSyncStarted
    )
}