package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.mockCompanyIntegration
import com.multiplier.integration.mockPlatformContractIntegration
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.LeaveTypeMappingRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.TimeoffEventRepository
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.repository.model.JpaTimeoffEvent
import com.multiplier.integration.sync.model.EmployeeLeaveRequest
import com.multiplier.integration.sync.model.IsPaid
import com.multiplier.integration.sync.model.LeaveType
import com.multiplier.integration.sync.model.LeaveTypeEnum
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.sync.model.Unit
import com.multiplier.timeoff.schema.GrpcBulkResponseItem
import com.multiplier.timeoff.schema.GrpcBulkTimeOffResponse
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesResponse
import com.multiplier.timeoff.schema.GrpcTimeOffType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class TimeoffServiceTest {

    private lateinit var timeoffEventRepository: TimeoffEventRepository
    private lateinit var leaveTypeMappingRepository: LeaveTypeMappingRepository
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository
    private lateinit var timeoffServiceAdapter: TimeoffServiceAdapter
    private lateinit var timeoffDataRepository: PlatformTimeoffIntegrationRepository
    private lateinit var timeoffService: TimeoffService
    private lateinit var platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository

    @BeforeEach
    fun setup() {
        timeoffEventRepository = mockk(relaxed = true)
        leaveTypeMappingRepository = mockk(relaxed = true)
        companyIntegrationRepository = mockk(relaxed = true)
        platformContractIntegrationRepository = mockk(relaxed = true)
        timeoffServiceAdapter = mockk(relaxed = true)
        timeoffDataRepository = mockk(relaxed = true)
        platformTimeoffDataRepository = mockk(relaxed = true)
        timeoffService = TimeoffService(
            timeoffEventRepository,
            leaveTypeMappingRepository,
            companyIntegrationRepository,
            platformContractIntegrationRepository,
            timeoffServiceAdapter,
            timeoffDataRepository,
            platformTimeoffDataRepository,
        )
    }

    @Test
    fun `processPendingTimeoffEvents processes all pending events`() {
        val event1 = JpaTimeoffEvent(id = 1, shouldProcess = true, externalId = "ext1", internalId = "1", integrationId = 1L, employeeId = "", timeoffType = "", timeoffData = "")
        val event2 = JpaTimeoffEvent(id = 2, shouldProcess = true, externalId = "ext2", internalId = "2", integrationId = 1L, employeeId = "", timeoffType = "", timeoffData = "")
        every { timeoffEventRepository.findAllByShouldProcess(true) } returns listOf(event1, event2)
        every { timeoffEventRepository.save(any()) } returnsArgument 0

        timeoffService.processPendingTimeoffEvents()

        verify(exactly = 2) { timeoffEventRepository.save(any()) }
        assertTrue(event1.processed!!)
        assertFalse(event1.shouldProcess!!)
        assertTrue(event2.processed!!)
        assertFalse(event2.shouldProcess!!)
    }

    @Test
    fun `revokeTimeoffEvent calls bulkRevokeTimeoffs`() {
        val event = JpaTimeoffEvent(shouldProcess = true, externalId = "ext1", internalId = "1", integrationId = 1L, employeeId = "", timeoffType = "", timeoffData = "")
        val companyId = 1L

        timeoffService.revokeTimeoffEvent(event, companyId)

        verify {
            timeoffServiceAdapter.bulkRevokeTimeoffs(match {
                it.companyId == companyId && it.externalTimeOffIdsList.contains("ext1")
            })
        }
    }

    @Test
    fun `processEvent processes approved leave request successfully`() {
        val event = JpaTimeoffEvent(
            id = 1,
            integrationId = 1,
            employeeId = "emp1",
            externalId = "ext1",
            internalId = "1",
            timeoffType = "VACATION",
            timeoffData = jacksonObjectMapper().writeValueAsString(
                EmployeeLeaveRequest(
                    id = "",
                    employeeId = "",
                    status = Status.APPROVED,
                    startDate = Date(),
                    endDate = Date(),
                    amount = 1.0,
                    unit = Unit.DAYS,
                    isPaid = IsPaid.TRUE,
                    leaveType = LeaveType(id = "LT1", name = "", type = LeaveTypeEnum.VACATION)
                )
            )
        )
        val companyIntegration = mockCompanyIntegration(companyId = 100, platform = mockk { every { id } returns 10L })
        val contract = mockPlatformContractIntegration(id=1L, contractId = 1L, platformEmployeeId = "employee")
        every { timeoffEventRepository.findAllByShouldProcess(true) } returns listOf(event)
        every { companyIntegrationRepository.findById(1L) } returns java.util.Optional.of(companyIntegration)
        every { platformContractIntegrationRepository.findFirstByPlatformEmployeeIdAndPlatformIdAndProviderId(any(), any(), any()) } returns contract
        every { leaveTypeMappingRepository.findByIntegrationIdAndExternalTypeId(any(), any()) } returns listOf(mockk { every { internalTypeName } returns "VACATION" })
        every { timeoffServiceAdapter.getCompanyTimeOffTypes(100L) } returns GrpcCompanyTimeOffTypesResponse.newBuilder()
            .addSystemTimeOffTypes(GrpcTimeOffType.newBuilder().setKey("VACATION").build())
            .build()
        every { timeoffServiceAdapter.bulkUpsertTimeOffs(any()) } returns GrpcBulkTimeOffResponse.newBuilder()
            .setSuccess(true)
            .addItems(GrpcBulkResponseItem.newBuilder().setTimeOffId(1000L).build())
            .build()
        every { timeoffEventRepository.save(any()) } returnsArgument 0

        timeoffService.processPendingTimeoffEvents()

        verify { timeoffDataRepository.save(any()) }
    }

    @Test
    fun `addTimeoffDataToCache saves and returns JpaPlatformTimeoffIntegration`() {
        val integrationId = 1L
        val contractId = 2L
        val employeeId = "emp1"
        val internalTimeoffId = 1000L
        val externalTimeoffId = "ext1"
        val savedEntity = JpaPlatformTimeoffIntegration(
            integrationId = integrationId,
            contractId = contractId,
            employeeId = employeeId,
            internalTimeoffId = internalTimeoffId,
            externalTimeoffId = externalTimeoffId
        )
        every { timeoffDataRepository.save(any()) } returns savedEntity

        val result = timeoffService.addTimeoffDataToCache(integrationId, contractId, employeeId, internalTimeoffId, externalTimeoffId)

        assertEquals(savedEntity, result)
        verify { timeoffDataRepository.save(match {
            it.integrationId == integrationId &&
                    it.contractId == contractId &&
                    it.employeeId == employeeId &&
                    it.internalTimeoffId == internalTimeoffId &&
                    it.externalTimeoffId == externalTimeoffId
        }) }
    }
}
