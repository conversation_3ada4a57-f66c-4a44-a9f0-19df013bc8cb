package com.multiplier.integration.service

import com.multiplier.growthbook.sdk.GrowthBookSDK
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class FeatureFlagServiceTest {
    val featureFlagService = FeatureFlagService("testUrl", "testKey")
    val mockedGrowthBookSDK = mockk<GrowthBookSDK>()

    init {
        featureFlagService.sdkInstance = mockedGrowthBookSDK
    }

    @Test
    fun `test integration field mapping feature flag is off`() {
        every { mockedGrowthBookSDK.feature(any(), any()) } returns GBFeatureResult(
            null,
            on = false,
            off = true,
            GBFeatureSource.defaultValue
        )
        val isOn = featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, mapOf())

        assertFalse(isOn)
    }

    @Test
    fun `test refresh feature flag`() {
        every { mockedGrowthBookSDK.reloadFeatureFlags() } returns Unit

        featureFlagService.refreshFeatureFlags()

        verify(exactly = 1) { mockedGrowthBookSDK.reloadFeatureFlags() }
    }

    @Test
    fun `test isMtmIntegration 1`() {
        every {
            mockedGrowthBookSDK.feature(
                FeatureFlag.MTM_KNIT_INTEGRATION_ID.flag,
                emptyMap()
            )
        } returns GBFeatureResult("integrationId", on = true, off = false, source = GBFeatureSource.defaultValue)

        val result = featureFlagService.isMtmIntegration("integrationId", "<EMAIL>")

        assertTrue(result)
    }

    @Test
    fun `test isMtmIntegration 2`() {
        every {
            mockedGrowthBookSDK.feature(
                FeatureFlag.MTM_KNIT_INTEGRATION_ID.flag,
                emptyMap()
            )
        } returns GBFeatureResult("integrationId", on = true, off = false, source = GBFeatureSource.defaultValue)

        val result = featureFlagService.isMtmIntegration("1", "<EMAIL>")

        assertTrue(result)
    }
}
