package com.multiplier.integration.service

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.GetLeaveTypesResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveType
import com.multiplier.integration.adapter.api.resources.knit.LeaveTypeEnum
import com.multiplier.integration.adapter.api.resources.knit.LeaveTypeListResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.LeaveTypeMappingRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaLeaveTypesMapping
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.service.exception.BadRequestException
import com.multiplier.integration.service.exception.IntegrationNotFoundException
import com.multiplier.integration.service.exception.InternalAPIException
import com.multiplier.integration.service.exception.KnitIntegrationException
import com.multiplier.integration.types.SaveLeaveTypesMappingInput
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesResponse
import com.multiplier.timeoff.schema.GrpcTimeOffType
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*
import kotlin.test.assertFalse
import kotlin.test.assertTrue

@ExtendWith(SpringExtension::class)
class TimeOffSyncServiceTest {

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var leaveTypeMappingRepository: LeaveTypeMappingRepository

    @MockK
    lateinit var timeoffServiceAdapter: TimeoffServiceAdapter

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @InjectMockKs
    lateinit var timeOffSyncService: TimeOffSyncService

    @Nested
    inner class GetInternalLeaveTypes {

        @Test
        fun `should throw when fail to fetch internal leave type`() {
            val currentUserCompanyId = 1L;

            every { timeoffServiceAdapter.getCompanyTimeOffTypes(currentUserCompanyId) } throws Exception()

            assertThrows<InternalAPIException> {
                timeOffSyncService.getInternalLeaveTypes(currentUserCompanyId)
            }
        }

        @Test
        fun `should get internal leave types`() {
            val currentUserCompanyId = 1L;

            every { timeoffServiceAdapter.getCompanyTimeOffTypes(currentUserCompanyId) } returns
                    GrpcCompanyTimeOffTypesResponse.newBuilder()
                        .addSystemTimeOffTypes(
                            GrpcTimeOffType.newBuilder()
                                .setId(1L)
                                .setLabel("label1")
                                .setKey("key1")
                                .build()
                        )
                        .addCompanyTimeOffTypes(
                            GrpcTimeOffType.newBuilder()
                                .setId(2L)
                                .setLabel("label2")
                                .setKey("key2")
                                .build()
                        )
                        .build()

            val actual = timeOffSyncService.getInternalLeaveTypes(currentUserCompanyId)

            assertTrue { actual.size == 2 }
            assertTrue { actual[0].id == "100001" }
            assertTrue { actual[0].name == "label1" }
            assertTrue { actual[0].leaveType == "key1" }
            assertTrue { actual[1].id == "100002" }
            assertTrue { actual[1].name == "label2" }
            assertTrue { actual[1].leaveType == "key2" }
        }
    }


    @Nested
    inner class GetExternalLeaveTypes {

        @Test
        fun `should throw when integration not found`() {
            val thisCompanyId = 1L;
            val integrationId = 1L;

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.empty()

            assertThrows<IntegrationNotFoundException> {
                timeOffSyncService.getExternalLeaveTypes(
                    thisCompanyId,
                    integrationId
                )
            }
        }

        @Test
        fun `should throw when integration company id not matched`() {
            val thisCompanyId = 1L;
            val integrationId = 1L;
            val mockJpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { id } returns integrationId
                every { companyId } returns 2L
            }

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(mockJpaCompanyIntegration)

            assertThrows<BadRequestException> {
                timeOffSyncService.getExternalLeaveTypes(
                    thisCompanyId,
                    integrationId
                )
            }
        }

        @Test
        fun `should throw when fail to fetch external leave types`() {
            val thisCompanyId = 1L;
            val integrationId = 1L;
            val platformId = 1L
            val mockJpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { id } returns integrationId
                every { companyId } returns thisCompanyId
                every { platform } returns mockk<JpaPlatform>(relaxed = true) {
                    every { id } returns platformId
                }
            }

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(mockJpaCompanyIntegration)
            coEvery {
                knitAdapter.getLeaveTypes(
                    thisCompanyId,
                    platformId
                )
            } returns GetLeaveTypesResponse(success = false)

            assertThrows<KnitIntegrationException> {
                timeOffSyncService.getExternalLeaveTypes(
                    thisCompanyId,
                    integrationId
                )
            }
        }

        @Test
        fun `should get external leave types`() {
            val thisCompanyId = 1L;
            val integrationId = 1L;
            val platformId = 1L
            val mockJpaCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
                every { id } returns integrationId
                every { companyId } returns thisCompanyId
                every { platform } returns mockk<JpaPlatform>(relaxed = true) {
                    every { id } returns platformId
                }
            }

            every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(mockJpaCompanyIntegration)
            coEvery { knitAdapter.getLeaveTypes(thisCompanyId, platformId) } returns
                    GetLeaveTypesResponse(
                        success = true,
                        data = LeaveTypeListResponse(
                            listOf(
                                LeaveType(
                                    id = "id1",
                                    name = "name1",
                                    type = LeaveTypeEnum.PERSONAL
                                ),
                                LeaveType(
                                    id = "id2",
                                    name = "name2"
                                )
                            )
                        )
                    )

            val actual = timeOffSyncService.getExternalLeaveTypes(thisCompanyId, integrationId)

            assertTrue { actual.size == 2 }
            assertTrue { actual[0].id == "id1" }
            assertTrue { actual[0].name == "name1" }
            assertTrue { actual[0].leaveType == "PERSONAL" }
            assertTrue { actual[1].id == "id2" }
            assertTrue { actual[1].name == "name2" }
            assertTrue { actual[1].leaveType == "UNKNOWN" }
        }
    }

    @Nested
    inner class GetLeaveTypeMappingDefinition {

        @Test
        fun `should return empty leaveTypesMappings`() {
            val currentUserCompanyId = 1L;
            val integrationId = 1L;

            every {
                leaveTypeMappingRepository.findByCompanyIdAndIntegrationId(
                    currentUserCompanyId,
                    integrationId
                )
            } returns emptyList()

            val actual = timeOffSyncService.getLeaveTypeMappingDefinition(currentUserCompanyId, integrationId)

            assertTrue { actual.isEmpty() }
        }

        @Test
        fun `should return leaveTypesMappings`() {
            val currentUserCompanyId = 1L;
            val integrationId = 1L;

            every {
                leaveTypeMappingRepository.findByCompanyIdAndIntegrationId(
                    currentUserCompanyId,
                    integrationId
                )
            } returns listOf(
                JpaLeaveTypesMapping(
                    internalTypeId = "internalTypeId1",
                    internalTypeName = "internalTypeName1",
                    externalTypeId = "externalTypeId1",
                    integrationId = integrationId,
                    companyId = currentUserCompanyId,
                )
            )

            val actual = timeOffSyncService.getLeaveTypeMappingDefinition(currentUserCompanyId, integrationId)

            assertTrue { actual.size == 1 }
            assertTrue { actual[0].internalTypeId == "internalTypeId1" }
            assertTrue { actual[0].externalTypeId == "externalTypeId1" }
        }
    }

    @Nested
    inner class SaveLeaveTypesMapping {

        @Test
        fun `should fail to update when mapping is empty`() {
            val companyId = 1L
            val integrationId = 1L
            val input = SaveLeaveTypesMappingInput.newBuilder()
                .integrationId(integrationId)
                .mapping(emptyList())
                .build()

            val actual = timeOffSyncService.saveLeaveTypesMapping(companyId, input)

            assertFalse { actual.success }
            assertTrue { actual.message == "No mapping found in the request" }
        }
    }
}