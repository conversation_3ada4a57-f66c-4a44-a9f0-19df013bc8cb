package com.multiplier.integration.service

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.common.exception.MplSystemException
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.io.ByteArrayInputStream

class CsvTimeSheetFileExtractorTest {

    @Nested
    inner class ExtractEntries {

        @Test
        fun `should extract entries from valid CSV file`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/valid.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(2)

            val firstEntry = entries[0]
            assertThat(firstEntry["email"]).isEqualTo("<EMAIL>")
            assertThat(firstEntry["date"]).isEqualTo("02/06/2025")
            assertThat(firstEntry["workStartTime"]).isEqualTo("14:00")
            assertThat(firstEntry["workEndTime"]).isEqualTo("15:33")

            val secondEntry = entries[1]
            assertThat(secondEntry["email"]).isEqualTo("<EMAIL>")
            assertThat(secondEntry["date"]).isEqualTo("03/06/2025")
            assertThat(secondEntry["workStartTime"]).isEqualTo("17:17")
            assertThat(secondEntry["workEndTime"]).isEqualTo("19:21")
        }

        @Test
        fun `should skip empty rows`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/skip_empty_rows.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(2)
            assertThat(entries[0]["email"]).isEqualTo("<EMAIL>")
            assertThat(entries[1]["email"]).isEqualTo("<EMAIL>")
        }

        @Test
        fun `should handle midnight and noon times correctly`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/midnight_noon.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(1)
            val entry = entries[0]
            assertThat(entry["workStartTime"]).isEqualTo("00:00")
            assertThat(entry["workEndTime"]).isEqualTo("12:00")
        }

        @Test
        fun `should throw exception for empty CSV file`() {
            // Given
            val inputStream = ByteArrayInputStream("".toByteArray())

            // When & Then
            assertThatThrownBy { CsvTimeSheetFileExtractor.extractEntries(inputStream) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("CSV file is empty")
        }

        @Test
        fun `should throw exception for missing required columns`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/missing_required_col.csv")

            // When & Then
            assertThatThrownBy { CsvTimeSheetFileExtractor.extractEntries(inputStream) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("Required column not found: user_email.")
        }

        @Test
        fun `should work with different column order`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/different_order.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(2)

            val firstEntry = entries[0]
            assertThat(firstEntry["email"]).isEqualTo("<EMAIL>")
            assertThat(firstEntry["date"]).isEqualTo("02/06/2025")
            assertThat(firstEntry["workStartTime"]).isEqualTo("14:00")
            assertThat(firstEntry["workEndTime"]).isEqualTo("15:33")
        }

        @Test
        fun `should work with only required columns`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/only_required_col.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(2)

            val firstEntry = entries[0]
            assertThat(firstEntry["email"]).isEqualTo("<EMAIL>")
            assertThat(firstEntry["date"]).isEqualTo("02/06/2025")
            assertThat(firstEntry["workStartTime"]).isEqualTo("14:00")
            assertThat(firstEntry["workEndTime"]).isEqualTo("15:33")
        }

        @Test
        fun `should work with case insensitive headers`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/case_insensitive_headers.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(1)

            val entry = entries[0]
            assertThat(entry["email"]).isEqualTo("<EMAIL>")
            assertThat(entry["date"]).isEqualTo("02/06/2025")
            assertThat(entry["workStartTime"]).isEqualTo("14:00")
            assertThat(entry["workEndTime"]).isEqualTo("15:33")
        }

        @Test
        fun `should work with original sample CSV format`() {
            // Given - This matches the original hubstaff.csv format
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/original_sample.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(2)

            val firstEntry = entries[0]
            assertThat(firstEntry["email"]).isEqualTo("<EMAIL>")
            assertThat(firstEntry["date"]).isEqualTo("02/06/2025")
            assertThat(firstEntry["workStartTime"]).isEqualTo("14:00")
            assertThat(firstEntry["workEndTime"]).isEqualTo("15:33")

            val secondEntry = entries[1]
            assertThat(secondEntry["email"]).isEqualTo("<EMAIL>")
            assertThat(secondEntry["date"]).isEqualTo("03/06/2025")
            assertThat(secondEntry["workStartTime"]).isEqualTo("17:17")
            assertThat(secondEntry["workEndTime"]).isEqualTo("19:21")
        }

        @Test
        fun `should throw exception for invalid datetime format`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/invalid_datetime.csv")

            // When & Then
            assertThatThrownBy { CsvTimeSheetFileExtractor.extractEntries(inputStream) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("Invalid datetime format")
        }

        @Test
        fun `should include entries with user email even if other fields are missing`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/missing_fields.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(3) // Now includes jane.doe, john.smith, and peter.pan (skips empty email)

            // First entry - complete data
            assertThat(entries[0]["email"]).isEqualTo("<EMAIL>")
            assertThat(entries[0]["date"]).isEqualTo("02/06/2025")
            assertThat(entries[0]["workStartTime"]).isEqualTo("14:00")
            assertThat(entries[0]["workEndTime"]).isEqualTo("15:33")

            // Second entry - missing start time (should have empty string)
            assertThat(entries[1]["email"]).isEqualTo("<EMAIL>")
            assertThat(entries[1]["date"]).isEqualTo("") // Empty because missing start_time
            assertThat(entries[1]["workStartTime"]).isEqualTo("") // Empty because missing
            assertThat(entries[1]["workEndTime"]).isEqualTo("19:21")

            // Third entry - complete data
            assertThat(entries[2]["email"]).isEqualTo("<EMAIL>")
            assertThat(entries[2]["date"]).isEqualTo("03/06/2025")
            assertThat(entries[2]["workStartTime"]).isEqualTo("17:17")
            assertThat(entries[2]["workEndTime"]).isEqualTo("19:21")
        }

        @Test
        fun `should skip rows with missing user email only`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/missing_user_email.csv")

            // When
            val entries = CsvTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).hasSize(2) // Only skips the row with empty user email
            assertThat(entries[0]["email"]).isEqualTo("<EMAIL>")
            assertThat(entries[1]["email"]).isEqualTo("<EMAIL>")
            assertThat(entries[1]["date"]).isEqualTo("") // Empty because missing start_time
        }

        @Test
        fun `should throw exception when CSV has only headers with no records`() {
            // Given
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet_csv/only_header.csv")

            // When & Then
            assertThatThrownBy { CsvTimeSheetFileExtractor.extractEntries(inputStream) }
                .isInstanceOf(MplBusinessException::class.java)
                .hasMessageContaining("CSV file has no valid data rows")
        }
    }
}
