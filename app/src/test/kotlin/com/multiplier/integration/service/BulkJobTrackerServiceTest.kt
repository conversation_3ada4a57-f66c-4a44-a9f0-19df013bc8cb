package com.multiplier.integration.service

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.common.exception.MplSystemException
import com.multiplier.integration.adapter.api.CUSTOMER_INTEGRATION_SERVICE_GROUP
import com.multiplier.integration.repository.BulkJobTrackerRepository
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaBulkJobTracker
import com.multiplier.integration.repository.model.URIType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.util.*

@ExtendWith(MockKExtension::class)
class BulkJobTrackerServiceTest {

    @MockK
    private lateinit var mockBulkJobTrackerRepository: BulkJobTrackerRepository

    @InjectMockKs
    private lateinit var bulkJobTrackerService: BulkJobTrackerService

    @Nested
    inner class FindByIdOrThrow {

        @Test
        fun `should return entity when found`() {
            // Given
            val id = 100L
            val expected = createJpaBulkJobTracker(id)
            every { mockBulkJobTrackerRepository.findById(id) } returns Optional.of(expected)

            // When
            val result = bulkJobTrackerService.findByIdOrThrow(id)

            // Then
            assertThat(result).isEqualTo(expected)
            verify { mockBulkJobTrackerRepository.findById(id) }
        }

        @Test
        fun `should throw exception when not found`() {
            // Given
            val id = 200L
            every { mockBulkJobTrackerRepository.findById(id) } returns Optional.empty()

            // Then
            assertThatThrownBy { bulkJobTrackerService.findByIdOrThrow(id) }
                .isInstanceOf(MplBusinessException::class.java)
        }
    }

    @Nested
    inner class FindByJobIdOrThrow {

        @Test
        fun `should return entity when found`() {
            // Given
            val jobId = 100L
            val expected = createJpaBulkJobTracker(jobId)
            every { mockBulkJobTrackerRepository.findByJobId(jobId) } returns expected

            // When
            val result = bulkJobTrackerService.findByJobIdOrThrow(jobId)

            // Then
            assertThat(result).isEqualTo(expected)
            verify { mockBulkJobTrackerRepository.findByJobId(jobId) }
        }

        @Test
        fun `should throw exception when not found`() {
            // Given
            val jobId = 100L
            every { mockBulkJobTrackerRepository.findByJobId(jobId) } returns null

            // Then
            assertThatThrownBy { bulkJobTrackerService.findByJobIdOrThrow(jobId) }
                .isInstanceOf(MplBusinessException::class.java)
        }
    }

    @Nested
    inner class CreateBulkJobTracker {

        @Test
        fun `should create new entity when valid`() {
            // Given
            val jobId = 100L
            val input = createJpaBulkJobTracker(jobId)
            every { mockBulkJobTrackerRepository.findByJobId(jobId) } returns null
            every { mockBulkJobTrackerRepository.save(input) } returns input

            // When
            val result = bulkJobTrackerService.createBulkJobTracker(input)

            // Then
            assertThat(result).isEqualTo(input)
            verify {
                mockBulkJobTrackerRepository.findByJobId(input.jobId)
                mockBulkJobTrackerRepository.save(input)
            }
        }

        @Test
        fun `should throw exception when jobId already exists`() {
            // When
            val jobId = 200L
            val input = createJpaBulkJobTracker(jobId)
            every { mockBulkJobTrackerRepository.findByJobId(input.jobId) } returns input

            // Then
            assertThatThrownBy { bulkJobTrackerService.createBulkJobTracker(input) }
                .isInstanceOf(MplSystemException::class.java)
            verify(exactly = 1) { mockBulkJobTrackerRepository.findByJobId(input.jobId) }
            verify(exactly = 0) { mockBulkJobTrackerRepository.save(any()) }
        }
    }

    @Nested
    inner class UpdateBulkJobTracker {

        @Test
        fun `updateBulkJobTracker should update existing entity`() {
            // Given
            val id = 1L
            val existingEntity = createJpaBulkJobTracker(id)
            val input = BulkJobTrackerDTO(
                id = id,
                jobStatus = BulkJobStatus.UPSERT_SUCCESSFUL,
                originalFileURI = "new-uri",
                reportFileURI = "report-uri",
                reportURIType = URIType.SFTP
            )
            every { mockBulkJobTrackerRepository.findById(id) } returns Optional.of(existingEntity)
            every { mockBulkJobTrackerRepository.save(any()) } returns existingEntity

            // When
            bulkJobTrackerService.updateBulkJobTracker(input)

            // Then
            verify(exactly = 1) {
                mockBulkJobTrackerRepository.findById(id)
                mockBulkJobTrackerRepository.save(match {
                    it.id == id &&
                    it.jobId == id &&
                    it.jobStatus == BulkJobStatus.UPSERT_SUCCESSFUL &&
                    it.originalFileURI == input.originalFileURI &&
                    it.reportFileURI == input.reportFileURI &&
                    it.reportURIType == input.reportURIType
                })
            }
        }
    }

    private fun createJpaBulkJobTracker(jobId: Long): JpaBulkJobTracker {
        return JpaBulkJobTracker(
            id = jobId,
            jobId = jobId,
            jobStatus = BulkJobStatus.UPSERT_IN_PROGRESS,
            companyId = 1L,
            entityId = 2L,
            groupName = CUSTOMER_INTEGRATION_SERVICE_GROUP,
            moduleNames = setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name),
            originalFileURI = "test-uri"
        )
    }
}