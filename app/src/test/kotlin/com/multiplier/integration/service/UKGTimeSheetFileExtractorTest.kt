package com.multiplier.integration.service

import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.io.ByteArrayInputStream
import java.time.LocalDateTime

class UKGTimeSheetFileExtractorTest {

    private lateinit var mockWorkbook: Workbook
    private lateinit var mockSheet: Sheet
    private lateinit var mockCell: Cell

    @BeforeEach
    fun setup() {
        mockWorkbook = mockk(relaxed = true)
        mockSheet = mockk(relaxed = true)
        mockCell = mockk(relaxed = true)
        mockkObject(ExcelProcessor)
    }

    @Nested
    inner class ExtractEntries {

        @Test
        fun `should extract entries from file`() {
            // Given
            val inputStream = ByteArrayInputStream(ByteArray(0))
            val mockEmployeeDataRow = mockk<Row>(relaxed = true)
            val mockTimesheetDataRow = mockk<Row>(relaxed = true)
            val mockEmployeeIdCell = mockk<Cell>()
            val mockEmployeeIdValueCell = mockk<Cell>()
            val mockDateTimeCell = mockk<Cell>()
            val localDateTime = LocalDateTime.of(2025, 4, 1, 9, 0)
            val mockStartTimeCell = mockk<Cell>()
            val mockEndTimeCell = mockk<Cell>()

            // Mock responses
            every { ExcelProcessor.createXSSFWorkbookOrThrow(inputStream) } returns mockWorkbook
            every { mockWorkbook.getSheetAt(0) } returns mockSheet
            every { mockSheet.lastRowNum } returns 2
            every { mockSheet.getRow(1) } returns mockEmployeeDataRow
            every { mockSheet.getRow(2) } returns mockTimesheetDataRow

            // Mock employee ID row
            every { mockEmployeeDataRow.getCell(0) } returns mockEmployeeIdCell
            every { mockEmployeeIdCell.stringCellValue } returns "Employee ID"

            every { mockEmployeeDataRow.getCell(1) } returns mockEmployeeIdValueCell
            every { mockEmployeeIdValueCell.cellType } returns CellType.STRING
            every { mockEmployeeIdValueCell.stringCellValue } returns "12345"

            // Mock working date
            every { mockTimesheetDataRow.getCell(1) } returns mockDateTimeCell
            every { mockDateTimeCell.cellType } returns CellType.NUMERIC
            every { mockDateTimeCell.localDateTimeCellValue } returns localDateTime

            // Mock working start time
            every { mockTimesheetDataRow.getCell(4) } returns mockStartTimeCell
            every { mockStartTimeCell.cellType } returns CellType.STRING
            every { mockStartTimeCell.stringCellValue } returns "09:00a"

            // Mock working end time
            every { mockTimesheetDataRow.getCell(5) } returns mockEndTimeCell
            every { mockEndTimeCell.cellType } returns CellType.STRING
            every { mockEndTimeCell.stringCellValue } returns "05:00p"

            // When
            val entries = UKGTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).isNotNull
            assertThat(entries).hasSize(1)
            assertThat(entries[0]["employeeId"]).isEqualTo("12345")
            assertThat(entries[0]["date"]).isEqualTo("01/04/2025")
            assertThat(entries[0]["workStartTime"]).isEqualTo("09:00")
            assertThat(entries[0]["workEndTime"]).isEqualTo("17:00")
        }

        @Test
        fun `should filter out invalid entries from file`() {
            // Given
            val inputStream = ByteArrayInputStream(ByteArray(0))
            val mockEmployeeDataRow = mockk<Row>(relaxed = true)
            val mockTimesheetDataRow = mockk<Row>(relaxed = true)
            val mockEmployeeIdCell = mockk<Cell>()
            val mockEmployeeIdValueCell = mockk<Cell>()
            val mockDateTimeCell = mockk<Cell>()
            val localDateTime = LocalDateTime.of(2025, 4, 1, 9, 0)
            val mockStartTimeCell = mockk<Cell>()
            val mockEndTimeCell = mockk<Cell>()

            // Mock responses
            every { ExcelProcessor.createXSSFWorkbookOrThrow(inputStream) } returns mockWorkbook
            every { mockWorkbook.getSheetAt(0) } returns mockSheet
            every { mockSheet.lastRowNum } returns 2
            every { mockSheet.getRow(1) } returns mockEmployeeDataRow
            every { mockSheet.getRow(2) } returns mockTimesheetDataRow

            // Mock employee ID row
            every { mockEmployeeDataRow.getCell(0) } returns mockEmployeeIdCell
            every { mockEmployeeIdCell.stringCellValue } returns "Employee ID"

            every { mockEmployeeDataRow.getCell(1) } returns mockEmployeeIdValueCell
            every { mockEmployeeIdValueCell.cellType } returns CellType.STRING
            every { mockEmployeeIdValueCell.stringCellValue } returns "12345"

            // Mock working date
            every { mockTimesheetDataRow.getCell(1) } returns mockDateTimeCell
            every { mockDateTimeCell.cellType } returns CellType.NUMERIC
            every { mockDateTimeCell.localDateTimeCellValue } returns localDateTime

            // Mock working start time
            every { mockTimesheetDataRow.getCell(4) } returns mockStartTimeCell
            every { mockStartTimeCell.cellType } returns CellType.STRING
            every { mockStartTimeCell.stringCellValue } returns "-" // invalid start time

            // Mock working end time
            every { mockTimesheetDataRow.getCell(5) } returns mockEndTimeCell
            every { mockEndTimeCell.cellType } returns CellType.STRING
            every { mockEndTimeCell.stringCellValue } returns "-" // invalid end time

            // When
            val entries = UKGTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).isNotNull
            assertThat(entries).isEmpty()
        }

        @Test
        fun `should return empty entries from file when employee does not have timesheet data`() {
            // Given
            val inputStream = ByteArrayInputStream(ByteArray(0))
            val mockEmployeeDataRow = mockk<Row>()
            val mockEmployeeIdCell = mockk<Cell>()
            val mockEmployeeIdValueCell = mockk<Cell>()

            // Mock responses
            every { ExcelProcessor.createXSSFWorkbookOrThrow(inputStream) } returns mockWorkbook
            every { mockWorkbook.getSheetAt(0) } returns mockSheet
            every { mockSheet.lastRowNum } returns 2
            every { mockSheet.getRow(any()) } returns mockEmployeeDataRow

            // Mock employee ID row
            every { mockEmployeeIdCell.stringCellValue } returns "Employee ID"
            every { mockEmployeeDataRow.getCell(0) } returns mockEmployeeIdCell

            every { mockEmployeeIdValueCell.cellType } returns CellType.STRING
            every { mockEmployeeIdValueCell.stringCellValue } returns "12345"
            every { mockEmployeeDataRow.getCell(1) } returns mockEmployeeIdValueCell

            // When
            val entries = UKGTimeSheetFileExtractor.extractEntries(inputStream)

            // Then
            assertThat(entries).isNotNull
            assertThat(entries).isEmpty()
        }

        @Test
        fun `should be able to extract data from real file`() {
            val inputStream = this.javaClass.classLoader.getResourceAsStream("sftp/timesheet/UKG_MAY_TIMESHEET_TEST.xlsx")
            val entries = UKGTimeSheetFileExtractor.extractEntries(inputStream)
            assertThat(entries).isNotNull
            assertThat(entries).hasSize(24)
        }
    }

    @Nested
    inner class FormatTime {
        @Test
        fun `should return correct day time`() {
            val input = "06:01a"
            val actual = UKGTimeSheetFileExtractor.formatTime(input)
            assertThat(actual).isEqualTo("06:01")
        }

        @Test
        fun `should return correct night time`() {
            val input = "06:01p"
            val actual = UKGTimeSheetFileExtractor.formatTime(input)
            assertThat(actual).isEqualTo("18:01")
        }
    }
}