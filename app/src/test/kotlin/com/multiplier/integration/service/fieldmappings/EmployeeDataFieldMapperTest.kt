package com.multiplier.integration.service.fieldmappings

import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.FieldMappingConfigurationRepository
import com.multiplier.integration.repository.FieldsMappingRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.model.FieldMappingConfigurationType
import com.multiplier.integration.repository.model.FieldType
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import io.kotest.matchers.shouldBe
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.util.Optional
import java.util.UUID

class EmployeeDataFieldMapperTest {

    @MockK
    private lateinit var fieldsMappingRepository: FieldsMappingRepository

    @MockK
    private lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    private lateinit var featureFlagService: FeatureFlagService

    @MockK
    private lateinit var fieldMappingConfigurationRepository: FieldMappingConfigurationRepository

    @MockK
    private lateinit var fieldMappingServiceAdapter: FieldMappingServiceAdapter

    @InjectMockKs
    private lateinit var employeeDataFieldMapper: EmployeeDataFieldMapper

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should use new field mapping service when both feature flags are enabled`() {
        val legalEntityId = 1L
        val eventData = mapOf("key" to "value")
        val companyId = 2L
        val integrationId = 3L
        val profileId = UUID.randomUUID().toString()
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, ffAttributes) } returns true
        every { featureFlagService.isOn(FeatureFlag.FIELD_MAPPING_SERVICE_ENABLED, ffAttributes) } returns true
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { configMap.fieldsMap["entityId"]?.stringValue } returns legalEntityId.toString()
                every { configMap.fieldsMap["integrationId"]?.stringValue } returns integrationId.toString()
                every { id } returns profileId
            })
        }
        every { fieldMappingServiceAdapter.executeMapping(profileId, eventData) } returns mockk {
            every { transformedData.fieldsMap } returns
                    mapOf("mappedKey" to mockk { every { allFields.values } returns mutableListOf("mappedValue") })
        }

        val result = employeeDataFieldMapper.mapEmployeeData(eventData, legalEntityId, OnboardingType.GLOBAL_PAYROLL, 4L, integrationId, companyId)

        result["mappedKey"] shouldBe "mappedValue"
    }

    @Test
    fun `should throw exception when new field mapping service fails`() {
        val legalEntityId = 1L
        val eventData = mapOf("mappedKey" to "value")
        val companyId = 2L
        val integrationId = 3L
        val ffAttributes = mapOf("company" to companyId, "platform" to 4L)

        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, ffAttributes) } returns true
        every { featureFlagService.isOn(FeatureFlag.FIELD_MAPPING_SERVICE_ENABLED, ffAttributes) } returns true
        every { fieldMappingServiceAdapter.listProfiles(companyId) } throws RuntimeException("Service error")
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, legalEntityId) } returns Optional.of(mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { entityId } returns legalEntityId
            every { isEnabled } returns true
        })
        every { fieldsMappingRepository.findByEntityIdAndIntegrationIdAndParentIdIsNull(legalEntityId, integrationId) } returns listOf(
            mockk {
                every { originField } returns "key"
                every { mappedField } returns "mappedKey"
                every { children } returns emptyList()
                every { type } returns FieldType.STRING
                every { isCalculated } returns false
            }
        )

        assertThrows<IllegalStateException> {
    employeeDataFieldMapper.mapEmployeeData(eventData, legalEntityId, OnboardingType.GLOBAL_PAYROLL, 4L, integrationId, companyId)
}

    }

    @Test
    fun `should use default mappings when customer integration field mapping feature flag is disabled`() {
        val eventData = mapOf("mappedKey" to "value")
        val platformId = 4L

        every { featureFlagService.isOn(FeatureFlag.CUSTOMER_INTEGRATION_FIELD_MAPPING_ENABLED, mapOf("company" to 2L, "platform" to platformId)) } returns false
        every { fieldMappingConfigurationRepository.getMappingsByTypeWithFallback(FieldMappingConfigurationType.DEFAULT, platformId) } returns listOf(
            mockk {
                every { key } returns "key"
                every { value } returns "mappedKey"
                every { enumMappings } returns null
                every { type } returns FieldMappingConfigurationType.DEFAULT
            }
        )
        every { fieldMappingConfigurationRepository.getMappingsByTypeWithFallback(FieldMappingConfigurationType.CALCULATED, platformId) } returns emptyList()

        val result = employeeDataFieldMapper.mapEmployeeData(eventData, 1L, OnboardingType.GLOBAL_PAYROLL, platformId, 3L, 2L)

        result["key"] shouldBe "value"
    }
}