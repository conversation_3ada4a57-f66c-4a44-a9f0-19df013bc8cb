package com.multiplier.integration.service.email

import com.multiplier.integration.types.NotificationType
import com.multiplier.integration.utils.toCapitalize
import io.mockk.MockKAnnotations
import io.mockk.impl.annotations.InjectMockKs
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.test.util.ReflectionTestUtils

class AdminEmailTemplateGeneratorTest {

    @InjectMockKs
    private lateinit var adminEmailTemplateGenerator: AdminEmailTemplateGenerator

    private val systemNotificationEmail = "<EMAIL>"

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        ReflectionTestUtils.setField(adminEmailTemplateGenerator, "systemNotificationEmail", systemNotificationEmail)
    }

    @Test
    fun `getEmailTemplateForEmployeeImportCompletion sets correct values`() {
        // Arrange
        val adminEmail = "<EMAIL>"
        val platformName = "HRMS"

        // Act
        val builder = adminEmailTemplateGenerator.getEmailTemplateForEmployeeImportCompletion(adminEmail, platformName)
        val emailNotificationBody = builder.build() // Build the actual EmailNotificationBody for assertions

        // Assert
        assertThat(emailNotificationBody.templateType).isEqualTo(NotificationType.IntegrationEmployeeImportCompletedEmailToAdmin.toString())
        assertThat(emailNotificationBody.subject).isEqualTo("Data sync completed from $platformName to Multiplier")
        assertThat(emailNotificationBody.from).isEqualTo(systemNotificationEmail)
        assertThat(emailNotificationBody.to).isEqualTo(adminEmail)
    }

    @Test
    fun `getEmailTemplateForSFTPIntegrationUpsertSuccess sets correct values`() {
        // Arrange
        val adminEmail = "<EMAIL>"
        val entriesCount = 10
        val fileName = "employees.csv"
        val feature = "timesheet"

        // Act
        val emailNotificationBody = adminEmailTemplateGenerator.getEmailTemplateForSFTPIntegrationUpsertSuccess(
            adminEmail, entriesCount, fileName, feature
        ).build()

        // Assert
        assertThat(emailNotificationBody.templateType).isEqualTo(NotificationType.IntegrationSFTPUpsertSuccessEmailToAdmin.toString())
        assertThat(emailNotificationBody.subject).isEqualTo("${feature.toCapitalize()} upload: $entriesCount values updated successfully - ($fileName)")
        assertThat(emailNotificationBody.from).isEqualTo(systemNotificationEmail)
        assertThat(emailNotificationBody.to).isEqualTo(adminEmail)
    }

    @Test
    fun `getEmailTemplateForSFTPIntegrationValidationFailure sets correct values`() {
        // Arrange
        val adminEmail = "<EMAIL>"
        val fileName = "employees.csv"
        val feature = "timesheet"

        // Act
        val emailNotificationBody = adminEmailTemplateGenerator.getEmailTemplateForSFTPIntegrationValidationFailure(
            adminEmail, fileName, feature
        ).build()

        // Assert
        assertThat(emailNotificationBody.templateType).isEqualTo(NotificationType.IntegrationSFTPValidationFailureEmailToAdmin.toString())
        assertThat(emailNotificationBody.subject).isEqualTo("${feature.toCapitalize()}: Some Entries Could Not Be Processed – ($fileName)")
        assertThat(emailNotificationBody.from).isEqualTo(systemNotificationEmail)
        assertThat(emailNotificationBody.to).isEqualTo(adminEmail)
    }

    @Test
    fun `getEmailTemplateForSftpAccessRequest sets correct values`() {
        // Arrange
        val opsEmail = "<EMAIL>"

        // Act
        val emailNotificationBody = adminEmailTemplateGenerator.getEmailTemplateForSftpAccessRequest(
            opsEmail
        ).build()

        // Assert
        assertThat(emailNotificationBody.templateType).isEqualTo(NotificationType.IntegrationSFTPAccessRequestEmailToOps.toString())
        assertThat(emailNotificationBody.subject).isEqualTo("New SFTP Access Request")
        assertThat(emailNotificationBody.from).isEqualTo(systemNotificationEmail)
        assertThat(emailNotificationBody.to).isEqualTo(opsEmail)
    }
}
