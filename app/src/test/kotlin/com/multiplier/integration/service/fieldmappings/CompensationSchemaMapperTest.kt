package com.multiplier.integration.service.fieldmappings

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData.Group
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import java.time.LocalDate

class CompensationSchemaMapperTest {

    @Test
    fun `should collect currency, frequency once and rate for every pay component`() {
        val input = listOf(
            bulkSpec("COMPONENT_NAME", Group.COMPENSATION_DATA.name, listOf("Base Salary", "Internet Allowance")),
            bulkSpec("BILLING_FREQUENCY", Group.COMPENSATION_DATA.name, listOf("MONTHLY"), label = "Billing Frequency"),
            bulkSpec("CURRENCY", Group.COMPENSATION_DATA.name, label = "Currency"),
        )
        val expected = listOf(
            bulkSpec("COMP_ITEM:BASE_SALARY", Group.COMPENSATION_DATA.name, label = "Base Salary", mandatory = false),
            bulkSpec("COMP_ITEM:INTERNET_ALLOWANCE", Group.COMPENSATION_DATA .name, label = "Internet Allowance", mandatory = false),
            bulkSpec("COMP_ATTR:BILLING_FREQUENCY", Group.COMPENSATION_DATA.name, listOf("MONTHLY"), label = "Billing Frequency"),
            bulkSpec("COMP_ATTR:CURRENCY", Group.COMPENSATION_DATA.name, label = "Currency"),
            bulkSpec("COMP_ATTR:PAY_SCHEDULE", Group.COMPENSATION_DATA.name, label = "Pay Schedule"),
        )

        val result = CompensationSchemaMapper.mapToBulkOnboardDataSpec(input)

        result.shouldContainExactlyInAnyOrder(expected)
    }

    @Test
    fun `should group the data so it can be sent to the bulk upload service`() {
        val input = mapOf(
            "employeeId" to "emp1",
            "name" to "John",
            "COMP_ITEM:BASE_SALARY" to "10000",
            "COMP_ITEM:INTERNET_ALLOWANCE" to "5000",
            "COMP_ITEM:EMPTY_ITEM" to "",
            "COMP_ATTR:BILLING_FREQUENCY" to "ANNUALLY",
            "COMP_ATTR:CURRENCY" to " USD",
        )

        val actual = CompensationSchemaMapper.groupEmployeeData(
            input = input,
            startDate = LocalDate.of(2025, 2, 11),
            payScheduleName = "Monthly"
        )

        actual.employeeData shouldBe mapOf(
            "group" to Group.EMPLOYMENT_DATA.name,
            "employeeId" to "emp1",
            "name" to "John",
        )

        actual.compensationData shouldBe listOf(
            mapOf(
                "employeeId" to "emp1",
                "group" to Group.COMPENSATION_DATA.name,
                "COMPONENT_NAME" to "Base Salary",
                "CURRENCY" to "USD",
                "BILLING_RATE" to "10000",
                "BILLING_RATE_TYPE" to "Value",
                "BILLING_FREQUENCY" to "ANNUALLY",
                "PAY_SCHEDULE_NAME" to "Monthly",
                "IS_INSTALLMENT" to "No",
                "START_DATE" to "2025-02-11"
            ),
            mapOf(
                "employeeId" to "emp1",
                "group" to Group.COMPENSATION_DATA.name,
                "COMPONENT_NAME" to "Internet Allowance",
                "CURRENCY" to "USD",
                "BILLING_RATE" to "5000",
                "BILLING_RATE_TYPE" to "Value",
                "BILLING_FREQUENCY" to "ANNUALLY",
                "PAY_SCHEDULE_NAME" to "Monthly",
                "IS_INSTALLMENT" to "No",
                "START_DATE" to "2025-02-11"
            )
        )
    }

    @Test
    fun `should return non-compensation data as-is`() {
        val input = listOf(
            bulkSpec("employeeId", Group.EMPLOYMENT_DATA.name),
            bulkSpec("firstName", Group.EMPLOYMENT_DATA.name)
        )

        val result = CompensationSchemaMapper.mapToBulkOnboardDataSpec(input)

        result.shouldContainExactly(input)
    }

    private fun bulkSpec(
        key: String,
        group: String,
        values: List<String> = emptyList(),
        label: String = key,
        mandatory: Boolean = true
    ): BulkOnboardDataSpec {
        return BulkOnboardDataSpec.newBuilder()
            .setKey(key)
            .setLabel(label)
            .setGroup(group)
            .addAllValues(values)
            .setRequired(mandatory)
            .build()
    }
}