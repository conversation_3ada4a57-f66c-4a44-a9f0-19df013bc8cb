package com.multiplier.integration.service.email

import com.multiplier.integration.types.NotificationType
import com.multiplier.pigeonservice.PigeonNotificationClient
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class EmailServiceTest {
    @MockK
    lateinit var pigeonNotificationClient: PigeonNotificationClient

    @InjectMockKs
    lateinit var emailService: EmailService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should send email successfully`() {
        val emailDto = EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.UpdateBasicDetailsOnMultiplierEmailToMember.toString())
            .setSubject("[Action Required] Update your details on Multiplier.")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")
            .build()

        every { pigeonNotificationClient.send(any()) } returns "ok"
        emailService.sendEmail(emailDto)
        verify(exactly = 1) { pigeonNotificationClient.send(any()) }
    }

    @Test
    fun `should send email with attachments successfully`() {
        val emailDto = EmailNotificationBody.newBuilder()
            .setTemplateType(NotificationType.UpdateBasicDetailsOnMultiplierEmailToMember.toString())
            .setSubject("[Action Required] Update your details on Multiplier.")
            .setFrom("<EMAIL>")
            .setTo("<EMAIL>")
            .build()

        every { pigeonNotificationClient.send(any()) } returns "ok"
        emailService.sendEmailWithAttachment(emailDto, listOf())
        verify(exactly = 1) { pigeonNotificationClient.send(any()) }
    }
}