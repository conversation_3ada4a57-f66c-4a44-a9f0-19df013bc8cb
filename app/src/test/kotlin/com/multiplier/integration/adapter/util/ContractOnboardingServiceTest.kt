package com.multiplier.integration.adapter.util

import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country.CountryCode
import com.multiplier.integration.adapter.api.ContractOnboardingServiceClient
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.platforms.Platform
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest

@SpringBootTest(
    classes = [
        ContractOnboardingService::class,
        DefaultContractOnboardingDataSanitizeStrategy::class,
        SuccessFactorsContractOnboardingDataSanitizeAction::class,
        ContractOnboardingServiceClient::class
    ]
)
class ContractOnboardingServiceTest {

    @Autowired
    private lateinit var contractOnboardingService: ContractOnboardingService

    @Test
    fun cleanBulkContractOnboardingRequest() {
        val request = BulkContractOnboardingRequest(
            companyId = 1,
            entityId = 1,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = CountryCode.COUNTRY_CODE_CAN,
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = mapOf(
                "dateOfBirth" to "2000-01-10T00:00:00+01:00",
                "startOn" to "2000-01-10T00:00:00Z",
                "endOn" to "2000-01-10T00:00Z",
                "address.zipcode" to "***********",
                "firstName" to "firstName",
                "lastName" to "lastName",
                "address.country" to "CAN",
                "bank.routingNumber" to "**********",
            )
        ))

        val data = contractOnboardingService.cleanBulkContractOnboardingRequest(request, Platform.UNKNOWN)

        Assertions.assertEquals("2000-01-10", data.data.all["dateOfBirth"])
        Assertions.assertEquals("2000-01-10", data.data.all["startOn"])
        Assertions.assertEquals("2000-01-10", data.data.all["endOn"])
        Assertions.assertEquals("123 456 789", data.data.all["address.zipcode"])
        Assertions.assertEquals("firstName lastName", data.data.all["bank.accountHolderName"])
        Assertions.assertEquals("CA", data.data.all["bank.address.country"])
        Assertions.assertEquals("123", data.data.all["bank.institutionNumber"])
        Assertions.assertEquals("456789", data.data.all["bank.transitNumber"])
    }
}