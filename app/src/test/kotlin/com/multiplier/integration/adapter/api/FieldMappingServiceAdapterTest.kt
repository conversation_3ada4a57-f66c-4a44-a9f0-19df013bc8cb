package com.multiplier.integration.adapter.api

import com.google.protobuf.Struct
import com.google.protobuf.Timestamp
import com.google.protobuf.Value
import com.multiplier.fieldmapping.grpc.schema.*
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.grpc.Status
import io.grpc.StatusRuntimeException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.Instant
import java.util.UUID

import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class FieldMappingServiceAdapterTest {
    @MockK
    private lateinit var fieldMappingStub: FieldMappingServiceGrpc.FieldMappingServiceBlockingStub

    @InjectMockKs
    private lateinit var adapter: DefaultFieldMappingServiceAdapter

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getProfile should return profile when found`() {
        // Arrange
        val profileId = UUID.randomUUID().toString()
        val now = Instant.now()
        val timestamp = Timestamp.newBuilder()
            .setSeconds(now.epochSecond)
            .setNanos(now.nano)
            .build()

        val rule = Rule.newBuilder()
            .setId("rule-1")
            .setSourceField("source_field")
            .setTargetField("target_field")
            .setTransformationType(TransformationType.DIRECT_MAPPING)
            .setIsRequired(true)
            .setOrder(1)
            .build()

        val profile = Profile.newBuilder()
            .setId(profileId)
            .setName("Test Profile")
            .setDescription("Test Description")
            .setCompanyId(100L)
            .setIsActive(true)
            .setCreatedAt(timestamp)
            .setUpdatedAt(timestamp)
            .addRules(rule)
            .build()

        val response = ProfileResponse.newBuilder()
            .setProfile(profile)
            .build()

        every { fieldMappingStub.getProfile(any()) } returns response

        // Act
        val result = adapter.getProfile(profileId).profile

        // Assert
        assertEquals(profileId, result.id)
        assertEquals("Test Profile", result.name)
        assertEquals("Test Description", result.description)
        assertEquals(100L, result.companyId)
        assertTrue(result.isActive)
        assertNotNull(result.createdAt)
        assertNotNull(result.updatedAt)
        assertEquals(1, result.rulesList.size)

        val resultRule = result.rulesList[0]
        assertEquals("rule-1", resultRule.id)
        assertEquals("source_field", resultRule.sourceField)
        assertEquals("target_field", resultRule.targetField)
        assertEquals(TransformationType.DIRECT_MAPPING, resultRule.transformationType)
        assertTrue(resultRule.isRequired)
        assertEquals(1, resultRule.order)

        verify(exactly = 1) { fieldMappingStub.getProfile(any()) }
    }

    @Test
    fun `getProfile should throw exception when gRPC call fails`() {
        // Arrange
        val profileId = UUID.randomUUID().toString()
        val statusException = StatusRuntimeException(Status.INTERNAL.withDescription("Internal error"))

        every { fieldMappingStub.getProfile(any()) } throws statusException

        // Act & Assert
        val exception = assertFailsWith<IntegrationDownstreamException> {
            adapter.getProfile(profileId)
        }

        assertEquals("INTERNAL: Internal error", exception.message)
    }

    @Test
    fun `listProfiles should return profiles when found`() {
        // Arrange
        val companyId = 100L
        val now = Instant.now()
        val timestamp = Timestamp.newBuilder()
            .setSeconds(now.epochSecond)
            .setNanos(now.nano)
            .build()

        val profile1 = Profile.newBuilder()
            .setId("profile-1")
            .setName("Profile 1")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setCreatedAt(timestamp)
            .build()

        val profile2 = Profile.newBuilder()
            .setId("profile-2")
            .setName("Profile 2")
            .setCompanyId(companyId)
            .setIsActive(false)
            .setCreatedAt(timestamp)
            .build()

        val response = ListProfilesResponse.newBuilder()
            .addProfiles(profile1)
            .addProfiles(profile2)
            .build()

        every { fieldMappingStub.listProfiles(any()) } returns response

        // Act
        val result = adapter.listProfiles(companyId).profilesList

        // Assert
        assertEquals(2, result.size)
        assertEquals("profile-1", result[0].id)
        assertEquals("Profile 1", result[0].name)
        assertTrue(result[0].isActive)
        assertEquals("profile-2", result[1].id)
        assertEquals("Profile 2", result[1].name)
        assertFalse(result[1].isActive)

        verify(exactly = 1) { fieldMappingStub.listProfiles(any()) }
    }

    @Test
    fun `executeMapping should transform data correctly`() {
        // Arrange
        val profileId = UUID.randomUUID().toString()
        val sourceData = mapOf(
            "firstName" to "John",
            "lastName" to "Doe",
            "age" to 30
        )

        val transformedStruct = Struct.newBuilder()
            .putFields("first_name", Value.newBuilder().setStringValue("John").build())
            .putFields("last_name", Value.newBuilder().setStringValue("Doe").build())
            .putFields("age", Value.newBuilder().setStringValue("30").build())
            .build()

        val response = ExecuteMappingResponse.newBuilder()
            .setTransformedData(transformedStruct)
            .build()

        every { fieldMappingStub.executeMapping(any()) } returns response

        // Act
        val result = adapter.executeMapping(profileId, sourceData).transformedData

        // Assert
        assertEquals(3, result.fieldsCount)
        assertEquals("John", result.fieldsMap["first_name"]?.stringValue)
        assertEquals("Doe", result.fieldsMap["last_name"]?.stringValue)
        assertEquals("30", result.fieldsMap["age"]?.stringValue)

        verify(exactly = 1) { fieldMappingStub.executeMapping(any()) }
    }

    @Test
    fun `executeBatchMapping should transform batch data correctly`() {
        // Arrange
        val profileId = UUID.randomUUID().toString()
        val sourceDataBatch = listOf(
            mapOf("firstName" to "John", "lastName" to "Doe"),
            mapOf("firstName" to "Jane", "lastName" to "Smith")
        )

        val transformedStruct1 = Struct.newBuilder()
            .putFields("first_name", Value.newBuilder().setStringValue("John").build())
            .putFields("last_name", Value.newBuilder().setStringValue("Doe").build())
            .build()

        val transformedStruct2 = Struct.newBuilder()
            .putFields("first_name", Value.newBuilder().setStringValue("Jane").build())
            .putFields("last_name", Value.newBuilder().setStringValue("Smith").build())
            .build()

        val response = ExecuteBatchMappingResponse.newBuilder()
            .addTransformedDataBatch(transformedStruct1)
            .addTransformedDataBatch(transformedStruct2)
            .build()

        every { fieldMappingStub.executeBatchMapping(any()) } returns response

        // Act
        val result = adapter.executeBatchMapping(profileId, sourceDataBatch).transformedDataBatchList

        // Assert
        assertEquals(2, result.size)
        assertEquals("John", result[0].fieldsMap["first_name"]?.stringValue)
        assertEquals("Doe", result[0].fieldsMap["last_name"]?.stringValue)
        assertEquals("Jane", result[1].fieldsMap["first_name"]?.stringValue)
        assertEquals("Smith", result[1].fieldsMap["last_name"]?.stringValue)

        verify(exactly = 1) { fieldMappingStub.executeBatchMapping(any()) }
    }

    @Test
    fun `createProfile should create and return profile`() {
        // Arrange
        val profileToCreate = Profile.newBuilder()
            .setName("New Profile")
            .setDescription("New Description")
            .setCompanyId(100L)
            .setIsActive(true)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("source_field")
                    .setTargetField("target_field")
                    .setTransformationType(TransformationType.DIRECT_MAPPING)
                    .setIsRequired(true)
                    .setOrder(1)
                    .build()
            )
            .build()

        val now = Instant.now()
        val timestamp = Timestamp.newBuilder()
            .setSeconds(now.epochSecond)
            .setNanos(now.nano)
            .build()

        val createdProfile = Profile.newBuilder()
            .setId("profile-new")
            .setName("New Profile")
            .setDescription("New Description")
            .setCompanyId(100L)
            .setIsActive(true)
            .setCreatedAt(timestamp)
            .setUpdatedAt(timestamp)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("source_field")
                    .setTargetField("target_field")
                    .setTransformationType(TransformationType.DIRECT_MAPPING)
                    .setIsRequired(true)
                    .setOrder(1)
                    .build()
            )
            .build()

        val response = ProfileResponse.newBuilder()
            .setProfile(createdProfile)
            .build()

        every { fieldMappingStub.createProfile(any()) } returns response

        // Act
        val result = adapter.createProfile(profileToCreate).profile

        // Assert
        assertEquals("profile-new", result.id)
        assertEquals("New Profile", result.name)
        assertEquals("New Description", result.description)
        assertEquals(100L, result.companyId)
        assertTrue(result.isActive)
        assertNotNull(result.createdAt)
        assertNotNull(result.updatedAt)
        assertEquals(1, result.rulesList.size)

        verify(exactly = 1) { fieldMappingStub.createProfile(any()) }
    }

    @Test
    fun `updateProfile should update and return profile`() {
        val profileId = UUID.randomUUID().toString()
        // Arrange
        val profileToUpdate = Profile.newBuilder()
            .setId(profileId)
            .setName("Updated Profile")
            .setDescription("Updated Description")
            .setCompanyId(100L)
            .setIsActive(true)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("updated_source")
                    .setTargetField("updated_target")
                    .setTransformationType(TransformationType.DIRECT_MAPPING)
                    .setIsRequired(false)
                    .setOrder(2)
                    .build()
            )
            .build()

        val now = Instant.now()
        val timestamp = Timestamp.newBuilder()
            .setSeconds(now.epochSecond)
            .setNanos(now.nano)
            .build()

        val updatedProfile = Profile.newBuilder()
            .setId(profileId)
            .setName("Updated Profile")
            .setDescription("Updated Description")
            .setCompanyId(100L)
            .setIsActive(true)
            .setCreatedAt(timestamp)
            .setUpdatedAt(timestamp)
            .addRules(
                Rule.newBuilder()
                    .setId("rule-1")
                    .setSourceField("updated_source")
                    .setTargetField("updated_target")
                    .setTransformationType(TransformationType.DIRECT_MAPPING)
                    .setIsRequired(false)
                    .setOrder(2)
                    .build()
            )
            .build()

        val response = ProfileResponse.newBuilder()
            .setProfile(updatedProfile)
            .build()

        every { fieldMappingStub.updateProfile(any()) } returns response

        // Act
        val result = adapter.updateProfile(profileToUpdate).profile

        // Assert
        assertEquals(profileId, result.id)
        assertEquals("Updated Profile", result.name)
        assertEquals("Updated Description", result.description)
        assertEquals(100L, result.companyId)
        assertTrue(result.isActive)
        assertNotNull(result.createdAt)
        assertNotNull(result.updatedAt)
        assertEquals(1, result.rulesCount)

        val resultRule = result.rulesList[0]
        assertEquals("rule-1", resultRule.id)
        assertEquals("updated_source", resultRule.sourceField)
        assertEquals("updated_target", resultRule.targetField)
        assertEquals(TransformationType.DIRECT_MAPPING, resultRule.transformationType)
        assertFalse(resultRule.isRequired)
        assertEquals(2, resultRule.order)

        verify(exactly = 1) { fieldMappingStub.updateProfile(any()) }
    }

    @Test
    fun `deleteProfile should return true when successful`() {
        // Arrange
        val profileId = UUID.randomUUID().toString()
        val response = DeleteProfileResponse.newBuilder()
            .setSuccess(true)
            .build()

        every { fieldMappingStub.deleteProfile(any()) } returns response

        // Act
        val result = adapter.deleteProfile(profileId).success

        // Assert
        assertTrue(result)
        verify(exactly = 1) { fieldMappingStub.deleteProfile(any()) }
    }

    @Test
    fun `deleteProfile should return false when unsuccessful`() {
        // Arrange
        val profileId = UUID.randomUUID().toString()
        val response = DeleteProfileResponse.newBuilder()
            .setSuccess(false)
            .build()

        every { fieldMappingStub.deleteProfile(any()) } returns response

        // Act
        val result = adapter.deleteProfile(profileId).success

        // Assert
        assertFalse(result)
        verify(exactly = 1) { fieldMappingStub.deleteProfile(any()) }
    }

    @Test
    fun `convertToValue should handle different data types correctly`() {

        // Arrange
        val profileId = UUID.randomUUID().toString()
        val sourceData = mapOf(
            "stringValue" to "text",
            "intValue" to 42,
            "doubleValue" to 3.14,
            "boolValue" to true,
            "listValue" to listOf("a", "b", "c"),
            "mapValue" to mapOf("key1" to "value1", "key2" to "value2")
        )

        // Create a response with just one field to simplify the test
        val transformedStruct = Struct.newBuilder()
            .putFields("result", Value.newBuilder().setStringValue("success").build())
            .build()

        val response = ExecuteMappingResponse.newBuilder()
            .setTransformedData(transformedStruct)
            .build()

        every { fieldMappingStub.executeMapping(any()) } returns response

        // Act
        val result = adapter.executeMapping(profileId, sourceData)

        // Assert - just verify the call was made successfully
        assertEquals("success", result.transformedData.fieldsMap["result"]?.stringValue)
        verify(exactly = 1) { fieldMappingStub.executeMapping(any()) }
    }
}
