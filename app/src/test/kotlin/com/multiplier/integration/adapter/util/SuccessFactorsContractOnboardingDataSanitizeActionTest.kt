package com.multiplier.integration.adapter.util

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country.CountryCode
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class SuccessFactorsContractOnboardingDataSanitizeActionTest {

    @MockK
    private lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @InjectMockKs
    private lateinit var successFactorsContractOnboardingDataSanitizeAction: SuccessFactorsContractOnboardingDataSanitizeAction

    @Test
    fun extractCompensation() {
        val request = BulkContractOnboardingRequest(
            companyId = 1,
            entityId = 1,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = CountryCode.COUNTRY_CODE_USA,
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = mapOf(
                "basePay" to "1000",
                "payrollFrequency" to "MONTHLY",
            )
        ))

        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(request) } returns listOf(
            BulkOnboardDataSpec.newBuilder()
                .setKey("rateFrequency")
                .addValues("ANNUALLY")
                .build()
        )

        val data = successFactorsContractOnboardingDataSanitizeAction.extractCompensation(request)

        Assertions.assertEquals("12000.0", data["basePay"])
        Assertions.assertEquals("ANNUALLY", data["rateFrequency"])
    }
}