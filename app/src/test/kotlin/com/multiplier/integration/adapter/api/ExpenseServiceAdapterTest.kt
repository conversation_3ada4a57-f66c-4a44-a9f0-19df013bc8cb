package com.multiplier.integration.adapter.api

import com.multiplier.common.exception.MplSystemException
import com.multiplier.expense.schema.*
import io.grpc.StatusRuntimeException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class ExpenseServiceAdapterTest {

    @MockK(relaxed = true)
    lateinit var stub: ExpenseServiceGrpc.ExpenseServiceBlockingStub

    @InjectMockKs
    var expenseServiceAdapter: DefaultExpenseServiceAdapter = DefaultExpenseServiceAdapter()

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `bulkCreateExpensesNonTransactional should call stub and return expected response`() {
        val request = BulkCreateExpensesRequest.newBuilder().build()
        val expectedResponse = BulkCreateExpensesResponse.newBuilder().build()

        every { stub.bulkCreateExpensesNonTransactional(request) } returns expectedResponse

        val actualResponse = expenseServiceAdapter.bulkCreateExpensesNonTransactional(request)

        assertEquals(expectedResponse, actualResponse)
        verify(exactly = 1) { stub.bulkCreateExpensesNonTransactional(request) }
    }

    @Test
    fun `bulkUpsertExpensesNonTransactional should call stub and return expected response`() {
        val request = BulkCreateExpensesRequest.newBuilder().build()
        val expectedResponse = BulkCreateExpensesResponse.newBuilder().build()

        every { stub.bulkUpsertExpensesNonTransactional(request) } returns expectedResponse

        val actualResponse = expenseServiceAdapter.bulkUpsertExpensesNonTransactional(request)

        assertEquals(expectedResponse, actualResponse)
        verify(exactly = 1) { stub.bulkUpsertExpensesNonTransactional(request) }
    }

    @Test
    fun `bulkRevokeExpensesNonTransactional should call stub and return expected response`() {
        val request = BulkDeleteExpensesRequest.newBuilder().build()
        val expectedResponse = GrpcBulkDeleteExpensesResponse.newBuilder().build()

        every { stub.bulkDeleteExpenses(request) } returns expectedResponse

        val actualResponse = expenseServiceAdapter.bulkRevokeExpensesNonTransactional(request)

        assertEquals(expectedResponse, actualResponse)
        verify(exactly = 1) { stub.bulkDeleteExpenses(request) }
    }

    @Test
    fun `bulkCreateExpensesNonTransactional should handle errors gracefully`() {
        val request = BulkCreateExpensesRequest.newBuilder().build()
        val exception = StatusRuntimeException(io.grpc.Status.INTERNAL)

        every { stub.bulkCreateExpensesNonTransactional(request) } throws exception

        val thrownException = assertThrows<MplSystemException> {
            expenseServiceAdapter.bulkCreateExpensesNonTransactional(request)
        }

        assertEquals(io.grpc.Status.INTERNAL.code.name, thrownException.message)
        verify(exactly = 1) { stub.bulkCreateExpensesNonTransactional(request) }
    }

    @Test
    fun `bulkUpsertExpensesNonTransactional should handle errors gracefully`() {
        val request = BulkCreateExpensesRequest.newBuilder().build()
        val exception = StatusRuntimeException(io.grpc.Status.INTERNAL)

        every { stub.bulkUpsertExpensesNonTransactional(request) } throws exception

        val thrownException = assertThrows<MplSystemException> {
            expenseServiceAdapter.bulkUpsertExpensesNonTransactional(request)
        }

        assertEquals(io.grpc.Status.INTERNAL.code.name, thrownException.message)
        verify(exactly = 1) { stub.bulkUpsertExpensesNonTransactional(request) }
    }

    @Test
    fun `bulkRevokeExpensesNonTransactional should handle errors gracefully`() {
        val request = BulkDeleteExpensesRequest.newBuilder().build()
        val exception = StatusRuntimeException(io.grpc.Status.INTERNAL)

        every { stub.bulkDeleteExpenses(request) } throws exception

        val thrownException = assertThrows<MplSystemException> {
            expenseServiceAdapter.bulkRevokeExpensesNonTransactional(request)
        }

        assertEquals(io.grpc.Status.INTERNAL.code.name, thrownException.message)
        verify(exactly = 1) { stub.bulkDeleteExpenses(request) }
    }

    @Test
    fun `bulkCreateExpensesNonTransactional should handle different error statuses`() {
        val request = BulkCreateExpensesRequest.newBuilder().build()
        val exception = StatusRuntimeException(io.grpc.Status.INVALID_ARGUMENT)

        every { stub.bulkCreateExpensesNonTransactional(request) } throws exception

        val thrownException = assertThrows<MplSystemException> {
            expenseServiceAdapter.bulkCreateExpensesNonTransactional(request)
        }

        assertEquals(io.grpc.Status.INVALID_ARGUMENT.code.name, thrownException.message)
        verify(exactly = 1) { stub.bulkCreateExpensesNonTransactional(request) }
    }

    @Test
    fun `bulkUpsertExpensesNonTransactional should handle deadline exceeded`() {
        val request = BulkCreateExpensesRequest.newBuilder().build()
        val exception = StatusRuntimeException(io.grpc.Status.DEADLINE_EXCEEDED)

        every { stub.bulkUpsertExpensesNonTransactional(request) } throws exception

        val thrownException = assertThrows<MplSystemException> {
            expenseServiceAdapter.bulkUpsertExpensesNonTransactional(request)
        }

        assertEquals(io.grpc.Status.DEADLINE_EXCEEDED.code.name, thrownException.message)
        verify(exactly = 1) { stub.bulkUpsertExpensesNonTransactional(request) }
    }

    @Test
    fun `bulkRevokeExpensesNonTransactional should handle permission denied`() {
        val request = BulkDeleteExpensesRequest.newBuilder().build()
        val exception = StatusRuntimeException(io.grpc.Status.PERMISSION_DENIED)

        every { stub.bulkDeleteExpenses(request) } throws exception

        val thrownException = assertThrows<MplSystemException> {
            expenseServiceAdapter.bulkRevokeExpensesNonTransactional(request)
        }

        assertEquals(io.grpc.Status.PERMISSION_DENIED.code.name, thrownException.message)
        verify(exactly = 1) { stub.bulkDeleteExpenses(request) }
    }
}
