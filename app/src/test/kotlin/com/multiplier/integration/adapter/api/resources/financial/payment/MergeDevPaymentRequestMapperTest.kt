package com.multiplier.integration.adapter.api.resources.financial.payment

import com.merge.api.resources.accounting.types.*
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.types.CurrencyCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId

class MergeDevPaymentRequestMapperTest {

    private val mergeDevPaymentRequestMapper = MergeDevPaymentRequestMapper()

    @Test
    fun `test mapPaymentRequestMapper`() {
        val date = LocalDateTime.now()
        val mergeDevPaymentCreateRequest = MergeDevPaymentCreateRequest(
            account = "123",
            contact = "123",
            amount = Amount(
                value = 100.0,
                currencyCode = CurrencyCode.USD
            ),
            createdDate = date,
            appliedInvoice = PaymentAppliedInvoices(
                externalInvoiceId = "123",
                appliedAmount = Amount(
                    value = 100.0,
                    currencyCode = CurrencyCode.USD
                ),
                appliedDate = date,
            )
        )

        val expectedPaymentRequest = PaymentRequest.builder()
            .account(PaymentRequestAccount.of("123"))
            .contact(PaymentRequestContact.of("123"))
            .totalAmount(100.0)
            .transactionDate(
                OffsetDateTime.of(date, ZoneId.systemDefault().rules.getOffset(Instant.now())))
            .currency(PaymentRequestCurrency.of(CurrencyEnum.USD))
            .exchangeRate("1")
            .appliedToLines(
                listOf(
                    PaymentRequestAppliedToLinesItem.of(
                        PaymentLineItemRequest.builder()
                            .appliedAmount("100.0")
                            .relatedObjectId("123")
                            .relatedObjectType("INVOICE")
                            .build()
                    )
                )
            )
            .build()

        val actualPaymentRequest = mergeDevPaymentRequestMapper.mapPaymentRequestMapper(mergeDevPaymentCreateRequest)
        assertThat(actualPaymentRequest).isEqualTo(expectedPaymentRequest)
    }
}