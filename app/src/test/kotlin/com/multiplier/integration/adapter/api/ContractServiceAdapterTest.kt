package com.multiplier.integration.adapter.api

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationServiceGrpc
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc.BulkContractServiceBlockingStub
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractFilters
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractMemberEmailsByContractIdsResponse
import com.multiplier.contract.schema.contract.ContractOuterClass.ListOfIdsResponse
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.contract.schema.onboarding.Onboarding
import com.multiplier.contract.schema.onboarding.OnboardingServiceGrpc.OnboardingServiceBlockingStub
import com.multiplier.contract.schema.performance.PerformanceReviewOuterClass.PerformanceReviewBulkCreateAsApprovedRequest
import com.multiplier.contract.schema.performance.PerformanceReviewOuterClass.PerformanceReviewBulkCreateAsApprovedResponse
import com.multiplier.contract.schema.performance.PerformanceReviewServiceGrpc
import com.multiplier.integration.adapter.model.AdditionalPay
import com.multiplier.integration.adapter.model.PerformanceReviewRequest
import com.multiplier.integration.adapter.model.knit.Frequency
import com.multiplier.integration.utils.mapToGoogleDate
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)

class ContractServiceAdapterTest {
    @MockK
    lateinit var stub: PerformanceReviewServiceGrpc.PerformanceReviewServiceBlockingStub

    @MockK
    lateinit var contractServiceStub: ContractServiceGrpc.ContractServiceBlockingStub

    @MockK
    private lateinit var bulkContractStub: BulkContractServiceBlockingStub

    @MockK
    private lateinit var onboardingStub: OnboardingServiceBlockingStub

    @MockK
    private lateinit var compensationStub: CompensationServiceGrpc.CompensationServiceBlockingStub

    @InjectMockKs
    lateinit var contractServiceClient: DefaultContractServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should create performance review as approved successfully`() {
        val contractId = 1L
        val request = PerformanceReviewRequest(
            contractId = contractId,
            effectiveDate = LocalDate.now(),
            amount = 9000.0,
            additionalPays = listOf(
                AdditionalPay(
                    type = "Bonus1",
                    amount = 1000.0,
                    frequency = Frequency.ANNUAL
                ),
                AdditionalPay(
                    type = "Bonus2",
                    amount = 2000.0,
                    frequency = Frequency.WEEKLY
                ),
                AdditionalPay(
                    type = "Bonus3",
                    amount = 3000.0,
                    frequency = Frequency.QUARTERLY
                ),
                AdditionalPay(
                    type = "Bonus4",
                    amount = 4000.0,
                    frequency = Frequency.SEMI_MONTHLY
                ),
                AdditionalPay(
                    type = "Bonus5",
                    amount = 5000.0,
                    frequency = Frequency.MONTHLY
                ),
                AdditionalPay(
                    type = "Bonus6",
                    amount = 6000.0,
                    frequency = Frequency.HOURLY
                ),
                AdditionalPay(
                    type = "Bonus7",
                    amount = 7000.0,
                    frequency = Frequency.BI_WEEKLY
                ),
                AdditionalPay(
                    type = "Bonus8",
                    amount = 8000.0,
                    frequency = Frequency.DAILY
                ),
                AdditionalPay(
                    type = "Bonus9",
                    amount = 9000.0,
                    frequency = null
                )
            )
        )

        val performanceReviewRequest = PerformanceReviewBulkCreateAsApprovedRequest.newBuilder().addAllInputs(
            listOf(
                PerformanceReviewBulkCreateAsApprovedRequest.Input.newBuilder()
                    .setContractId(contractId)
                    .setEffectiveDate(LocalDate.now().mapToGoogleDate())
                    .setBasePayAmount(9000.0)
                    .addAllAdditionalPays(
                        listOf(PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                            .setLabel("Bonus1")
                            .setAmount(1000.0)
                            .setFrequency(CompensationOuterClass.RateFrequency.ANNUALLY)
                            .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                            .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus2")
                                .setAmount(2000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.WEEKLY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus3")
                                .setAmount(3000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.QUATERLY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus4")
                                .setAmount(4000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.SEMIMONTHLY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus5")
                                .setAmount(5000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.MONTHLY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus6")
                                .setAmount(6000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.HOURLY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus7")
                                .setAmount(7000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.BI_WEEKLY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus8")
                                .setAmount(8000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.DAILY)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build(),
                            PerformanceReviewBulkCreateAsApprovedRequest.Input.AdditionalPay.newBuilder()
                                .setLabel("Bonus9")
                                .setAmount(9000.0)
                                .setFrequency(CompensationOuterClass.RateFrequency.RATE_FREQUENCY_NULL)
                                .setAmountType(CompensationOuterClass.PayAmountType.VARIABLE_AMOUNT)
                                .build()
                            )
                    )
                    .build()
            )
        ).build()

        every { stub.performanceReviewBulkCreateAsApproved(performanceReviewRequest) } returns PerformanceReviewBulkCreateAsApprovedResponse.newBuilder()
            .addSavedPerformanceReviewIds(1L)
            .build()

        val result = contractServiceClient.performanceReviewBulkCreateAsApproved(request)

        Assertions.assertEquals(1L, result[0])
    }

    @Test
    fun `should throw error when creating performance review as approved`() {
        val contractId = 1L
        val request = PerformanceReviewRequest(
            contractId = contractId,
            effectiveDate = null,
            amount = 9000.0,
            additionalPays = null
        )
        val performanceReviewRequest = PerformanceReviewBulkCreateAsApprovedRequest.newBuilder().addAllInputs(
            listOf(
                PerformanceReviewBulkCreateAsApprovedRequest.Input.newBuilder()
                    .setContractId(contractId)
                    .setBasePayAmount(9000.0)
                    .build()
            )
        ).build()
        val expectedException =
            RuntimeException("Effective dates must not be null")
        every { stub.performanceReviewBulkCreateAsApproved(performanceReviewRequest) } throws expectedException

        val actualException =
            assertFailsWith<RuntimeException> {
                contractServiceClient.performanceReviewBulkCreateAsApproved(request)
            }

        Assertions.assertEquals(actualException.message, expectedException.message)
    }

//    @Test
//    fun `should correctly return contract IDs for multiple filters`() {
//        // Setup
//        val contractStatus = ContractStatus.ACTIVE
//        val contractType = ContractOuterClass.ContractType.EMPLOYEE
//        val filters = ContractOuterClass.ContractFilters.newBuilder()
//            .setContractStatus(contractStatus)
//            .addContractTypes(contractType)
//            .build()
//
//        val expected
//        Ids = listOf<Long>(1L, 2L)
//        val request = GetContractMemberIdsRequest.newBuilder().setContractFilters(filters).build()
//        every { contractServiceStub.getContractIdsByContractFilters(request) } returns ListOfIdsResponse.newBuilder()
//            .addAllIds(expectedIds)
//            .build()
//
//        // Action
//        val result = contractServiceClient.getContractIdsByContractFilters(filters)
//
//        // Assert
//        Assertions.assertEquals(expectedIds, result, "Expected contract IDs did not match the actual result")
//    }

//    @Test
//    fun `should return empty list for non-existing filter criteria`() {
//        val nonExistingCountryCode = "XX"
//        val filters = ContractOuterClass.ContractFilters.newBuilder().setCountryCode(nonExistingCountryCode).build()
//        val request = GetContractMemberIdsRequest.newBuilder().setContractFilters(filters).build()
//
//        every { contractServiceStub.getContractIdsByContractFilters(request) } returns ListOfIdsResponse.newBuilder().build()
//
//        val result = contractServiceClient.getContractIdsByContractFilters(filters)
//
//        Assertions.assertTrue(result.isEmpty())
//    }

//    @Test
//    fun `should handle exceptions when fetching contract IDs by filters`() {
//        val filters = ContractOuterClass.ContractFilters.newBuilder()
//            .setContractStatus(ContractStatus.ACTIVE)
//            .build()
//        val request = GetContractMemberIdsRequest.newBuilder().setContractFilters(filters).build()
//
//        every { contractServiceStub.getContractIdsByContractFilters(request) } throws RuntimeException("Service unavailable")
//
//        val exception = assertThrows<RuntimeException> {
//            contractServiceClient.getContractIdsByContractFilters(filters)
//        }
//
//        assertEquals("Service unavailable", exception.message)
//    }

    @Test
    fun getMemberEmailsByContractIds() {
        val grpcResponse =
            ContractMemberEmailsByContractIdsResponse.newBuilder()
                .putAllMemberEmailByContractId(mapOf(1L to "email"))
                .build()
        every { contractServiceStub.getContractMembersEmailByContractIds(any()) } returns grpcResponse

        val expected = mapOf(1L to "email")

        assertEquals(expected, contractServiceClient.getContractMemberEmailsByContractIds(setOf(1L)))
    }

    @Test
    fun getContractIdsByContractFilters() {
        val testContractFilters = ContractFilters.newBuilder()
            .addCompanyIds(1)
            .addContractStatuses(ContractOuterClass.ContractStatus.ACTIVE)
            .addContractStatuses(ContractOuterClass.ContractStatus.OFFBOARDING)
            .setIsTestContract(true)
            .build()

        val grpcResponse = ListOfIdsResponse.newBuilder().addIds(1).build()
        every { contractServiceStub.getContractIdsByContractFilters(any()) } returns grpcResponse

        val expected = listOf(1L)

        assertEquals(expected, contractServiceClient.getContractIdsByContractFilters(testContractFilters, false))

        val expectedException =
            RuntimeException("Exception")
        every { contractServiceStub.getContractIdsByContractFilters(any()) } throws expectedException

        val actualException =
            assertFailsWith<Exception> {
                contractServiceClient.getContractIdsByContractFilters(testContractFilters, false)
            }

        Assertions.assertEquals(actualException.message, expectedException.message)
    }

    @Test
    fun `should find contract by contractId`() {
        val contractId = 1L
        val expectedResponse = ContractOuterClass.Contract.newBuilder().build()
        every { contractServiceStub.getContractByIdAnyStatus(any()) } returns expectedResponse

        val result = contractServiceClient.findContractByContractId(contractId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { contractServiceStub.getContractByIdAnyStatus(any()) }
    }

    @Test
    fun `should find onboarding by contractId and experience`() {
        val contractId = 1L
        val experience = "5 years"
        val expectedResponse = Onboarding.GetOnboardingStatusResponse.newBuilder().build()
        every { onboardingStub.getOnboardingStatus(any()) } returns expectedResponse

        val result = contractServiceClient.findOnboardingByContractIdAndExperience(contractId, experience)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { onboardingStub.getOnboardingStatus(any()) }
    }

    @Test
    fun `should find contract by memberId`() {
        val memberId = 1L
        val expectedResponse = ContractOuterClass.Contract.newBuilder().build()
        every { contractServiceStub.getContractByMemberId(any()) } returns expectedResponse

        val result = contractServiceClient.findContractByMemberId(memberId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { contractServiceStub.getContractByMemberId(any()) }
    }

    @Test
    fun `should find contract by work email`() {
        val expectedResponse = ContractOuterClass.Contract.newBuilder().build()
        every { contractServiceStub.getContractByWorkEmail(any()) } returns expectedResponse

        val result = contractServiceClient.findContractByWorkEmail("workEmail")

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { contractServiceStub.getContractByWorkEmail(any()) }
    }

    @Test
    fun `should update contract employeeId`() {
        val contractId = 1L
        val employeeId = "12345"
        val expectedResponse = ContractOuterClass.Contract.newBuilder().build()
        every { contractServiceStub.updateContractEmployeeId(any()) } returns expectedResponse

        val result = contractServiceClient.updateContractEmployeeId(contractId, employeeId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { contractServiceStub.updateContractEmployeeId(any()) }
    }

    @Test
    fun `should get current compensation by contractId`() {
        val contractId = 1L
        val expectedResponse = CompensationOuterClass.Compensation.newBuilder().build()
        every { compensationStub.getCurrentCompensation(any()) } returns expectedResponse

        val result = contractServiceClient.getCurrentCompensation(contractId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { compensationStub.getCurrentCompensation(any()) }
    }

    @Test
    fun `should invite bulk member contracts`() {
        val inviteBulkRequest = BulkContract.InviteBulkMemberContractsRequest.newBuilder().build()
        val expectedResponse = BulkContract.InviteBulkMemberContractsResponse.newBuilder().build()
        every { bulkContractStub.inviteBulkMemberContracts(any()) } returns expectedResponse

        val result = contractServiceClient.inviteBulkMemberContracts(inviteBulkRequest)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { bulkContractStub.inviteBulkMemberContracts(any()) }
    }

}