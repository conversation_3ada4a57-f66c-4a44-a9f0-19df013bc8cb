package com.multiplier.integration.adapter.api

import com.multiplier.bulkupload.schema.bulkuploadjob.BulkUploadJobServiceGrpc
import com.multiplier.bulkupload.schema.bulkuploadjob.GetJobRequest
import com.multiplier.bulkupload.schema.bulkuploadjob.GetJobResponse
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkUploadServiceClientTest {

    @MockK
    private lateinit var stub: BulkUploadJobServiceGrpc.BulkUploadJobServiceBlockingStub

    @InjectMockKs
    private lateinit var bulkUploadServiceClient: BulkUploadServiceClient

    @Nested
    inner class GetJob {

        @Test
        fun `getJob should return job detail when successful`() {
            // Arrange
            val jobId = 123L
            val requestSlot = slot<GetJobRequest>()

            // Create a mock response with validation summary
            val response = mockGetJobResponse()

            every { stub.getJob(capture(requestSlot)) } returns response

            // Act
            val result = bulkUploadServiceClient.getJob(jobId)

            // Assert
            assertThat(requestSlot.captured.jobId).isEqualTo(jobId)
            assertThat(result.validationSummary.validationSummaryChunks).hasSize(1)

            val resultChunk = result.validationSummary.validationSummaryChunks[0]
            assertThat(resultChunk.name).isEqualTo("Test Chunk")
            assertThat(resultChunk.identifiedRowCount).isEqualTo(10)
            assertThat(resultChunk.failedRowCount).isEqualTo(2)
        }

        @Test
        fun `getJob should throw exception when grpc call fails`() {
            // Arrange
            val jobId = 123L
            val exception = RuntimeException("GRPC call failed")

            every { stub.getJob(any()) } throws exception

            // Act & Assert
            assertThatThrownBy { bulkUploadServiceClient.getJob(jobId) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("GRPC call failed")
        }

        // Helper method to create mock response for getJob
        private fun mockGetJobResponse(): GetJobResponse {
            val validationSummaryChunk = com.multiplier.bulkupload.schema.bulkuploadjob.ValidationSummaryChunk.newBuilder()
                .setName("Test Chunk")
                .setIdentifiedRowCount(10)
                .setFailedRowCount(2)
                .build()

            val validationSummary = com.multiplier.bulkupload.schema.bulkuploadjob.ValidationSummary.newBuilder()
                .addValidationSummaryChunks(validationSummaryChunk)
                .build()

            return GetJobResponse.newBuilder()
                .setValidationSummary(validationSummary)
                .build()
        }
    }
}
