package com.multiplier.integration.adapter.api

import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import com.multiplier.company.schema.grpc.*
import com.multiplier.company.schema.grpc.CompanyOuterClass.Address
import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyStatus
import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyUsers
import com.multiplier.company.schema.grpc.CompanyOuterClass.GetLegalEntitiesResponse
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.grpc.Status
import io.grpc.StatusRuntimeException
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class CompanyServiceAdapterTest {
    @MockK
    lateinit var stub: CompanyServiceGrpc.CompanyServiceBlockingStub

    @InjectMockKs
    lateinit var companyServiceClient: NewCompanyServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should get company details successfully`() {
        val companyId = 1L
        val request = CompanyOuterClass.GetCompanyRequest.newBuilder()
            .setId(companyId)
            .build()
        val company = CompanyOuterClass.Company.newBuilder()
            .setId(1L)
            .setStatus(CompanyStatus.newBuilder().setCompanyStatusEnum(CompanyStatus.CompanyStatusEnum.ACTIVE).build())
            .setDisplayName("Test Company")
            .build()

        every { stub.getCompany(request) } returns company

        val result = companyServiceClient.getCompanyById(companyId)

        assertEquals(company.displayName, result.displayName)
        assertEquals(company.status, result.status)
    }

    @Test
    fun `should get company admins successfully`() {
        val companyId = 1L
        val request = CompanyOuterClass.GetCompanyRequest.newBuilder()
            .setId(companyId)
            .build()
        val companyAdmins = CompanyUsers.newBuilder()
            .addAllUsers(
                listOf(CompanyOuterClass.CompanyUser.newBuilder()
                    .setCompanyId(companyId)
                    .setIsAdmin(true)
                    .setFirstName("Test")
                    .setLastName("Test")
                    .build()
                )
            )
            .build()

        every { stub.getCompanyAdmins(request) } returns companyAdmins

        val result = companyServiceClient.getCompanyAdmins(companyId)

        assertEquals(companyAdmins.usersList[0].firstName, result.usersList[0].firstName)
        assertEquals(companyAdmins.usersList[0].lastName, result.usersList[0].lastName)
        assertEquals(true, result.usersList[0].isAdmin)
    }

    @Test
    fun `should throw exception company not found when get company by id`() {
        val companyId = 1L
        val request = CompanyOuterClass.GetCompanyRequest.newBuilder()
            .setId(companyId)
            .build()

        val expectedException =
            StatusRuntimeException(Status.INTERNAL.withDescription("Company not found for member id $companyId"))

        every { stub.getCompany(request) } throws expectedException

        val actualException =
            assertFailsWith<IntegrationDownstreamException> {
                companyServiceClient.getCompanyById(companyId)
            }

        assertEquals(actualException.message, "INTERNAL: Company not found for member id $companyId")
    }

    @Test
    fun `should get company legal entities successfully`() {
        val companyId = 1L
        val request = CompanyOuterClass.GetLegalEntitiesRequest.newBuilder()
            .addAllCompanyIds(listOf(companyId))
            .build()
        val legalEntities = GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(LegalEntity.newBuilder()
                    .setCompanyId(companyId)
                    .setAddress(Address.newBuilder()
                        .setCountry("VN")
                        .build())
                    .build()
                )
            )
            .build()

        every { stub.getLegalEntities(request) } returns legalEntities

        val result = companyServiceClient.getLegalEntities(companyId)

        assertEquals(result[0].companyId, companyId)
        assertEquals(result[0].address.country,  "VN")
    }
}