package com.multiplier.integration.adapter

import com.multiplier.common.exception.MplSystemException
import com.multiplier.integration.repository.SFTPAccessRequestRepository
import com.multiplier.integration.repository.model.BulkUploadModule
import com.multiplier.integration.repository.model.JpaSFTPAccessRequest
import com.multiplier.integration.repository.model.SftpAccessRequestStatus
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.rest.model.SFTPWebhookRequest
import com.multiplier.integration.service.BulkModule
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class SFTPWebhookAdapterTest {

    @MockK
    private lateinit var sftpAccessRequestRepository: SFTPAccessRequestRepository

    @InjectMockKs
    private lateinit var sftpWebhookAdapter: SFTPWebhookAdapter

    @Nested
    inner class ToIntegrationInput {

        @Test
        fun `should convert SFTPWebhookRequest to IntegrationInput when matching SFTP access request exists`() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val mainSftpDirectory = "/sftp/company123/entity456" // This should match the directory after removing /upload
            val fileURI = "$mainSftpDirectory/upload/test.xlsx"

            val request = SFTPWebhookRequest(
                fileURI = fileURI,
                bucket = "sftp-bucket",
                uploadedUser = "<EMAIL>"
            )

            val sftpAccessRequest = JpaSFTPAccessRequest(
                id = 1L,
                companyId = companyId,
                entityId = entityId,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.APPROVED,
                mainSFTPDirectory = mainSftpDirectory
            )

            every { sftpAccessRequestRepository.findByStatusAndMainSFTPDirectory(SftpAccessRequestStatus.APPROVED, any()) } returns sftpAccessRequest

            // When
            val result = sftpWebhookAdapter.toIntegrationInput(request)

            // Then
            assertThat(result.type).isEqualTo(URIType.SFTP)
            assertThat(result.uri).isEqualTo(fileURI)
            assertThat(result.companyId).isEqualTo(companyId)
            assertThat(result.entityId).isEqualTo(entityId)
            assertThat(result.module).isEqualTo(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA)
        }

        @Test
        fun `should throw exception when no matching SFTP access request exists`() {
            // Given
            val fileURI = "/sftp/unknown/path/test.xlsx"

            val request = SFTPWebhookRequest(
                fileURI = fileURI,
                bucket = "sftp-bucket",
                uploadedUser = "<EMAIL>"
            )

            every { sftpAccessRequestRepository.findByStatusAndMainSFTPDirectory(SftpAccessRequestStatus.APPROVED, any()) } returns null

            // When/Then
            assertThatThrownBy { sftpWebhookAdapter.toIntegrationInput(request) }
                .isInstanceOf(MplSystemException::class.java)
                .hasMessageContaining("No approved SFTP access request found for directory")
        }

        @Test
        fun `should throw exception when only non-approved SFTP access requests exist`() {
            // Given
            val mainSftpDirectory = "/sftp/company123/entity456" // This should match the directory after removing /upload
            val fileURI = "$mainSftpDirectory/upload/test.xlsx"

            val request = SFTPWebhookRequest(
                fileURI = fileURI,
                bucket = "sftp-bucket",
                uploadedUser = "<EMAIL>"
            )

            every { sftpAccessRequestRepository.findByStatusAndMainSFTPDirectory(SftpAccessRequestStatus.APPROVED, any()) } returns null

            // When/Then
            assertThatThrownBy { sftpWebhookAdapter.toIntegrationInput(request) }
                .isInstanceOf(MplSystemException::class.java)
                .hasMessageContaining("No approved SFTP access request found for directory")
        }

        @Test
        fun `should match the most specific directory path when multiple matches exist`() {
            // Given
            val companyId = 123L
            val entityId = 456L
            val mainSftpDirectory = "/sftp/company123/entity456" // This should match the directory after removing /upload

            val fileURI = "$mainSftpDirectory/upload/test.xlsx"

            val request = SFTPWebhookRequest(
                fileURI = fileURI,
                bucket = "sftp-bucket",
                uploadedUser = "<EMAIL>"
            )

            val sftpAccessRequest = JpaSFTPAccessRequest(
                id = 2L,
                companyId = companyId,
                entityId = entityId,
                bulkModule = BulkUploadModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA,
                status = SftpAccessRequestStatus.APPROVED,
                mainSFTPDirectory = mainSftpDirectory
            )

            // In a real scenario with exact matching, we would only have one match
            // We'll return the second request (more specific) for this test
            every { sftpAccessRequestRepository.findByStatusAndMainSFTPDirectory(SftpAccessRequestStatus.APPROVED, any()) } returns sftpAccessRequest

            // When
            val result = sftpWebhookAdapter.toIntegrationInput(request)

            // Then
            assertThat(result.type).isEqualTo(URIType.SFTP)
            assertThat(result.uri).isEqualTo(fileURI)
            assertThat(result.companyId).isEqualTo(companyId)
            assertThat(result.entityId).isEqualTo(entityId)
            assertThat(result.module).isEqualTo(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA)
        }
    }
}
