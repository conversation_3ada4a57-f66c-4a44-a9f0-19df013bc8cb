package com.multiplier.integration.adapter.api

import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.CurrencyConversionDetails
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import com.multiplier.payse.schema.common.AccountType
import com.multiplier.payse.schema.common.PaymentDirection
import com.multiplier.payse.schema.common.PaymentPartner
import com.multiplier.payse.schema.common.TransferType
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPartner
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementsRequest
import com.multiplier.payse.schema.grpc.IntegrationRequirements.IntegrationPaymentAccountRequirementsServiceGrpc
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.any
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class PaymentServiceAdapterTest {
    @MockK
    lateinit var stub: IntegrationPaymentAccountRequirementsServiceGrpc.IntegrationPaymentAccountRequirementsServiceBlockingStub

    @InjectMockKs
    lateinit var paymentServiceAdapter: DefaultPaymentServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should run successfully`() {
        every { stub.getIntegrationPaymentAccountRequirements(any()) } returns mockk()

        val request = IntegrationPaymentAccountRequirementsRequest.newBuilder()
            .setAccountType(AccountType.PERSONAL)
            .setTransferType(TransferType.FIAT)
            .setSourceCurrency("USD")
            .setTargetCurrency("USD")
            .setCountryCode("USA")
            .setPaymentDirection(PaymentDirection.PAY_OUT)
            .addPaymentPartners(PaymentPartner.HSBC)
            .setIntegrationPartner(IntegrationPartner.KNIT)
            .build()
        paymentServiceAdapter.getIntegrationPaymentAccountRequirements(request)

        verify(exactly = 1) { stub.getIntegrationPaymentAccountRequirements(any()) }
    }
}