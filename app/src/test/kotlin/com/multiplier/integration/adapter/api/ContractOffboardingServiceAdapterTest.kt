package com.multiplier.integration.adapter.api

import com.multiplier.contract.offboarding.schema.ContractOffboardingServiceGrpc
import com.multiplier.contract.offboarding.schema.ContractOffboardingStatus
import com.multiplier.contract.offboarding.schema.InitialiseResignationOffboardingRequest
import com.multiplier.contract.offboarding.schema.RescheduleOffboardingRequest
import com.multiplier.contract.offboarding.schema.VerifyAndCompleteOffboardingRequest
import com.multiplier.integration.adapter.model.ContractOffBoardingRequest
import com.multiplier.integration.adapter.model.RescheduleContractOffBoardingRequest
import com.multiplier.integration.mock.getMockContractOffboarding
import com.multiplier.integration.mock.lwd
import com.multiplier.integration.mock.today
import com.multiplier.integration.utils.toGrpcTimestamp
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)

class ContractOffboardingServiceAdapterTest {
    @MockK
    lateinit var stub: ContractOffboardingServiceGrpc.ContractOffboardingServiceBlockingStub

    @InjectMockKs
    lateinit var contractOffboardingServiceClient: ContractOffBoardingServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should initialise offboarding successfully`() {
        val contractId = 1L
        val request = InitialiseResignationOffboardingRequest.newBuilder()
            .setContractId(contractId)
            .setLastWorkingDay(lwd.toGrpcTimestamp())
            .setIsSkippedValidateLastWorkingDay(true)
            .build()
        val contractOffboarding = getMockContractOffboarding(contractId, lwd)

        every { stub.initialiseResignationOffboarding(request) } returns contractOffboarding

        val result = contractOffboardingServiceClient.initialiseResignationOffboarding(ContractOffBoardingRequest(
            contractId = contractId,
            lastWorkingDay = lwd.toString(),
            terminationReason = "Test"
        ))

        Assertions.assertEquals(contractOffboarding.contractId, result.contractId)
        Assertions.assertEquals(contractOffboarding.lastWorkingDay, result.lastWorkingDay)
        Assertions.assertEquals(contractOffboarding.contractOffboardingType, result.contractOffboardingType)
    }

    @Test
    fun `should reschedule offboarding successfully`() {
        val contractOffboardingId = 1L
        val contractId = 2L
        val request = RescheduleOffboardingRequest.newBuilder()
            .setContractOffboardingId(contractOffboardingId)
            .setRevisedDate(today.toGrpcTimestamp())
            .setIsSkippedValidateLastWorkingDay(true)
            .build()
        val contractOffboarding = getMockContractOffboarding(contractId, lwd)

        every { stub.rescheduleContractOffboarding(request) } returns contractOffboarding

        val result = contractOffboardingServiceClient.rescheduleOffboarding(
            RescheduleContractOffBoardingRequest(
                contractOffboardingId = contractOffboardingId,
                lastWorkingDay = today.toString(),
            )
        )

        Assertions.assertEquals(contractOffboarding.contractId, result.contractId)
        Assertions.assertEquals(contractOffboarding.lastWorkingDay, result.lastWorkingDay)
    }

    @Test
    fun `should throw exception invalid status when reschedule offboarding`() {
        val contractOffboardingId = 1L
        val request = RescheduleOffboardingRequest.newBuilder()
            .setContractOffboardingId(contractOffboardingId)
            .setRevisedDate(today.toGrpcTimestamp())
            .setIsSkippedValidateLastWorkingDay(true)
            .build()

        val expectedException =
            RuntimeException("Offboarding reschedule is not allowed. Off board status: ${ContractOffboardingStatus.COMPLETED}")

        every { stub.rescheduleContractOffboarding(request) } throws expectedException

        val actualException =
            assertFailsWith<RuntimeException> {
                contractOffboardingServiceClient.rescheduleOffboarding(RescheduleContractOffBoardingRequest(
                    contractOffboardingId = contractOffboardingId,
                    lastWorkingDay = today.toString(),
                ))
            }

        Assertions.assertEquals(actualException.message, expectedException.message)
    }

    @Test
    fun `should verify and complete offboarding successfully`() {
        val contractOffboardingId = 1L
        val contractId = 2L
        val request = VerifyAndCompleteOffboardingRequest.newBuilder()
            .setContractOffboardingId(contractOffboardingId)
            .build()
        val contractOffboarding = getMockContractOffboarding(contractId, lwd, status = ContractOffboardingStatus.COMPLETED)

        every { stub.verifyAndCompleteContractOffboarding(request) } returns contractOffboarding

        val result = contractOffboardingServiceClient.verifyAndCompleteOffboarding(contractOffboardingId)

        Assertions.assertEquals(contractOffboarding.contractId, result.contractId)
        Assertions.assertEquals(ContractOffboardingStatus.COMPLETED, result.contractOffBoardingStatus)
    }

}