package com.multiplier.integration.adapter.api

import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.CurrencyConversionDetails
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class BenefitServiceAdapterTest {
    @MockK
    lateinit var stub: BenefitServiceGrpc.BenefitServiceBlockingStub

    @InjectMockKs
    lateinit var benefitServiceAdapter: DefaultBenefitServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should run successfully`() {
        every { stub.getContractBenefitDocumentsByContractId(any()) } returns mockk()

        benefitServiceAdapter.getContractBenefitDocumentsByContractId(1)

        verify(exactly = 1) { stub.getContractBenefitDocumentsByContractId(any()) }
    }
}