package com.multiplier.integration.adapter.api

import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.MemberServiceGrpc
import com.multiplier.member.schema.UpdateMemberFieldsRequest
import com.multiplier.member.schema.UpsertBankDetailsRequest
import com.multiplier.member.schema.UpsertBankDetailsResponse
import com.multiplier.member.schema.UpsertMemberAddressDetailRequest
import io.grpc.Status
import io.grpc.StatusRuntimeException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class MemberServiceAdapterTest {
    @MockK
    lateinit var stub: MemberServiceGrpc.MemberServiceBlockingStub

    @MockK
    private lateinit var bulkService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    @InjectMockKs
    lateinit var memberServiceClient: DefaultMemberServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should update member details successfully`() {
        val memberId = 1L
        val request = UpdateMemberFieldsRequest.newBuilder()
            .setMemberId(memberId)
            .setFirstName("Test")
            .setLastName("Test")
            .setMaritalStatus(Member.MaritalStatus.MARRIED)
            .build()
        val member = Member.newBuilder()
            .setId(memberId)
            .setFirstName("Test")
            .setLastName("Test")
            .setGender(Gender.FEMALE)
            .setAge(25F)
            .setMartialStatus(Member.MaritalStatus.MARRIED)
            .build()

        every { stub.updateMemberFields(request) } returns member

        val result = memberServiceClient.updateMemberFields(request)

        assertEquals(member.firstName, result.firstName)
        assertEquals(member.lastName, result.lastName)
        assertEquals(member.martialStatus, result.martialStatus)
    }

    @Test
    fun `should throw exception member not found when update member detail`() {
        val memberId = 1L
        val request = UpdateMemberFieldsRequest.newBuilder()
            .setMemberId(memberId)
            .setFirstName("Test")
            .build()
        val expectedException =
            StatusRuntimeException(Status.INTERNAL.withDescription("Member not found for member id $memberId"))

        every { stub.updateMemberFields(request) } throws expectedException

        val actualException =
            assertFailsWith<IntegrationDownstreamException> {
                memberServiceClient.updateMemberFields(request)
            }

        assertEquals(actualException.message, "INTERNAL: Member not found for member id $memberId")
    }

    @Test
    fun `should find member by memberId`() {
        val memberId = 1L
        val expectedResponse = Member.newBuilder().build()
        every { stub.getMemberWithSubfields(any()) } returns expectedResponse

        val result = memberServiceClient.findMemberByMemberId(memberId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { stub.getMemberWithSubfields(any()) }
    }

    @Test
    fun `should find member by email address`() {
        val emailAddress = "<EMAIL>"
        val expectedResponse = Member.newBuilder().build()
        every { stub.getMemberByEmailAddress(any()) } returns expectedResponse

        val result = memberServiceClient.findMemberByEmailAddress(emailAddress)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { stub.getMemberByEmailAddress(any()) }
    }

    @Test
    fun `should get member by memberId`() {
        val memberId = 1L
        val expectedResponse = Member.newBuilder().build()
        every { stub.getMember(any()) } returns expectedResponse

        val result = memberServiceClient.getMember(memberId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { stub.getMember(any()) }
    }

    @Test
    fun `should upsert bank details`() {
        val upsertBankDetailsRequest = UpsertBankDetailsRequest.newBuilder().build()
        val expectedResponse = UpsertBankDetailsResponse.newBuilder().build()
        every { bulkService.upsertBankDetails(any()) } returns expectedResponse

        val result = memberServiceClient.upsertBankDetails(upsertBankDetailsRequest)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { bulkService.upsertBankDetails(any()) }
    }

    @Test
    fun `should upsert address details`() {
        val upsertAddressDetailsRequest = UpsertMemberAddressDetailRequest.newBuilder().build()
        val expectedResponse = Member.newBuilder().build()
        every { stub.upsertMemberAddressDetail(any()) } returns expectedResponse

        val result = memberServiceClient.upsertMemberAddressDetails(upsertAddressDetailsRequest)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { stub.upsertMemberAddressDetail(any()) }
    }
}