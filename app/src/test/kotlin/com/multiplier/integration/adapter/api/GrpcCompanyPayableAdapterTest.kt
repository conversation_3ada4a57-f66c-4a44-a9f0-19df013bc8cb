package com.multiplier.integration.adapter.api

import com.google.protobuf.Timestamp
import com.multiplier.grpc.common.currency.v2.Currency.CurrencyCode
import com.multiplier.integration.accounting.domain.model.CompanyPayableStatus
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.multiplier.integration.accounting.domain.model.Invoice
import com.multiplier.payable.common.schema.GrpcAmount
import com.multiplier.payable.common.schema.GrpcDate
import com.multiplier.payable.common.schema.GrpcTax
import com.multiplier.payable.grpc.schema.*
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNote
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteLineItem
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteStatus
import com.multiplier.payable.grpc.schema.lineitem.GrpcLineItem
import com.multiplier.payable.grpc.schema.lineitem.GrpcLineItemType
import io.grpc.StatusRuntimeException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate
import java.time.ZoneOffset

@ExtendWith(SpringExtension::class)
class GrpcCompanyPayableAdapterTest {
    @MockK
    private lateinit var payableServiceBlockingStub: PayableServiceGrpc.PayableServiceBlockingStub

    @InjectMockKs
    private lateinit var grpcCompanyPayableAdapter: GrpcCompanyPayableAdapter

    private val companyPayableId = 123L

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getCompanyPayable should return company payable for invoice when found`() {
        val grpcCompanyPayable = getGrpcCompanyPayable()

        val response =
            GetCompanyPayableByIdResponse
                .newBuilder()
                .setCompanyPayable(grpcCompanyPayable)
                .build()

        every { payableServiceBlockingStub.getCompanyPayableById(any()) } returns response

        val companyPayable = grpcCompanyPayableAdapter.getCompanyPayable(companyPayableId)
        val localDate = LocalDate.of(2021, 1, 1)
        assertNotNull(companyPayable)
        assertEquals(1L, companyPayable.companyPayableId)
        assertEquals(CompanyPayableStatus.AUTHORIZED, companyPayable.status)
        assertEquals(FinancialTransactionType.INVOICE, companyPayable.type)
        assertTrue(companyPayable is Invoice)
        val invoice = companyPayable as Invoice
        assertTrue(invoice.lineItems.isNotEmpty())
        assertEquals(1L, invoice.id)
        assertEquals(1L, invoice.companyId)
        assertEquals(localDate, invoice.dueDate)
        assertEquals(0.0, invoice.amountPaid.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, invoice.amountPaid.currencyCode)
        assertEquals(0.0, invoice.amountDue.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, invoice.amountDue.currencyCode)
        assertEquals(0.0, invoice.totalAmount.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, invoice.totalAmount.currencyCode)
        assertEquals("Test Reference", invoice.reference)
        assertEquals("Test Invoice Id", invoice.multiplierExternalInvoiceId.id)
        assertEquals("Test Invoice", invoice.multiplierExternalInvoiceNumber.id)
        val lineItem = invoice.lineItems[0]
        assertEquals("Test Line Item", lineItem.description)
        assertEquals(1.0, lineItem.quantity)
        assertEquals(0.0, lineItem.unitPrice.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, lineItem.unitPrice.currencyCode)
        assertEquals(0.0, lineItem.tax.amount.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, lineItem.tax.amount.currencyCode)
        assertEquals("NO_TAX", lineItem.tax.taxType)
        assertEquals("0.0", lineItem.tax.taxRate)
        assertEquals(1L, lineItem.contractId)
        assertEquals("Test Member", lineItem.memberName)
        assertEquals(0.0, lineItem.amountInBaseCurrency.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, lineItem.amountInBaseCurrency.currencyCode)
        assertEquals("Test Country", lineItem.countryName)
        assertEquals(0.0, lineItem.grossAmount.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, lineItem.grossAmount.currencyCode)
        assertEquals(localDate, lineItem.startInvoiceCycleDate)
        assertEquals(localDate, lineItem.endInvoiceCycleDate)
    }

    @Test
    fun `getCompanyPayable should return company payable for credit note when found`() {
        val grpcCompanyPayable = getGrpcCreditNotePayable()

        val response =
            GetCompanyPayableByIdResponse
                .newBuilder()
                .setCompanyPayable(grpcCompanyPayable)
                .build()

        every { payableServiceBlockingStub.getCompanyPayableById(any()) } returns response

        val companyPayable = grpcCompanyPayableAdapter.getCompanyPayable(companyPayableId)
        val localDate = LocalDate.of(2021, 1, 1)
        assertNotNull(companyPayable)
        assertEquals(1L, companyPayable.companyPayableId)
        assertEquals(CompanyPayableStatus.AUTHORIZED, companyPayable.status)
        assertEquals(FinancialTransactionType.CREDIT_NOTE, companyPayable.type)
        assertTrue(companyPayable is CreditNote)
        val creditNote = companyPayable as CreditNote
        assertTrue(creditNote.lineItems.isNotEmpty())
        assertEquals(1L, creditNote.id)
        assertEquals(1L, creditNote.companyId)
        assertEquals(localDate, creditNote.createdDate)
        val lineItem = creditNote.lineItems[0]
        assertEquals("Test Line Item", lineItem.description)
        assertEquals(1.0, lineItem.quantity)
        assertEquals("NO_TAX", lineItem.tax.taxType)
        assertEquals("0.0", lineItem.tax.taxRate)
        assertEquals(1L, lineItem.contractId)
        assertEquals("Test Member", lineItem.memberName)
        assertEquals(0.0, lineItem.amountInBaseCurrency.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, lineItem.amountInBaseCurrency.currencyCode)
        assertEquals("Test Country", lineItem.countryName)
        assertEquals(0.0, lineItem.grossAmount.value)
        assertEquals(com.multiplier.integration.types.CurrencyCode.USD, lineItem.grossAmount.currencyCode)
        assertEquals(localDate, lineItem.startInvoiceCycleDate)
        assertEquals(localDate, lineItem.endInvoiceCycleDate)
    }

    @Test
    fun `getCompanyPayable should throw RuntimeException when grpc call fails`() {
        every { payableServiceBlockingStub.getCompanyPayableById(any()) } throws StatusRuntimeException(io.grpc.Status.INTERNAL)

        val exception =
            assertThrows(RuntimeException::class.java) {
                grpcCompanyPayableAdapter.getCompanyPayable(companyPayableId)
            }

        assertEquals("Failed to get company payable with id $companyPayableId", exception.message)
    }

    @Test
    fun `getCompanyPayable should use the created on date if grpc invoice createdDate not present`() {
        var grpcCompanyPayable = getGrpcCompanyPayable()
        grpcCompanyPayable =
            grpcCompanyPayable
                .toBuilder()
                .setGrpcInvoice(
                    grpcCompanyPayable.grpcInvoice
                        .toBuilder()
                        .clearCreatedDate()
                        .build(),
                ).build()

        val response =
            GetCompanyPayableByIdResponse
                .newBuilder()
                .setCompanyPayable(grpcCompanyPayable)
                .build()

        every { payableServiceBlockingStub.getCompanyPayableById(any()) } returns response

        val companyPayable = grpcCompanyPayableAdapter.getCompanyPayable(companyPayableId)
        val localDate = LocalDate.of(2021, 1, 1)
        assertEquals(localDate, companyPayable.createdDate)
    }

    @Test
    fun `getCompanyPayable should use the created on date if grpc credit note createdDate not present`() {
        var grpcCompanyPayable = getGrpcCreditNotePayable()
        grpcCompanyPayable =
            grpcCompanyPayable
                .toBuilder()
                .setCreditNote(
                    grpcCompanyPayable.creditNote
                        .toBuilder()
                        .clearCreatedDate()
                        .build(),
                ).build()

        val response =
            GetCompanyPayableByIdResponse
                .newBuilder()
                .setCompanyPayable(grpcCompanyPayable)
                .build()

        every { payableServiceBlockingStub.getCompanyPayableById(any()) } returns response

        val companyPayable = grpcCompanyPayableAdapter.getCompanyPayable(companyPayableId)
        val localDate = LocalDate.of(2021, 1, 1)
        assertEquals(localDate, companyPayable.createdDate)
    }

    @Test
    fun `getCompanyPayableIdsFromInvoiceIds should return payable ids`()  {
        val response =
            GetInvoicesByIdsResponse
                .newBuilder()
                .addAllGrpcInvoiceById(
                    listOf(
                        GrpcInvoiceById
                            .newBuilder()
                            .setId(1L)
                            .setGrpcInvoice(GrpcInvoice.newBuilder().setCompanyPayableId(1L).build())
                            .build(),
                    ),
                )
                .build()

        every { payableServiceBlockingStub.getInvoicesByIds(any()) } returns response
        val companyPayableIds = grpcCompanyPayableAdapter.getCompanyPayableIdsFromInvoiceIds(listOf(1L));
        assertEquals(companyPayableIds.size,1)
        assertEquals(companyPayableIds.first(),1L)
    }

    private fun getGrpcCompanyPayable() =
        GrpcCompanyPayable
            .newBuilder()
            .setId(1L)
            .setCompanyId(1L)
            .setCurrency("USD")
            .setCreatedOn(createTimestamp())
            .setStatus(GrpcPayableStatus.AUTHORIZED)
            .setGrpcInvoice(getGrpcInvoice())
            .build()

    private fun getGrpcCreditNotePayable() =
        GrpcCompanyPayable
            .newBuilder()
            .setId(1L)
            .setCompanyId(1L)
            .setCurrency("USD")
            .setCreatedOn(createTimestamp())
            .setStatus(GrpcPayableStatus.AUTHORIZED)
            .setCreditNote(getGrpcCreditNote())
            .build()

    private fun getGrpcCreditNote() =
        GrpcCreditNote
            .newBuilder()
            .setId(1L)
            .setStatus(GrpcCreditNoteStatus.CREDIT_NOTE_STATUS_FULLY_APPLIED)
            .setCompanyId(1L)
            .setAmountApplied(0.0)
            .addAllAppliedInvoices(listOf(1L))
            .addItems(getGrpcCreditNoteLineItem())
            .setCurrencyCode("USD")
            .setCreatedDate(
                GrpcDate
                    .newBuilder()
                    .setDay(1)
                    .setMonth(1)
                    .setYear(2021)
                    .build(),
            ).build()

    private fun getGrpcInvoice() =
        GrpcInvoice
            .newBuilder()
            .setId(1L)
            .setInvoiceNo("Test Invoice")
            .setInvoiceId("Test Invoice Id")
            .addLineItems(getGrpcLineItem())
            .setDueDate(createTimestamp())
            .setAmountPaid(0.0)
            .setAmountDue(0.0)
            .setTotalAmount(0.0)
            .setCreatedDate(
                GrpcDate
                    .newBuilder()
                    .setDay(1)
                    .setMonth(1)
                    .setYear(2021)
                    .build(),
            ).setReference("Test Reference")
            .build()

    private fun getGrpcLineItem() =
        GrpcLineItem
            .newBuilder()
            .setDescription("Test Line Item")
            .setItemType(GrpcLineItemType.EOR_SALARY_DISBURSEMENT)
            .setQuantity(1.0)
            .setUnitPrice(GrpcAmount.newBuilder().setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE).build())
            .setTax(
                GrpcTax
                    .newBuilder()
                    .setTaxAmount(
                        GrpcAmount
                            .newBuilder()
                            .setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE)
                            .setValue(0.0)
                            .build(),
                    ).setTaxRate("0.0")
                    .setTaxType("NO_TAX")
                    .build(),
            ).setContractId(1L)
            .setMemberName("Test Member")
            .setAmountInBaseCurrency(
                GrpcAmount
                    .newBuilder()
                    .setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE)
                    .setValue(0.0)
                    .build(),
            ).setCountryName("Test Country")
            .setGrossAmount(
                GrpcAmount
                    .newBuilder()
                    .setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE)
                    .setValue(0.0)
                    .build(),
            ).setStartInvoiceCycleDate(
                GrpcDate
                    .newBuilder()
                    .setDay(1)
                    .setMonth(1)
                    .setYear(2021)
                    .build(),
            ).setEndInvoiceCycleDate(
                GrpcDate
                    .newBuilder()
                    .setDay(1)
                    .setMonth(1)
                    .setYear(2021)
                    .build(),
            ).build()

    private fun getGrpcCreditNoteLineItem() =
        GrpcCreditNoteLineItem
            .newBuilder()
            .setDescription("Test Line Item")
            .setLineItemType(GrpcLineItemType.EOR_SALARY_DISBURSEMENT)
            .setUnitAmount(GrpcAmount.newBuilder().setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE).build())
            .setQuantity(1.0)
            .setTax(
                GrpcTax
                    .newBuilder()
                    .setTaxAmount(
                        GrpcAmount
                            .newBuilder()
                            .setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE)
                            .setValue(0.0)
                            .build(),
                    ).setTaxRate("0.0")
                    .setTaxType("NO_TAX")
                    .build(),
            ).setContractId(1L)
            .setMemberName("Test Member")
            .setAmountInBaseCurrency(
                GrpcAmount
                    .newBuilder()
                    .setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE)
                    .setValue(0.0)
                    .build(),
            ).setCountryName("Test Country")
            .setGrossAmount(
                GrpcAmount
                    .newBuilder()
                    .setCurrencyCodeValue(CurrencyCode.CURRENCY_CODE_USD_VALUE)
                    .setValue(0.0)
                    .build(),
            ).setStartInvoiceCycleDate(
                GrpcDate
                    .newBuilder()
                    .setDay(1)
                    .setMonth(1)
                    .setYear(2021)
                    .build(),
            ).setEndInvoiceCycleDate(
                GrpcDate
                    .newBuilder()
                    .setDay(1)
                    .setMonth(1)
                    .setYear(2021)
                    .build(),
            ).build()

    private fun createTimestamp(): Timestamp {
        val localDate = LocalDate.of(2021, 1, 1)
        val instant = localDate.atStartOfDay().toInstant(ZoneOffset.UTC)
        return Timestamp
            .newBuilder()
            .setSeconds(instant.epochSecond)
            .setNanos(instant.nano)
            .build()
    }
}
