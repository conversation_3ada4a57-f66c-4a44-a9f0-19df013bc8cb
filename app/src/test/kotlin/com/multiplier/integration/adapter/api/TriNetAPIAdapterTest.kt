package com.multiplier.integration.adapter.api

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.resources.trinet.TerminateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeContactRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeNameRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateEmployeeTitleRequest
import com.multiplier.integration.adapter.api.resources.trinet.UpdateSingleContactRequest
import com.multiplier.integration.adapter.model.knit.EmployeeData
import com.multiplier.integration.mock.getMockPlatformEmployeeDataWithTriNetData
import com.multiplier.integration.mockPlatform
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.type.ProviderName
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import kotlinx.coroutines.runBlocking
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Protocol
import okhttp3.Request
import okhttp3.ResponseBody.Companion.toResponseBody
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.util.ReflectionTestUtils
import java.io.IOException
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith
import kotlin.test.assertNotNull

@ExtendWith(SpringExtension::class)
class TriNetAPIAdapterTest {
    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @InjectMockKs
    lateinit var triNetAPIAdapter: DefaultTriNetAPIAdapter

    @MockK
    private lateinit var client: OkHttpClient

    @BeforeEach
    fun setUp() {
        ReflectionTestUtils.setField(triNetAPIAdapter, "baseApiUrl", "http://localhost:8080")
        ReflectionTestUtils.setField(triNetAPIAdapter, "baseApiUrl", "http://localhost:8080")
        DefaultTriNetAPIAdapter.setIgnoreTokenCheck(true)
    }

    @Test
    fun `should get access token successfully`() {
        val mockJsonResp = "{\n" +
                "    \"token_type\": \"BearerToken\",\n" +
                "    \"api_product_list\": \"[API-Multiplier-qen1]\",\n" +
                "    \"access_token\": \"3Hu7JXL7WxcNhycREsLFzuBmaQhf\",\n" +
                "    \"expires_in\": \"3599\"\n" +
                "}"
        val mockRequest = Request.Builder()
            .url("https://apiqe1.trinet.com")
            .build()

        every {
            client.newCall(any())
                .execute()
        } returns okhttp3.Response.Builder()
            .request(mockRequest)
            .protocol(Protocol.HTTP_2)
            .message("")
            .code(200)
            .body(
                mockJsonResp.toResponseBody("application/json".toMediaTypeOrNull()))
            .build()

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.getAccessToken("test", "test")
        }
        assertEquals(response!!.accessToken, "3Hu7JXL7WxcNhycREsLFzuBmaQhf")
    }

    @Test
    fun `should update employee name successfully`() {
        val mockJsonResp = "{\n" +
                "    \"_requestId\": \"a18b643f-957d-4e1e-bbd8-a5bda6590b2a\",\n" +
                "    \"_statusCode\": \"200\",\n" +
                "    \"_statusText\": \"OK\",\n" +
                "    \"_statusMessage\": \"Changes saved successfully \"\n" +
                "}"

        val mockRequest = Request.Builder()
            .url("https://apiqe1.trinet.com")
            .build()

        every {
            client.newCall(any())
                .execute()
        } returns okhttp3.Response.Builder()
            .request(mockRequest)
            .protocol(Protocol.HTTP_2)
            .message("")
            .code(200)
            .body(
                mockJsonResp.toResponseBody("application/json".toMediaTypeOrNull()))
            .build()

        val updateNameRequest = UpdateEmployeeNameRequest(
            effectiveDate = "2024-04-12",
            nameType = "PRF",
            firstName = "Foo",
            lastName = "Bar",
        )

        val companyIntegration = JpaCompanyIntegration(
            id = 1L,
            companyId = 1L,
            externalCompanyId = "1",
            provider = JpaProvider(1L, ProviderName.MERGE_DEV),
            platform = mockPlatform(platformName = "BambooHR"),
            accountToken = "123",
            accountName = "abc",
            enabled = true,
            outgoingSyncEnabled = true,
            incomingSyncEnabled = true,
            lastOutgoingSyncTime = LocalDateTime.now(),
            lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now()
        )

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.updateEmployeeName(companyIntegration, "employee1", 0L, updateNameRequest)
        }
        assertNotNull(response)
    }

    @Test
    fun `should update employee title successfully`() {
        val mockJsonResp = "{\n" +
                "    \"_requestId\": \"a18b643f-957d-4e1e-bbd8-a5bda6590b2a\",\n" +
                "    \"_statusCode\": \"200\",\n" +
                "    \"_statusText\": \"OK\",\n" +
                "    \"_statusMessage\": \"Changes saved successfully \"\n" +
                "}"

        val mockRequest = Request.Builder()
            .url("https://apiqe1.trinet.com")
            .build()

        every {
            client.newCall(any())
                .execute()
        } returns okhttp3.Response.Builder()
            .request(mockRequest)
            .protocol(Protocol.HTTP_2)
            .message("")
            .code(200)
            .body(
                mockJsonResp.toResponseBody("application/json".toMediaTypeOrNull()))
            .build()

        val updateNameRequest = UpdateEmployeeTitleRequest(
            effectiveDate = "2024-04-12",
            reasonId = "XFR",
            businessTitle = "Designer"
        )

        val companyIntegration = JpaCompanyIntegration(
            id = 1L,
            companyId = 1L,
            externalCompanyId = "1",
            provider = JpaProvider(1L, ProviderName.MERGE_DEV),
            platform = mockPlatform(platformName = "BambooHR"),
            accountToken = "123",
            accountName = "abc",
            enabled = true,
            outgoingSyncEnabled = true,
            incomingSyncEnabled = true,
            lastOutgoingSyncTime = LocalDateTime.now(),
            lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now()
        )

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.updateEmployeeTitle(companyIntegration, "employee1", 0L, updateNameRequest)
        }
        assertNotNull(response)
    }

    @Test
    fun `should update employee contact successfully`() {
        val mockJsonResp = "{\n" +
                "    \"_requestId\": \"a18b643f-957d-4e1e-bbd8-a5bda6590b2a\",\n" +
                "    \"_statusCode\": \"200\",\n" +
                "    \"_statusText\": \"OK\",\n" +
                "    \"_statusMessage\": \"Changes saved successfully \"\n" +
                "}"

        val mockRequest = Request.Builder()
            .url("https://apiqe1.trinet.com")
            .build()

        every {
            client.newCall(any())
                .execute()
        } returns okhttp3.Response.Builder()
            .request(mockRequest)
            .protocol(Protocol.HTTP_2)
            .message("")
            .code(200)
            .body(
                mockJsonResp.toResponseBody("application/json".toMediaTypeOrNull()))
            .build()

        val companyIntegration = JpaCompanyIntegration(
            id = 1L,
            companyId = 1L,
            externalCompanyId = "1",
            provider = JpaProvider(1L, ProviderName.MERGE_DEV),
            platform = mockPlatform(platformName = "BambooHR"),
            accountToken = "123",
            accountName = "abc",
            enabled = true,
            outgoingSyncEnabled = true,
            incomingSyncEnabled = true,
            lastOutgoingSyncTime = LocalDateTime.now(),
            lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now()
        )

        val updateEmployeeContactRequest = UpdateEmployeeContactRequest(
            contactList = listOf()
        )

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.updateEmployeeContacts(companyIntegration, "employee1", 0L, updateEmployeeContactRequest)
        }
        assertNotNull(response)
    }

    @Test
    fun `should terminate employee successfully`() {
        val mockJsonResp = "{\n" +
                "    \"_requestId\": \"a18b643f-957d-4e1e-bbd8-a5bda6590b2a\",\n" +
                "    \"_statusCode\": \"200\",\n" +
                "    \"_statusText\": \"OK\",\n" +
                "    \"_statusMessage\": \"Success \"\n" +
                "}"

        val mockRequest = Request.Builder()
            .url("https://apiqe1.trinet.com/v1/termination/test/test123/TAsubmit")
            .build()

        every {
            client.newCall(any())
                .execute()
        } returns okhttp3.Response.Builder()
            .request(mockRequest)
            .protocol(Protocol.HTTP_2)
            .message("")
            .code(200)
            .body(
                mockJsonResp.toResponseBody("application/json".toMediaTypeOrNull()))
            .build()

        val terminateEmployeeRequest = TerminateEmployeeRequest(
            terminationDate = "2024-04-12",
        )

        val companyIntegration = JpaCompanyIntegration(
            id = 1L,
            companyId = 1L,
            provider = JpaProvider(1L, ProviderName.TRINET),
            platform = mockPlatform(platformName = "BambooHR"),
            accountToken = "123",
            accountName = "abc",
            enabled = true,
            outgoingSyncEnabled = true,
            incomingSyncEnabled = true,
            lastOutgoingSyncTime = LocalDateTime.now(),
            lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now(),
            externalCompanyId = "test"
        )

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.terminateEmployee(companyIntegration, "test123", terminateEmployeeRequest)
        }

        assertEquals(true, response.success)
        assertEquals(200, response.responseCode)
    }

    @Test
    fun `should terminate employee failed`() {
        val mockJsonResp = "{\n" +
                "    \"_requestId\": \"a18b643f-957d-4e1e-bbd8-a5bda6590b2a\",\n" +
                "    \"_statusCode\": \"500\",\n" +
                "    \"_statusText\": \"Internal Server Error\",\n" +
                "    \"_statusMessage\": \"Failed \"\n" +
                "}"

        val mockRequest = Request.Builder()
            .url("https://apiqe1.trinet.com/v1/termination/C6X/00072139175/TAsubmit")
            .build()

        every {
            client.newCall(any())
                .execute()
        } returns okhttp3.Response.Builder()
            .request(mockRequest)
            .protocol(Protocol.HTTP_2)
            .message("")
            .code(500)
            .body(
                mockJsonResp.toResponseBody("application/json".toMediaTypeOrNull()))
            .build()

        val terminateEmployeeRequest = TerminateEmployeeRequest(
            terminationDate = "2024-03-12",
        )

        val companyIntegration = JpaCompanyIntegration(
            id = 1L,
            companyId = 1L,
            externalCompanyId = "1",
            provider = JpaProvider(1L, ProviderName.TRINET),
            platform = mockPlatform(platformName = "BambooHR"),
            accountToken = "123",
            accountName = "abc",
            enabled = true,
            outgoingSyncEnabled = true,
            incomingSyncEnabled = true,
            lastOutgoingSyncTime = LocalDateTime.now(),
            lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now()
        )

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.terminateEmployee(companyIntegration, "test123", terminateEmployeeRequest)
        }

        assertEquals(false, response.success)
        assertEquals(500, response.responseCode)
    }

    @Test
    fun `should terminate employee failed for IO Exception`() {
        every {
            client.newCall(any())
                .execute()
        } throws IOException("Test Exception")

        val terminateEmployeeRequest = TerminateEmployeeRequest(
            terminationDate = "2024-03-12",
        )

        val companyIntegration = JpaCompanyIntegration(
            id = 1L,
            companyId = 1L,
            externalCompanyId = "1",
            provider = JpaProvider(1L, ProviderName.TRINET),
            platform = mockPlatform(platformName = "BambooHR"),
            accountToken = "123",
            accountName = "abc",
            enabled = true,
            outgoingSyncEnabled = true,
            incomingSyncEnabled = true,
            lastOutgoingSyncTime = LocalDateTime.now(),
            lastIncomingSyncTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOffTime = LocalDateTime.now(),
            lastOutgoingSyncTimeToggleOnTime = LocalDateTime.now()
        )

        val response = runBlocking {
            triNetAPIAdapter.setDefaultHttpClient(client)
            triNetAPIAdapter.terminateEmployee(companyIntegration, "test123", terminateEmployeeRequest)
        }

        assertEquals(false, response.success)
        assertEquals("Test Exception", response.error?.msg)
    }

    @Test
    fun `updateInternalTriNetEmployeeData successfully`() {
        val mockPlatformEmployeeData = getMockPlatformEmployeeDataWithTriNetData()
        val externalEmployeeId = "Test"
        val objectMapper = ObjectMapper().registerKotlinModule()
        val employeeDataCacheObj = objectMapper.readValue(mockPlatformEmployeeData[0].employeeData, EmployeeData::class.java)

        every { platformEmployeeDataRepository.save(mockPlatformEmployeeData[0]) } returns mockPlatformEmployeeData[0]

        val updateNameRequest = UpdateEmployeeNameRequest(
            effectiveDate = "2024-04-29",
            nameType = "PRF",
            firstName = "Test1",
            lastName = "Test2",
        )
        val updatePhoneContactRequest = UpdateSingleContactRequest(
            effectiveDate = "2024-04-29",
            accessType = "Work",
            media = Constants.UpdateContactMedia.PHONE,
            telephoneNumber = null
        )
        val updateEmailContactRequest = UpdateSingleContactRequest(
            effectiveDate = "2024-04-29",
            accessType = "Work",
            media = Constants.UpdateContactMedia.EMAIL,
            url = null
        )
        val updateContactRequest = UpdateEmployeeContactRequest(contactList = listOf(updatePhoneContactRequest, updateEmailContactRequest))
        val updateEmployeeTitleRequest = UpdateEmployeeTitleRequest(
            effectiveDate = "2024-04-29",
            reasonId = "XFR",
            businessTitle = "test"
        )
        val newTriNetData = employeeDataCacheObj.employeeDetailData?.copy(
            firstName = updateNameRequest.firstName,
            lastName = updateNameRequest.lastName,
            emailAddress = updateContactRequest.contactList[1].url,
            contactNumber = updateContactRequest.contactList[0].telephoneNumber,
            designation = updateEmployeeTitleRequest.businessTitle
        )

        triNetAPIAdapter.updateInternalTriNetEmployeeData(mockPlatformEmployeeData[0], newTriNetData, externalEmployeeId)

        verify(exactly=1) { platformEmployeeDataRepository.save(any())}
    }

    @Test
    fun `updateInternalTriNetEmployeeData failed for repository save throw exception`() {
        val mockPlatformEmployeeData = getMockPlatformEmployeeDataWithTriNetData()
        val externalEmployeeId = "Test"
        val objectMapper = ObjectMapper().registerKotlinModule()
        val employeeDataCacheObj = objectMapper.readValue(mockPlatformEmployeeData[0].employeeData, EmployeeData::class.java)

        every { platformEmployeeDataRepository.save(mockPlatformEmployeeData[0]) } throws Exception("Test")

        val updateNameRequest = UpdateEmployeeNameRequest(
            effectiveDate = "2024-04-29",
            nameType = "PRF",
            firstName = "Test1",
            lastName = "Test2",
        )
        val updatePhoneContactRequest = UpdateSingleContactRequest(
            effectiveDate = "2024-04-29",
            accessType = "Work",
            media = Constants.UpdateContactMedia.PHONE,
            telephoneNumber = null
        )
        val updateEmailContactRequest = UpdateSingleContactRequest(
            effectiveDate = "2024-04-29",
            accessType = "Work",
            media = Constants.UpdateContactMedia.EMAIL,
            url = null
        )
        val updateContactRequest = UpdateEmployeeContactRequest(contactList = listOf(updatePhoneContactRequest, updateEmailContactRequest))
        val updateEmployeeTitleRequest = UpdateEmployeeTitleRequest(
            effectiveDate = "2024-04-29",
            reasonId = "XFR",
            businessTitle = "test"
        )
        val newTriNetData = employeeDataCacheObj.employeeDetailData?.copy(
            firstName = updateNameRequest.firstName,
            lastName = updateNameRequest.lastName,
            emailAddress = updateContactRequest.contactList[1].url,
            contactNumber = updateContactRequest.contactList[0].telephoneNumber,
            designation = updateEmployeeTitleRequest.businessTitle
        )

        val actualException =
            assertFailsWith<Exception> {
                triNetAPIAdapter.updateInternalTriNetEmployeeData(mockPlatformEmployeeData[0], newTriNetData, externalEmployeeId)
            }

        assertEquals("Test", actualException.message)
        verify(exactly=1) { platformEmployeeDataRepository.save(any())}
    }
}