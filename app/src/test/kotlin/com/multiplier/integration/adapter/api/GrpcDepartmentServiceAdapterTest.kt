package com.multiplier.integration.adapter.api

import com.multiplier.orgmanagement.schema.Department
import com.multiplier.orgmanagement.schema.Department.GrpcContractDepartment
import com.multiplier.orgmanagement.schema.Department.GrpcDepartmentStatus
import com.multiplier.orgmanagement.schema.DepartmentServiceGrpc
import io.grpc.StatusRuntimeException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class GrpcDepartmentServiceAdapterTest {
    @MockK
    private lateinit var stub: DepartmentServiceGrpc.DepartmentServiceBlockingStub

    @InjectMockKs
    private lateinit var grpcDepartmentServiceAdapter: GrpcDepartmentServiceAdapter

    private val contractId = 123L

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `findDepartmentForContractId should return department when found`() {
        val grpcContractDepartment =
            GrpcContractDepartment
                .newBuilder()
                .setDepartment(
                    Department.GrpcDepartment
                        .newBuilder()
                        .setId(1L)
                        .setName("Test Department")
                        .setStatus(GrpcDepartmentStatus.CREATED)
                        .build(),
                ).build()

        val response =
            Department.GrpcGetDepartmentsByContractIdsResponse
                .newBuilder()
                .addContractDepartments(grpcContractDepartment)
                .build()

        every { stub.getDepartmentsByContractIds(any()) } returns response

        val department = grpcDepartmentServiceAdapter.findDepartmentForContractId(contractId)

        assertNotNull(department)
        assertEquals(1L, department?.id)
        assertEquals("Test Department", department?.name)
    }

    @Test
    fun `findDepartmentForContractId should return null when department not found`() {
        val response =
            Department.GrpcGetDepartmentsByContractIdsResponse
                .newBuilder()
                .build()

        every { stub.getDepartmentsByContractIds(any()) } returns response

        val department = grpcDepartmentServiceAdapter.findDepartmentForContractId(contractId)

        assertNull(department)
    }

    @Test
    fun `findDepartmentForContractId should throw RuntimeException when grpc call fails`() {
        every { stub.getDepartmentsByContractIds(any()) } throws StatusRuntimeException(io.grpc.Status.INTERNAL)

        val exception =
            assertThrows(RuntimeException::class.java) {
                grpcDepartmentServiceAdapter.findDepartmentForContractId(contractId)
            }

        assertEquals("Failed to get department with contractId $contractId", exception.message)
    }
}
