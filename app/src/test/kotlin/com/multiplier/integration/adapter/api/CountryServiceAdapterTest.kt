package com.multiplier.integration.adapter.api

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.CurrencyConversionDetails
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.CountryServiceGrpc
import com.multiplier.country.schema.contract.Contract
import com.multiplier.country.schema.currency.Currency.GrpcCurrencyCode
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class CountryServiceAdapterTest {
    @MockK
    lateinit var stub: CountryServiceGrpc.CountryServiceBlockingStub

    @InjectMockKs
    lateinit var countryServiceAdapter: CountryServiceAdapterImpl

    private val countryCode = Country.GrpcCountryCode.USA

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should return country name when getCountryNameByCode is called`() {
        val countryNameResponse = Country.GetCountryNameByCodeResponse.newBuilder().setCountryName("TestCountry").build()
        every { stub.getCountryNameByCode(any()) } returns countryNameResponse

        val result = countryServiceAdapter.getCountryNameByCode(countryCode)

        assertEquals("TestCountry", result)
        verify(exactly = 1) { stub.getCountryNameByCode(any()) }
    }

    @Test
    fun `should return supported currencies when getSupportedCurrencies is called`() {
        val currencyResponse = Country.GetSupportedCurrencyResponse.newBuilder()
            .addCurrencies("USD")
            .addCurrencies("EUR")
            .build()
        every { stub.getSupportedCurrencies(any()) } returns currencyResponse

        val result = countryServiceAdapter.getSupportedCurrencies(countryCode, Contract.GrpcContractType.EMPLOYEE)

        assertEquals(setOf(GrpcCurrencyCode.USD, GrpcCurrencyCode.EUR), result)
        verify(exactly = 1) { stub.getSupportedCurrencies(any()) }
    }

    @Test
    fun `should return contract detail restrictions when getContractDetailRestriction is called with countryCode`() {
        val restriction = Country.GrpcContractDetailRestriction.newBuilder().build()
        val restrictionResponse = Country.GetContractDetailRestrictionResponse.newBuilder()
            .addContractDetailRestrictions(restriction)
            .build()
        every { stub.getContractDetailRestriction(any()) } returns restrictionResponse

        val result = countryServiceAdapter.getContractDetailRestriction(countryCode)

        assertEquals(listOf(restriction), result)
        verify(exactly = 1) { stub.getContractDetailRestriction(any()) }
    }

    @Test
    fun `should return contract detail restrictions when getContractDetailRestriction is called without countryCode`() {
        val restriction = Country.GrpcContractDetailRestriction.newBuilder().build()
        val restrictionResponse = Country.GetContractDetailRestrictionResponse.newBuilder()
            .addContractDetailRestrictions(restriction)
            .build()
        every { stub.getContractDetailRestriction(any()) } returns restrictionResponse

        val result = countryServiceAdapter.getContractDetailRestriction(null)

        assertEquals(listOf(restriction), result)
        verify(exactly = 1) { stub.getContractDetailRestriction(any()) }
    }
}