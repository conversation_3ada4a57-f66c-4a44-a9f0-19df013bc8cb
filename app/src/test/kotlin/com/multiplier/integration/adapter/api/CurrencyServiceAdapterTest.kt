package com.multiplier.integration.adapter.api

import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.CurrencyConversionDetails
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class CurrencyServiceAdapterTest {
    @MockK
    lateinit var stub: CurrencyConversionServiceV2Grpc.CurrencyConversionServiceV2BlockingStub

    @InjectMockKs
    lateinit var currencyServiceClient: DefaultCurrencyServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should convert currency successfully`() {
        val mockAmount = 1.0
        val mockFromCurrencyCode = Currency.CurrencyCode.USD
        val mockToCurrencyCode = Currency.CurrencyCode.VND
        val mockExchangeRate = 25000.0
        val stubRequest = CurrencyV2.CurrencyConversionGrpcRequest.newBuilder()
            .setSourceAmount(CurrencyV2.Amount.newBuilder()
                .setAmount(mockAmount)
                .setCurrency(mockFromCurrencyCode)
                .build())
            .setTargetCurrency(mockToCurrencyCode)
            .setConversionInstructions(
                CurrencyV2.ConversionInstruction.newBuilder()
                .setConversionRate(CurrencyV2.CurrencyConversionRate.TOPPED_UP)
                .setConversionSource(Currency.CurrencyConversionSource.ON_DEMAND)
                .build())
            .build()
        val mockConvertedAmount = 25000.0
        val stubResponse = CurrencyV2.CurrencyConversionGrpcResponse.newBuilder()
            .setConvertedAmount(CurrencyV2.Amount.newBuilder()
                .setAmount(mockConvertedAmount)
                .build())
            .setConversionDetails(CurrencyConversionDetails.newBuilder()
                .setConversionRate(mockExchangeRate)
                .build())
            .build()

        every { stub.convertCurrency(stubRequest) } returns stubResponse

        val response = currencyServiceClient.getCurrencyExchangeAmount(mockFromCurrencyCode, mockToCurrencyCode, mockAmount)
        Assertions.assertEquals(mockConvertedAmount, response)
    }

    @Test
    fun `should throw exception when converting currency`(){
        val mockAmount = 1.0
        val mockFromCurrencyCode = Currency.CurrencyCode.USD
        val mockToCurrencyCode = Currency.CurrencyCode.VND
        val stubRequest = CurrencyV2.CurrencyConversionGrpcRequest.newBuilder()
            .setSourceAmount(CurrencyV2.Amount.newBuilder()
                .setAmount(mockAmount)
                .setCurrency(mockFromCurrencyCode)
                .build())
            .setTargetCurrency(mockToCurrencyCode)
            .setConversionInstructions(
                CurrencyV2.ConversionInstruction.newBuilder()
                    .setConversionRate(CurrencyV2.CurrencyConversionRate.TOPPED_UP)
                    .setConversionSource(Currency.CurrencyConversionSource.ON_DEMAND)
                    .build())
            .build()

        val expectedException =
            RuntimeException("Currency Conversion data for ${mockFromCurrencyCode.name} to ${mockToCurrencyCode.name} not found! in INTERNA")
        every { stub.convertCurrency(stubRequest) } throws expectedException

        val actualException =
            assertFailsWith<RuntimeException> {
                currencyServiceClient.getCurrencyExchangeAmount(mockFromCurrencyCode, mockToCurrencyCode, mockAmount)
            }

        Assertions.assertEquals(actualException.message, expectedException.message)


    }
}