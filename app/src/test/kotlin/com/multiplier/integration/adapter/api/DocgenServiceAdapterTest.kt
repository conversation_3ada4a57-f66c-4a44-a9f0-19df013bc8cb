package com.multiplier.integration.adapter.api

import com.multiplier.core.common.rest.client.docgen.DocGenRestClient
import com.multiplier.core.common.rest.client.docgen.enums.TemplateType
import com.multiplier.core.common.rest.client.docgen.model.CreateDocumentRequest
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.core.common.rest.client.docgen.model.GenerateDocumentRequest
import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.CurrencyConversionDetails
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import com.multiplier.timeoff.schema.GrpcBulkRevokeTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffResponse
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesRequest
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesResponse
import com.multiplier.timeoff.schema.GrpcEmpty
import com.multiplier.timeoff.schema.TimeOffServiceGrpc
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.web.multipart.MultipartFile
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class DefaultDocGenServiceClientTest {

    @MockK
    private lateinit var docGenClient: DocGenRestClient

    @InjectMockKs
    private lateinit var docgenServiceAdapter: DefaultDocGenServiceClient

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should get document by id`() {
        val documentId = 1L
        val expectedResponse = DocumentResponse()
        every { docGenClient.getDocument(documentId) } returns expectedResponse

        val result = docgenServiceAdapter.getDocument(documentId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { docGenClient.getDocument(documentId) }
    }

    @Test
    fun `should create document`() {
        val createDocumentRequest = CreateDocumentRequest()
        val expectedResponse = DocumentResponse()
        every { docGenClient.createDocument(createDocumentRequest) } returns expectedResponse

        val result = docgenServiceAdapter.createDocument(createDocumentRequest)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { docGenClient.createDocument(createDocumentRequest) }
    }

    @Test
    fun `should replace document`() {
        val documentId = 1L
        val file = mockk<MultipartFile>()
        val expectedResponse = DocumentResponse()
        every { docGenClient.replaceDocument(documentId, file) } returns expectedResponse

        val result = docgenServiceAdapter.replaceDocument(documentId, file)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { docGenClient.replaceDocument(documentId, file) }
    }

    @Test
    fun `should generate document`() {
        val documentId = 1L
        val generateDocumentRequest = GenerateDocumentRequest<String>()
        val expectedResponse = DocumentResponse()
        every { docGenClient.generateDocument(documentId, generateDocumentRequest) } returns expectedResponse

        val result = docgenServiceAdapter.generateDocument(documentId, generateDocumentRequest)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { docGenClient.generateDocument(documentId, generateDocumentRequest) }
    }

    @Test
    fun `should freeze document`() {
        val documentId = 1L
        val freezeDocumentRequest = GenerateDocumentRequest<String>()
        val expectedResponse = DocumentResponse()
        every { docGenClient.freezeDocument(documentId, freezeDocumentRequest) } returns expectedResponse

        val result = docgenServiceAdapter.freezeDocument(documentId, freezeDocumentRequest)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { docGenClient.freezeDocument(documentId, freezeDocumentRequest) }
    }

    @Test
    fun `should upload document`() {
        val file = mockk<MultipartFile>()
        val type = TemplateType.COMPANY_LOGO
        val createDocumentResponse = DocumentResponse().apply { id(1L) }
        every { docGenClient.createDocument(any()) } returns createDocumentResponse
        every { docGenClient.replaceDocument(any(), any()) } returns DocumentResponse()

        val result = docgenServiceAdapter.uploadDocument(type, file)

        verify(exactly = 1) { docGenClient.createDocument(any()) }
        verify(exactly = 1) { docGenClient.replaceDocument(any(), any()) }
    }
}