package com.multiplier.integration.adapter.api

import com.multiplier.core.schema.currency.Currency
import com.multiplier.core.schema.currency.CurrencyConversionServiceV2Grpc
import com.multiplier.core.schema.currency.CurrencyV2
import com.multiplier.core.schema.currency.CurrencyV2.CurrencyConversionDetails
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import com.multiplier.timeoff.schema.GrpcBulkRevokeTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffRequest
import com.multiplier.timeoff.schema.GrpcBulkTimeOffResponse
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesRequest
import com.multiplier.timeoff.schema.GrpcCompanyTimeOffTypesResponse
import com.multiplier.timeoff.schema.GrpcEmpty
import com.multiplier.timeoff.schema.TimeOffServiceGrpc
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class DefaultTimeoffServiceAdapterTest {

    @MockK
    private lateinit var timeoffStub: TimeOffServiceGrpc.TimeOffServiceBlockingStub

    @InjectMockKs
    private lateinit var timeoffServiceAdapter: DefaultTimeoffServiceAdapter

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should bulk upsert time offs`() {
        val request = GrpcBulkTimeOffRequest.newBuilder().build()
        val expectedResponse = GrpcBulkTimeOffResponse.newBuilder().build()

        every { timeoffStub.bulkUpsertTimeOffs(any()) } returns expectedResponse

        val result = timeoffServiceAdapter.bulkUpsertTimeOffs(request)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { timeoffStub.bulkUpsertTimeOffs(any()) }
    }

    @Test
    fun `should bulk revoke time offs`() {
        val request = GrpcBulkRevokeTimeOffRequest.newBuilder().build()
        val expectedResponse = GrpcEmpty.newBuilder().build()

        every { timeoffStub.bulkRevokeTimeOffs(any()) } returns expectedResponse

        val result = timeoffServiceAdapter.bulkRevokeTimeoffs(request)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { timeoffStub.bulkRevokeTimeOffs(any()) }
    }

    @Test
    fun `should get company time off types`() {
        val companyId = 123L
        val expectedResponse = GrpcCompanyTimeOffTypesResponse.newBuilder().build()

        every { timeoffStub.getCompanyTimeOffTypes(any()) } returns expectedResponse

        val result = timeoffServiceAdapter.getCompanyTimeOffTypes(companyId)

        assertEquals(expectedResponse, result)
        verify(exactly = 1) { timeoffStub.getCompanyTimeOffTypes(any()) }
    }
}