package com.multiplier.integration.adapter.api

import com.merge.api.MergeApiClient
import com.merge.api.resources.accounting.AccountingClient
import com.merge.api.resources.accounting.accounttoken.AccountTokenClient
import com.merge.api.resources.accounting.invoices.InvoicesClient
import com.merge.api.resources.accounting.linktoken.LinkTokenClient
import com.merge.api.resources.accounting.types.*
import com.multiplier.integration.TestHttpClientConfig
import com.multiplier.integration.accounting.domain.mapping.AccountingTransactionInvoice
import com.multiplier.integration.adapter.api.resources.financial.*
import com.multiplier.integration.adapter.api.resources.financial.mapping.MergeDevInvoiceRequestMapper
import com.multiplier.integration.adapter.api.resources.financial.payment.MergeDevPaymentRequestMapper
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCredit
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditRequestBody
import com.multiplier.integration.types.PlatformCategory
import graphql.Assert.assertFalse
import io.ktor.client.*
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.spy
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.test.context.junit.jupiter.SpringExtension
import org.springframework.test.util.ReflectionTestUtils
import java.time.OffsetDateTime
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExtendWith(SpringExtension::class)
class MergeDevAdapterTest {
    private lateinit var mergeDevAdapter: MergeDevAdapter

    @Mock
    private lateinit var mergeDevInvoiceRequestMapper: MergeDevInvoiceRequestMapper

    @Mock
    private lateinit var mergeDevPaymentRequestMapper: MergeDevPaymentRequestMapper

    @Mock
    private lateinit var accountingClient: AccountingClient

    @Mock
    private lateinit var linkTokenClient: LinkTokenClient

    @Mock
    private lateinit var accountTokenClient: AccountTokenClient

    @Mock
    private lateinit var apiClient: MergeApiClient

    @Mock
    private lateinit var invoiceClient: InvoicesClient

    private var httpClient: HttpClient = TestHttpClientConfig.success201MergeDevHttpClient()

    @BeforeEach
    fun setup() {
        mergeDevAdapter = spy(MergeDevAdapter(mergeDevInvoiceRequestMapper, mergeDevPaymentRequestMapper, httpClient))
        ReflectionTestUtils.setField(mergeDevAdapter, "apiKey", "test-api-key")
        ReflectionTestUtils.setField(mergeDevAdapter, "apiUrl", "http://localhost:8080")
        whenever(mergeDevAdapter.getMergeAccountingClient()).thenReturn(accountingClient)
        whenever(mergeDevAdapter.getMergeApiClient("Test Account Token")).thenReturn(apiClient)
        whenever(accountingClient.linkToken()).thenReturn(linkTokenClient)
        whenever(accountingClient.accountToken()).thenReturn(accountTokenClient)
        whenever(apiClient.accounting()).thenReturn(accountingClient)
    }

    @Test
    fun `createLinkToken should return successful response`() {
        val request =
            MergeDevLinkTokenRequest(
                endUserEmailAddress = "<EMAIL>",
                endUserOrganizationName = "Test Org",
                endUserOriginId = "origin-id",
                appId = "app-id",
            )

        val linkTokenResponse =
            mockk<LinkToken>(relaxed = true) {
                every { linkToken } returns "test-link-token"
                every { integrationName } returns Optional.of("test-integration")
                every { magicLinkUrl } returns Optional.of("https://magic-link-url")
            }

        whenever(linkTokenClient.create(any())).thenReturn(linkTokenResponse)

        val response = mergeDevAdapter.createLinkToken(request)

        assertTrue(response.success)
        assertNotNull(response.message)

        val message = response.message as MergeDevLinkTokenMessage
        assertEquals("test-link-token", message.linkToken)
        assertEquals("test-integration", message.integrationName)
        assertEquals("https://magic-link-url", message.magicLinkUrl)

        verify(linkTokenClient).create(any())
    }

    @Test
    fun `createLinkToken should handle exceptions`() {
        val request =
            MergeDevLinkTokenRequest(
                endUserEmailAddress = "<EMAIL>",
                endUserOrganizationName = "Test Org",
                endUserOriginId = "origin-id",
                appId = "app-id",
            )

        whenever(linkTokenClient.create(any())).thenThrow(RuntimeException("Test error"))

        val response = mergeDevAdapter.createLinkToken(request)

        assertFalse(response.success)
        assertNull(response.message)
        assertNotNull(response.error)
        assertEquals("Test error", response.error?.errorMessage)
        verify(linkTokenClient).create(any())
    }

    @Test
    fun `retrieveAccountToken should return successful response`() {
        val request = MergeDevAccountTokenRetrieveRequest(publicToken = "test-public-token")

        val accountIntegration =
            mockk<AccountIntegration>(relaxed = true) {
                every { name } returns "test-integration"
                every { categories } returns Optional.of(listOf(CategoriesEnum.ACCOUNTING))
            }

        val accountTokenResponse =
            mockk<AccountToken>(relaxed = true) {
                every { accountToken } returns "test-account-token"
                every { integration } returns accountIntegration
            }

        whenever(accountTokenClient.retrieve(request.publicToken)).thenReturn(accountTokenResponse)

        val response = mergeDevAdapter.retrieveAccountToken(request)
        val mergeDevAccountTokenRetrieveMessage = response.message as MergeDevAccountTokenRetrieveMessage
        assertTrue(response.success)
        assertNotNull(response.message)
        assertEquals("test-account-token", mergeDevAccountTokenRetrieveMessage.accountToken)
        assertEquals("test-integration", mergeDevAccountTokenRetrieveMessage.integrationName)
        assertEquals(listOf(PlatformCategory.ACCOUNTING), mergeDevAccountTokenRetrieveMessage.integrationCategories)
        verify(accountTokenClient).retrieve(request.publicToken)
    }

    @Test
    fun `retrieveAccountToken should handle exceptions`() {
        val request = MergeDevAccountTokenRetrieveRequest(publicToken = "test-public-token")

        whenever(accountTokenClient.retrieve(any())).thenThrow(RuntimeException("Test error"))

        val response = mergeDevAdapter.retrieveAccountToken(request)

        assertFalse(response.success)
        assertNull(response.message)
        assertNotNull(response.error)
        assertEquals("Test error", response.error?.errorMessage)
        verify(accountTokenClient).retrieve(any())
    }

    @Test
    fun `create should successfully create invoice`() {
        val invoiceResponseModel = Invoice.builder().id("test-invoice-id").build()
        val invoiceRequest = InvoiceRequest.builder().build()
        val invoiceResponse =
            mockk<InvoiceResponse>(relaxed = true) {
                every { model } returns invoiceResponseModel
            }

        whenever(mergeDevInvoiceRequestMapper.toInvoiceRequest(any())).thenReturn(invoiceRequest)
        whenever(accountingClient.invoices()).thenReturn(invoiceClient)
        whenever(invoiceClient.create(any())).thenReturn(invoiceResponse)

        val accountingInvoice = getMergeDevAccountingTransactionInvoice()
        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )

        val response = mergeDevAdapter.createInvoice(mergeDevInvoiceRequest)
        assertTrue(response.success)
        assertNotNull(response.message)
        assertEquals("test-invoice-id", (response.message as MergeDevInvoiceMessage).id)
    }

    @Test
    fun `create should successfully create invoice mergeResponse test`() {
        val invoiceResponseModel = buildInvoiceResponseModel()
        val invoiceRequest = InvoiceRequest.builder().build()
        val invoiceResponse =
            mockk<InvoiceResponse>(relaxed = true) {
                every { model } returns invoiceResponseModel
            }

        whenever(mergeDevInvoiceRequestMapper.toInvoiceRequest(any())).thenReturn(invoiceRequest)
        whenever(accountingClient.invoices()).thenReturn(invoiceClient)
        whenever(invoiceClient.create(any())).thenReturn(invoiceResponse)

        val accountingInvoice = getMergeDevAccountingTransactionInvoice()
        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )

        val response = mergeDevAdapter.createInvoice(mergeDevInvoiceRequest)
        val mergeDevInvoiceMessage = response.message as MergeDevInvoiceMessage

        assertTrue(response.success)
        assertNotNull(response.message)
        assertEquals("test-invoice-id", mergeDevInvoiceMessage.id)
        assertEquals("test-remote-id", mergeDevInvoiceMessage.remoteId)
        val invoice = mergeDevInvoiceMessage.invoice
        assertEquals("test-invoice-number", invoice.number)
        assertEquals("Test Contact", invoice.contact)
        assertEquals("test-memo", invoice.memo)
        assertEquals("Test Company", invoice.commonFields?.company)
        assertEquals("CAD", invoice.commonFields?.currency)
        assertEquals(100.0, invoice.subTotal)
        assertEquals(0.0, invoice.totalDiscount)
        assertEquals(0.0, invoice.totalTaxAmount)
        assertEquals(100.0, invoice.totalAmount)
        assertEquals("DRAFT", invoice.status)
        assertEquals(100.0, invoice.balance)
        assertEquals("1.0", invoice.commonFields?.exchangeRate)
        assertEquals("Test Accounting Period", invoice.accountingPeriod)
        assertEquals(1, invoice.lineItems.size)
        assertEquals("test-line-item-id", invoice.lineItems[0].id)
        assertEquals("test-remote-id", invoice.lineItems[0].commonFields?.remoteId)
    }

    @Test
    fun `create should handle error`() {
        val invoiceRequest = InvoiceRequest.builder().build()
        whenever(mergeDevInvoiceRequestMapper.toInvoiceRequest(any())).thenReturn(invoiceRequest)
        whenever(accountingClient.invoices()).thenReturn(invoiceClient)
        whenever(invoiceClient.create(any())).thenThrow(RuntimeException("Test error"))
        val accountingInvoice = getMergeDevAccountingTransactionInvoice()
        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )

        val response = mergeDevAdapter.createInvoice(mergeDevInvoiceRequest)
        assertFalse(response.success)
        assertNull(response.message)
        assertNotNull(response.error)
        assertEquals("Test error", response.error?.errorMessage)
    }

    @Test
    fun `update should successfully update invoice`() {
        val invoiceResponseModel = Invoice.builder().id("test-invoice-id").build()
        val invoiceRequest = InvoiceRequest.builder().build()
        val invoiceResponse =
            mockk<InvoiceResponse>(relaxed = true) {
                every { model } returns invoiceResponseModel
            }

        whenever(mergeDevInvoiceRequestMapper.toInvoiceRequest(any())).thenReturn(invoiceRequest)
        whenever(accountingClient.invoices()).thenReturn(invoiceClient)
        whenever(invoiceClient.partialUpdate(any(), any())).thenReturn(invoiceResponse)

        val accountingInvoice = getMergeDevAccountingTransactionInvoice()
        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )

        val response = mergeDevAdapter.updateInvoice(mergeDevInvoiceRequest, "id")
        assertTrue(response.success)
        assertNotNull(response.message)
        assertEquals("test-invoice-id", (response.message as MergeDevInvoiceMessage).id)
    }

    @Test
    fun `update should handle error`() {
        val invoiceRequest = InvoiceRequest.builder().build()
        whenever(mergeDevInvoiceRequestMapper.toInvoiceRequest(any())).thenReturn(invoiceRequest)
        whenever(accountingClient.invoices()).thenReturn(invoiceClient)
        whenever(invoiceClient.partialUpdate(any(), any())).thenThrow(RuntimeException("Test error"))
        val accountingInvoice = getMergeDevAccountingTransactionInvoice()
        val mergeDevInvoiceRequest =
            MergeDevInvoiceRequest(
                accountApi = MergeDevAccountApi("Test Account Token"),
                invoice = accountingInvoice,
            )

        val response = mergeDevAdapter.updateInvoice(mergeDevInvoiceRequest, "id")
        assertFalse(response.success)
        assertNull(response.message)
        assertNotNull(response.error)
        assertEquals("Test error", response.error?.errorMessage)
    }

    @Test
    fun `create vendor note should success`() {
        runBlocking {
            val mergeDevVendorCreditRequest =
                MergeDevVendorCreditRequest(
                    accountApi = MergeDevAccountApi("Test Account Token"),
                    vendorCredit = getMergeDevVendorCreditNote(),
                )
            val response = mergeDevAdapter.createVendorCredit(mergeDevVendorCreditRequest)
            assertNotNull(response)
        }
    }

    private fun getMergeDevVendorCreditNote(): VendorCreditRequestBody =
        VendorCreditRequestBody(
            model =
                VendorCredit(
                    vendor = "vendor",
                    currency = "USD",
                ),
        )

    private fun getMergeDevAccountingTransactionInvoice(): AccountingTransactionInvoice =
        AccountingTransactionInvoice(
            contact = "Test Contact",
        )

    private fun buildInvoiceResponseModel(): Invoice =
        Invoice
            .builder()
            .id("test-invoice-id")
            .remoteId("test-remote-id")
            .createdAt(Optional.of(OffsetDateTime.now()))
            .modifiedAt(Optional.of(OffsetDateTime.now()))
            .type(
                InvoiceType.of(
                    InvoiceTypeEnum.ACCOUNTS_PAYABLE,
                ),
            ).contact(InvoiceContact.of("Test Contact"))
            .number("test-invoice-number")
            .issueDate(Optional.of(OffsetDateTime.now()))
            .dueDate(Optional.of(OffsetDateTime.now()))
            .paidOnDate(Optional.of(OffsetDateTime.now()))
            .memo("test-memo")
            .company(InvoiceCompany.of("Test Company"))
            .currency(InvoiceCurrency.of(CurrencyEnum.CAD))
            .exchangeRate("1.0")
            .totalDiscount(0.0)
            .subTotal(100.0)
            .status(InvoiceStatus.of(InvoiceStatusEnum.DRAFT))
            .totalTaxAmount(0.0)
            .totalAmount(100.0)
            .balance(100.0)
            .remoteUpdatedAt(Optional.of(OffsetDateTime.now()))
            .trackingCategories(listOf(Optional.of(InvoiceTrackingCategoriesItem.of("Test Tracking Categories"))))
            .accountingPeriod(InvoiceAccountingPeriod.of("Test Accounting Period"))
            .lineItems(mutableListOf(getInvoiceLineItem()))
            .build()

    private fun getInvoiceLineItem() =
        InvoiceLineItem
            .builder()
            .id("test-line-item-id")
            .remoteId("test-remote-id")
            .createdAt(Optional.of(OffsetDateTime.now()))
            .modifiedAt(Optional.of(OffsetDateTime.now()))
            .description("test-description")
            .quantity(1.0)
            .unitPrice(100.0)
            .totalAmount(0.0)
            .currency(InvoiceLineItemCurrency.of(CurrencyEnum.CAD))
            .item(InvoiceLineItemItem.of("Test Item"))
            .taxRate("test-tax-rate")
            .trackingCategories(mutableListOf(Optional.of(InvoiceLineItemTrackingCategoriesItem.of("Test Tracking Categories"))))
            .company("Test Company")
            .build()

    fun vendorCreditHttpTextBody(): String =
        """
        {
            "model": {
                "id": "5a66829b-6a09-4249-8ec8-cbd7090ca871",
                "remote_id": "1525",
                "created_at": "2025-01-21T18:14:08.437607Z",
                "modified_at": "2025-01-21T18:14:09.663689Z",
                "number": null,
                "transaction_date": "2025-01-17T00:00:00Z",
                "vendor": "5cff164c-66bf-4947-83b5-7da44bf74d9a",
                "total_amount": 811.54,
                "currency": "SGD",
                "exchange_rate": "1",
                "inclusive_of_tax": null,
                "company": null,
                "lines": [
                    {
                        "id": "07c2f344-7b2b-4fda-b3a7-a7da1648a800",
                        "remote_id": "1",
                        "created_at": "2025-01-21T18:14:09.731388Z",
                        "modified_at": "2025-01-21T18:14:09.755486Z",
                        "net_amount": 811.54,
                        "tracking_category": null,
                        "tracking_categories": [],
                        "description": null,
                        "account": "0ec10d0b-7f89-4851-a958-b37e097ce737",
                        "company": null,
                        "tax_rate": null,
                        "exchange_rate": null,
                        "remote_was_deleted": false
                    }
                ],
                "tracking_categories": [],
                "applied_to_lines": [
                    {
                        "remote_id": null,
                        "created_at": "2025-01-21T18:14:10.845510Z",
                        "modified_at": "2025-01-21T18:14:10.845529Z",
                        "invoice": "ef9686d7-d7df-4002-8d39-fb6efa7a35ea",
                        "applied_date": "2025-01-17T00:00:00Z",
                        "applied_amount": "811.****************",
                        "remote_was_deleted": false
                    }
                ],
                "remote_was_deleted": false,
                "accounting_period": null,
                "field_mappings": null,
                "remote_data": null
            },
            "warnings": [],
            "errors": []
        }
        """.trimIndent()
}
