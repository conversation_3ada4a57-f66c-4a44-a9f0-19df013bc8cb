package com.multiplier.integration

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.http.HttpStatus
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class CustomerIntegrationApplicationTest {
    @Autowired
    lateinit var restTemplate: TestRestTemplate
//    @Test
//    fun `should load the app and health check returns ok`() {
//        val entity = restTemplate.getForEntity("/actuator/health", String::class.java)
//        assertEquals(HttpStatus.OK, entity.statusCode)
//    }
}
