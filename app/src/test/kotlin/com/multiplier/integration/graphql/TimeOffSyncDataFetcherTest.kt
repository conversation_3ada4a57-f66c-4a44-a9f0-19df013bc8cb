package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.integration.service.TimeOffSyncService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)
class TimeOffSyncDataFetcherTest {

    @MockK
    private lateinit var timeOffSyncService: TimeOffSyncService
    @MockK
    private lateinit var currentUser: CurrentUser

    @InjectMockKs
    private lateinit var timeOffSyncDataFetcher: TimeOffSyncDataFetcher

    @Test
    fun `get external leave types successfully`() {
        val integrationId = 2L
        val mockedContext = UserContext(id = 1L, scopes = UserScopes(companyId = 1L))

        every { currentUser.context } returns mockedContext
        every { timeOffSyncService.getExternalLeaveTypes(any(), any()) } returns emptyList()

        val actual = timeOffSyncDataFetcher.getExternalLeaveTypes(integrationId)

        assertEquals(actual.size, 0)
    }

    @Test
    fun `get leave types fields mapping successfully`() {
        val integrationId = 2L
        val mockedContext = UserContext(id = 1L, scopes = UserScopes(companyId = 1L))
        every { currentUser.context } returns mockedContext
        every { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns emptyList()

        val actual = timeOffSyncDataFetcher.getLeaveTypeMappingDefinition(integrationId)

        assertEquals(actual.size, 0)
    }
}