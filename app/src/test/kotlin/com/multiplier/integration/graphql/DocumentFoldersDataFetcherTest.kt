package com.multiplier.integration.graphql

import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.common.transport.user.UserScopes
import com.multiplier.integration.service.DocumentFoldersService
import com.multiplier.integration.types.DocumentFolder
import com.multiplier.integration.types.DocumentFolderDetail
import com.multiplier.integration.types.DocumentFolderType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals

@ExtendWith(SpringExtension::class)
class DocumentFoldersDataFetcherTest {

    @MockK
    private lateinit var documentFoldersService: DocumentFoldersService
    @MockK
    private lateinit var currentUser: CurrentUser

    @InjectMockKs
    private lateinit var documentFoldersDataFetcher: DocumentFoldersDataFetcher

    @Test
    fun `getDocumentFolders successfully`() {
        val integrationId = 2L
        val mockedContext = UserContext(id = 1L, scopes = UserScopes(companyId = 1L))
        val mockDocumentFolder = mockk<DocumentFolder> {
            every { savedFolder } returns DocumentFolderDetail.newBuilder()
                .label("test")
                .build()
        }

        every { currentUser.context } returns mockedContext
        every { documentFoldersService.getDocumentFolders(1L, integrationId, DocumentFolderType.PAYSLIP) } returns mockDocumentFolder

        val resp = documentFoldersDataFetcher.getDocumentFolders(integrationId, DocumentFolderType.PAYSLIP)

        assertEquals("test", resp.savedFolder.label)
    }
}