package com.multiplier.integration.graphql

import com.multiplier.integration.accounting.domain.FinancialTransactionAmountUpdatedHandlerService
import com.multiplier.integration.accounting.domain.FinancialTransactionHandlerService
import com.multiplier.integration.types.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.*
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class AccountingCustomerIntegrationDataMutatorTest {

    @Mock
    private lateinit var financialTransactionHandlerService: FinancialTransactionHandlerService
    @Mock
    private lateinit var financialTransactionAmountUpdatedHandlerService: FinancialTransactionAmountUpdatedHandlerService
    @InjectMocks
    private lateinit var accountingCustomerIntegrationDataMutator: AccountingCustomerIntegrationDataMutator

    @Nested
    inner class AccountingSyncCompanyPayables {

        @Test
        fun `should return success response when company payable is synced`() {

            val companyPayableId = 123L
            val expectedResponse = AccountingSyncCompanyPayablesResponse(
                companyPayableId,
                TaskResponse.newBuilder().success(true).message("successfully synced company payable id = $companyPayableId").build()
            )

            doNothing().`when`(financialTransactionHandlerService).upsertHandler(companyPayableId)

            val actualResponse = accountingCustomerIntegrationDataMutator.accountingSyncCompanyPayables(
                AccountingSyncCompanyPayablesInput(listOf(companyPayableId))
            )

            assertEquals(expectedResponse, actualResponse.first())
        }

        @Test
        fun `should return error response when company payable is not synced`() {
            val companyPayableId = 123L
            val expectedResponse = AccountingSyncCompanyPayablesResponse(
                companyPayableId,
                TaskResponse.newBuilder().success(false).message("Unknown error").build()
            )

            doThrow(RuntimeException("Unknown error")).`when`(financialTransactionHandlerService).upsertHandler(companyPayableId)

            val actualResponse = accountingCustomerIntegrationDataMutator.accountingSyncCompanyPayables(
                AccountingSyncCompanyPayablesInput(listOf(companyPayableId))
            )
            assertEquals(expectedResponse, actualResponse.first())
        }

    }

    @Nested
    inner class AccountingCreatePaymentForCompanyPayable {

        @Test
        fun `should return success response when payment is created for company payable`() {
            val companyPayableId = 123L
            val expectedResponse = AccountingCreatePaymentForCompanyPayableResponse(
                companyPayableId,
                TaskResponse.newBuilder().success(true)
                    .message("successfully created payment for company payable id = $companyPayableId").build()
            )

            doNothing()
                .`when`(financialTransactionAmountUpdatedHandlerService)
                .amountUpdateHandler(companyPayableId)

            val actualResponse = accountingCustomerIntegrationDataMutator.accountingCreatePaymentForCompanyPayable(
                AccountingCreatePaymentForCompanyPayableInput(companyPayableId)
            )

            assertEquals(expectedResponse, actualResponse)
        }

        @Test
        fun `should return error response when payment is not created for company payable`() {
            val companyPayableId = 123L
            val expectedResponse = AccountingCreatePaymentForCompanyPayableResponse(
                companyPayableId,
                TaskResponse.newBuilder().success(false).message("Unknown error").build()
            )

            doThrow(RuntimeException("Unknown error"))
                .`when`(financialTransactionAmountUpdatedHandlerService)
                .amountUpdateHandler(companyPayableId)

            val actualResponse = accountingCustomerIntegrationDataMutator.accountingCreatePaymentForCompanyPayable(
                AccountingCreatePaymentForCompanyPayableInput(companyPayableId)
            )

            assertEquals(expectedResponse, actualResponse)
        }
    }
}