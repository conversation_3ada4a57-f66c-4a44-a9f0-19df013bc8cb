package com.multiplier.integration.handlers

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.core.model.ContractDocumentUpdateEvent
import com.multiplier.integration.handlers.contract.ContractDocumentUpdateEventHandler
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class ContractDocumentUpdateEventHandlerTest {
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @InjectMockKs
    lateinit var testHandler: ContractDocumentUpdateEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }


    @Test
    fun `should handle contract document update event`() {
        var event  = ContractDocumentUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100,1)
        every { employeeService.updateContractDocument(any(),any(),any()) } returns Unit
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit
        every { docgenServiceAdapter.getDocument(any()) } returns getDocumentResponse()
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        testHandler.handleContractDocumentUpdateEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }
    }

    @Test
    fun `should handle downstream error in document update event`() {
        var event  = ContractDocumentUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } throws IntegrationDownstreamException("Test")
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit

        testHandler.handleContractDocumentUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle UnsupportedOperationException in document update event`() {
        var event  = ContractDocumentUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every {  employeeService.updateContractDocument(any(),any(),any()) } throws UnsupportedOperationException("Unsupported operation")
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { docgenServiceAdapter.getDocument(any()) } returns getDocumentResponse()
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100,1)
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleContractDocumentUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle IllegalStateException in document update event`() {
        var event  = ContractDocumentUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { docgenServiceAdapter.getDocument(any()) } returns getDocumentResponse()
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100,1)
        every {  employeeService.updateContractDocument(any(),any(),any()) } throws UnsupportedOperationException("Unsupported operation")
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleContractDocumentUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

}
