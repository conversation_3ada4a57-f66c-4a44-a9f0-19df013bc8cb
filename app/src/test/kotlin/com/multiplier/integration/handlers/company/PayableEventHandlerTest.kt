package com.multiplier.integration.handlers.company

import com.multiplier.integration.accounting.domain.FinancialTransactionAmountUpdatedHandlerService
import com.multiplier.integration.accounting.domain.FinancialTransactionHandlerService
import com.multiplier.integration.mock.getEventsWithPayablePayload
import com.multiplier.integration.mock.getEventsWithPayablePayloadForAmountUpdated
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EventLogService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class PayableEventHandlerTest{

    @MockK
    private lateinit var eventLogService: EventLogService

    @MockK
    private lateinit var financialTransactionHandlerService: FinancialTransactionHandlerService

    @MockK
    private lateinit var financialTransactionAmountUpdatedHandlerService: FinancialTransactionAmountUpdatedHandlerService

    @InjectMockKs
    private lateinit var payableEventHandler: PayableEventHandler

    @Test
    fun `should handle payable upsert event`() {
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getEventsWithPayablePayload().get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { financialTransactionHandlerService.upsertHandler(any()) } returns Unit
        every { eventLogService.markEventSuccessful(eventLog) } returns Unit

        payableEventHandler.handlePayableEvent(eventLogId)

        verify(exactly = 1) { financialTransactionHandlerService.upsertHandler(any()) }
    }

    @Test
    fun `should ignore failed events`(){
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getEventsWithPayablePayload().get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { financialTransactionHandlerService.upsertHandler(any()) } throws  RuntimeException()
        every { eventLogService.handleNonRetryableEvent(eventLog, any()) } returns Unit


        payableEventHandler.handlePayableEvent(eventLogId)

        verify(exactly = 1) { financialTransactionHandlerService.upsertHandler(any()) }
    }

    @Test
    fun `when event is of type due amount updated, call the amount update handler`() {
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getEventsWithPayablePayloadForAmountUpdated().get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { financialTransactionAmountUpdatedHandlerService.amountUpdateHandler(any()) } returns Unit
        every { eventLogService.markEventSuccessful(eventLog) } returns Unit

        payableEventHandler.handlePayableEvent(eventLogId)

        verify(exactly = 1) { financialTransactionAmountUpdatedHandlerService.amountUpdateHandler(any()) }
    }
}