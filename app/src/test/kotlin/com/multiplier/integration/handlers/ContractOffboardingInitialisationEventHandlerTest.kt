package com.multiplier.integration.handlers

import com.multiplier.integration.adapter.api.ContractOffBoardingServiceAdapter
import com.multiplier.integration.adapter.model.ContractOffBoardingRequest
import com.multiplier.integration.adapter.model.RescheduleContractOffBoardingRequest
import com.multiplier.integration.handlers.contract.ContractOffboardingInitialisationEventHandler
import com.multiplier.integration.mock.getMockContractOffboarding
import com.multiplier.integration.mock.getMockEventData
import com.multiplier.integration.mock.lwd
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate

@ExtendWith(SpringExtension::class)

class ContractOffboardingInitialisationEventHandlerTest {
    @MockK
    lateinit var contractOffboardingServiceAdapter: ContractOffBoardingServiceAdapter

    @InjectMockKs
    lateinit var testHandler: ContractOffboardingInitialisationEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should reschedule contract offboarding when there exists contract offboarding`() {
        val contractId = 1L
        val contractOffboardingId = 2L
        val mockContractOffboarding = getMockContractOffboarding(contractId, lwd)
        val mockUpdateContractOffboarding = getMockContractOffboarding(contractId, LocalDate.of(2024, 1, 30))
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val rescheduleRequest = RescheduleContractOffBoardingRequest(
            contractOffboardingId = contractOffboardingId,
            lastWorkingDay = "2024-01-30"
        )

        every { contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId)) } returns listOf(
            mockContractOffboarding
        )
        every { contractOffboardingServiceAdapter.rescheduleOffboarding(rescheduleRequest) } returns mockUpdateContractOffboarding

        testHandler.handleContractOffboardingInitialisation(mockEventData, contractId)
        verify(exactly = 1) { contractOffboardingServiceAdapter.rescheduleOffboarding(rescheduleRequest) }
    }

    @Test
    fun `should init contract offboarding when there not exists contract offboarding`() {
        val contractId = 1L
        val mockUpdateContractOffboarding = getMockContractOffboarding(contractId, LocalDate.of(2024, 1, 30))
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "INACTIVE",
            terminationDate = "2024-01-30T00:00:00Z"
        )
        val initRequest = ContractOffBoardingRequest(
            contractId = contractId,
            lastWorkingDay = "2024-01-30",
            terminationReason = "Terminated by external platform"
        )

        every { contractOffboardingServiceAdapter.getContractOffboardings(listOf(contractId)) } returns emptyList()
        every { contractOffboardingServiceAdapter.initialiseResignationOffboarding(initRequest) } returns mockUpdateContractOffboarding

        testHandler.handleContractOffboardingInitialisation(mockEventData, contractId)
        verify(exactly = 1) { contractOffboardingServiceAdapter.initialiseResignationOffboarding(initRequest) }
    }
}