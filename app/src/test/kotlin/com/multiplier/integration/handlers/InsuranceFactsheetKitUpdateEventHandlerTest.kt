package com.multiplier.integration.handlers

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.BenefitServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.handlers.contract.InsuranceFactsheetKitUpdateEventHandler
import com.multiplier.integration.mock.getBenefitDocumentResponse
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getFailedEvents
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class InsuranceFactsheetKitUpdateEventHandlerTest{
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @MockK
    lateinit var benefitServiceAdapter: BenefitServiceAdapter

    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter

    @InjectMockKs
    lateinit var testHandler: InsuranceFactsheetKitUpdateEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle insurance factsheet update event`() {
        var eventLogId = "Test"
        val eventLog: JpaEventLog =
            getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } returns getMockContract(100, 1, status = ContractOuterClass.ContractStatus.DELETED)
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        every { employeeService.markEmployeeAsInactive(any(),any()) } returns listOf(Unit)
        every { benefitServiceAdapter.getContractBenefitDocumentsByContractId(100) } returns getBenefitDocumentResponse()
        every { docgenServiceAdapter.getDocument(1) } returns getDocumentResponse()
        every { employeeService.updateFactsheetDocument(any(),any(),any()) } returns Unit

        testHandler.handleInsuranceFactsheetUpdateEvent(eventLogId)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }
    }

    @Test
    fun `should handle downstream error in factsheet update event`() {
        var eventLogId = "Test"
        val eventLog: JpaEventLog =
            getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit

        testHandler.handleInsuranceFactsheetUpdateEvent(eventLogId)

        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle UnsupportedOperationException in factsheet update event`() {
        var eventLogId = "Test"
        val eventLog: JpaEventLog =
            getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) }  returns Unit
        every { benefitServiceAdapter.getContractBenefitDocumentsByContractId(100) } returns getBenefitDocumentResponse()
        every { docgenServiceAdapter.getDocument(1) } returns getDocumentResponse()
        every { employeeService.updateFactsheetDocument(any(),any(),any()) } throws UnsupportedOperationException("Unsupported operation")
        every { contractServiceAdapter.findContractByContractId(any()) } returns getMockContract(100, 1)
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleInsuranceFactsheetUpdateEvent(eventLogId)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle IllegalStateException in factsheet update event`() {
        var eventLogId = "Test"
        val eventLog: JpaEventLog =
            getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { benefitServiceAdapter.getContractBenefitDocumentsByContractId(100) } returns getBenefitDocumentResponse()
        every { docgenServiceAdapter.getDocument(1) } returns getDocumentResponse()
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationIllegalStateException("Illegal state")
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleInsuranceFactsheetUpdateEvent(eventLogId)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }
}