package com.multiplier.integration.handlers

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractOffboardingStatusUpdateEvent
import com.multiplier.integration.handlers.contract.ContractOffboardingStatusUpdateEventHandler
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class ContractOffboardingStatusUpdateEventHandlerTest {
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @InjectMockKs
    lateinit var testHandler: ContractOffboardingStatusUpdateEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }


    @Test
    fun `should handle contract offboarding status update event`() {
        var event  = ContractOffboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100,1)
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        testHandler.handleContractOffboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }
    }

    @Test
    fun `should handle downstream error in offboarding status update event`() {
        var event  = ContractOffboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } throws IntegrationDownstreamException("Test")
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit

        testHandler.handleContractOffboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle UnsupportedOperationException in offboarding status update event`() {
        var event  = ContractOffboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { employeeService.terminateEmployee(any(),any(),any(),any(),any()) } throws UnsupportedOperationException("Unsupported operation")
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100,1,status = ContractOuterClass.ContractStatus.ENDED)
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleContractOffboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle IllegalStateException in offboarding status update event`() {
        var event  = ContractOffboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } throws IntegrationIllegalStateException("Illegal state")
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleContractOffboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

}
