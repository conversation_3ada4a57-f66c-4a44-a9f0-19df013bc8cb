package com.multiplier.integration.handlers

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractCompensationUpdateEvent
import com.multiplier.integration.handlers.contract.CompensationCreationEventHandler
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getFailedEvents
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class CompensationCreationEventHandlerTest {
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @InjectMockKs
    lateinit var testHandler: CompensationCreationEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle compensation creation event`() {
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(100) } returns getMockContract(100,1)
        every { employeeService.updateEmployeeCompensation(1,100,any()) } returns Unit
        every { eventLogService.markEventSuccessful(eventLog) } returns Unit

        testHandler.handleCompensationCreationEvent(eventLogId)

        verify(exactly = 1) { employeeService.updateEmployeeCompensation(any(),any(),any()) }
    }

    @Test
    fun `should handle downstream error compensation creation event`() {
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(100) } throws IntegrationDownstreamException("Test")
        every { employeeService.updateEmployeeCompensation(1,100,any()) } returns Unit
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit

        testHandler.handleCompensationCreationEvent(eventLogId)

        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle UnsupportedOperationException in compensation creation event`() {
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(100) } returns  getMockContract(100,1)
        every { employeeService.updateEmployeeCompensation(1,100,any()) } throws UnsupportedOperationException()
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleCompensationCreationEvent(eventLogId)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle IllegalStateException in compensation creation event`() {
        val eventLogId = "Test"
        val eventLog: JpaEventLog = getFailedEvents(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId(eventLogId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(100) } throws IntegrationIllegalStateException("Test")
        every { employeeService.updateEmployeeCompensation(1,100,any()) } returns Unit
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit

        testHandler.handleCompensationCreationEvent(eventLogId)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle compensation update event`() {
        var event  = ContractCompensationUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(100) } returns getMockContract(100,1)
        every { employeeService.updateEmployeeCompensation(1,100,any()) } returns Unit
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100,1)
        every { employeeService.updateEmployeeCompensation(1,0,any()) } returns Unit
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        testHandler.handleCompensationUpdationEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }
    }

    @Test
    fun `should handle downstream error compensation update event`() {
        var event  = ContractCompensationUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")
        every { employeeService.updateEmployeeCompensation(1,100,any()) } returns Unit
        every { eventLogService.handleRetryableEvent(any(),any()) } returns Unit
        every { employeeService.updateEmployeeCompensation(1,0,any()) } returns Unit
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        testHandler.handleCompensationUpdationEvent(event)

        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle UnsupportedOperationException in compensation update event`() {
        var event  = ContractCompensationUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } returns getMockContract(100,1)
        every { employeeService.updateEmployeeCompensation(any(),any(),any()) } throws UnsupportedOperationException("Unsupported operation")
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        testHandler.handleCompensationUpdationEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle IllegalStateException in compensation update event`() {
        var event  = ContractCompensationUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog = getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationIllegalStateException("Illegal state")
        every { employeeService.updateEmployeeCompensation(1,100,any()) } returns Unit
        every { eventLogService.handleNonRetryableEvent(any(),any()) } returns Unit
        every { employeeService.updateEmployeeCompensation(1,0,any()) } returns Unit
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        testHandler.handleCompensationUpdationEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }
}
