package com.multiplier.integration.handlers

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.core.model.ContractStartedEvent
import com.multiplier.integration.handlers.contract.ContractStartedEventHandler
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class ContractStartedEventHandlerTest {
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @InjectMockKs
    lateinit var testHandler: ContractStartedEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle contract started event`() {
        var event = ContractStartedEvent(eventLogId = "Test")
        val eventLog: JpaEventLog =
            getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100, 1)
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        every { employeeService.markEmployeeAsActive(any(),any()) } returns listOf(Unit)

        testHandler.handleContractStartedEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }

        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")

        testHandler.handleContractStartedEvent(event)
        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }
}
