package com.multiplier.integration.handlers

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.core.model.ContractSalaryDocumentUpdateEvent
import com.multiplier.integration.handlers.contract.SalaryReviewDocumentEventHandler
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class SalaryReviewDocumentEventHandlerTest{

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter

    @InjectMockKs
    lateinit var testHandler: SalaryReviewDocumentEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle salary review document update event`() {
        var event = ContractSalaryDocumentUpdateEvent("Test")
        val eventLog: JpaEventLog =
            getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } returns getMockContract(100, 1, status = ContractOuterClass.ContractStatus.DELETED)
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        every { docgenServiceAdapter.getDocument(0) } returns getDocumentResponse()
        every{employeeService.updateSalaryReviewDocument(any(), any(), any()) } returns Unit

        testHandler.handleSalaryReviewDocumentUpdateEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }
    }

    @Test
    fun `should handle downstream error on the event`() {
        var event = ContractSalaryDocumentUpdateEvent("Test")
        val eventLog: JpaEventLog =
            getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")
        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { docgenServiceAdapter.getDocument(0) } returns getDocumentResponse()

        testHandler.handleSalaryReviewDocumentUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }
}