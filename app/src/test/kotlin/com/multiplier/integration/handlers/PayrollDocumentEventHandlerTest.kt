package com.multiplier.integration.handlers

import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.core.model.PayrollPayslipDocumentPublishedEvent
import com.multiplier.integration.core.model.PayrollPayslipDocumentUploadedEvent
import com.multiplier.integration.handlers.contract.PayrollDocumentEventHandler
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class PayrollDocumentEventHandlerTest{

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter

    @InjectMockKs
    lateinit var testHandler: PayrollDocumentEventHandler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle upload payslip event`() {
        var event = PayrollPayslipDocumentUploadedEvent("Test")
        val eventLog: JpaEventLog =
            getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } returns getMockContract(100, 1, status = ContractOuterClass.ContractStatus.DELETED)
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        every { docgenServiceAdapter.getDocument(0) } returns getDocumentResponse()
        every{employeeService.uploadPayslipDocument(any(), any(), any()) } returns Unit
        testHandler.handleUploadPayslipEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }

        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")

        testHandler.handleUploadPayslipEvent(event)
        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle publish payslip event`() {
        var event = PayrollPayslipDocumentPublishedEvent("Test")
        val eventLog: JpaEventLog =
            getEventsWithEmptyPayload(EventType.INCOMING_PAYROLL_PAYSLIP_UPLOADED).get(0)

        val contract = getMockContract(100, 1, status = ContractOuterClass.ContractStatus.DELETED)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } returns contract
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        every { docgenServiceAdapter.getDocument(0) } returns getDocumentResponse()
        every{employeeService.uploadPayslipDocument(any(), any(), any()) } returns Unit
        testHandler.handlePublishPayslipEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }

        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")

        testHandler.handlePublishPayslipEvent(event)
        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }
}
