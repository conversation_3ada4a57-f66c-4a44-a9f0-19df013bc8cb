package com.multiplier.integration.handlers

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.core.model.ContractOnboardingStatusUpdateEvent
import com.multiplier.integration.handlers.contract.ContractOnboardingStatusUpdateEventHandler
import com.multiplier.integration.mock.getEventsWithEmptyPayload
import com.multiplier.integration.mock.getEventsWithExperiencePayload
import com.multiplier.integration.mock.getGetOnboardingStatusResponse
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class ContractOnboardingStatusUpdateEventHandlerTest {
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService
    @MockK
    lateinit var eventLogRepository: EventLogRepository
    @InjectMockKs
    lateinit var testHandler: ContractOnboardingStatusUpdateEventHandler

    @MockK
    lateinit var notificationsService: NotificationsService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle contract onboarding status update event`() {
        var event = ContractOnboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog =
            getEventsWithExperiencePayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100, 1)
        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every {
            contractServiceAdapter.findOnboardingByContractIdAndExperience(
                any(),
                any()
            )
        } returns getGetOnboardingStatusResponse()
        every { memberServiceAdapter.findMemberByMemberId(any()) } returns getMockMember(1)
        every { employeeService.checkIfEmployeeAlreadyExistedInCache(any()) } returns false
        every { eventLogService.markEventSuccessful(any()) } returns Unit
        every { employeeService.isTriNetPlatform(any()) } returns false
        every { employeeService.createEmployee(any(), any(), any(), any(), any()) } returns Unit
        every { eventLogRepository.save(any()) } returns eventLog
        every { eventLogService.findEventLogByEventId(any()) } returns eventLog
        every {
            eventLogService.createEventLog(any(), any(), any(), any(), any(), any(), any())
        } returns getEventsWithEmptyPayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)
        testHandler.handleContractOnboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }

        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")

        testHandler.handleContractOnboardingStatusUpdateEvent(event)
        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle UnsupportedOperationException in onboarding status update event`() {
        val event = ContractOnboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog =
            getEventsWithExperiencePayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId(any()) } returns eventLog
        every { eventLogRepository.save(any()) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every {
            contractServiceAdapter.findOnboardingByContractIdAndExperience(
                any(),
                any()
            )
        } returns getGetOnboardingStatusResponse()
        every { memberServiceAdapter.findMemberByMemberId(any()) } returns getMockMember(1)
        every { employeeService.createEmployee(any(), any(), any(), any(), any()) } throws UnsupportedOperationException("Unsupported operation")
        every { employeeService.checkIfEmployeeAlreadyExistedInCache(any()) } returns false
        every { contractServiceAdapter.findContractByContractId(0) } returns getMockContract(100, 1)
        every { eventLogService.handleNonRetryableEvent(any(), any()) } returns Unit

        testHandler.handleContractOnboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle IllegalStateException in onboarding status update event`() {
        val event = ContractOnboardingStatusUpdateEvent(eventLogId = "Test")
        val eventLog: JpaEventLog =
            getEventsWithExperiencePayload(EventType.SERVICE_INTERNAL_CREATE_INSURANCE_ONBOARDING_KIT).get(0)

        every { eventLogService.findEventLogByEventId("Test") } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { contractServiceAdapter.findContractByContractId(0) } throws IntegrationIllegalStateException("Illegal state")
        every { eventLogService.handleNonRetryableEvent(any(), any()) } returns Unit

        testHandler.handleContractOnboardingStatusUpdateEvent(event)

        verify(exactly = 1) { eventLogService.handleNonRetryableEvent(any(), any()) }
    }
}
