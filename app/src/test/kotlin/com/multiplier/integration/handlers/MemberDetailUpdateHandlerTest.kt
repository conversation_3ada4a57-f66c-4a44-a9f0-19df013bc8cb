package com.multiplier.integration.handlers

import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.core.model.MemberBasicDetailUpdateEvent
import com.multiplier.integration.handlers.member.MemberDetailUpdateHandler
import com.multiplier.integration.mock.getContractWorkEmailUpdateEvent
import com.multiplier.integration.mock.getMemberDetailUpdateEvent
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class MemberDetailUpdateHandlerTest {
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var eventLogService: EventLogService

    @MockK
    lateinit var eventLogRepository: EventLogRepository

    @InjectMockKs
    lateinit var testHandler: MemberDetailUpdateHandler

    @MockK
    lateinit var notificationsService: NotificationsService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should handle member basic detail update event`() {
        val mockEventId = "ev_5t0qc2z7kVRbaJH5UL4aAg"
        val mockContractId = 1L
        val mockMemberId = 2L
        val event = MemberBasicDetailUpdateEvent(eventLogId = mockEventId)
        val eventLog: JpaEventLog =
            getMemberDetailUpdateEvent(mockEventId, mockContractId, mockMemberId)
        val mockMember = getMockMember(mockMemberId)
        val mockContract = getMockContract(mockContractId, mockMemberId)

        every { eventLogService.findEventLogByEventId(mockEventId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { employeeService.getEmployeeDataOrigin(mockContractId) } returns Constants.EmployeeOrigin.INTERNAL
        every { contractServiceAdapter.findContractByContractId(mockContractId) } returns mockContract
        every { memberServiceAdapter.findMemberByMemberId(mockMemberId) } returns mockMember
        every { eventLogRepository.save(any()) } returns eventLog
        every { employeeService.updateEmployee(mockContract.companyId, null, mockMember, mockContract, eventLog) } returns Unit
        every { eventLogService.markEventSuccessful(eventLog) } returns Unit

        testHandler.handleBasicDetailsUpdate(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }

        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")

        testHandler.handleBasicDetailsUpdate(event)
        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }

    @Test
    fun `should handle contract work email update event`() {
        val mockEventId = "ev_5t0qc2z7kVRbaJH5UL4aAg"
        val mockContractId = 1L
        val mockMemberId = 2L
        val event = MemberBasicDetailUpdateEvent(eventLogId = mockEventId)
        val eventLog: JpaEventLog =
            getContractWorkEmailUpdateEvent(mockEventId, mockContractId)
        val mockMember = getMockMember(mockMemberId)
        val mockContract = getMockContract(mockContractId, mockMemberId)

        every { eventLogService.findEventLogByEventId(mockEventId) } returns eventLog
        every { eventLogService.processEvent(eventLog) } returns Unit
        every { employeeService.getEmployeeDataOrigin(mockContractId) } returns Constants.EmployeeOrigin.INTERNAL
        every { contractServiceAdapter.findContractByContractId(mockContractId) } returns mockContract
        every { memberServiceAdapter.findMemberByMemberId(mockMemberId) } returns mockMember
        every { eventLogRepository.save(any()) } returns eventLog
        every { employeeService.updateEmployee(mockContract.companyId, null, mockMember, mockContract, eventLog) } returns Unit
        every { eventLogService.markEventSuccessful(eventLog) } returns Unit

        testHandler.handleBasicDetailsUpdate(event)

        verify(exactly = 1) { eventLogService.markEventSuccessful(any()) }

        every { eventLogService.handleRetryableEvent(any(), any()) } returns Unit
        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")

        testHandler.handleBasicDetailsUpdate(event)
        verify(exactly = 1) { eventLogService.handleRetryableEvent(any(), any()) }
    }
}
