package com.multiplier.integration.listeners

import com.multiplier.expense.schema.ExpenseEventMessageOuterClass
import com.multiplier.integration.service.ExpenseProcessorService
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.messaging.MessageHeaders
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class ExpenseEventListenerTest {
    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var expenseProcessorService: ExpenseProcessorService

    @InjectMockKs
    lateinit var expenseEventListener: ExpenseEventListener

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should receive paid expense event and process successfully`() = testScope.runTest {
        val expenseId = 1L
        val mockMessage = ExpenseEventMessageOuterClass.ExpenseEventMessage.newBuilder()
            .setEventType(ExpenseEventMessageOuterClass.ExpenseEventType.EXPENSE_PAID)
            .setEvent(ExpenseEventMessageOuterClass.ExpenseEvent.newBuilder()
                .setExpenseId(expenseId)
                .build())
            .build()
        val mockHeader = MessageHeaders(mapOf())

        coEvery { expenseProcessorService.processPaidExpenseById(expenseId) } returns Unit

        expenseEventListener.onExpenseStatusChange(mockMessage, mockHeader)

        coVerify(exactly = 1) { expenseProcessorService.processPaidExpenseById(expenseId) }
    }

    @Test
    fun `should receive unpaid expense event and not process event`() = testScope.runTest {
        val expenseId = 1L
        val mockMessage = ExpenseEventMessageOuterClass.ExpenseEventMessage.newBuilder()
            .setEventType(ExpenseEventMessageOuterClass.ExpenseEventType.EXPENSE_APPROVED)
            .setEvent(ExpenseEventMessageOuterClass.ExpenseEvent.newBuilder()
                .setExpenseId(expenseId)
                .build())
            .build()
        val mockHeader = MessageHeaders(mapOf())

        expenseEventListener.onExpenseStatusChange(mockMessage, mockHeader)

        coVerify(exactly = 0) { expenseProcessorService.processPaidExpenseById(expenseId) }
    }
}