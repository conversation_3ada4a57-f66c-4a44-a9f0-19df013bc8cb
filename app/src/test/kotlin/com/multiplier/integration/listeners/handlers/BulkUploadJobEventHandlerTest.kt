package com.multiplier.integration.listeners.handlers

import com.multiplier.bulk.upload.schema.kafka.BulkUploadJob
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobEventType
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobMessage
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobStatus
import com.multiplier.integration.adapter.api.CUSTOMER_INTEGRATION_SERVICE_GROUP
import com.multiplier.integration.repository.model.BulkJobStatus
import com.multiplier.integration.repository.model.JpaBulkJobTracker
import com.multiplier.integration.repository.model.URIType
import com.multiplier.integration.service.BulkJobTrackerService
import com.multiplier.integration.service.BulkModule
import com.multiplier.integration.service.IntegrationOrchestrator
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkUploadJobEventHandlerTest {

    @MockK
    private lateinit var integrationOrchestrator: IntegrationOrchestrator

    @MockK
    private lateinit var bulkJobTrackerService: BulkJobTrackerService

    @InjectMockKs
    private lateinit var handler: BulkUploadJobEventHandler

    @Nested
    inner class MessageValidation {
        @Test
        fun `should ignore message when group is not CUSTOMER_INTEGRATION_SERVICE_GROUP`() {
            // given
            val message = createMessage(
                group = "DIFFERENT_GROUP",
                eventType = BulkUploadJobEventType.BULK_UPLOAD_JOB_UPDATED
            )

            // when
            handler.handle(message)

            // then
            verify(exactly = 0) { bulkJobTrackerService.findByJobIdOrThrow(any()) }
        }

        @Test
        fun `should ignore message when event type is not BULK_UPLOAD_JOB_UPDATED`() {
            // given
            val message = createMessage(
                group = CUSTOMER_INTEGRATION_SERVICE_GROUP,
                eventType = BulkUploadJobEventType.BULK_UPLOAD_JOB_CREATED
            )

            // when
            handler.handle(message)

            // then
            verify(exactly = 0) { bulkJobTrackerService.findByJobIdOrThrow(any()) }
        }
    }

    @Nested
    inner class ValidationFailureHandling {

        @Test
        fun `should handle validation failure when job tracker status matches`() {
            // given
            val jobId = 11L
            val message = createMessage(
                id = jobId,
                status = BulkUploadJobStatus.VALIDATION_FAILED
            )
            val bulkJobTracker = createBulkJobTracker(
                jobId = jobId,
                status = BulkJobStatus.VALIDATION_IN_PROGRESS, // The status must be VALIDATION_IN_PROGRESS
                                                               // Otherwise, we consider it as invalid state
            )
            every { bulkJobTrackerService.findByJobIdOrThrow(jobId) } returns bulkJobTracker
            every { integrationOrchestrator.handleBulkUploadJobValidationFailure(any(), any()) } just runs

            // when
            handler.handle(message)

            // then
            verify(exactly = 1) {
                integrationOrchestrator.handleBulkUploadJobValidationFailure(jobId, URIType.SFTP)
            }
        }

        @Test
        fun `should handle validation failure when job tracker status does not match`() {
            // given
            val jobId = 11L
            val message = createMessage(
                id = jobId,
                status = BulkUploadJobStatus.VALIDATION_FAILED
            )
            val bulkJobTracker = createBulkJobTracker(
                jobId = jobId,
                status = BulkJobStatus.VALIDATION_FAILED, // The status must be VALIDATION_IN_PROGRESS
                                                          // Otherwise, we consider it as invalid state
            )
            every { bulkJobTrackerService.findByJobIdOrThrow(jobId) } returns bulkJobTracker
            every { integrationOrchestrator.handleBulkUploadJobValidationFailure(any(), any()) } just runs

            // when
            handler.handle(message)

            // then
            verify(exactly = 0) {
                integrationOrchestrator.handleBulkUploadJobValidationFailure(any(), any())
            }
        }
    }

    @Nested
    inner class ValidationSuccessHandling {

        @Test
        fun `should handle validation success when job tracker status matches`() {
            // given
            val jobId = 11L
            val message = createMessage(
                id = jobId,
                status = BulkUploadJobStatus.VALIDATION_SUCCESS
            )
            val bulkJobTracker = createBulkJobTracker(
                jobId = jobId,
                status = BulkJobStatus.VALIDATION_IN_PROGRESS, // The status must be VALIDATION_IN_PROGRESS
                                                               // Otherwise, we consider it as invalid state
            )
            every { bulkJobTrackerService.findByJobIdOrThrow(jobId) } returns bulkJobTracker
            every { integrationOrchestrator.handleBulkUploadJobValidationSuccess(any(), any()) } just runs

            // when
            handler.handle(message)

            // then
            verify(exactly = 1) {
                integrationOrchestrator.handleBulkUploadJobValidationSuccess(jobId, URIType.SFTP)
            }
        }

        @Test
        fun `should handle validation success when job tracker status does not match`() {
            // given
            val jobId = 11L
            val message = createMessage(
                id = jobId,
                status = BulkUploadJobStatus.VALIDATION_SUCCESS
            )
            val bulkJobTracker = createBulkJobTracker(
                jobId = jobId,
                status = BulkJobStatus.UPSERT_IN_PROGRESS, // The status must be VALIDATION_IN_PROGRESS
                                                           // Otherwise, we consider it as invalid state
            )
            every { bulkJobTrackerService.findByJobIdOrThrow(jobId) } returns bulkJobTracker
            every { integrationOrchestrator.handleBulkUploadJobValidationSuccess(any(), any()) } just runs

            // when
            handler.handle(message)

            // then
            verify(exactly = 0) {
                integrationOrchestrator.handleBulkUploadJobValidationSuccess(jobId, URIType.SFTP)
            }
        }
    }

    @Nested
    inner class DataCreationSuccessHandling {

        @Test
        fun `should handle data creation success when job tracker status matches`() {
            // given
            val jobId = 11L
            val message = createMessage(
                id = jobId,
                status = BulkUploadJobStatus.DATA_CREATION_SUCCESS
            )
            val bulkJobTracker = createBulkJobTracker(
                jobId = jobId,
                status = BulkJobStatus.UPSERT_IN_PROGRESS, // The status must be UPSERT_IN_PROGRESS
                                                           // Otherwise, we consider it as invalid state
            )
            every { bulkJobTrackerService.findByJobIdOrThrow(jobId) } returns bulkJobTracker
            every { integrationOrchestrator.handleBulkUploadJobDataCreationSuccess(any(), any()) } just runs

            // when
            handler.handle(message)

            // then
            verify(exactly = 1) {
                integrationOrchestrator.handleBulkUploadJobDataCreationSuccess(jobId, URIType.SFTP)
            }
        }

        @Test
        fun `should handle data creation success when job tracker status does not match`() {
            // given
            val jobId = 11L
            val message = createMessage(
                id = jobId,
                status = BulkUploadJobStatus.DATA_CREATION_SUCCESS
            )
            val bulkJobTracker = createBulkJobTracker(
                jobId = jobId,
                status = BulkJobStatus.UPSERT_SUCCESSFUL, // The status must be UPSERT_IN_PROGRESS
                                                          // Otherwise, we consider it as invalid state
            )
            every { bulkJobTrackerService.findByJobIdOrThrow(jobId) } returns bulkJobTracker
            every { integrationOrchestrator.handleBulkUploadJobDataCreationSuccess(any(), any()) } just runs

            // when
            handler.handle(message)

            // then
            verify(exactly = 0) {
                integrationOrchestrator.handleBulkUploadJobDataCreationSuccess(jobId, URIType.SFTP)
            }
        }
    }

    private fun createMessage(
        id: Long = 1L,
        group: String = CUSTOMER_INTEGRATION_SERVICE_GROUP,
        status: BulkUploadJobStatus = BulkUploadJobStatus.VALIDATION_SUCCESS,
        eventType: BulkUploadJobEventType = BulkUploadJobEventType.BULK_UPLOAD_JOB_UPDATED
    ) = BulkUploadJobMessage.newBuilder()
        .setEventType(eventType)
        .setPayload(
            BulkUploadJob.newBuilder()
                .setId(id)
                .setGroup(group)
                .setStatus(status)
                .build()
        )
        .build()

    private fun createBulkJobTracker(
        jobId: Long,
        status: BulkJobStatus,
    ) = JpaBulkJobTracker(
        id = jobId,
        jobId = jobId,
        jobStatus = status,
        companyId = 2,
        entityId = 3,
        groupName = CUSTOMER_INTEGRATION_SERVICE_GROUP,
        moduleNames = setOf(BulkModule.TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA.name),
        originalURIType = URIType.SFTP,

    )
}