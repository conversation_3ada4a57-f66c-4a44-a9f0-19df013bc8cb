//package com.multiplier.integration.listeners
//
//import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventType.*
//import com.multiplier.contract.schema.contract.ContractOuterClass
//import com.multiplier.integration.*
//import com.multiplier.integration.adapter.api.CompanyServiceAdapter
//import com.multiplier.integration.adapter.api.ContractServiceAdapter
//import com.multiplier.integration.adapter.api.MergeDevAdapter
//import com.multiplier.integration.repository.PlatformRepository
//import com.multiplier.integration.service.CustomerIntegrationService
//import com.multiplier.integration.service.EventLogService
//import com.multiplier.integration.types.PlatformCategory
//import com.ninjasquad.springmockk.MockkBean
//import com.ninjasquad.springmockk.SpykBean
//import io.mockk.*
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.messaging.MessageHeaders
//import org.springframework.test.context.ActiveProfiles
//
//@ActiveProfiles("test")
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//class ContractEventListenerTest {
//
//   @MockkBean private lateinit var contractServiceAdapter: ContractServiceAdapter
//
//   @MockkBean private lateinit var companyServiceAdapter: CompanyServiceAdapter
//
//   @SpykBean private lateinit var mergeDevAdapter: MergeDevAdapter
//
//   @MockkBean private lateinit var customerIntegrationService: CustomerIntegrationService
//   @MockkBean private lateinit var eventLogService: EventLogService
//
//   @MockkBean private lateinit var platformRepository: PlatformRepository
//
//   @Autowired private lateinit var contractEventListener: ContractEventListener
//
//   @Test
//   fun `should mark employee as INACTIVE when contract is deleted`() {
//       every { eventLogService.createEventLog(any(), any(), any(), any()) } returns mockEventLog()
//       every { customerIntegrationService.isBambooHRIntegrationEnabled(any()) } returns true
//       every { contractServiceAdapter.findContractByContractId(any()) } returns
//           mockGrpcContract(status = ContractOuterClass.ContractStatus.DELETED)
//       every { companyServiceAdapter.findCompanyByCompanyId(any()) } returns mockGrpcCompany()
//       every {
//           platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR")
//       } returns mockPlatform(1, "BambooHR")
//       every { platformRepository.findByNameIn(setOf("BambooHR")) } returns
//           listOf(mockPlatform(1, "BambooHR"))
//       every { eventLogService.save(any()) } just Runs
//       coEvery { mergeDevAdapter.updateEmployeeFields(any(), any(), any(), any()) } returns
//           mockRemoteResponse()
//
//       contractEventListener.onExternalEventFromContractTopic(
//           mockKafkaContractEventMessage(eventType = CONTRACT_STATUS_UPDATE),
//           MessageHeaders(mapOf()))
//
//       coVerify { mergeDevAdapter.updateEmployeeFields(any(), any(), any(), any()) }
//   }
//}
