//package com.multiplier.integration.listeners
//
//import com.multiplier.integration.*
//import com.multiplier.integration.adapter.api.ContractServiceAdapter
//import com.multiplier.integration.adapter.api.MemberServiceAdapter
//import com.multiplier.integration.adapter.api.MergeDevAdapter
//import com.multiplier.integration.repository.PlatformRepository
//import com.multiplier.integration.service.EventLogService
//import com.multiplier.integration.types.PlatformCategory
//import com.multiplier.member.schema.MemberUpdateMessage
//import com.ninjasquad.springmockk.MockkBean
//import com.ninjasquad.springmockk.SpykBean
//import io.mockk.*
//import org.junit.jupiter.api.Assertions.assertEquals
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.messaging.MessageHeaders
//import org.springframework.test.context.ActiveProfiles
//
//@ActiveProfiles("test")
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//class MemberUpdateEventListenerTest {
//
//    @MockkBean private lateinit var contractServiceAdapter: ContractServiceAdapter
//
//    @MockkBean private lateinit var memberServiceAdapter: MemberServiceAdapter
//
//    @SpykBean private lateinit var mergeDevAdapter: MergeDevAdapter
//
//    @MockkBean private lateinit var platformRepository: PlatformRepository
//
//    @Autowired private lateinit var memberUpdateEventListener: MemberUpdateEventListener
//
//    @MockkBean private lateinit var eventLogService: EventLogService
//
//    @Test
//    fun `should update member basic details when member changes are approved`() {
//        every { eventLogService.createEventLog(any(), any(), any(), any()) } returns mockEventLog()
//        every { eventLogService.save(any()) } returns Unit
//        every { memberServiceAdapter.findMemberByMemberId(any()) } returns mockGrpcMember()
//        every { contractServiceAdapter.findContractByMemberId(any()) } returns mockGrpcContract()
//        every { platformRepository.findByNameIn(setOf("BambooHR", "Hibob", "Workday")) } returns
//            listOf(mockPlatform(1, "BambooHR"), mockPlatform(2, "Hibob"), mockPlatform(3, "Workday"))
//        every {
//            platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR")
//        } returns mockPlatform(1, "BambooHR")
//
//        val updatedFields = slot<Map<String, String>>()
//        coEvery {
//            mergeDevAdapter.updateEmployeeFields(any(), any(), any(), capture(updatedFields))
//        } returns mockRemoteResponse()
//
//        memberUpdateEventListener.onExternalEventFromMemberTopic(
//            mockGrpcMemberEventMessage(
//                type = MemberUpdateMessage.MemberUpdateEventType.BASIC_DETAIL_UPDATE,
//            ),
//            MessageHeaders(mapOf()))
//
//        coVerify { mergeDevAdapter.updateEmployeeFields(any(), any(), any(), any()) }
//        assertEquals(4, updatedFields.captured.size)
//    }
//
//    @Test
//    fun `should update member contact details when member changes are approved`() {
//        every { eventLogService.createEventLog(any(), any(), any(), any()) } returns mockEventLog()
//        every { eventLogService.save(any()) } returns Unit
//        every { memberServiceAdapter.findMemberByMemberId(any()) } returns mockGrpcMember()
//        every { contractServiceAdapter.findContractByMemberId(any()) } returns mockGrpcContract()
//        every { platformRepository.findByNameIn(setOf("BambooHR", "Hibob", "Workday")) } returns
//            listOf(mockPlatform(1, "BambooHR"), mockPlatform(2, "Hibob"), mockPlatform(3, "Workday"))
//        every {
//            platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR")
//        } returns mockPlatform(1, "BambooHR")
//
//        val updatedFields = slot<Map<String, String>>()
//        coEvery {
//            mergeDevAdapter.updateEmployeeFields(any(), any(), any(), capture(updatedFields))
//        } returns mockRemoteResponse()
//
//        memberUpdateEventListener.onExternalEventFromMemberTopic(
//            mockGrpcMemberEventMessage(
//                type = MemberUpdateMessage.MemberUpdateEventType.CONTACT_DETAIL_UPDATE,
//            ),
//            MessageHeaders(mapOf()))
//
//        coVerify { mergeDevAdapter.updateEmployeeFields(any(), any(), any(), any()) }
//        assertEquals(6, updatedFields.captured.size)
//    }
//}
