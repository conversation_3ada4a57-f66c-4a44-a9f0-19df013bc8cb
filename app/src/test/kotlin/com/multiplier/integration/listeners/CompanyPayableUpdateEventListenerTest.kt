package com.multiplier.integration.listeners

import com.google.protobuf.Timestamp
import com.multiplier.integration.mock.getEventsWithPayablePayload
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.payable.kafka.schema.CompanyPayableEvent
import com.multiplier.payable.kafka.schema.CompanyPayableStatus
import com.multiplier.payable.kafka.schema.EventType
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.messaging.MessageHeaders
import org.springframework.test.context.junit.jupiter.SpringExtension


@ExtendWith(SpringExtension::class)
class CompanyPayableUpdateEventListenerTest {
    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var eventLogService: EventLogService

    @MockK
    lateinit var featureFlagService: FeatureFlagService

    @InjectMockKs
    lateinit var companyPayableUpdateEventListener: CompanyPayableUpdateEventListener

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should receive payable events and process successfully`() =
        testScope.runTest {
            val timestamp: Timestamp = Timestamp.newBuilder()
                .setSeconds(1672531200) // Represents the timestamp (seconds since epoch)
                .build()
            val mockEvent = CompanyPayableEvent.newBuilder()
                .setEventType(EventType.STATUS_UPDATED)
                .setEventId("id")
                .setCompanyPayableId(1)
                .setCompanyId(1)
                .setStatus(CompanyPayableStatus.AUTHORIZED)
                .setTimestamp(timestamp)
                .build()

            val mockHeader = MessageHeaders(mapOf())

            val eventLog: JpaEventLog = getEventsWithPayablePayload().get(0)

            coEvery { eventLogService.createPayableEventLog(any(),any(),any(),any(),any(),any(),any()) } returns eventLog
            coEvery { featureFlagService.isOn(any(),any()) } returns true
            companyPayableUpdateEventListener.onCompanyPayableUpdateEvent(mockEvent, mockHeader)
            coVerify(exactly = 1) { eventLogService.createPayableEventLog(any(),any(),any(),any(),any(),any(),any()) }
        }

    @Test
    fun `should return if company is not in the feature flag`() =
        testScope.runTest {

            val timestamp: Timestamp = Timestamp.newBuilder()
                .setSeconds(1672531200) // Represents the timestamp (seconds since epoch)
                .build()
            val mockEvent = CompanyPayableEvent.newBuilder()
                .setEventType(EventType.STATUS_UPDATED)
                .setEventId("id")
                .setCompanyPayableId(1)
                .setCompanyId(1)
                .setStatus(CompanyPayableStatus.AUTHORIZED)
                .setTimestamp(timestamp)
                .build()

            val mockHeader = MessageHeaders(mapOf())


            coEvery { featureFlagService.isOn(any(),any()) } returns false
            companyPayableUpdateEventListener.onCompanyPayableUpdateEvent(mockEvent, mockHeader)
            coVerify(exactly = 0) { eventLogService.createPayableEventLog(any(),any(),any(),any(),any(),any(),any()) }

        }
}
