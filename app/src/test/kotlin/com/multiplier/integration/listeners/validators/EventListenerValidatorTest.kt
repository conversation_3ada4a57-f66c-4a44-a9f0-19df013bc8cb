package com.multiplier.integration.listeners.validators

import com.multiplier.contract.schema.onboarding.Onboarding
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.type.EventType
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.Test

@ExtendWith(SpringExtension::class)
class EventListenerValidatorTest {
    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @InjectMockKs
    lateinit var validator: EventListenerValidator

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    val payloadContract = """
        event_type: CONTRACT_WORK_EMAIL_CHANGED
        event {
          contract_id: 546378
          contract_status: ONBOARDING
        }
    """.trimIndent()

    val payloadOnboarding = """
        event {
          onboarding_id: 60822
          onboarding_status: MEMBER_VERIFICATION_COMPLETED
          onboarding_step: ONBOARDING_ACTIVATION
          contract_id: 546365
          experience: "company"
        }
    """.trimIndent()

    val payloadOnboardingInvalidStatus = """
        event {
          onboarding_id: 60822
          onboarding_status: CONTRACT_PREPARING
          onboarding_step: ONBOARDING_ACTIVATION
          contract_id: 546365
          experience: "company"
        }
    """.trimIndent()

    val payloadOnboardingInvalidExperience = """
        event {
          onboarding_id: 60822
          onboarding_status: MEMBER_VERIFICATION_COMPLETED
          onboarding_step: ONBOARDING_ACTIVATION
          contract_id: 546365
          experience: "individual"
        }
    """.trimIndent()

    @Test
    fun `validateContractRelatedEvent should return false when contractIntegration is null`() {
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) } returns null

        val result = validator.validateContractRelatedEvent(payloadContract, EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)

        assertFalse(result)
        verify { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) }
    }

    @Test
    fun `validateContractRelatedEvent should return true when contractIntegration is not null and outgoing sync is enabled`() {
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) } returns mockk(relaxed = true)
        every { contractServiceAdapter.findContractByContractId(any()) } returns mockk { every { companyId } returns 1L }
        every { companyIntegrationRepository.findByCompanyIdIn(any()) } returns listOf(mockk { every { outgoingSyncEnabled } returns true })

        val result = validator.validateContractRelatedEvent(payloadContract, EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)

        assertTrue(result)
        verify { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) }
    }

    @Test
    fun `validateContractRelatedEvent should return false when outgoing sync is not enabled`() {
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) } returns mockk(relaxed = true)
        every { contractServiceAdapter.findContractByContractId(any()) } returns mockk { every { companyId } returns 1L }
        every { companyIntegrationRepository.findByCompanyIdIn(any()) } returns emptyList()

        val result = validator.validateContractRelatedEvent(payloadContract, EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)

        assertFalse(result)
        verify { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) }
    }

    @Test
    fun `validateOnboardingEvent should return false when experience is not company`() {
        val result = validator.validateOnboardingEvent(payloadOnboardingInvalidExperience, EventType.INCOMING_ONBOARDING_STATUS_UPDATE)

        assertFalse(result)
    }

    @Test
    fun `validateOnboardingEvent should return false when onboarding status is not supported`() {
        every { contractServiceAdapter.findOnboardingByContractIdAndExperience(any(), any()) } returns mockk {
            every { status } returns Onboarding.ContractOnboardingStatus.CONTRACT_PREPARING
        }

        val result = validator.validateOnboardingEvent(payloadOnboardingInvalidStatus, EventType.INCOMING_ONBOARDING_STATUS_UPDATE)

        assertFalse(result)
    }

    @Test
    fun `validateOnboardingEvent should return true when onboarding status is supported and outgoing sync is enabled`() {
        every { contractServiceAdapter.findOnboardingByContractIdAndExperience(any(), any()) } returns mockk {
            every { status } returns Onboarding.ContractOnboardingStatus.ACTIVE
        }
        every { contractServiceAdapter.findContractByContractId(any()) } returns mockk { every { companyId } returns 1L }
        every { companyIntegrationRepository.findByCompanyIdIn(any()) } returns listOf(mockk { every { outgoingSyncEnabled } returns true })

        val result = validator.validateOnboardingEvent(payloadOnboarding, EventType.INCOMING_ONBOARDING_STATUS_UPDATE)

        assertTrue(result)
    }

    @Test
    fun `isOutgoingSyncEnabled should return true when outgoing sync is enabled`() {
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) } returns mockk(relaxed = true)
        every { contractServiceAdapter.findContractByContractId(any()) } returns mockk { every { companyId } returns 1L }
        every { companyIntegrationRepository.findByCompanyIdIn(any<Set<Long>>()) } returns listOf(mockk { every { outgoingSyncEnabled } returns true })

        val result = validator.validateContractRelatedEvent(payloadContract, EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)

        assertTrue(result)
    }

    @Test
    fun `isOutgoingSyncEnabled should return false when no outgoing sync is enabled`() {
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(any()) } returns mockk(relaxed = true)
        every { contractServiceAdapter.findContractByContractId(any()) } returns mockk { every { companyId } returns 1L }
        every { companyIntegrationRepository.findByCompanyIdIn(setOf(1L)) } returns listOf(mockk { every { outgoingSyncEnabled } returns false })

        val result = validator.validateContractRelatedEvent(payloadContract, EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)

        assertFalse(result)
    }

    @Test
    fun `validateOnboardingEvent should throw exception with invalid payload`() {
        val result = validator.validateOnboardingEvent("payload", EventType.INCOMING_ONBOARDING_STATUS_UPDATE)

        assertFalse(result)
    }

    @Test
    fun `validateContractRelatedEvent should throw exception with invalid payload`() {
        val result = validator.validateContractRelatedEvent("payload", EventType.INCOMING_CONTRACT_WORK_EMAIL_CHANGED)

        assertFalse(result)
    }
}