//package com.multiplier.integration.listeners
//
//import com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufSerializer
//import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage
//import com.multiplier.contract.kafka.compensation.SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage
//import com.multiplier.contract.kafka.onboarding.ContractOnboardingEventMessageOuterClass.*
//import com.multiplier.contract.kafka.onboarding.ContractOnboardingEventMessageOuterClass.ContractOnboardingEventType.*
//import com.multiplier.contract.schema.contract.ContractOuterClass
//import com.multiplier.integration.adapter.api.ContractServiceAdapter
//import com.multiplier.integration.core.model.ContractCompensationUpdateEvent
//import com.multiplier.integration.core.model.ContractOnboardingStatusUpdateEvent
//import com.multiplier.integration.handlers.contract.ContractCompensationUpdateEventHandler
//import com.multiplier.integration.handlers.contract.ContractOnboardingStatusUpdateEventHandler
//import java.util.concurrent.TimeUnit
//import org.apache.kafka.clients.producer.KafkaProducer
//import org.apache.kafka.clients.producer.ProducerConfig.*
//import org.apache.kafka.clients.producer.ProducerRecord
//import org.apache.kafka.common.serialization.StringSerializer
//import org.junit.jupiter.api.AfterAll
//import org.junit.jupiter.api.Assertions.assertEquals
//import org.junit.jupiter.api.BeforeAll
//import org.junit.jupiter.api.Test
//import org.junit.jupiter.api.TestInstance
//import org.mockito.kotlin.*
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import org.springframework.boot.test.mock.mockito.MockBean
//import org.springframework.kafka.test.EmbeddedKafkaBroker
//import org.springframework.kafka.test.context.EmbeddedKafka
//import org.springframework.test.annotation.DirtiesContext
//import org.springframework.test.context.ActiveProfiles
//import kotlin.test.Ignore
//
//@SpringBootTest
//@ActiveProfiles("test")
//@DirtiesContext
//@TestInstance(TestInstance.Lifecycle.PER_CLASS)
//@EmbeddedKafka(partitions = 1, bootstrapServersProperty = "platform.kafka.bootstrap-servers")
//class KafkaListenerIntegrationTest {
//
//    @Autowired private lateinit var embeddedKafkaBroker: EmbeddedKafkaBroker
//
//    @MockBean private lateinit var contractEventHandler: ContractOnboardingStatusUpdateEventHandler
//
//    @MockBean
//    private lateinit var compensationUpdateEventHandler: ContractCompensationUpdateEventHandler
//
//    @MockBean private lateinit var contractServiceAdapter: ContractServiceAdapter
//
//    private lateinit var contractEventProducer:
//        KafkaProducer<String, ContractOnboardingEventMessage>
//    private lateinit var salaryReviewEventProducer:
//        KafkaProducer<String, SalaryReviewUpdateEventMessage>
//
//    @BeforeAll
//    fun setUp() {
//        contractEventProducer =
//            KafkaProducer<String, ContractOnboardingEventMessage>(
//                mapOf(
//                    BOOTSTRAP_SERVERS_CONFIG to embeddedKafkaBroker.brokersAsString,
//                    KEY_SERIALIZER_CLASS_CONFIG to StringSerializer::class.java,
//                    VALUE_SERIALIZER_CLASS_CONFIG to KafkaProtobufSerializer::class.java,
//                    ENABLE_IDEMPOTENCE_CONFIG to "true",
//                    ACKS_CONFIG to "all",
//                    MAX_BLOCK_MS_CONFIG to 3200,
//                    RETRIES_CONFIG to 3,
//                    RETRY_BACKOFF_MS_CONFIG to 1000))
//        salaryReviewEventProducer =
//            KafkaProducer<String, SalaryReviewUpdateEventMessage>(
//                mapOf(
//                    BOOTSTRAP_SERVERS_CONFIG to embeddedKafkaBroker.brokersAsString,
//                    KEY_SERIALIZER_CLASS_CONFIG to StringSerializer::class.java,
//                    VALUE_SERIALIZER_CLASS_CONFIG to KafkaProtobufSerializer::class.java,
//                    ENABLE_IDEMPOTENCE_CONFIG to "true",
//                    ACKS_CONFIG to "all",
//                    MAX_BLOCK_MS_CONFIG to 3200,
//                    RETRIES_CONFIG to 3,
//                    RETRY_BACKOFF_MS_CONFIG to 1000))
//    }
//
//    @AfterAll
//    fun tearDown() {
//        embeddedKafkaBroker.kafkaServers.forEach { it.shutdown() }
//        embeddedKafkaBroker.kafkaServers.forEach { it.awaitShutdown() }
//    }
//
////    @Test
////    fun should_send_application_event_on_receiving_onboarding_event_message_from_kafka() {
////        // given
////        val record =
////            ProducerRecord(
////                "topic.internal.v1.contract-onboarding",
////                "1",
////                ContractOnboardingEventMessage.newBuilder()
////                    .setEventType(ONBOARDING_STATUS_UPDATE)
////                    .setEvent(
////                        ContractOnboardingEvent.newBuilder()
////                            .setOnboardingId(1L)
////                            .setContractId(2L)
////                            .setExperience("company")
////                            .build())
////                    .build())
////        doReturn(ContractOuterClass.Contract.newBuilder().setId(2L).build())
////            .`when`(contractServiceAdapter)
////            .findContractByContractId(2L)
////
////        // when
////        contractEventProducer.send(record)
////        contractEventProducer.close()
////        TimeUnit.SECONDS.sleep(15)
////
////        // then
////        val captor = argumentCaptor<ContractOnboardingStatusUpdateEvent>()
////        verify(contractEventHandler).handleContractOnboardingStatusUpdateEvent(captor.capture())
////        val event: ContractOnboardingStatusUpdateEvent = captor.firstValue
////        assertEquals(2L, event.contract.id)
////        assertEquals("company", event.experience)
////    }
//
//    @Test
//    @Ignore
//    fun should_send_application_event_on_receiving_salary_review_event_message_from_kafka() {
//        // given
//        val record =
//            ProducerRecord(
//                "topic.internal.v1.performance",
//                "1",
//                SalaryReviewUpdateMessage.SalaryReviewUpdateEventMessage.newBuilder()
//                    .setType(
//                        SalaryReviewUpdateMessage.SalaryReviewEventType.SALARY_REVIEW_ACTIVATED)
//                    .setSalaryReviewId(1L)
//                    .setContractId(2L)
//                    .build())
//        val contract = ContractOuterClass.Contract.newBuilder().setId(2L).build()
//        doReturn(contract).`when`(contractServiceAdapter).findContractByContractId(2L)
//
//        // when
//        salaryReviewEventProducer.send(record)
//        salaryReviewEventProducer.close()
//        TimeUnit.SECONDS.sleep(15)
//
//        // then
//        val captor = argumentCaptor<ContractCompensationUpdateEvent>()
//        verify(compensationUpdateEventHandler)
//            .handleContractCompensationUpdateEvent(captor.capture())
//        val event: ContractCompensationUpdateEvent = captor.firstValue
//        assertEquals(2L, event.contract.id)
//    }
//}
