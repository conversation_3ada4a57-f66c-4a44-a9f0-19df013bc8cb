package com.multiplier.integration.accounting.domain.common

import com.multiplier.integration.types.CurrencyCode
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class AmountTest {

    @Nested
    inner class MinusOperationTest {

        @Test
        fun `when the amount passed is null, return the current amount`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.minus(null)

            assertEquals(amount, result)
        }

        @Test
        fun `when the current amount passed is less, give correct amount`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.minus(Amount(CurrencyCode.USD, 10.00))

            assertEquals(
                Amount(CurrencyCode.USD, 90.00), result
            )
        }

        @Test
        fun `when the amount passed is more, give correct amount`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.minus(Amount(CurrencyCode.USD, 110.00))

            assertEquals(
                Amount(CurrencyCode.USD, -10.00), result
            )
        }

        @Test
        fun `when the currency do not match, throw exception`() {
            val amount = Amount(CurrencyCode.USD, 100.00)
            assertThrows<IllegalArgumentException> {
                amount.minus(
                    Amount(CurrencyCode.AED, 100.00)
                )
            }
        }

    }

    @Nested
    inner class IsGreaterThanOperation {
        @Test
        fun `when the currency do not match, throw exception`() {
            val amount = Amount(CurrencyCode.USD, 100.00)
            assertThrows<IllegalArgumentException> {
                amount.isGreaterThan(
                    Amount(CurrencyCode.AED, 100.00)
                )
            }
        }

        @Test
        fun `when the amount passed is null, return the current amount`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.isGreaterThan(null)

            assertTrue { result }
        }

        @Test
        fun `when amount passed is greater than current amount, return false`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.isGreaterThan(
                Amount(
                    CurrencyCode.USD,
                    110.00
                )
            )

            assertFalse { result }
        }

        @Test
        fun `when amount passed is less than current amount, return true`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.isGreaterThan(
                Amount(
                    CurrencyCode.USD,
                    90.00
                )
            )

            assertTrue { result }
        }

        @Test
        fun `when amount passed is equal to current amount, return false`() {
            val amount = Amount(CurrencyCode.USD, 100.00)

            val result = amount.isGreaterThan(
                Amount(
                    CurrencyCode.USD,
                    100.00
                )
            )

            assertFalse { result }
        }
    }

    @Nested
    inner class IsNotEqualTo {

        @Test
        fun `when the currency do not match, throw exception`() {
            val amount = Amount(CurrencyCode.USD, 100.00)
            assertThrows<IllegalArgumentException> {
                amount.isNotEqualTo(
                    Amount(CurrencyCode.AED, 100.00)
                )
            }
        }

        @Test
        fun `when amount passed is different, return true`() {
            val amount = Amount(CurrencyCode.USD, 100.00)
            assertTrue {
                amount.isNotEqualTo(
                    Amount(CurrencyCode.USD, 90.00)
                )
            }
        }

        @Test
        fun `when amount passed is same, return false`() {
            val amount = Amount(CurrencyCode.USD, 100.00)
            assertFalse {
                amount.isNotEqualTo(
                    Amount(CurrencyCode.USD, 100.00)
                )
            }
        }
    }

    @Nested
    inner class IsAmountZero {

        @Test
        fun `when amount is not zero, return false`() {
            assertFalse {
                Amount(CurrencyCode.USD, 100.00).isAmountZero()
            }
        }

        @Test
        fun `when amount is zero, return true`() {
            assertTrue {
                Amount(CurrencyCode.USD, 0.00).isAmountZero()
            }
        }
    }
}