package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.model.Invoice
import com.multiplier.integration.types.CurrencyCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class InvoicePaymentStrategyFactoryTest {

    @Mock
    private lateinit var invoiceUpdatePaymentStrategy: InvoiceUpdatePaymentStrategy

    @Mock
    private lateinit var invoicePaymentWithCreditNoteAndPaymentVoucherStrategy: InvoicePaymentWithCreditNoteAndPaymentVoucherStrategy

    @Mock
    private lateinit var noInvoicePaymentStrategy: NoInvoicePaymentStrategy


    private lateinit var invoicePaymentStrategyFactory: InvoicePaymentStrategyFactory

    @BeforeEach
    fun init() {

        doReturn(InvoicePaymentStrategyType.NO_PAYMENT)
            .`when`(noInvoicePaymentStrategy).getStrategyType()
        doReturn(InvoicePaymentStrategyType.CREDIT_NOTE_AND_PAYMENT_VOUCHER)
            .`when`(invoicePaymentWithCreditNoteAndPaymentVoucherStrategy).getStrategyType()
        doReturn(InvoicePaymentStrategyType.INVOICE_UPDATED)
            .`when`(invoiceUpdatePaymentStrategy).getStrategyType()

        invoicePaymentStrategyFactory = InvoicePaymentStrategyFactory(
            listOf(
                invoicePaymentWithCreditNoteAndPaymentVoucherStrategy,
                noInvoicePaymentStrategy,
                invoiceUpdatePaymentStrategy,
            )
        )
    }
    @Test
    fun `when the invoice total amount has changed, return invoice update strategy`() {
        val invoice = mock<Invoice>()
        val externalCompanyPayableAmount = ExternalCompanyPayableAmount(
            dueAmount = Amount(CurrencyCode.USD, value = 0.00),
            paidAmount = Amount(CurrencyCode.USD, value = 0.00),
            totalAmount = Amount(CurrencyCode.USD, value = 100.00)
        )

        doReturn(Amount(CurrencyCode.USD, value = 120.00)).`when`(invoice).totalAmount

        val invoicePaymentStrategyFactoryInput = InvoicePaymentStrategyFactoryInput(
            invoice,
            externalCompanyPayableAmount,
            null
        )

        val strategy = invoicePaymentStrategyFactory.getInvoicePaymentStrategy(invoicePaymentStrategyFactoryInput)

        assertEquals(invoiceUpdatePaymentStrategy, strategy)
    }

    @Test
    fun `when the invoice due amount is changed using credit note, do nothing`() {
        val invoice = mock<Invoice>()
        val externalCompanyPayableAmount = ExternalCompanyPayableAmount(
            dueAmount = Amount(CurrencyCode.USD, value = 10.00),
            paidAmount = Amount(CurrencyCode.USD, value = 90.00),
            totalAmount = Amount(CurrencyCode.USD, value = 100.00)
        )
        val creditNoteAmount = Amount(CurrencyCode.USD, 90.00)
        doReturn(Amount(CurrencyCode.USD, value = 90.00)).`when`(invoice).amountPaid
        doReturn(Amount(CurrencyCode.USD, value = 100.00)).`when`(invoice).totalAmount

        val invoicePaymentStrategyFactoryInput = InvoicePaymentStrategyFactoryInput(
            invoice,
            externalCompanyPayableAmount,
            creditNoteAmount
        )

        val strategy = invoicePaymentStrategyFactory.getInvoicePaymentStrategy(invoicePaymentStrategyFactoryInput)

        assertEquals(noInvoicePaymentStrategy, strategy)
    }

    @Test
    fun `when invoice due amount is changed using credit note and payment, call payment strategy`() {
        val invoice = mock<Invoice>()
        val externalCompanyPayableAmount = ExternalCompanyPayableAmount(
            dueAmount = Amount(CurrencyCode.USD, value = 10.00),
            paidAmount = Amount(CurrencyCode.USD, value = 90.00),
            totalAmount = Amount(CurrencyCode.USD, value = 100.00)
        )
        val creditNoteAmount = Amount(CurrencyCode.USD, 80.00)
        doReturn(Amount(CurrencyCode.USD, value = 100.00)).`when`(invoice).amountPaid
        doReturn(Amount(CurrencyCode.USD, value = 100.00)).`when`(invoice).totalAmount

        val invoicePaymentStrategyFactoryInput = InvoicePaymentStrategyFactoryInput(
            invoice,
            externalCompanyPayableAmount,
            creditNoteAmount
        )

        val strategy = invoicePaymentStrategyFactory.getInvoicePaymentStrategy(invoicePaymentStrategyFactoryInput)

        assertEquals(invoicePaymentWithCreditNoteAndPaymentVoucherStrategy, strategy)
    }

    @Test
    fun `when invoice due amount is changed using payment, call payment`() {
        val invoice = mock<Invoice>()
        val externalCompanyPayableAmount = ExternalCompanyPayableAmount(
            dueAmount = Amount(CurrencyCode.USD, value = 10.00),
            paidAmount = Amount(CurrencyCode.USD, value = 90.00),
            totalAmount = Amount(CurrencyCode.USD, value = 100.00)
        )

        doReturn(Amount(CurrencyCode.USD, value = 100.00)).`when`(invoice).amountPaid
        doReturn(Amount(CurrencyCode.USD, value = 100.00)).`when`(invoice).totalAmount

        val invoicePaymentStrategyFactoryInput = InvoicePaymentStrategyFactoryInput(
            invoice,
            externalCompanyPayableAmount,
            null
        )

        val strategy = invoicePaymentStrategyFactory.getInvoicePaymentStrategy(invoicePaymentStrategyFactoryInput)

        assertEquals(invoicePaymentWithCreditNoteAndPaymentVoucherStrategy, strategy)
    }

}