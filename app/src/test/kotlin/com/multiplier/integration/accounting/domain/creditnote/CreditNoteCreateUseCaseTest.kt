package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableAdapter
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.mapping.FinancialTransactionLineItemsMapper
import com.multiplier.integration.accounting.domain.model.CreditNote
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class CreditNoteCreateUseCaseTest {
    @MockK
    private lateinit var companyPayableAdapter: CompanyPayableAdapter

    @MockK
    private lateinit var companyPayableExternalSystemService: CompanyPayableExternalSystemService

    @MockK
    private lateinit var mergeDevService: MergeDevService

    @MockK
    private lateinit var financialTransactionLineItemsMapper: FinancialTransactionLineItemsMapper

    @InjectMockKs
    private lateinit var creditNoteCreateUseCase: CreditNoteCreateUseCase

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `when for given credit note CompanyPayableExternalSystemTransaction already exists then throw exception `() {
        val companyPayable =
            mockk<CreditNote> {
                every { companyPayableId } returns 1L
            }
        val companyPayableExternalSystemTransaction = mockk<CompanyPayableExternalSystemTransaction>()

        every {
            companyPayableExternalSystemService.findExternalSystemCompanyPayable(
                any(),
                any(),
            )
        } returns companyPayableExternalSystemTransaction

        val exception =
            assertThrows<IllegalArgumentException> {
                creditNoteCreateUseCase.createHandler(
                    companyPayable,
                )
            }
        assertEquals(
            "vendor credit transaction already executed for the company payable(id = 1)",
            exception.message,
        )
    }
}
