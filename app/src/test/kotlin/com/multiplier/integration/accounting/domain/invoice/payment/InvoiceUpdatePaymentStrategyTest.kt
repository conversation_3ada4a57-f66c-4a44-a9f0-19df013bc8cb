package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.FinancialTransactionUpsertOrchestratorFactory
import com.multiplier.integration.accounting.domain.invoice.InvoiceExternalSystemUpsertOrchestrator
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.multiplier.integration.accounting.domain.model.Invoice
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.verify

@ExtendWith(MockitoExtension::class)
class InvoiceUpdatePaymentStrategyTest {

    @Mock
    private lateinit var financialTransactionUpsertOrchestratorFactory: FinancialTransactionUpsertOrchestratorFactory

    @InjectMocks
    private lateinit var invoiceUpdatePaymentStrategy: InvoiceUpdatePaymentStrategy

    @Test
    fun getStrategyType() {
        assertEquals(
            InvoicePaymentStrategyType.INVOICE_UPDATED, invoiceUpdatePaymentStrategy.getStrategyType()
        )
    }

    @Test
    fun handle() {
        val invoiceExternalSystemUpsertOrchestrator: InvoiceExternalSystemUpsertOrchestrator = mock()
        val invoicePaymentStrategyRequest = mock<InvoicePaymentStrategyRequest>()
        val invoice = mock<Invoice>()

        doReturn(invoice)
            .`when`(invoicePaymentStrategyRequest)
                .invoice

        doReturn(invoiceExternalSystemUpsertOrchestrator)
            .`when`(financialTransactionUpsertOrchestratorFactory)
                .getHandler(FinancialTransactionType.INVOICE)

        invoiceUpdatePaymentStrategy.handle(invoicePaymentStrategyRequest)

        verify(invoiceExternalSystemUpsertOrchestrator).upsertHandler(invoice)
    }
}