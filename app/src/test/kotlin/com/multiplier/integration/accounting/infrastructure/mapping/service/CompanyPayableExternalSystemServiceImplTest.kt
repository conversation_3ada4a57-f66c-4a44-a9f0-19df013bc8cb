package com.multiplier.integration.accounting.infrastructure.mapping.service

import com.multiplier.integration.accounting.db.JpaCompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.db.repo.JpaCompanyPayableExternalSystemTransactionRepository
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.mapping.AccountingExternalEntity
import com.multiplier.integration.accounting.domain.mapping.AccountingTransaction
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class CompanyPayableExternalSystemServiceImplTest {

    private lateinit var service: CompanyPayableExternalSystemServiceImpl
    private val repository: JpaCompanyPayableExternalSystemTransactionRepository = mockk()

    @BeforeEach
    fun setUp() {
        service = CompanyPayableExternalSystemServiceImpl(repository)
    }

    @Test
    fun `test findExternalSystemCompanyPayable`() {
        val companyPayableId = 1L
        val jpaTransaction = JpaCompanyPayableExternalSystemTransaction(
            id = 1L,
            integrationId = 1L,
            companyPayableId = companyPayableId,
            entityId = 1L,
            financialTransactionType = FinancialTransactionType.INVOICE,
            requestPayload = AccountingTransaction(),
            externalId = AccountingExternalEntity("id", "type", "remote id"),
            responsePayload = AccountingTransaction()
        )

        every { repository.findFirstByCompanyPayableIdAndFinancialTransactionType(companyPayableId, FinancialTransactionType.INVOICE) } returns jpaTransaction

        val result = service.findExternalSystemCompanyPayable(companyPayableId, FinancialTransactionType.INVOICE)

        assertNotNull(result)
        assertEquals(jpaTransaction.id, result?.id)
        assertEquals(jpaTransaction.integrationId, result?.integrationId)
        assertEquals(jpaTransaction.companyPayableId, result?.companyPayableId)
        assertEquals(jpaTransaction.entityId, result?.entityId)
        assertEquals(jpaTransaction.financialTransactionType, result?.financialTransactionType)
        assertEquals(jpaTransaction.requestPayload, result?.requestPayload)
        assertEquals(jpaTransaction.externalId, result?.externalSystemTransactionId)
        assertEquals(jpaTransaction.responsePayload, result?.responsePayload)
    }

    @Test
    fun `test createExternalSystemCompanyPayable`() {
        val transaction = CompanyPayableExternalSystemTransaction(
            id = null,
            integrationId = 1L,
            companyPayableId = 1L,
            entityId = 1L,
            financialTransactionType = FinancialTransactionType.INVOICE,
            requestPayload = AccountingTransaction(),
            externalSystemTransactionId = AccountingExternalEntity("id", "type", "remote id"),
            responsePayload = AccountingTransaction()
        )

        val savedJpaTransaction = JpaCompanyPayableExternalSystemTransaction(
            id = 1L,
            integrationId = 1L,
            companyPayableId = 1L,
            entityId = 1L,
            financialTransactionType = FinancialTransactionType.INVOICE,
            requestPayload = AccountingTransaction(),
            externalId = AccountingExternalEntity("id", "type", "remote id"),
            responsePayload =AccountingTransaction(),
        )

        every { repository.save(any()) } returns savedJpaTransaction

        val result = service.createExternalSystemCompanyPayable(transaction)

        assertNotNull(result)
        assertEquals(savedJpaTransaction.id, result.id)
        assertEquals(savedJpaTransaction.integrationId, result.integrationId)
        assertEquals(savedJpaTransaction.companyPayableId, result.companyPayableId)
        assertEquals(savedJpaTransaction.entityId, result.entityId)
        assertEquals(savedJpaTransaction.financialTransactionType, result.financialTransactionType)
        assertEquals(savedJpaTransaction.requestPayload, result.requestPayload)
        assertEquals(savedJpaTransaction.externalId, result.externalSystemTransactionId)
        assertEquals(savedJpaTransaction.responsePayload, result.responsePayload)
    }

    @Test
    fun `test updateExternalSystemCompanyPayable`() {
        val transaction = CompanyPayableExternalSystemTransaction(
            id = 1L,
            integrationId = 1L,
            companyPayableId = 1L,
            entityId = 1L,
            financialTransactionType = FinancialTransactionType.INVOICE,
            requestPayload = AccountingTransaction(),
            externalSystemTransactionId = AccountingExternalEntity("id", "type", "remote id"),
            responsePayload = AccountingTransaction()
        )

        val existingJpaTransaction = JpaCompanyPayableExternalSystemTransaction(
            id = 1L,
            integrationId = 1L,
            companyPayableId = 1L,
            entityId = 1L,
            financialTransactionType = FinancialTransactionType.INVOICE,
            requestPayload = AccountingTransaction(),
            externalId = AccountingExternalEntity("id", "type", "remote id"),
            responsePayload = AccountingTransaction()
        )

        every { repository.findById(1L) } returns java.util.Optional.of(existingJpaTransaction)
        every { repository.save(any()) } returns existingJpaTransaction.copy(
            requestPayload = AccountingTransaction(),
            externalId = AccountingExternalEntity("id", "type", "remote id"),
            responsePayload = AccountingTransaction(),
        )

        val result = service.updateExternalSystemCompanyPayable(transaction)

        assertNotNull(result)
        assertEquals(transaction.id, result.id)
        assertEquals(transaction.integrationId, result.integrationId)
        assertEquals(transaction.companyPayableId, result.companyPayableId)
        assertEquals(transaction.entityId, result.entityId)
        assertEquals(transaction.financialTransactionType, result.financialTransactionType)
        assertEquals(transaction.requestPayload, result.requestPayload)
        assertEquals(transaction.externalSystemTransactionId, result.externalSystemTransactionId)
        assertEquals(transaction.responsePayload, result.responsePayload)

        verify { repository.findById(1L) }
        verify { repository.save(any()) }
    }
}