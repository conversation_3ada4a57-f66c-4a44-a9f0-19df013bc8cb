package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.common.Tax
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.LineItem
import com.multiplier.integration.types.CurrencyCode
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.DisplayName
import java.time.LocalDate

/**
 * Test class for AccountingTransactionMapper interface
 * Focuses specifically on the mapLineItemToAccountingTransactionLineItem method
 * to ensure correct mapping of totalAmount from grossAmount.value
 */
class AccountingTransactionMapperTest {

    private lateinit var mapper: TestAccountingTransactionMapper
    private lateinit var mockCompanyPayable: CompanyPayable

    @BeforeEach
    fun setUp() {
        mapper = TestAccountingTransactionMapper()
        mockCompanyPayable = mockk<CompanyPayable>(relaxed = true)
    }

    @Nested
    @DisplayName("mapLineItemToAccountingTransactionLineItem Tests")
    inner class MapLineItemToAccountingTransactionLineItemTest {

        @Test
        @DisplayName("Should correctly map totalAmount from grossAmount.value when quantity is 1")
        fun `should map totalAmount from grossAmount when quantity is 1`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 100.0)
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 1.0
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(100.0, result.unitPrice, "Unit price should match lineItem.unitPrice.value")
            assertEquals(100.0, result.totalAmount, "Total amount should match lineItem.grossAmount.value")
            assertEquals(1.0, result.quantity, "Quantity should be preserved")
        }

        @Test
        @DisplayName("Should correctly map totalAmount from grossAmount.value when quantity is 2 - RCA Case")
        fun `should map totalAmount from grossAmount when quantity is 2 - RCA case`() {
            // Given - This is the exact scenario from the RCA
            val unitPrice = Amount(CurrencyCode.USD, 400.0)
            val grossAmount = Amount(CurrencyCode.USD, 800.0) // unitPrice * quantity
            val lineItem = createTestLineItem(
                description = "Annual Management Fee for 2 members (GBR)",
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 2.0
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(400.0, result.unitPrice, "Unit price should be 400.0")
            assertEquals(800.0, result.totalAmount, "Total amount should be 800.0 (grossAmount.value), not 400.0 (unitPrice.value)")
            assertEquals(2.0, result.quantity, "Quantity should be 2.0")
            assertEquals("Annual Management Fee for 2 members (GBR)", result.description)
        }

        @Test
        @DisplayName("Should correctly map totalAmount from grossAmount.value when quantity is 5")
        fun `should map totalAmount from grossAmount when quantity is 5`() {
            // Given
            val unitPrice = Amount(CurrencyCode.EUR, 50.0)
            val grossAmount = Amount(CurrencyCode.EUR, 250.0) // 50 * 5
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 5.0
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(50.0, result.unitPrice, "Unit price should be 50.0")
            assertEquals(250.0, result.totalAmount, "Total amount should be 250.0 (grossAmount.value)")
            assertEquals(5.0, result.quantity, "Quantity should be 5.0")
        }

        @Test
        @DisplayName("Should correctly map totalAmount from grossAmount.value with fractional quantity")
        fun `should map totalAmount from grossAmount with fractional quantity`() {
            // Given
            val unitPrice = Amount(CurrencyCode.GBP, 100.0)
            val grossAmount = Amount(CurrencyCode.GBP, 150.0) // 100 * 1.5
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 1.5
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(100.0, result.unitPrice, "Unit price should be 100.0")
            assertEquals(150.0, result.totalAmount, "Total amount should be 150.0 (grossAmount.value)")
            assertEquals(1.5, result.quantity, "Quantity should be 1.5")
        }

        @Test
        @DisplayName("Should correctly map totalAmount from grossAmount.value when quantity is 0")
        fun `should map totalAmount from grossAmount when quantity is 0`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 0.0)
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 0.0
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(100.0, result.unitPrice, "Unit price should be 100.0")
            assertEquals(0.0, result.totalAmount, "Total amount should be 0.0 (grossAmount.value)")
            assertEquals(0.0, result.quantity, "Quantity should be 0.0")
        }

        @Test
        @DisplayName("Should correctly map totalAmount from grossAmount.value with negative amounts")
        fun `should map totalAmount from grossAmount with negative amounts`() {
            // Given - Credit/refund scenario
            val unitPrice = Amount(CurrencyCode.USD, -100.0)
            val grossAmount = Amount(CurrencyCode.USD, -300.0) // -100 * 3
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 3.0
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(-100.0, result.unitPrice, "Unit price should be -100.0")
            assertEquals(-300.0, result.totalAmount, "Total amount should be -300.0 (grossAmount.value)")
            assertEquals(3.0, result.quantity, "Quantity should be 3.0")
        }

        @Test
        @DisplayName("Should correctly map currency from unitPrice")
        fun `should map currency from unitPrice`() {
            // Given
            val unitPrice = Amount(CurrencyCode.EUR, 100.0)
            val grossAmount = Amount(CurrencyCode.EUR, 200.0)
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 2.0
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals("EUR", result.commonFields?.currency, "Currency should match unitPrice currency")
        }

        @Test
        @DisplayName("Should correctly map line item with account mapping")
        fun `should map line item with account mapping`() {
            // Given
            val externalAccount = AccountingExternalEntity("acc-123", "EXPENSE", "Office Supplies")
            val externalDepartment = AccountingExternalEntity("dept-456", "DEPARTMENT", "Sales")
            val lineItemAccountMapping = LineItemAccountMapping(
                lineItemType = "OFFICE_SUPPLIES",
                externalAccount = externalAccount,
                externalDepartments = setOf(externalDepartment)
            )
            
            val unitPrice = Amount(CurrencyCode.USD, 50.0)
            val grossAmount = Amount(CurrencyCode.USD, 150.0)
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 3.0,
                lineItemAccountMapping = lineItemAccountMapping
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(50.0, result.unitPrice)
            assertEquals(150.0, result.totalAmount)
            assertEquals(3.0, result.quantity)
            assertEquals("acc-123", result.item, "Item should be mapped from external account ID")
            assertEquals(listOf("dept-456"), result.commonFields?.trackingCategories, "Tracking categories should be mapped from external departments")
        }

        @Test
        @DisplayName("Should correctly map line item without account mapping")
        fun `should map line item without account mapping`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 200.0)
            val lineItem = createTestLineItem(
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                quantity = 2.0,
                lineItemAccountMapping = null
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockCompanyPayable)

            // Then
            assertEquals(100.0, result.unitPrice)
            assertEquals(200.0, result.totalAmount)
            assertEquals(2.0, result.quantity)
            assertNull(result.item, "Item should be null when no account mapping")
            assertEquals(emptyList<String>(), result.commonFields?.trackingCategories, "Tracking categories should be empty when no account mapping")
        }

        private fun createTestLineItem(
            description: String = "Test Line Item",
            unitPrice: Amount,
            grossAmount: Amount,
            quantity: Double,
            lineItemAccountMapping: LineItemAccountMapping? = null
        ): LineItem {
            return LineItem(
                description = description,
                quantity = quantity,
                unitPrice = unitPrice,
                tax = Tax(
                    taxType = "NO_TAX",
                    taxRate = "0.0",
                    amount = Amount(unitPrice.currencyCode, 0.0)
                ),
                contractId = 1L,
                memberName = "Test Member",
                lineItemType = "TEST_LINE_ITEM",
                amountInBaseCurrency = grossAmount,
                countryName = "USA",
                grossAmount = grossAmount,
                startInvoiceCycleDate = LocalDate.now(),
                endInvoiceCycleDate = LocalDate.now().plusDays(30),
                lineItemAccountMapping = lineItemAccountMapping
            )
        }
    }

    /**
     * Test implementation of AccountingTransactionMapper for testing purposes
     */
    private class TestAccountingTransactionMapper : AccountingTransactionMapper {
        override fun mapFinancialTransactionToAccountingTransaction(
            companyPayable: CompanyPayable,
            contact: String
        ): AccountingTransaction {
            // Not needed for these tests
            throw NotImplementedError("Not implemented for test purposes")
        }
    }
}
