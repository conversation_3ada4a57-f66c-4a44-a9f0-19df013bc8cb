package com.multiplier.integration.accounting.infrastructure.mapping.creditnote

import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.infrastructure.creditnote.CreditNoteServiceAdapterImpl
import com.multiplier.integration.types.CurrencyCode
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNote
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteById
import com.multiplier.payable.grpc.schema.creditnote.GrpcCreditNoteServiceGrpc
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock

@ExtendWith(MockitoExtension::class)
class CreditNoteServiceAdapterImplTest {

    @Mock
    private lateinit var creditNoteServiceAdapter: GrpcCreditNoteServiceGrpc.GrpcCreditNoteServiceBlockingStub

    private val creditNoteServiceAdapterImpl = CreditNoteServiceAdapterImpl()

    @BeforeEach
    fun setUp() {
        creditNoteServiceAdapterImpl.creditNoteService = creditNoteServiceAdapter
    }

    @Test
    fun `when credit note are not of distinct currency, throw exception`() {
        // Arrange
        val creditNoteIds = listOf(1L, 2L)
        val invoiceId = 1L
        val mockCreditNote1 = mock<GrpcCreditNote> { }
        val mockCreditNote2 = mock<GrpcCreditNote> { }
        val creditNotes = listOf(
            mockCreditNote1,
            mockCreditNote2
        )
        val creditNoteById1 = GrpcCreditNoteById.newBuilder()
            .setCreditNote(creditNotes[0])
            .setId(1L)
            .build()
        val creditNoteById2 = GrpcCreditNoteById.newBuilder()
            .setCreditNote(creditNotes[1])
            .setId(2L)
            .build()

        doReturn("USD").`when`(mockCreditNote1).currencyCode
        doReturn("EUR").`when`(mockCreditNote2).currencyCode
        doReturn(listOf(invoiceId)).`when`(mockCreditNote1).appliedInvoicesList
        doReturn(listOf(invoiceId)).`when`(mockCreditNote2).appliedInvoicesList

        doReturn(
            com.multiplier.payable.grpc.schema.creditnote.GetCreditNoteByIdsResponse.newBuilder()
                .addAllCreditNoteById(listOf(creditNoteById1, creditNoteById2))
                .build()
        ).`when`(creditNoteServiceAdapter).getCreditNoteByIds(any())

        // Act and Assert
        assertThrows(IllegalArgumentException::class.java) {
            creditNoteServiceAdapterImpl.getTotalAmountForCreditNotes(invoiceId, creditNoteIds)
        }
    }

    @Test
    fun `when the currencies are same, get the total amount`() {
        val creditNoteIds = listOf(1L, 2L)
        val invoiceId = 1L
        val mockCreditNote1 = mock<GrpcCreditNote> { }
        val mockCreditNote2 = mock<GrpcCreditNote> { }
        val creditNotes = listOf(
            mockCreditNote1,
            mockCreditNote2
        )
        val creditNoteById1 = GrpcCreditNoteById.newBuilder()
            .setCreditNote(creditNotes[0])
            .setId(1L)
            .build()
        val creditNoteById2 = GrpcCreditNoteById.newBuilder()
            .setCreditNote(creditNotes[1])
            .setId(2L)
            .build()

        val amountApplied1 = 100.00
        val amountApplied2 = 200.0

        doReturn("USD").`when`(mockCreditNote1).currencyCode
        doReturn("USD").`when`(mockCreditNote2).currencyCode

        doReturn(amountApplied1).`when`(mockCreditNote1).amountApplied
        doReturn(amountApplied2).`when`(mockCreditNote2).amountApplied

        doReturn(listOf(invoiceId)).`when`(mockCreditNote1).appliedInvoicesList
        doReturn(listOf(invoiceId)).`when`(mockCreditNote2).appliedInvoicesList

        doReturn(
            com.multiplier.payable.grpc.schema.creditnote.GetCreditNoteByIdsResponse.newBuilder()
                .addAllCreditNoteById(listOf(creditNoteById1, creditNoteById2))
                .build()
        ).`when`(creditNoteServiceAdapter).getCreditNoteByIds(any())

        // Act and Assert
        val expectedAmount = Amount(CurrencyCode.USD, amountApplied1 + amountApplied2)

        assertThat(creditNoteServiceAdapterImpl.getTotalAmountForCreditNotes(invoiceId, creditNoteIds)).isEqualTo(expectedAmount)
    }

    @Test
    fun `when applied invoices are different, throw Exception`() {
        val creditNoteIds = listOf(1L, 2L)
        val invoiceId = 1L
        val invoiceId2 = 2L
        val mockCreditNote1 = mock<GrpcCreditNote> { }
        val mockCreditNote2 = mock<GrpcCreditNote> { }
        val creditNotes = listOf(
            mockCreditNote1,
            mockCreditNote2
        )
        val creditNoteById1 = GrpcCreditNoteById.newBuilder()
            .setCreditNote(creditNotes[0])
            .setId(1L)
            .build()
        val creditNoteById2 = GrpcCreditNoteById.newBuilder()
            .setCreditNote(creditNotes[1])
            .setId(2L)
            .build()

        doReturn(listOf(invoiceId, invoiceId2)).`when`(mockCreditNote1).appliedInvoicesList
        doReturn(listOf(invoiceId)).`when`(mockCreditNote2).appliedInvoicesList

        doReturn(
            com.multiplier.payable.grpc.schema.creditnote.GetCreditNoteByIdsResponse.newBuilder()
                .addAllCreditNoteById(listOf(creditNoteById1, creditNoteById2))
                .build()
        ).`when`(creditNoteServiceAdapter).getCreditNoteByIds(any())

        assertThrows(IllegalArgumentException::class.java) {creditNoteServiceAdapterImpl.getTotalAmountForCreditNotes(invoiceId, creditNoteIds)}
    }
}