package com.multiplier.integration.accounting.domain.payment

import com.multiplier.integration.accounting.application.mapping.usecase.ExternalPaymentCreationRequest
import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.model.CompanyPayableStatus
import com.multiplier.integration.accounting.domain.model.Invoice
import com.multiplier.integration.types.CurrencyCode
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import java.time.LocalDateTime
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class PaymentAggregateTest {

    @Test
    fun `when invoice is not PAID, throw exception`() {
        val createPaymentCreateRequest = mock<PaymentCreateRequest>()
        val invoice = mock<Invoice>()
        val mergeDevService = mock<MergeDevService>()
        doReturn(invoice).`when`(createPaymentCreateRequest).invoice
        doReturn(CompanyPayableStatus.AUTHORIZED).`when`(invoice).status

        val payment = PaymentAggregate(null)

        assertThrows<IllegalArgumentException> {
            payment.createPayment(createPaymentCreateRequest, mergeDevService)
        }
    }

    @Test
    fun `when invoice is PAID but due amount is not zero, throw exception`() {
        val createPaymentCreateRequest = mock<PaymentCreateRequest>()
        val invoice = mock<Invoice>()
        val mergeDevService = mock<MergeDevService>()
        doReturn(invoice).`when`(createPaymentCreateRequest).invoice
        doReturn(CompanyPayableStatus.PAID).`when`(invoice).status
        doReturn(Amount(CurrencyCode.USD, 10.00)).`when`(invoice).amountDue

        val payment = PaymentAggregate(null)

        assertThrows<IllegalArgumentException> {
            payment.createPayment(createPaymentCreateRequest, mergeDevService)
        }
    }

    @Test
    fun `when payment is already created, throw IllegalStateException`() {
        val companyPayableExternalSystemTransaction = mock<CompanyPayableExternalSystemTransaction>()
        val createPaymentCreateRequest = mock<PaymentCreateRequest>()
        val invoice = mock<Invoice>()
        val mergeDevService = mock<MergeDevService>()
        doReturn(invoice).`when`(createPaymentCreateRequest).invoice

        val payment = PaymentAggregate(companyPayableExternalSystemTransaction)

        assertThrows<IllegalArgumentException> {
            payment.createPayment(createPaymentCreateRequest, mergeDevService)
        }
    }

    //The test cases assuming no previous payment is applied
    @Nested
    inner class NoPreviousPaymentTest {

        @Test
        fun `when credit note amount is not present, create payment for whole invoice amount`() {
            val externalPaymentCreationRequestCapture = argumentCaptor<ExternalPaymentCreationRequest>()
            val createPaymentCreateRequest = mock<PaymentCreateRequest>()
            val invoice = mock<Invoice>()
            val mergeDevService = mock<MergeDevService>()
            val expectedAmount = Amount(CurrencyCode.USD, 100.00)
            val createdDate = LocalDateTime.now()
            val companyId = 1L
            val integrationId = 1L

            doReturn(invoice).`when`(createPaymentCreateRequest).invoice
            doReturn(CompanyPayableStatus.PAID).`when`(invoice).status
            doReturn(Amount(CurrencyCode.USD, 0.00)).`when`(invoice).amountDue
            doReturn(Amount(CurrencyCode.USD, 100.00)).`when`(invoice).amountPaid
            doReturn(null).`when`(createPaymentCreateRequest).appliedCreditNotesAmount
            doReturn(createdDate).`when`(createPaymentCreateRequest).createdDate
            doReturn(companyId).`when`(createPaymentCreateRequest).companyId
            doReturn(integrationId).`when`(createPaymentCreateRequest).integrationId

            val paymentAggregate = PaymentAggregate(null)
            paymentAggregate.createPayment(
                createPaymentCreateRequest,
                mergeDevService
            )

            verify(mergeDevService).createPayment(externalPaymentCreationRequestCapture.capture())

            val externalPaymentCreationRequest = externalPaymentCreationRequestCapture.firstValue

            assertEquals(expectedAmount, externalPaymentCreationRequest.amount)
            assertEquals(companyId, externalPaymentCreationRequest.companyId)
            assertEquals(integrationId, externalPaymentCreationRequest.integrationId)
            assertEquals(createdDate, externalPaymentCreationRequest.createdDate)
        }

        @Test
        fun `when credit note amount is present, create payment for only the remaining amount`() {
            val externalPaymentCreationRequestCapture = argumentCaptor<ExternalPaymentCreationRequest>()
            val createPaymentCreateRequest = mock<PaymentCreateRequest>()
            val invoice = mock<Invoice>()
            val mergeDevService = mock<MergeDevService>()
            val expectedAmount = Amount(CurrencyCode.USD, 60.00)
            val createdDate = LocalDateTime.now()
            val companyId = 1L
            val integrationId = 1L
            val appliedCreditNoteAmount = Amount(CurrencyCode.USD, 40.00)

            doReturn(invoice).`when`(createPaymentCreateRequest).invoice
            doReturn(CompanyPayableStatus.PAID).`when`(invoice).status
            doReturn(Amount(CurrencyCode.USD, 0.00)).`when`(invoice).amountDue
            doReturn(Amount(CurrencyCode.USD, 100.00)).`when`(invoice).amountPaid
            doReturn(appliedCreditNoteAmount).`when`(createPaymentCreateRequest).appliedCreditNotesAmount
            doReturn(createdDate).`when`(createPaymentCreateRequest).createdDate
            doReturn(companyId).`when`(createPaymentCreateRequest).companyId
            doReturn(integrationId).`when`(createPaymentCreateRequest).integrationId

            val paymentAggregate = PaymentAggregate(null)
            paymentAggregate.createPayment(
                createPaymentCreateRequest,
                mergeDevService
            )

            verify(mergeDevService).createPayment(externalPaymentCreationRequestCapture.capture())

            val externalPaymentCreationRequest = externalPaymentCreationRequestCapture.firstValue

            assertEquals(expectedAmount, externalPaymentCreationRequest.amount)
            assertEquals(companyId, externalPaymentCreationRequest.companyId)
            assertEquals(integrationId, externalPaymentCreationRequest.integrationId)
            assertEquals(createdDate, externalPaymentCreationRequest.createdDate)
        }

    }

}