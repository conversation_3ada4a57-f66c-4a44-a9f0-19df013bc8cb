package com.multiplier.integration.accounting.infrastructure.mapping.service

import com.multiplier.integration.accounting.db.*
import com.multiplier.integration.accounting.db.repo.*
import com.multiplier.integration.accounting.domain.mapping.AccountingExternalEntity
import com.multiplier.integration.accounting.domain.mapping.LineItemMappingParentType
import com.multiplier.integration.accounting.domain.mapping.exception.ExternalMultiplierMappingNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

@ExtendWith(SpringExtension::class)
class CompanyAccountingIntegrationMappingReadServiceImplTest {
    @MockK
    private lateinit var jpaAccountingDepartmentMappingRepository: JpaAccountingDepartmentMappingRepository

    @MockK
    private lateinit var jpaAccountingLineItemMappingRepository: JpaAccountingLineItemMappingRepository

    @MockK
    private lateinit var jpaAccountingLegalEntityMappingRepository: JpaAccountingLegalEntityMappingRepository

    @MockK
    private lateinit var jpaAccountingExternalMultiplierVendorMappingRepository: JpaAccountingExternalMultiplierVendorMappingRepository

    @MockK
    private lateinit var jpaAccountingExternalPaymentAccountMappingRepository: JpaAccountingExternalPaymentAccountMappingRepository

    @InjectMockKs
    private lateinit var companyAccountingIntegrationMappingReadServiceImpl: CompanyAccountingIntegrationMappingReadServiceImpl

    @Test
    fun `should read company integration mapping`() {
        // Given
        val jpaLineItemMapping =
            JpaAccountingLineItemMapping(
                id = 1,
                mappedParentId = 1,
                externalAccount = AccountingExternalEntity("id", "SALARY", "remote id"),
                mappedParentType = LineItemMappingParentType.COMPANY_INTEGRATION,
                lineItemType = "Line Item Type",
            )
        every {
            jpaAccountingLineItemMappingRepository.findByMappedParentIdAndMappedParentType(
                any(),
                LineItemMappingParentType.COMPANY_INTEGRATION,
            )
        } returns
            listOf(jpaLineItemMapping)

        val jpaEntityMapping =
            JpaAccountingLegalEntityMapping(
                id = 1,
                entityId = 1,
                integrationId = 1,
                externalEntity = AccountingExternalEntity("id", "entity", "remote id"),
            )
        every { jpaAccountingLegalEntityMappingRepository.findByIntegrationId(any()) } returns listOf(jpaEntityMapping)
        val jpaLineItemMappingLegalEntity =
            JpaAccountingLineItemMapping(
                id = 1,
                mappedParentId = 1,
                externalAccount = AccountingExternalEntity("id", "SALARY", "remote id"),
                mappedParentType = LineItemMappingParentType.LEGAL_ENTITY_INTEGRATION,
                lineItemType = "Line Item Type",
            )
        every {
            jpaAccountingLineItemMappingRepository.findByMappedParentIdAndMappedParentType(
                any(),
                LineItemMappingParentType.LEGAL_ENTITY_INTEGRATION,
            )
        } returns listOf(jpaLineItemMappingLegalEntity)


        val jpaDepartment = JpaAccountingDepartmentMapping(
            id = 1,
            integrationId = 1,
            departmentId = 1,
            externalDepartment = AccountingExternalEntity("id", "department", "remote id"),
        )

        every { jpaAccountingDepartmentMappingRepository.findByIntegrationId(any()) } returns listOf(jpaDepartment)

        val jpaLineItemDepartmentMapping =
            JpaAccountingLineItemMapping(
                id = 1,
                mappedParentId = 1,
                externalAccount = AccountingExternalEntity("id", "SALARY", "remote id"),
                mappedParentType = LineItemMappingParentType.DEPARTMENT_INTEGRATION,
                lineItemType = "Line Item Type",
            )

        every { jpaAccountingLineItemMappingRepository.findByMappedParentIdAndMappedParentType(any(), LineItemMappingParentType.DEPARTMENT_INTEGRATION) } returns listOf(jpaLineItemDepartmentMapping)


        val jpaExternalMultiplierVendorMapping = JpaAccountingExternalMultiplierVendorMapping(
            id = 1,
            integrationId = 1,
            externalVendor = AccountingExternalEntity("id", "vendor", "remote id"),
        )

        every { jpaAccountingExternalMultiplierVendorMappingRepository.findByIntegrationId(any()) } returns jpaExternalMultiplierVendorMapping
        val result = companyAccountingIntegrationMappingReadServiceImpl.read(1)

        assert(result.integrationId == 1L)
        assert(result.legalEntityMappings.isNotEmpty())
        assert(result.departmentMappings.isNotEmpty())
        assert(result.multiplierVendorMapping != null)
        result.fallbackLineItemMappings?.let { assert(it.isNotEmpty()) }
    }

    @Nested
    inner class ReadExternalPaymentAccount {
        @Test
        fun `when there is no mapping present, throw exception`() {
            val companyIntegrationId = 1L

            every { jpaAccountingExternalPaymentAccountMappingRepository.findByIntegrationId(companyIntegrationId) }
                .throws(ExternalMultiplierMappingNotFoundException(""))

            assertThrows<ExternalMultiplierMappingNotFoundException> { companyAccountingIntegrationMappingReadServiceImpl.readExternalPaymentAccount(companyIntegrationId) }
        }

        @Test
        fun `when there is a mapping present, return mapping`() {
            val companyIntegrationId = 1L
            val jpaAccountingExternalPaymentAccountMapping = mockk<JpaAccountingExternalPaymentAccountMapping>()
            val externalPaymentAccount = mockk<AccountingExternalEntity>()

            every { jpaAccountingExternalPaymentAccountMappingRepository.findByIntegrationId(companyIntegrationId) } returns jpaAccountingExternalPaymentAccountMapping
            every { jpaAccountingExternalPaymentAccountMapping.integrationId } returns companyIntegrationId
            every { jpaAccountingExternalPaymentAccountMapping.externalPaymentAccount } returns externalPaymentAccount

            val externalPaymentAccountMapping = companyAccountingIntegrationMappingReadServiceImpl.readExternalPaymentAccount(companyIntegrationId)

            assertNotNull(externalPaymentAccountMapping)
            assertEquals(companyIntegrationId, externalPaymentAccountMapping.companyIntegrationId)
            assertEquals(externalPaymentAccount, externalPaymentAccountMapping.externalBankAccount)
        }
    }
}
