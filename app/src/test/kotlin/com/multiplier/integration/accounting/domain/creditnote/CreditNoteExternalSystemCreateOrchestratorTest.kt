package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class CreditNoteExternalSystemCreateOrchestratorTest  {

    private lateinit var creditNoteCreateUseCase: CreditNoteCreateUseCase
    private lateinit var createOrchestrator: CreditNoteExternalSystemCreateOrchestrator

    @BeforeEach
    fun setUp() {
        creditNoteCreateUseCase = mockk()
        createOrchestrator = CreditNoteExternalSystemCreateOrchestrator(creditNoteCreateUseCase)
    }

    @Test
    fun `should call createHandler when upsertHandler is invoked`()  {
        val companyPayable =
            mockk<CompanyPayable> {
                every { companyPayableId } returns 1L
            }
        every { creditNoteCreateUseCase.createHandler(any()) } just runs

        createOrchestrator.upsertHandler(companyPayable)
        verify(exactly = 1) { creditNoteCreateUseCase.createHandler(companyPayable) }
    }

    @Test
    fun `should return CREDIT_NOTE as transaction type`() {
        val result = createOrchestrator.getTransactionType()

        assert(result == FinancialTransactionType.CREDIT_NOTE)
    }
}
