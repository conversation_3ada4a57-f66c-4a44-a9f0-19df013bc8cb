package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.mapping.*
import com.multiplier.integration.accounting.domain.model.CompanyPayableStatus
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.accounting.domain.model.FinancialTransactionType
import com.multiplier.integration.accounting.domain.model.Invoice
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class InvoiceExternalSystemTransactionServiceTest {

    private lateinit var financialTransactionLineItemsMapper: FinancialTransactionLineItemsMapper
    private lateinit var accountingTransactionInvoiceMapper: AccountingTransactionInvoiceMapper
    private lateinit var companyPayableExternalSystemService: CompanyPayableExternalSystemService
    private lateinit var companyAccountingIntegrationMappingReadService: CompanyAccountingIntegrationMappingReadService
    private lateinit var mergeDevService: MergeDevService
    private lateinit var aggregate: InvoiceExternalSystemTransactionService


    @BeforeEach
    fun setUp() {
        financialTransactionLineItemsMapper = mockk(relaxed = true)
        accountingTransactionInvoiceMapper = mockk(relaxed = true)
        companyPayableExternalSystemService = mockk(relaxed = true)
        companyAccountingIntegrationMappingReadService = mockk(relaxed = true)
        mergeDevService = mockk(relaxed = true)
        aggregate = InvoiceExternalSystemTransactionService(
            financialTransactionLineItemsMapper,
            accountingTransactionInvoiceMapper,
            companyPayableExternalSystemService,
            companyAccountingIntegrationMappingReadService,
            mergeDevService
        )
    }

    @Test
    fun `test upsert should success for create`() {
        val invoice = mockk<Invoice>(relaxed = true) {
            every { status } returns CompanyPayableStatus.AUTHORIZED
        }
        val createTransaction = UpsertCompanyPayableExternalSystemTransaction(
            customerIntegrationId = 1L,
            companyPayable = invoice,
            financialTransactionType = FinancialTransactionType.INVOICE
        )

        val vendorMapping = mockk<ExternalMultiplierVendorMapping>(relaxed = true)
        val mappedTransaction = mockk<CompanyPayableExternalSystemTransaction>(relaxed = true)
        val accountingTransaction = mockk<AccountingTransaction>(relaxed = true)

        every { companyAccountingIntegrationMappingReadService.readExternalVendorMapping(any()) } returns vendorMapping
        every { financialTransactionLineItemsMapper.lineItemMapping(any()) } returns listOf()
        every { accountingTransactionInvoiceMapper.mapFinancialTransactionToAccountingTransaction(any(), any()) } returns accountingTransaction
        every { companyPayableExternalSystemService.createExternalSystemCompanyPayable(any()) } returns mappedTransaction
        every { companyPayableExternalSystemService.findExternalSystemCompanyPayable(any(), any()) } returns null

        val result = aggregate.upsert(createTransaction)

        assertNotNull(result)
        verify { companyAccountingIntegrationMappingReadService.readExternalVendorMapping(1L) }
        verify { financialTransactionLineItemsMapper.lineItemMapping(any()) }
        verify { accountingTransactionInvoiceMapper.mapFinancialTransactionToAccountingTransaction(any(), any()) }
        verify { companyPayableExternalSystemService.createExternalSystemCompanyPayable(any()) }
    }

    @Test
    fun `test upsert should success for update`() {
        val invoice = mockk<Invoice>(relaxed = true) {
            every { status } returns CompanyPayableStatus.AUTHORIZED
        }
        val createTransaction = UpsertCompanyPayableExternalSystemTransaction(
            customerIntegrationId = 1L,
            companyPayable = invoice,
            financialTransactionType = FinancialTransactionType.INVOICE
        )

        val vendorMapping = mockk<ExternalMultiplierVendorMapping>(relaxed = true)
        val mappedTransaction = mockk<CompanyPayableExternalSystemTransaction>(relaxed = true){
            every { externalSystemTransactionId } returns AccountingExternalEntity("id", "remoteId", "name")
        }
        val accountingTransaction = mockk<AccountingTransaction>(relaxed = true)

        every { companyAccountingIntegrationMappingReadService.readExternalVendorMapping(any()) } returns vendorMapping
        every { financialTransactionLineItemsMapper.lineItemMapping(any()) } returns listOf()
        every { accountingTransactionInvoiceMapper.mapFinancialTransactionToAccountingTransaction(any(), any()) } returns accountingTransaction
        every { companyPayableExternalSystemService.createExternalSystemCompanyPayable(any()) } returns mappedTransaction
        every { companyPayableExternalSystemService.findExternalSystemCompanyPayable(any(), any()) } returns mappedTransaction
        every { companyPayableExternalSystemService.updateExternalSystemCompanyPayable(any()) } returns mappedTransaction

        val result = aggregate.upsert(createTransaction)

        assertNotNull(result)
        verify { companyAccountingIntegrationMappingReadService.readExternalVendorMapping(1L) }
        verify { financialTransactionLineItemsMapper.lineItemMapping(any()) }
        verify { accountingTransactionInvoiceMapper.mapFinancialTransactionToAccountingTransaction(any(), any()) }
        verify { companyPayableExternalSystemService.findExternalSystemCompanyPayable(any(), any()) }
        verify { companyPayableExternalSystemService.updateExternalSystemCompanyPayable(any()) }
    }

    @Test
    fun `test upsert with credit note throws exception`() {
        val creditNote = mockk<CreditNote>(relaxed = true) {
            every { status } returns CompanyPayableStatus.DRAFT
        }
        val createTransaction = UpsertCompanyPayableExternalSystemTransaction(
            customerIntegrationId = 1L,
            companyPayable = creditNote,
            financialTransactionType = FinancialTransactionType.INVOICE
        )


        assertThrows<IllegalArgumentException>{
            aggregate.upsert(createTransaction)
        }
    }

    @Test
    fun `Test upsert with invalid vendor mapping should fail`() {
        val invoice = mockk<Invoice>(relaxed = true) {
            every { status } returns CompanyPayableStatus.AUTHORIZED
        }
        val createTransaction = UpsertCompanyPayableExternalSystemTransaction(
            customerIntegrationId = 1L,
            companyPayable = invoice,
            financialTransactionType = FinancialTransactionType.INVOICE
        )

        every { companyAccountingIntegrationMappingReadService.readExternalVendorMapping(any()) } returns null

        assertThrows<IllegalArgumentException> {
            aggregate.upsert(createTransaction)
        }
    }
}