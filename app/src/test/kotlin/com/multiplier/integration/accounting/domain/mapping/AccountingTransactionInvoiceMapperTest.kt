package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.MultiplierAccountingSystemIdentifier
import com.multiplier.integration.accounting.domain.MultiplierExternalSystem
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.common.Tax
import com.multiplier.integration.accounting.domain.model.CompanyPayableStatus
import com.multiplier.integration.accounting.domain.model.Invoice
import com.multiplier.integration.accounting.domain.model.LineItem
import com.multiplier.integration.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Nested
import java.time.LocalDate

class AccountingTransactionInvoiceMapperTest {

    private lateinit var mapper: AccountingTransactionInvoiceMapper

    @BeforeEach
    fun setUp() {
        mapper = AccountingTransactionInvoiceMapper()
    }

    @Test
    fun `test mapFinancialTransactionToAccountingTransaction`() {
        val referenceText = "Test Reference"
        val invoiceNumber = "MULTINV123"
        val lineItem = mockk<LineItem>(relaxed = true) {
            every { unitPrice } returns Amount(CurrencyCode.USD,100.0)
            every { grossAmount } returns Amount(CurrencyCode.USD,100.0)
        }
        val invoice = mockk<Invoice>(relaxed = true) {
            every { totalAmount } returns  Amount(CurrencyCode.USD,100.0)
            every { amountDue } returns Amount(CurrencyCode.USD,100.0)
            every { createdDate } returns LocalDate.now()
            every { dueDate } returns LocalDate.now().plusDays(30)
            every { status } returns CompanyPayableStatus.AUTHORIZED
            every { lineItems } returns listOf(lineItem)
            every { reference } returns referenceText
            every { multiplierExternalInvoiceNumber } returns MultiplierAccountingSystemIdentifier(invoiceNumber, MultiplierExternalSystem.NETSUITE)
        }
        val contact = "Test Contact"

        val result = mapper.mapFinancialTransactionToAccountingTransaction(invoice, contact) as AccountingTransactionInvoice

        assertEquals(invoice.totalAmount.value, result.totalAmount)
        assertEquals(invoice.amountDue.value, result.balance)
        assertEquals(invoice.createdDate, result.issueDate)
        assertEquals(invoice.dueDate, result.dueDate)
        assertEquals(invoice.status.name, result.status)
        assertEquals("$referenceText : $invoiceNumber", result.memo)
        assertEquals(contact, result.contact)
        assertEquals(invoice.lineItems.size, result.lineItems.size)
    }

    @Nested
    inner class MapLineItemToAccountingTransactionLineItemTest {

        private lateinit var mockInvoice: Invoice

        @BeforeEach
        fun setUp() {
            mockInvoice = mockk<Invoice>(relaxed = true)
        }

        @Test
        fun `should map line item with quantity 1 correctly`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 100.0)
            val lineItem = createLineItem(
                description = "Test Service",
                quantity = 1.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Test Service", result.description)
            assertEquals(1.0, result.quantity)
            assertEquals(100.0, result.unitPrice)
            assertEquals(100.0, result.totalAmount) // Should use grossAmount.value
            assertEquals("USD", result.commonFields?.currency)
        }

        @Test
        fun `should map line item with quantity greater than 1 correctly - Annual Management Fee case`() {
            // Given - This is the specific case from the RCA
            val unitPrice = Amount(CurrencyCode.USD, 400.0)
            val grossAmount = Amount(CurrencyCode.USD, 800.0) // unitPrice * quantity
            val lineItem = createLineItem(
                description = "Annual Management Fee for 2 members (GBR)",
                quantity = 2.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Annual Management Fee for 2 members (GBR)", result.description)
            assertEquals(2.0, result.quantity)
            assertEquals(400.0, result.unitPrice)
            assertEquals(800.0, result.totalAmount) // Should be grossAmount.value (800), not unitPrice.value (400)
            assertEquals("USD", result.commonFields?.currency)
        }

        @Test
        fun `should map line item with quantity 5 correctly`() {
            // Given
            val unitPrice = Amount(CurrencyCode.EUR, 50.0)
            val grossAmount = Amount(CurrencyCode.EUR, 250.0) // 50 * 5
            val lineItem = createLineItem(
                description = "Bulk Service Package",
                quantity = 5.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Bulk Service Package", result.description)
            assertEquals(5.0, result.quantity)
            assertEquals(50.0, result.unitPrice)
            assertEquals(250.0, result.totalAmount) // Should use grossAmount.value
            assertEquals("EUR", result.commonFields?.currency)
        }

        @Test
        fun `should map line item with fractional quantity correctly`() {
            // Given
            val unitPrice = Amount(CurrencyCode.GBP, 100.0)
            val grossAmount = Amount(CurrencyCode.GBP, 150.0) // 100 * 1.5
            val lineItem = createLineItem(
                description = "Partial Service",
                quantity = 1.5,
                unitPrice = unitPrice,
                grossAmount = grossAmount
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Partial Service", result.description)
            assertEquals(1.5, result.quantity)
            assertEquals(100.0, result.unitPrice)
            assertEquals(150.0, result.totalAmount) // Should use grossAmount.value
            assertEquals("GBP", result.commonFields?.currency)
        }

        @Test
        fun `should map line item with zero quantity correctly`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 0.0)
            val lineItem = createLineItem(
                description = "Cancelled Service",
                quantity = 0.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Cancelled Service", result.description)
            assertEquals(0.0, result.quantity)
            assertEquals(100.0, result.unitPrice)
            assertEquals(0.0, result.totalAmount) // Should use grossAmount.value
            assertEquals("USD", result.commonFields?.currency)
        }

        @Test
        fun `should map line item with negative total amount correctly`() {
            // Given - Credit/refund scenario
            val unitPrice = Amount(CurrencyCode.USD, -100.0)
            val grossAmount = Amount(CurrencyCode.USD, -200.0) // -100 * 2
            val lineItem = createLineItem(
                description = "Refund for 2 services",
                quantity = 2.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Refund for 2 services", result.description)
            assertEquals(2.0, result.quantity)
            assertEquals(-100.0, result.unitPrice)
            assertEquals(-200.0, result.totalAmount) // Should use grossAmount.value
            assertEquals("USD", result.commonFields?.currency)
        }

        @Test
        fun `should map line item with line item account mapping correctly`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 300.0)
            val externalAccount = AccountingExternalEntity("acc-123", "EXPENSE", "Office Supplies")
            val externalDepartment = AccountingExternalEntity("dept-456", "DEPARTMENT", "Sales")
            val lineItemAccountMapping = LineItemAccountMapping(
                lineItemType = "OFFICE_SUPPLIES",
                externalAccount = externalAccount,
                externalDepartments = setOf(externalDepartment)
            )

            val lineItem = createLineItem(
                description = "Office Supplies",
                quantity = 3.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                lineItemAccountMapping = lineItemAccountMapping
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Office Supplies", result.description)
            assertEquals(3.0, result.quantity)
            assertEquals(100.0, result.unitPrice)
            assertEquals(300.0, result.totalAmount)
            assertEquals("acc-123", result.item)
            assertEquals(listOf("dept-456"), result.commonFields?.trackingCategories)
        }

        @Test
        fun `should map line item without line item account mapping correctly`() {
            // Given
            val unitPrice = Amount(CurrencyCode.USD, 100.0)
            val grossAmount = Amount(CurrencyCode.USD, 100.0)
            val lineItem = createLineItem(
                description = "Service without mapping",
                quantity = 1.0,
                unitPrice = unitPrice,
                grossAmount = grossAmount,
                lineItemAccountMapping = null
            )

            // When
            val result = mapper.mapLineItemToAccountingTransactionLineItem(lineItem, mockInvoice)

            // Then
            assertEquals("Service without mapping", result.description)
            assertEquals(1.0, result.quantity)
            assertEquals(100.0, result.unitPrice)
            assertEquals(100.0, result.totalAmount)
            assertNull(result.item)
            assertEquals(emptyList<String>(), result.commonFields?.trackingCategories)
        }

        private fun createLineItem(
            description: String,
            quantity: Double,
            unitPrice: Amount,
            grossAmount: Amount,
            lineItemAccountMapping: LineItemAccountMapping? = null
        ): LineItem {
            return LineItem(
                description = description,
                quantity = quantity,
                unitPrice = unitPrice,
                tax = Tax(
                    taxType = "NO_TAX",
                    taxRate = "0.0",
                    amount = Amount(unitPrice.currencyCode, 0.0)
                ),
                contractId = 1L,
                memberName = "Test Member",
                lineItemType = "TEST_LINE_ITEM",
                amountInBaseCurrency = grossAmount,
                countryName = "USA",
                grossAmount = grossAmount,
                startInvoiceCycleDate = LocalDate.now(),
                endInvoiceCycleDate = LocalDate.now().plusDays(30),
                lineItemAccountMapping = lineItemAccountMapping
            )
        }
    }
}