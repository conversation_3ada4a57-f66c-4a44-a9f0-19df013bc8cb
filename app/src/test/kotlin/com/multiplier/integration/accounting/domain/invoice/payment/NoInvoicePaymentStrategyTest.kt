package com.multiplier.integration.accounting.domain.invoice.payment

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.verifyNoInteractions

class NoInvoicePaymentStrategyTest {

    private val noInvoicePaymentStrategy = NoInvoicePaymentStrategy()

    @Test
    fun getStrategyType() {
        assertEquals(InvoicePaymentStrategyType.NO_PAYMENT, noInvoicePaymentStrategy.getStrategyType())
    }

    @Test
    fun handle() {
        val invoicePaymentStrategyRequest = mock<InvoicePaymentStrategyRequest>()
        noInvoicePaymentStrategy.handle(invoicePaymentStrategyRequest)
        verifyNoInteractions(invoicePaymentStrategyRequest)
    }
}