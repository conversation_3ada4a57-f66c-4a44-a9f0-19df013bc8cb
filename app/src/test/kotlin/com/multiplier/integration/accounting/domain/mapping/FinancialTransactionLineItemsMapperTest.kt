package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.DepartmentService
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.common.Tax
import com.multiplier.integration.accounting.domain.model.Department
import com.multiplier.integration.accounting.domain.model.LineItem
import com.multiplier.integration.types.CurrencyCode
import io.mockk.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate

@ExtendWith(SpringExtension::class)
class FinancialTransactionLineItemsMapperTest {

    private lateinit var companyAccountingIntegrationMappingReadService: CompanyAccountingIntegrationMappingReadService
    private lateinit var departmentService: DepartmentService
    private lateinit var mapper: FinancialTransactionLineItemsMapper

    @BeforeEach
    fun setUp() {
        companyAccountingIntegrationMappingReadService = mockk(relaxed = true)
        departmentService = mockk(relaxed = true)
        mapper = FinancialTransactionLineItemsMapper(companyAccountingIntegrationMappingReadService, departmentService)
    }

    @Test
    fun `test lineItemMapping`() {
       val lineItem = LineItem(
            description = "Line Item 1",
            quantity = 1.0,
            contractId = 1L,
            unitPrice = Amount(CurrencyCode.USD, 0.0),
            tax = Tax(
                taxType = "taxCode",
                taxRate = "0.0",
                amount = Amount(CurrencyCode.USD, 0.0)
            ),
            memberName = "Member 1",
            lineItemType = "EOR_SALARY_DISBURSEMENT",
            amountInBaseCurrency = Amount(CurrencyCode.USD, 0.0),
            grossAmount = Amount(CurrencyCode.USD, 0.0),
            countryName = "USA",
            startInvoiceCycleDate = LocalDate.now(),
            endInvoiceCycleDate = LocalDate.now().plusDays(30)
        )

        val input = FinancialTransactionLineItemsMapperInput(
            integrationId = 1L,
            lineItems = listOf(lineItem)
        )

        val lineItemAccountMapping1 = LineItemAccountMapping(
            lineItemType = "EOR_SALARY_DISBURSEMENT",
            externalAccount = AccountingExternalEntity(
                "externalItemAccount",
                "externalItemAccountType",
                "externalItemAccountSubType"
            )
        )
        val accountingExternalEntity =
            AccountingExternalEntity("externalDepartment", "externalDepartmentType", "externalDepartmentSubType")

        val departmentMapping = mockk<DepartmentMapping>(relaxed = true){
            every { departmentId } returns 1L
            every { externalDepartmentMapping } returns accountingExternalEntity
            every { lineItemAccountMapping } returns listOf(lineItemAccountMapping1)
        }

        val companyAccountingIntegrationMapping = mockk<CompanyAccountingIntegrationMapping>(relaxed = true) {
            every { departmentMappings } returns listOf(departmentMapping)
        }

        every { companyAccountingIntegrationMappingReadService.read(any()) } returns companyAccountingIntegrationMapping
        every { departmentService.findDepartmentForContractId(any()) } returns mockk<Department>(relaxed = true) {
            every { id } returns 1L
        }

        val result = mapper.lineItemMapping(input)

        assertEquals(1, result.size)
        assertEquals(1, result[0].lineItemAccountMapping?.externalDepartments?.size)
    }

    @Test
    fun `test department id is less than zero`() {
        val lineItem = LineItem(
            description = "Line Item 1",
            quantity = 1.0,
            contractId = -1L,
            unitPrice = Amount(CurrencyCode.USD, 0.0),
            tax = Tax(
                taxType = "taxCode",
                taxRate = "0.0",
                amount = Amount(CurrencyCode.USD, 0.0)
            ),
            memberName = "Member 1",
            lineItemType = "EOR_SALARY_DISBURSEMENT",
            amountInBaseCurrency = Amount(CurrencyCode.USD, 0.0),
            grossAmount = Amount(CurrencyCode.USD, 0.0),
            countryName = "USA",
            startInvoiceCycleDate = LocalDate.now(),
            endInvoiceCycleDate = LocalDate.now().plusDays(30)
        )

        val input = FinancialTransactionLineItemsMapperInput(
            integrationId = 1L,
            lineItems = listOf(lineItem)
        )

        val lineItemAccountMapping1 = LineItemAccountMapping(
            lineItemType = "EOR_SALARY_DISBURSEMENT",
            externalAccount = AccountingExternalEntity(
                "externalItemAccount",
                "externalItemAccountType",
                "externalItemAccountSubType"
            )
        )

        val accountingExternalEntity =
            AccountingExternalEntity("externalDepartment", "externalDepartmentType", "externalDepartmentSubType")



        val departmentMapping = mockk<DepartmentMapping>(relaxed = true){
            every { departmentId } returns 1L
            every { externalDepartmentMapping } returns accountingExternalEntity
            every { lineItemAccountMapping } returns listOf(lineItemAccountMapping1)
        }

        val companyAccountingIntegrationMapping = mockk<CompanyAccountingIntegrationMapping>(relaxed = true) {
            every { departmentMappings } returns listOf(departmentMapping)
            every { fallbackLineItemMappings }  returns listOf(lineItemAccountMapping1)
        }

        every { companyAccountingIntegrationMappingReadService.read(any()) } returns companyAccountingIntegrationMapping
        every { departmentService.findDepartmentForContractId(any()) } returns mockk<Department>(relaxed = true) {
            every { id } returns 1L
        }

        val result = mapper.lineItemMapping(input)

        assertEquals(1, result.size)
        assertEquals(0, result[0].lineItemAccountMapping?.externalDepartments?.size)
    }
}