package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.types.CustomerIntegration
import io.mockk.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class InvoiceExternalSystemUpsertOrchestratorTest {

    private lateinit var customerIntegrationService: CustomerIntegrationService
    private lateinit var invoiceExternalSystemTransactionService: InvoiceExternalSystemTransactionService
    private lateinit var orchestrator: InvoiceExternalSystemUpsertOrchestrator

    @BeforeEach
    fun setUp() {
        customerIntegrationService = mockk(relaxed = true)
        invoiceExternalSystemTransactionService = mockk(relaxed = true)
        orchestrator = InvoiceExternalSystemUpsertOrchestrator(
            customerIntegrationService,
            invoiceExternalSystemTransactionService,
        )
    }

    @Test
    fun `test createHandler success`() {
        val companyPayable = mockk<CompanyPayable>(relaxed = true)
        val accountingIntegrationInfo = mockk<CustomerIntegration>(relaxed = true) {
            every { id } returns 1L
        }

        every { customerIntegrationService.getAccountingIntegrationInfo(any()) } returns accountingIntegrationInfo
        every { invoiceExternalSystemTransactionService.upsert(any()) } returns Unit

        orchestrator.upsertHandler(companyPayable)

        verify { customerIntegrationService.getAccountingIntegrationInfo(companyPayable.companyId) }
        verify { invoiceExternalSystemTransactionService.upsert(any()) }
        verify { invoiceExternalSystemTransactionService.upsert(any()) }
    }
}