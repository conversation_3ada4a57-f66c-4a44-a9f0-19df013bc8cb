package com.multiplier.integration.accounting.domain.mapping

import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.model.CompanyPayableStatus
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.accounting.domain.model.LineItem
import com.multiplier.integration.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.ZoneOffset

class AccountingTransactionVendorCreditMapperTest {
    private lateinit var accountingTransactionVendorCreditMapper: AccountingTransactionVendorCreditMapper

    @BeforeEach
    fun setup() {
        accountingTransactionVendorCreditMapper = AccountingTransactionVendorCreditMapper()
    }

    @Test
    fun `test mapFinancialTransactionToAccountingTransaction`() {
        val lineItem =
            mockk<LineItem>(relaxed = true) {
                every { lineItemAccountMapping } returns
                    LineItemAccountMapping(
                        lineItemType = "line item type",
                        externalDepartments = setOf(AccountingExternalEntity("id", "external id", "name")),
                        externalAccount = AccountingExternalEntity("id", "external id", "name"),
                    )
                every { grossAmount } returns Amount(value = 1.0, currencyCode = CurrencyCode.USD)
            }

        val transactionDate = LocalDate.now()

        val creditNote =
            mockk<CreditNote>(relaxed = true) {
                every { amountTotal } returns 100.0
                every { appliedToInvoiceIds } returns emptyList()
                every { createdDate } returns transactionDate
                every { amountApplied } returns 0.0
                every { status } returns CompanyPayableStatus.AUTHORIZED
                every { currencyCode } returns CurrencyCode.USD
                every { lineItems } returns listOf(lineItem)
            }

        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction>(relaxed = true) {
                every { externalSystemTransactionId } returns AccountingExternalEntity("id", "external id", "name")
            }

        val contact = "Test Contact"
        val result =
            accountingTransactionVendorCreditMapper.mapFinancialTransactionToAccountingTransaction(
                creditNote,
                contact,
                companyPayableExternalSystemTransaction,
            )

        assertEquals(
            creditNote.amountTotal.toString(),
            result.model.appliedToLines
                ?.get(0)
                ?.appliedAmount ?: 0.0,
        )
        assertEquals(
            creditNote.createdDate
                .atStartOfDay(ZoneOffset.systemDefault().rules.getOffset(creditNote.createdDate.atStartOfDay()))
                .toString(),
            result.model.transactionDate,
        )
        assertEquals(contact, result.model.vendor)
        assertEquals(creditNote.currencyCode.name, result.model.currency)
        assertEquals(creditNote.lineItems.size, result.model.lines?.size ?: 0)
        assertEquals(1, result.model.appliedToLines?.size ?: 0)
    }
}