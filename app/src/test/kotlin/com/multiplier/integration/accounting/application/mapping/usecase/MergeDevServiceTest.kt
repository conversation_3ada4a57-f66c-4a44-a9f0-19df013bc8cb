import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.creditnote.ExternalVendorCreditCreationRequest
import com.multiplier.integration.accounting.domain.mapping.*
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.adapter.api.MergeDevAdapter
import com.multiplier.integration.adapter.api.resources.financial.MergeDevInvoiceMessage
import com.multiplier.integration.adapter.api.resources.financial.MergeDevResponse
import com.multiplier.integration.adapter.api.resources.financial.MergeDevVendorCreditMessage
import com.multiplier.integration.adapter.api.resources.financial.vendorCredit.VendorCreditRequestBody
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*

@ExtendWith(SpringExtension::class)
class MergeDevServiceTest {
    @MockK
    private lateinit var mergeDevAdapter: MergeDevAdapter

    @MockK
    private lateinit var jpaCompanyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var companyPayableIntegrationMappingReadService: CompanyAccountingIntegrationMappingReadService

    @MockK
    private lateinit var companyPayableExternalSystemService: CompanyPayableExternalSystemService

    @MockK
    private lateinit var vendorCreditMapper: AccountingTransactionVendorCreditMapper

    @InjectMockKs
    private lateinit var mergeDevService: MergeDevService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `test createInvoice success`() {
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction>(relaxed = true) {
                every { integrationId } returns 1
                every { requestPayload } returns mockk(relaxed = true)
                every { requestPayload as AccountingTransactionInvoice } returns mockk(relaxed = true)
            }

        val companyIntegration =
            mockk<Optional<JpaCompanyIntegration>>(relaxed = true) {
                every { isPresent } returns true
                every { get() } returns
                    mockk(relaxed = true) {
                        every { accountToken } returns "accountToken"
                    }
            }
        val mergeDevInvoiceMessage =
            mockk<MergeDevInvoiceMessage>(relaxed = true) {
                every { id } returns "id"
                every { remoteId } returns "remoteId"
            }
        val createInvoiceResponse =
            mockk<MergeDevResponse>(relaxed = true) {
                every { success } returns true
                every { message } returns mergeDevInvoiceMessage
            }

        every { jpaCompanyIntegrationRepository.findById(any()) } returns companyIntegration
        every { mergeDevAdapter.createInvoice(any()) } returns createInvoiceResponse

        val result = mergeDevService.createInvoice(companyPayableExternalSystemTransaction)

        assertNotNull(result)
        verify { jpaCompanyIntegrationRepository.findById(any()) }
        verify { mergeDevAdapter.createInvoice(any()) }
    }

    @Test
    fun `test createInvoice failure`() {
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction>(relaxed = true) {
                every { integrationId } returns 1
                every { requestPayload } returns mockk(relaxed = true)
                every { requestPayload as AccountingTransactionInvoice } returns mockk(relaxed = true)
            }

        val companyIntegration =
            mockk<Optional<JpaCompanyIntegration>>(relaxed = true) {
                every { isPresent } returns true
                every { get() } returns
                    mockk(relaxed = true) {
                        every { accountToken } returns "accountToken"
                    }
            }
        val mergeDevInvoiceMessage =
            mockk<MergeDevInvoiceMessage>(relaxed = true) {
                every { id } returns "id"
                every { remoteId } returns "remoteId"
            }
        val createInvoiceResponse =
            mockk<MergeDevResponse>(relaxed = true) {
                every { success } returns false
                every { message } returns mergeDevInvoiceMessage
            }

        every { jpaCompanyIntegrationRepository.findById(any()) } returns companyIntegration
        every { mergeDevAdapter.createInvoice(any()) } returns createInvoiceResponse

        assertThrows<RuntimeException> {
            mergeDevService.createInvoice(companyPayableExternalSystemTransaction)
        }
        verify { jpaCompanyIntegrationRepository.findById(any()) }
        verify { mergeDevAdapter.createInvoice(any()) }
    }

    @Test
    fun `test updateInvoice success`() {
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction>(relaxed = true) {
                every { integrationId } returns 1
                every { requestPayload } returns mockk(relaxed = true)
                every { requestPayload as AccountingTransactionInvoice } returns mockk(relaxed = true)
            }

        val companyIntegration =
            mockk<Optional<JpaCompanyIntegration>>(relaxed = true) {
                every { isPresent } returns true
                every { get() } returns
                    mockk(relaxed = true) {
                        every { accountToken } returns "accountToken"
                    }
            }
        val mergeDevInvoiceMessage =
            mockk<MergeDevInvoiceMessage>(relaxed = true) {
                every { id } returns "id"
                every { remoteId } returns "remoteId"
            }
        val createInvoiceResponse =
            mockk<MergeDevResponse>(relaxed = true) {
                every { success } returns true
                every { message } returns mergeDevInvoiceMessage
            }

        val accountingExternalEntity =
            mockk<AccountingExternalEntity>(relaxed = true) {
                every { id } returns "id"
            }

        every { jpaCompanyIntegrationRepository.findById(any()) } returns companyIntegration
        every { mergeDevAdapter.updateInvoice(any(), any()) } returns createInvoiceResponse

        val result = mergeDevService.updateInvoice(companyPayableExternalSystemTransaction, accountingExternalEntity)

        assertNotNull(result)
        verify { jpaCompanyIntegrationRepository.findById(any()) }
        verify { mergeDevAdapter.updateInvoice(any(), any()) }
    }

    @Test
    fun `test updateInvoice failure`() {
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction>(relaxed = true) {
                every { integrationId } returns 1
                every { requestPayload } returns mockk(relaxed = true)
                every { requestPayload as AccountingTransactionInvoice } returns mockk(relaxed = true)
            }

        val companyIntegration =
            mockk<Optional<JpaCompanyIntegration>>(relaxed = true) {
                every { isPresent } returns true
                every { get() } returns
                    mockk(relaxed = true) {
                        every { accountToken } returns "accountToken"
                    }
            }
        val mergeDevInvoiceMessage =
            mockk<MergeDevInvoiceMessage>(relaxed = true) {
                every { id } returns "id"
                every { remoteId } returns "remoteId"
            }
        val createInvoiceResponse =
            mockk<MergeDevResponse>(relaxed = true) {
                every { success } returns false
                every { message } returns mergeDevInvoiceMessage
            }

        val accountingExternalEntity =
            mockk<AccountingExternalEntity>(relaxed = true) {
                every { id } returns "id"
            }

        every { jpaCompanyIntegrationRepository.findById(any()) } returns companyIntegration
        every { mergeDevAdapter.updateInvoice(any(), any()) } returns createInvoiceResponse

        assertThrows<RuntimeException> {
            mergeDevService.updateInvoice(companyPayableExternalSystemTransaction, accountingExternalEntity)
        }
        verify { jpaCompanyIntegrationRepository.findById(any()) }
        verify { mergeDevAdapter.updateInvoice(any(), any()) }
    }

    @Test
    fun `test getCompanyIntegration exception`() {
        val companyIntegration =
            mockk<Optional<JpaCompanyIntegration>>(relaxed = true) {
                every { isPresent } returns false
                every { get() } returns mockk(relaxed = true)
            }

        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction>(relaxed = true) {
                every { integrationId } returns 1
                every { requestPayload } returns mockk(relaxed = true)
                every { requestPayload as AccountingTransactionInvoice } returns mockk(relaxed = true)
            }

        val accountingExternalEntity =
            mockk<AccountingExternalEntity>(relaxed = true) {
                every { id } returns "id"
            }

        every { jpaCompanyIntegrationRepository.findById(any()) } returns companyIntegration

        assertThrows<IllegalArgumentException> {
            mergeDevService.updateInvoice(companyPayableExternalSystemTransaction, accountingExternalEntity)
        }
        verify { jpaCompanyIntegrationRepository.findById(any()) }
    }

    @Test
    fun `test createVendorCredit`() {
        val mockedCreditNote =
            mockk<CreditNote> {
                every { lineItems } returns emptyList()
                every { companyPayableId } returns 1L
                every { companyId } returns 1L
            }
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction> {
                every { id } returns 1L
            }
        val externalVendorCreditCreationRequest =
            mockk<ExternalVendorCreditCreationRequest> {
                every { integrationId } returns 1L
                every { invoicedExternalSystemCompanyPayable } returns companyPayableExternalSystemTransaction
                every { creditNote } returns mockedCreditNote
                every { companyId } returns 1L
            }
        val accountingExternalEntity =
            mockk<AccountingExternalEntity> {
                every { id } returns "vendor contact id"
            }
        val externalMultiplierVendorMapping =
            mockk<ExternalMultiplierVendorMapping> {
                every { externalVendor } returns accountingExternalEntity
            }
        val jpaCompanyIntegration =
            mockk<JpaCompanyIntegration> {
                every { accountToken } returns "account token"
            }
        val vendorCreditRequest = mockk<VendorCreditRequestBody>()

        val mergeDevVendorCreditMessage =
            mockk<MergeDevVendorCreditMessage> {
                every { id } returns "id"
                every { remoteId } returns "remote id"
            }
        val mergeDevResponse =
            mockk<MergeDevResponse> {
                every { message } returns mergeDevVendorCreditMessage
            }

        every { jpaCompanyIntegrationRepository.findById(any()) } returns Optional.of(jpaCompanyIntegration)
        every { vendorCreditMapper.mapFinancialTransactionToAccountingTransaction(any(), any(), any()) } returns vendorCreditRequest
        every { companyPayableExternalSystemService.createExternalSystemCompanyPayable(any()) } returns
            companyPayableExternalSystemTransaction
        every { companyPayableIntegrationMappingReadService.readExternalVendorMapping(any()) } returns externalMultiplierVendorMapping
        every { mergeDevAdapter.createVendorCredit(any()) } returns mergeDevResponse
        every {
            companyPayableExternalSystemTransaction.copy(
                externalSystemTransactionId = any(),
            )
        } returns companyPayableExternalSystemTransaction
        every { companyPayableExternalSystemService.updateExternalSystemCompanyPayable(any()) } returns
            companyPayableExternalSystemTransaction

        mergeDevService.createVendorCredit(externalVendorCreditCreationRequest)
    }
}