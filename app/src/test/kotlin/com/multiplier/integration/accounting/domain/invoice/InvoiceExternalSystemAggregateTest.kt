package com.multiplier.integration.accounting.domain.invoice

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.creditnote.CreditNoteServiceAdapter
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategy
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyFactory
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyFactoryInput
import com.multiplier.integration.accounting.domain.invoice.payment.InvoicePaymentStrategyRequest
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.Invoice
import com.multiplier.integration.types.CurrencyCode
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.*
import kotlin.test.assertEquals

@ExtendWith(MockitoExtension::class)
class InvoiceExternalSystemAggregateTest {

    @Test
    fun `when external transaction is not present, throw exception`() {
        val companyPayable = mock<CompanyPayable>()
        val invoicePaymentStrategyFactory = mock<InvoicePaymentStrategyFactory>()
        val creditNoteServiceAdapter = mock<CreditNoteServiceAdapter>()
        val mergeDevService = mock<MergeDevService>()

        val invoiceExternalSystemAggregate = InvoiceExternalSystemAggregate(null)

        assertThrows<IllegalArgumentException> {
            invoiceExternalSystemAggregate.updateAmount(
                companyPayable = companyPayable,
                invoicePaymentStrategyFactory = invoicePaymentStrategyFactory,
                creditNoteServiceAdapter = creditNoteServiceAdapter,
                mergeDevService = mergeDevService,
            )
        }
    }

    @Test
    fun `when the transaction is not invoice, throw exception`() {
        val companyPayable = mock<CompanyPayable>()
        val invoicePaymentStrategyFactory = mock<InvoicePaymentStrategyFactory>()
        val creditNoteServiceAdapter = mock<CreditNoteServiceAdapter>()
        val mergeDevService = mock<MergeDevService>()
        val companyPayableExternalSystemTransaction = mock<CompanyPayableExternalSystemTransaction>()

        val invoiceExternalSystemAggregate = InvoiceExternalSystemAggregate(companyPayableExternalSystemTransaction)

        assertThrows<IllegalArgumentException> {
            invoiceExternalSystemAggregate.updateAmount(
                companyPayable = companyPayable,
                invoicePaymentStrategyFactory = invoicePaymentStrategyFactory,
                creditNoteServiceAdapter = creditNoteServiceAdapter,
                mergeDevService = mergeDevService,
            )
        }
    }

    @Test
    fun `when the applied credit note size is not one, throw exception`() {
        val companyPayable = mock<Invoice>()
        val invoicePaymentStrategyFactory = mock<InvoicePaymentStrategyFactory>()
        val creditNoteServiceAdapter = mock<CreditNoteServiceAdapter>()
        val mergeDevService = mock<MergeDevService>()
        val companyPayableExternalSystemTransaction = mock<CompanyPayableExternalSystemTransaction>()
        val creditNoteId1 = 1L
        val creditNoteId2 = 2L

        doReturn(listOf(creditNoteId2, creditNoteId1)).`when`(companyPayable).appliedCreditNoteIds

        val invoiceExternalSystemAggregate = InvoiceExternalSystemAggregate(companyPayableExternalSystemTransaction)

        verifyNoInteractions(creditNoteServiceAdapter)
        assertThrows<IllegalArgumentException> {
            invoiceExternalSystemAggregate.updateAmount(
                companyPayable = companyPayable,
                invoicePaymentStrategyFactory = invoicePaymentStrategyFactory,
                creditNoteServiceAdapter = creditNoteServiceAdapter,
                mergeDevService = mergeDevService,
            )
        }
    }

    @Test
    fun `when the credit note is applied and size is one, vanilla case call`() {
        val invoicePaymentStrategyFactoryRequest = argumentCaptor<InvoicePaymentStrategyFactoryInput>()
        val invoicePaymentStrategyRequest = argumentCaptor<InvoicePaymentStrategyRequest>()
        val companyPayable = mock<Invoice>()
        val invoicePaymentStrategyFactory = mock<InvoicePaymentStrategyFactory>()
        val creditNoteServiceAdapter = mock<CreditNoteServiceAdapter>()
        val mergeDevService = mock<MergeDevService>()
        val companyPayableExternalSystemTransaction = mock<CompanyPayableExternalSystemTransaction>()
        val externalTransactionAmount = mock<ExternalCompanyPayableAmount>()
        val creditNoteId = 1L
        val creditNoteAmount = Amount(CurrencyCode.USD, 100.00)
        val integrationId = 100L
        val companyId = 100L
        val invoiceId = 100L
        val invoicePaymentStrategy = mock<InvoicePaymentStrategy>()

        doReturn(listOf(creditNoteId)).`when`(companyPayable).appliedCreditNoteIds
        doReturn(invoiceId).`when`(companyPayable).id
        doReturn(creditNoteAmount)
            .`when`(creditNoteServiceAdapter)
            .getTotalAmountForCreditNotes(invoiceId, listOf(creditNoteId))
        doReturn(externalTransactionAmount)
            .`when`(mergeDevService)
            .getAmountsForExternalTransaction(companyPayableExternalSystemTransaction)
        doReturn(integrationId).`when`(companyPayableExternalSystemTransaction).integrationId
        doReturn(companyId).`when`(companyPayable).companyId
        doReturn(invoicePaymentStrategy).`when`(invoicePaymentStrategyFactory).getInvoicePaymentStrategy(any())

        val invoiceExternalSystemAggregate = InvoiceExternalSystemAggregate(companyPayableExternalSystemTransaction)

        invoiceExternalSystemAggregate.updateAmount(
            companyPayable = companyPayable,
            invoicePaymentStrategyFactory = invoicePaymentStrategyFactory,
            creditNoteServiceAdapter = creditNoteServiceAdapter,
            mergeDevService = mergeDevService,
        )

        verify(invoicePaymentStrategy).handle(invoicePaymentStrategyRequest.capture())
        verify(invoicePaymentStrategyFactory).getInvoicePaymentStrategy(invoicePaymentStrategyFactoryRequest.capture())

        val factoryInput = invoicePaymentStrategyFactoryRequest.firstValue
        val handlerRequest = invoicePaymentStrategyRequest.firstValue

        assertEquals(companyPayable, factoryInput.invoice)
        assertEquals(externalTransactionAmount, factoryInput.externalCompanyPayableAmount)
        assertEquals(creditNoteAmount, factoryInput.totalCreditNoteAmount)

        assertEquals(companyPayable, handlerRequest.invoice)
        assertEquals(externalTransactionAmount, handlerRequest.currentExternalInvoiceAmount)
        assertEquals(creditNoteAmount, handlerRequest.appliedCreditNoteAmount)
        assertEquals(integrationId, handlerRequest.integrationId)
        assertEquals(companyId, handlerRequest.companyId)
    }

}