package com.multiplier.integration.accounting.domain.invoice.payment

import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.model.Invoice
import com.multiplier.integration.accounting.domain.payment.PaymentCreateRequest
import com.multiplier.integration.accounting.domain.payment.PaymentCreateUseCase
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

@ExtendWith(MockitoExtension::class)
class InvoicePaymentWithCreditNoteAndPaymentVoucherStrategyTest {

    @Mock
    private lateinit var paymentCreateUseCase: PaymentCreateUseCase
    @InjectMocks
    private lateinit var invoicePaymentWithCreditNoteAndPaymentVoucherStrategy: InvoicePaymentWithCreditNoteAndPaymentVoucherStrategy

    @Test
    fun getStrategyType() {
        assertEquals(
            InvoicePaymentStrategyType.CREDIT_NOTE_AND_PAYMENT_VOUCHER,
            invoicePaymentWithCreditNoteAndPaymentVoucherStrategy.getStrategyType()
        )
    }

    @Test
    fun handle() {
        val paymentCreateRequest = argumentCaptor<PaymentCreateRequest> {  }
        val invoicePaymentStrategyRequest = mock<InvoicePaymentStrategyRequest>()
        val invoice = mock<Invoice>()
        val currentExternalInvoiceAmount = mock<ExternalCompanyPayableAmount>()
        val integrationId = 1L
        val companyId = 100L

        doReturn(invoice).`when`(invoicePaymentStrategyRequest).invoice
        doReturn(currentExternalInvoiceAmount).`when`(invoicePaymentStrategyRequest).currentExternalInvoiceAmount
        doReturn(null).`when`(invoicePaymentStrategyRequest).appliedCreditNoteAmount
        doReturn(integrationId).`when`(invoicePaymentStrategyRequest).integrationId
        doReturn(companyId).`when`(invoicePaymentStrategyRequest).companyId

        invoicePaymentWithCreditNoteAndPaymentVoucherStrategy.handle(invoicePaymentStrategyRequest)

        verify(paymentCreateUseCase).createPaymentUseCase(paymentCreateRequest.capture())

        val paymentRequestActual = paymentCreateRequest.firstValue
        assertEquals(invoice, paymentRequestActual.invoice)
        assertEquals(currentExternalInvoiceAmount, paymentRequestActual.currentExternalInvoiceAmount)
        assertEquals(integrationId, paymentRequestActual.integrationId)
        assertEquals(companyId, paymentRequestActual.companyId)
        assertEquals(null, paymentRequestActual.appliedCreditNotesAmount)
    }
}