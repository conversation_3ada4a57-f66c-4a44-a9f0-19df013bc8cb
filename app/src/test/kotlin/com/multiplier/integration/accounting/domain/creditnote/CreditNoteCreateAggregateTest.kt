package com.multiplier.integration.accounting.domain.creditnote

import com.multiplier.integration.accounting.application.mapping.usecase.MergeDevService
import com.multiplier.integration.accounting.domain.CompanyPayableAdapter
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemService
import com.multiplier.integration.accounting.domain.CompanyPayableExternalSystemTransaction
import com.multiplier.integration.accounting.domain.ExternalCompanyPayableAmount
import com.multiplier.integration.accounting.domain.common.Amount
import com.multiplier.integration.accounting.domain.mapping.FinancialTransactionLineItemsMapper
import com.multiplier.integration.accounting.domain.model.CompanyPayable
import com.multiplier.integration.accounting.domain.model.CreditNote
import com.multiplier.integration.accounting.domain.model.CreditNoteStatus
import com.multiplier.integration.types.CurrencyCode
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class CreditNoteCreateAggregateTest {
    @Test
    fun `when companyPayable is not a credit note then throw exception`() {
        val companyPayable =
            mockk<CompanyPayable> {
                every { companyPayableId } returns 1L
            }

        val companyPayableAdapter = mockk<CompanyPayableAdapter>()
        val companyPayableExternalSystemService = mockk<CompanyPayableExternalSystemService>()
        val mergeDevService = mockk<MergeDevService>()
        val financialTransactionLineItemsMapper = mockk<FinancialTransactionLineItemsMapper>()
        val creditNoteCreateAggregate = CreditNoteCreateAggregate()

        val exception =
            assertThrows<IllegalArgumentException> {
                creditNoteCreateAggregate.handle(
                    companyPayableAdapter = companyPayableAdapter,
                    companyPayableExternalSystemService = companyPayableExternalSystemService,
                    financialTransactionLineItemsMapper = financialTransactionLineItemsMapper,
                    mergeDevService = mergeDevService,
                    companyPayable = companyPayable,
                )
            }
        assertEquals(
            "the company payable(id = 1) must be a credit note",
            exception.message,
        )
    }

    @Test
    fun `when credit note does not have only one applied invoices then throw exception`() {
        val companyPayableAdapter = mockk<CompanyPayableAdapter>()
        val companyPayableExternalSystemService = mockk<CompanyPayableExternalSystemService>()
        val mergeDevService = mockk<MergeDevService>()
        val financialTransactionLineItemsMapper = mockk<FinancialTransactionLineItemsMapper>()
        val companyPayable =
            mockk<CreditNote> {
                every { creditNoteStatus } returns CreditNoteStatus.FULLY_APPLIED
                every { companyPayableId } returns 1L
                every { id } returns 1L
                every { appliedToInvoiceIds } returns emptyList()
            }

        val creditNoteCreateAggregate = CreditNoteCreateAggregate()

        val exception =
            assertThrows<IllegalArgumentException> {
                creditNoteCreateAggregate.handle(
                    companyPayableAdapter = companyPayableAdapter,
                    companyPayableExternalSystemService = companyPayableExternalSystemService,
                    financialTransactionLineItemsMapper = financialTransactionLineItemsMapper,
                    mergeDevService = mergeDevService,
                    companyPayable = companyPayable,
                )
            }
        assertEquals(
            "Constraint violation. Constraint = At-most one credit note is applied to invoice. Company Payable id = 1 and applied to invoice = []",
            exception.message,
        )
    }

    @Test
    fun `when for credit note applied invoice, not found the payable id then throw an exception`() {
        val companyPayableAdapter = mockk<CompanyPayableAdapter>()
        val companyPayableExternalSystemService = mockk<CompanyPayableExternalSystemService>()
        val mergeDevService = mockk<MergeDevService>()
        val financialTransactionLineItemsMapper = mockk<FinancialTransactionLineItemsMapper>()
        val companyPayable =
            mockk<CreditNote> {
                every { creditNoteStatus } returns CreditNoteStatus.FULLY_APPLIED
                every { companyPayableId } returns 1L
                every { id } returns 1L
                every { appliedToInvoiceIds } returns listOf(1L)
            }

        val creditNoteCreateAggregate = CreditNoteCreateAggregate()

        every { companyPayableAdapter.getCompanyPayableIdsFromInvoiceIds(any()) } returns emptyList()

        val exception =
            assertThrows<IllegalArgumentException> {
                creditNoteCreateAggregate.handle(
                    companyPayableAdapter = companyPayableAdapter,
                    companyPayableExternalSystemService = companyPayableExternalSystemService,
                    financialTransactionLineItemsMapper = financialTransactionLineItemsMapper,
                    mergeDevService = mergeDevService,
                    companyPayable = companyPayable,
                )
            }

        assertEquals("Could not find the invoice for invoiceIds: [1]", exception.message)
    }

    @Test
    fun `when for credit note applied invoice, not found the external company payable then throw an exception`() {
        val companyPayableAdapter = mockk<CompanyPayableAdapter>()
        val companyPayableExternalSystemService = mockk<CompanyPayableExternalSystemService>()
        val mergeDevService = mock<MergeDevService>()
        val financialTransactionLineItemsMapper = mockk<FinancialTransactionLineItemsMapper>()
        val companyPayable =
            mockk<CreditNote> {
                every { creditNoteStatus } returns CreditNoteStatus.FULLY_APPLIED
                every { companyPayableId } returns 1L
                every { id } returns 1L
                every { appliedToInvoiceIds } returns listOf(1L)
            }

        val creditNoteCreateAggregate = CreditNoteCreateAggregate()

        every { companyPayableAdapter.getCompanyPayableIdsFromInvoiceIds(any()) } returns listOf(1L)
        every { companyPayableExternalSystemService.findExternalSystemCompanyPayable(any(), any()) } returns null

        val exception =
            assertThrows<IllegalArgumentException> {
                creditNoteCreateAggregate.handle(
                    companyPayableAdapter = companyPayableAdapter,
                    companyPayableExternalSystemService = companyPayableExternalSystemService,
                    financialTransactionLineItemsMapper = financialTransactionLineItemsMapper,
                    mergeDevService = mergeDevService,
                    companyPayable = companyPayable,
                )
            }

        assertEquals("Invoice is not present for this company payable: 1", exception.message)
    }

    @Test
    fun `when for credit note applied invoice, external transaction amount difference throw an exception`() {
        val companyPayableAdapter = mockk<CompanyPayableAdapter>()
        val companyPayableExternalSystemService = mockk<CompanyPayableExternalSystemService>()
        val mergeDevService = mockk<MergeDevService>()
        val financialTransactionLineItemsMapper = mockk<FinancialTransactionLineItemsMapper>()
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction> {
                every { companyPayableId } returns 1L
            }
        val companyPayable =
            mockk<CreditNote> {
                every { creditNoteStatus } returns CreditNoteStatus.FULLY_APPLIED
                every { companyPayableId } returns 1L
                every { id } returns 1L
                every { appliedToInvoiceIds } returns listOf(1L)
                every { amountApplied } returns 110.0
                every { amountTotal } returns 110.0
            }

        val creditNoteCreateAggregate = CreditNoteCreateAggregate()

        every { companyPayableAdapter.getCompanyPayableIdsFromInvoiceIds(any()) } returns listOf(1L)
        every { companyPayableExternalSystemService.findExternalSystemCompanyPayable(any(), any()) } returns
            companyPayableExternalSystemTransaction
        every { mergeDevService.getAmountsForExternalTransaction(any()) } returns
            ExternalCompanyPayableAmount(
                dueAmount = Amount(CurrencyCode.USD, 100.0),
                totalAmount = Amount(CurrencyCode.USD, 100.0),
                paidAmount = Amount(CurrencyCode.USD, 0.0),
            )

        val exception =
            assertThrows<IllegalArgumentException> {
                creditNoteCreateAggregate.handle(
                    companyPayableAdapter = companyPayableAdapter,
                    companyPayableExternalSystemService = companyPayableExternalSystemService,
                    financialTransactionLineItemsMapper = financialTransactionLineItemsMapper,
                    mergeDevService = mergeDevService,
                    companyPayable = companyPayable,
                )
            }

        assertEquals(
            "credit note applied amount is not less than or equal to due amount.\n" +
                "credit note amount: 110.0 for company payable id: 1\n" +
                "external due amount: 100.0 for company payable id: 1",
            exception.message,
        )
    }

    @Test
    fun `successfully create vendor credit note`() {
        val companyPayableAdapter = mockk<CompanyPayableAdapter>()
        val companyPayableExternalSystemService = mockk<CompanyPayableExternalSystemService>()
        val mergeDevService = mockk<MergeDevService>()
        val financialTransactionLineItemsMapper = mockk<FinancialTransactionLineItemsMapper>()
        val companyPayableExternalSystemTransaction =
            mockk<CompanyPayableExternalSystemTransaction> {
                every { companyPayableId } returns 1L
                every { integrationId } returns 1L
            }
        val companyPayable =
            mockk<CreditNote> {
                every { creditNoteStatus } returns CreditNoteStatus.FULLY_APPLIED
                every { companyPayableId } returns 1L
                every { id } returns 1L
                every { appliedToInvoiceIds } returns listOf(1L)
                every { amountApplied } returns 10.0
                every { amountTotal } returns 10.0
                every { lineItems } returns emptyList()
                every { companyId } returns 1L
            }

        val creditNoteCreateAggregate = CreditNoteCreateAggregate()

        every { companyPayableAdapter.getCompanyPayableIdsFromInvoiceIds(any()) } returns listOf(1L)
        every { companyPayableExternalSystemService.findExternalSystemCompanyPayable(any(), any()) } returns
            companyPayableExternalSystemTransaction
        every { financialTransactionLineItemsMapper.lineItemMapping(any()) } returns emptyList()
        every { companyPayable.copy(lineItems = any()) } returns companyPayable
        every { mergeDevService.getAmountsForExternalTransaction(any()) } returns
            ExternalCompanyPayableAmount(
                dueAmount = Amount(CurrencyCode.USD, 100.0),
                totalAmount = Amount(CurrencyCode.USD, 100.0),
                paidAmount = Amount(CurrencyCode.USD, 0.0),
            )
        every { mergeDevService.createVendorCredit(any()) } returns companyPayableExternalSystemTransaction

        creditNoteCreateAggregate.handle(
            companyPayableAdapter = companyPayableAdapter,
            companyPayableExternalSystemService = companyPayableExternalSystemService,
            financialTransactionLineItemsMapper = financialTransactionLineItemsMapper,
            mergeDevService = mergeDevService,
            companyPayable = companyPayable,
        )
    }
}