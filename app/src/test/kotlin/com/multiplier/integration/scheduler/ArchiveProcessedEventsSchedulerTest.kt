package com.multiplier.integration.scheduler

import com.multiplier.integration.repository.EventLogArchiveRepository
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.type.EventStatus
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class ArchiveProcessedEventsSchedulerTest {
    @MockK
    lateinit var eventLogArchiveRepository: EventLogArchiveRepository
    @MockK
    lateinit var eventLogRepository: EventLogRepository
    @MockK
    lateinit var receivedEventsArchiveRepository: ReceivedEventsArchiveRepository
    @MockK
    lateinit var receivedEventsRepository: ReceivedEventRepository

    @InjectMockKs
    lateinit var testScheduler: ArchiveProcessedEventsScheduler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should process successfully`() {
        val eventLogs = listOf(
            JpaEventLog(
                eventId = "eventLog.eventId",
                eventType = com.multiplier.integration.repository.type.EventType.SERVICE_INTERNAL_CREATE_CONTRACT,
                eventPayload = "eventLog.eventPayload",
                status = EventStatus.SUCCESS,
            )
        )
        val receivedEvents = listOf(
            JpaReceivedEvent(
                eventId = "event-id",
                syncId = "sync-id",
                integrationId = "integrationId",
                eventType = null,
                syncDataType = null,
                errors = null,
                identifiervalue = null,
                receivedTime = null,
                data = null,
                confirmedByUser = null,
                processed = null,
                isEntityEnabled = false,
                entityId = null,
                entityCountry = null
            )
        )

        every { eventLogRepository.findByStatusIn(any()) } returns eventLogs
        every { receivedEventsRepository.findByProcessed(any()) } returns receivedEvents
        every { eventLogArchiveRepository.save(any()) } returns mockk(relaxed = true)
        every { receivedEventsArchiveRepository.save(any()) } returns mockk(relaxed = true)
        justRun { receivedEventsRepository.delete(any()) }
        justRun { eventLogRepository.delete(any()) }
        testScheduler.archiveProcessedEvents()

        verify(exactly = 1) { eventLogRepository.delete(any()) }
        verify(exactly = 1) { receivedEventsRepository.delete(any()) }
    }
}