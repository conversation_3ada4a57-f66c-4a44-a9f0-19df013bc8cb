package com.multiplier.integration.scheduler

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.platforms.BambooHRPlatformStrategy
import com.multiplier.integration.platforms.HibobHRPlatformStrategy
import com.multiplier.integration.platforms.KekaHRPlatformStrategy
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.service.TimeoffService
import com.multiplier.integration.service.exception.IntegrationNotFoundException
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class EORTimeOffSchedulerTest {

    private lateinit var integrationRepository: CompanyIntegrationRepository
    private lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository
    private lateinit var timeOffServiceAdapter: TimeoffServiceAdapter
    private lateinit var knitAdapter: KnitAdapter
    private lateinit var platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository
    private lateinit var timeOffService: TimeoffService
    private lateinit var timeOffSyncService: TimeOffSyncService
    private lateinit var eorTimeOffScheduler: EORTimeOffScheduler
    private lateinit var bambooHRPlatformStrategy: BambooHRPlatformStrategy
    private lateinit var kekaHRPlatformStrategy: KekaHRPlatformStrategy
    private lateinit var hibobHRPlatformStrategy: HibobHRPlatformStrategy

    @BeforeEach
    fun setup() {
        integrationRepository = mockk()
        platformContractIntegrationRepository = mockk()
        timeOffServiceAdapter = mockk()
        knitAdapter = mockk()
        platformTimeoffDataRepository = mockk()
        timeOffService = mockk()
        timeOffSyncService = mockk()
        bambooHRPlatformStrategy = mockk()
        kekaHRPlatformStrategy = mockk()
        hibobHRPlatformStrategy = mockk()

        eorTimeOffScheduler = EORTimeOffScheduler(
            integrationRepository,
            platformContractIntegrationRepository,
            timeOffServiceAdapter,
            knitAdapter,
            platformTimeoffDataRepository,
            timeOffService,
            timeOffSyncService,
            bambooHRPlatformStrategy,
            kekaHRPlatformStrategy,
            hibobHRPlatformStrategy
        )
    }

    @Test
    fun `processEORTimeOffs should process all integrations and time offs`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        every { integration.id } returns 1L
        every { integration.platform.name } returns "BambooHR"

        coEvery { integrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration)
        coEvery { bambooHRPlatformStrategy.processTimeOff(integration) } just Runs

        eorTimeOffScheduler.processEORTimeOffs()

        coVerify { integrationRepository.findEORTimeoffEnabledIntegration() }
        coVerify { bambooHRPlatformStrategy.processTimeOff(integration) }
    }

    @Test
    fun `processEORTimeOffs handles specific integrationId`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        every { integration.id } returns 1L
        every { integration.platform.name } returns "BambooHR"

        coEvery { integrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration)
        coEvery { bambooHRPlatformStrategy.processTimeOff(integration) } just Runs

        eorTimeOffScheduler.processEORTimeOffs(1L)

        coVerify { integrationRepository.findEORTimeoffEnabledIntegration() }
        coVerify { bambooHRPlatformStrategy.processTimeOff(integration) }
    }

    @Test
    fun `processEORTimeOffs handles unknown platform`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        every { integration.id } returns 1L
        every { integration.platform.name } returns "UnknownPlatform"

        coEvery { integrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration)

        eorTimeOffScheduler.processEORTimeOffs()

        coVerify { integrationRepository.findEORTimeoffEnabledIntegration() }
        coVerify(exactly = 0) { bambooHRPlatformStrategy.processTimeOff(any()) }
        coVerify(exactly = 0) { kekaHRPlatformStrategy.processTimeOff(any()) }
        coVerify(exactly = 0) { hibobHRPlatformStrategy.processTimeOff(any()) }
    }

    @Test
    fun `processEORTimeOffs handles not found integrationId`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        every { integration.platform.name } returns "BambooHR"
        every { integration.id } returns 2L
        coEvery { integrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration)
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf()

        assertThrows<IntegrationNotFoundException> {
            eorTimeOffScheduler.processEORTimeOffs(1L)
        }
        coVerify { integrationRepository.findEORTimeoffEnabledIntegration() }
        coVerify(exactly = 0) { bambooHRPlatformStrategy.processTimeOff(any()) }
        coVerify(exactly = 0) { kekaHRPlatformStrategy.processTimeOff(any()) }
        coVerify(exactly = 0) { hibobHRPlatformStrategy.processTimeOff(any()) }

    }

    @Test
    fun `processEORTimeOffs skips processing when no integrations are found`() = runBlocking {
        coEvery { integrationRepository.findEORTimeoffEnabledIntegration() } returns emptyList()

        eorTimeOffScheduler.processEORTimeOffs()

        coVerify { integrationRepository.findEORTimeoffEnabledIntegration() }
        coVerify(exactly = 0) { bambooHRPlatformStrategy.processTimeOff(any()) }
        coVerify(exactly = 0) { kekaHRPlatformStrategy.processTimeOff(any()) }
        coVerify(exactly = 0) { hibobHRPlatformStrategy.processTimeOff(any()) }
    }

    @Test
    fun `processEORTimeOffs processes multiple integrations`() = runBlocking {
        val integration1 = mockk<JpaCompanyIntegration>()
        val integration2 = mockk<JpaCompanyIntegration>()
        every { integration1.id } returns 1L
        every { integration1.platform.name } returns "BambooHR"
        every { integration2.id } returns 2L
        every { integration2.platform.name } returns "KekaHR"

        coEvery { integrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration1, integration2)
        coEvery { bambooHRPlatformStrategy.processTimeOff(integration1) } just Runs
        coEvery { kekaHRPlatformStrategy.processTimeOff(integration2) } just Runs

        eorTimeOffScheduler.processEORTimeOffs()

        coVerify { integrationRepository.findEORTimeoffEnabledIntegration() }
        coVerify { bambooHRPlatformStrategy.processTimeOff(integration1) }
        coVerify { kekaHRPlatformStrategy.processTimeOff(integration2) }
    }
}