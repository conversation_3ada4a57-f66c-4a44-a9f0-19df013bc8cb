package com.multiplier.integration.scheduler

import com.multiplier.integration.Constants
import com.multiplier.integration.mock.getEmptyMockReceivedEvents
import com.multiplier.integration.mock.getMockExpenseDataReceivedEvents
import com.multiplier.integration.mock.getMockReceivedEvents
import com.multiplier.integration.mock.getMockUpdatedReceivedEvents
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.service.ExpenseProcessorService
import com.multiplier.integration.service.SyncService
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)

class ReceivedDeleteEventSchedulerTest {
    @MockK
    lateinit var jpaReceivedEventRepository: ReceivedEventRepository

    @MockK
    lateinit var synService: SyncService

    @InjectMockKs
    lateinit var testScheduler: ReceivedDeleteEventsScheduler

    @MockK
    lateinit var expenseProcessorService: ExpenseProcessorService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        testScheduler = ReceivedDeleteEventsScheduler(
            jpaReceivedEventRepository,
            synService,
            expenseProcessorService
        )
    }

    @Test
    fun `should process empty delete events successfully`() {
        every { jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(eventType = EventType.RECORD_DELETE, any(),true, false) } returns getEmptyMockReceivedEvents()
        testScheduler.fetchAndProcessDeleteEvents()
        verify(exactly = 0) { synService.processDeletedEvent(any()) }
    }

    @Test
    fun `should process delete events successfully`() {
        val mockReceivedEvents = getMockReceivedEvents(firstName = "\"Britanni\"", lastName = "\"Buchanan\"", birthDate = "null", gender = "null", maritalStatus = "null", status = "null", terminationDate = "\"2024-01-30T00:00:00Z\"")
        val mockUpdatedReceivedEvent = getMockUpdatedReceivedEvents(firstName = "\"Britanni\"", lastName = "\"Buchanan\"", birthDate = "null", gender = "null", maritalStatus = "null", status = "null", terminationDate = "\"2024-01-30T00:00:00Z\"")

        every { jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(eventType = EventType.RECORD_DELETE, any(),true, false) } returns mockReceivedEvents
        every { synService.processDeletedEvent(mockReceivedEvents[0]) } returns Unit
        every { jpaReceivedEventRepository.save(any()) } returns mockUpdatedReceivedEvent
        every { expenseProcessorService.getExpenseIdFromEventData(any()) } returns 123L

        every { expenseProcessorService.processBulkRevokeExpenseRequest(any()) } returns Unit
        testScheduler.fetchAndProcessDeleteEvents()
        verify(exactly = 1) { synService.processDeletedEvent(any()) }
    }

    @Test
    fun `should process delete expense events but not process delete employee events successfully`() {
        val mockExpenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "APPROVED")
        val mockSyncEndEvent = getMockExpenseDataReceivedEvents(EventType.SYNC_END)

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns listOf(mockSyncEndEvent)
        every { jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(eventType = EventType.RECORD_DELETE, Constants.SyncDataType.EMPLOYEE,true, false) } returns emptyList()
        every { jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(eventType = EventType.RECORD_DELETE, Constants.SyncDataType.EXPENSE,true, false) } returns listOf(mockExpenseUpdateEvent)
        every { jpaReceivedEventRepository.save(any()) } returns mockExpenseUpdateEvent
        every { expenseProcessorService.getExpenseIdFromEventData(any()) } returns 123L

        every { expenseProcessorService.processBulkRevokeExpenseRequest(any()) } returns Unit
        testScheduler.fetchAndProcessDeleteEvents()

        verify(exactly = 0) { synService.processDeletedEvent(any()) }
        verify(exactly = 1) { expenseProcessorService.processBulkRevokeExpenseRequest(any()) }
        verify(exactly = 1) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should not process delete expense events for not existed sync end event`() {
        val mockExpenseUpdateEvent = getMockExpenseDataReceivedEvents(status = "APPROVED")

        every { jpaReceivedEventRepository.findByEventTypeAndSyncId(EventType.SYNC_END, any()) } returns emptyList()
        every { jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(eventType = EventType.RECORD_DELETE, Constants.SyncDataType.EMPLOYEE,true, false) } returns emptyList()
        every { jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(eventType = EventType.RECORD_DELETE, Constants.SyncDataType.EXPENSE,true, false) } returns listOf(mockExpenseUpdateEvent)

        testScheduler.fetchAndProcessDeleteEvents()

        verify(exactly = 0) { synService.processDeletedEvent(any()) }
        verify(exactly = 0) { expenseProcessorService.processBulkRevokeExpenseRequest(any()) }
        verify(exactly = 0) { expenseProcessorService.getExpenseIdFromEventData(any()) }
        verify(exactly = 0) { jpaReceivedEventRepository.save(any()) }
    }
}