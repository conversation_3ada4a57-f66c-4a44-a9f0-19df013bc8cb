package com.multiplier.integration.scheduler

import com.multiplier.integration.repository.ManualSyncRepository
import com.multiplier.integration.repository.model.JpaManualSync
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.types.SyncStatus
import com.multiplier.integration.types.SyncType
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import java.time.LocalDateTime

@ExtendWith(MockKExtension::class)
class ManualSyncSchedulerTest {

    @MockK
    lateinit var manualSyncRepository: ManualSyncRepository

    @MockK
    lateinit var customerIntegrationService: CustomerIntegrationService

    @InjectMockKs
    lateinit var manualSyncScheduler: ManualSyncScheduler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `test updateInProgressManualSync with no manual sync in progress`() {
        every { manualSyncRepository.findByStatus(SyncStatus.IN_PROGRESS) } returns emptyList()
        manualSyncScheduler.updateInProgressManualSync()
        verify(exactly = 0) { customerIntegrationService.getLatestSyncResultForIntegrationInternal(any()) }
    }

    @Test
    fun `test updateInProgressManualSync with manual sync in progress`() {
        // Arrange
        val mockManualSync = JpaManualSync(syncId = "123", integrationId = 1, status = SyncStatus.IN_PROGRESS, type = SyncType.MANUAL_OUTGOING, startedOn = LocalDateTime.now(), completedOn = null, dismissedOn = null)
            every { manualSyncRepository.findByStatus(SyncStatus.IN_PROGRESS) } returns listOf(mockManualSync)

        every { customerIntegrationService.getLatestSyncResultForIntegrationInternal(any()) } just Runs

        manualSyncScheduler.updateInProgressManualSync()

        verify { customerIntegrationService.getLatestSyncResultForIntegrationInternal(mockManualSync.integrationId) }
    }

}

