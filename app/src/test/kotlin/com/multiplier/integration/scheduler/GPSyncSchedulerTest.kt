package com.multiplier.integration.scheduler

import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.repository.model.JpaSync
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDateTime

@ExtendWith(SpringExtension::class)
class GPSyncSchedulerTest {

    @MockK
    lateinit var syncRepository: SyncRepository

    @MockK
    lateinit var notificationsService: NotificationsService

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @InjectMockKs
    lateinit var gpSyncScheduler: GPSyncScheduler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should update in-progress manual syncs successfully`() {
        val thresholdTime = LocalDateTime.now().minusMinutes(5)
        val sync = JpaSync(syncId = "syncId", integrationId = "integrationId", inProgress = true, startTime = thresholdTime.minusMinutes(10), endTime = null, clientSyncId = "clientSyncId")
        val integration = mockk<JpaCompanyIntegration>(relaxed = true)

        every { syncRepository.findInProgressSyncsOlderThanThresholdTime(any()) } returns listOf(sync)
        every { companyIntegrationRepository.findByAccountToken("integrationId") } returns integration
        every { syncRepository.save(any()) } returns mockk()
        every { companyIntegrationRepository.save(any()) } returns mockk()
        justRun { notificationsService.sendAdminGPSyncClosure(any(), any(), any()) }

        gpSyncScheduler.disableStaleGPSync()

        verify(exactly = 1) { syncRepository.save(sync) }
        verify(exactly = 1) { companyIntegrationRepository.save(integration) }
//        verify(exactly = 1) { notificationsService.sendAdminGPSyncClosure(sync.syncId, integration.companyId, integration.platform.name) }
    }

    @Test
    fun `should handle exception when closing stale syncs`() {
        val thresholdTime = LocalDateTime.now().minusMinutes(5)
        val sync = JpaSync(syncId = "syncId", integrationId = "integrationId", inProgress = true, startTime = thresholdTime.minusMinutes(10), endTime = null, clientSyncId = "clientSyncId")

        every { syncRepository.findInProgressSyncsOlderThanThresholdTime(any()) } throws RuntimeException("Database error")
        gpSyncScheduler.disableStaleGPSync()
        verify(exactly = 1) { syncRepository.findInProgressSyncsOlderThanThresholdTime(any()) }
        verify(exactly = 0) { companyIntegrationRepository.findByAccountToken("integrationId") }
        verify(exactly = 0) { syncRepository.save(any()) }
        verify(exactly = 0) { notificationsService.sendAdminGPSyncClosure(any(), any(), any()) }

        every { syncRepository.findInProgressSyncsOlderThanThresholdTime(any()) } returns listOf(sync)
        every { companyIntegrationRepository.findByAccountToken("integrationId") } throws RuntimeException("Database error")

        gpSyncScheduler.disableStaleGPSync()

        verify(exactly = 2) { syncRepository.findInProgressSyncsOlderThanThresholdTime(any()) }
        verify(exactly = 1) { companyIntegrationRepository.findByAccountToken("integrationId") }
        verify(exactly = 0) { syncRepository.save(any()) }
        verify(exactly = 0) { notificationsService.sendAdminGPSyncClosure(any(), any(), any()) }
    }

}
