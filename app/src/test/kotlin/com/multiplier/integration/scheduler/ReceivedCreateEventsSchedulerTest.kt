package com.multiplier.integration.scheduler

import com.multiplier.integration.adapter.api.BenefitServiceAdapter
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.handlers.contract.CompensationCreationEventHandler
import com.multiplier.integration.handlers.contract.ContractDocumentUpdateEventHandler
import com.multiplier.integration.handlers.contract.ContractOffboardingStatusUpdateEventHandler
import com.multiplier.integration.handlers.contract.ContractOnboardingStatusUpdateEventHandler
import com.multiplier.integration.handlers.contract.InsuranceFactsheetKitUpdateEventHandler
import com.multiplier.integration.handlers.contract.InsuranceOnboardingKitUpdateEventHandler
import com.multiplier.integration.handlers.contract.PayrollDocumentEventHandler
import com.multiplier.integration.handlers.contract.SalaryReviewDocumentEventHandler
import com.multiplier.integration.handlers.member.MemberDetailUpdateHandler
import com.multiplier.integration.mock.getEmptyFailedEvents
import com.multiplier.integration.mock.getFailedEvents
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.EventLogService
import com.multiplier.integration.repository.type.EventType
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension;
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.service.SyncService

@ExtendWith(SpringExtension::class)

class ReceivedCreateEventsSchedulerTest {
    @MockK
    lateinit var syncService : SyncService

    @InjectMockKs
    lateinit var testScheduler: ReceivedCreateEventsScheduler

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should retry failed events`() {
        every { syncService.processApprovedCreateEvents() } returns Unit

        testScheduler.processApprovedCreateEvents()

        verify(exactly = 1) { syncService.processApprovedCreateEvents() }

    }

}