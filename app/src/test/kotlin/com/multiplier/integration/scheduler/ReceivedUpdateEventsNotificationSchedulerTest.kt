package com.multiplier.integration.scheduler

import com.multiplier.expense.schema.GrpcBulkExpenseRequestInput
import com.multiplier.integration.Constants
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.MemberServiceAdapter
import com.multiplier.integration.adapter.model.AdditionalPay
import com.multiplier.integration.adapter.model.PerformanceReviewRequest
import com.multiplier.integration.adapter.model.knit.Compensation
import com.multiplier.integration.adapter.model.knit.CompensationData
import com.multiplier.integration.adapter.model.knit.CompensationItem
import com.multiplier.integration.adapter.model.knit.CompensationItemData
import com.multiplier.integration.adapter.model.knit.CompensationType
import com.multiplier.integration.adapter.model.knit.Frequency
import com.multiplier.integration.handlers.contract.ContractOffboardingInitialisationEventHandler
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockEventData
import com.multiplier.integration.mock.getMockGrpcBulkExpenseRequestInput
import com.multiplier.integration.mock.getMockKnitEmployeeData
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.mock.getMockPlatformEmployeeData
import com.multiplier.integration.mock.getMockReceivedEvents
import com.multiplier.integration.mock.getMockReceivedEventsNoCompensation
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.SyncRepository
import com.multiplier.integration.repository.model.EventType
import com.multiplier.integration.service.CustomerIntegrationService
import com.multiplier.integration.service.EmployeeService
import com.multiplier.integration.service.ExpenseProcessorService
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.NotificationsService
import com.multiplier.integration.service.SyncService
import com.multiplier.integration.service.exception.IntegrationDownstreamException
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.justRun
import io.mockk.verify
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate

@ExtendWith(SpringExtension::class)

class ReceivedUpdateEventsNotificationSchedulerTest {
    @MockK
    lateinit var jpaReceivedEventRepository: ReceivedEventRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var syncRepository: SyncRepository

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK
    lateinit var notificationsService: NotificationsService

    @MockK
    lateinit var customerIntegrationService: CustomerIntegrationService

    @MockK
    lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @MockK
    lateinit var employeeService: EmployeeService

    @MockK
    lateinit var syncService: SyncService

    @MockK
    lateinit var contractOffboardingInitialisationEventHandler: ContractOffboardingInitialisationEventHandler

    @MockK
    lateinit var expenseProcessorService: ExpenseProcessorService

    @InjectMockKs
    lateinit var testScheduler: ReceivedUpdateEventsNotificationScheduler

    @MockK
    lateinit var featureFlagService: FeatureFlagService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
        testScheduler = ReceivedUpdateEventsNotificationScheduler(
            jpaReceivedEventRepository,
            contractServiceAdapter,
            memberServiceAdapter,
            notificationsService,
            customerIntegrationService,
            platformEmployeeDataRepository,
            employeeService,
            contractOffboardingInitialisationEventHandler,
            syncService,
            featureFlagService,
        )
    }

    @Test
    fun `should process update events successfully`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni Test First Name\"",
            lastName = "\"Buchanan HHH Ahihi\"",
            birthDate = "null",
            gender = "\"FEMALE\"",
            maritalStatus = "\"DIVORCED\"",
            status = "\"INACTIVE\"",
            terminationDate = "null"
        )
        val contractId = 1L
        val memberId = 2L
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockContract = getMockContract(contractId, memberId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val (mockCompanyIntegration, mockPlatformContractIntegration) = mockContractIntegrationPair
        val mockMember = getMockMember(memberId)
        val mockUpdatedPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "\"FEMALE\"",
            firstName = "\"Britanni Test First Name\"",
            lastName = "\"Buchanan HHH Ahihi\"",
            birthDate = "null",
            maritalStatus = "\"DIVORCED\"",
            status = "\"INACTIVE\"",
            terminationDate = "null"
        )
        var grpcBulkExpenseRequestInput: GrpcBulkExpenseRequestInput = getMockGrpcBulkExpenseRequestInput()

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_UPDATE,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { memberServiceAdapter.updateMemberFields(any()) } returns mockMember
        every { platformEmployeeDataRepository.save(any()) } returns mockUpdatedPlatformEmployeeData[0]
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents[0]
        justRun {
            notificationsService.sendMemberToUpdateBasicDetailEmailNotification(
                mockContract,
                mockPlatformContractIntegration.platform.name
            )
            syncService.processExpenseUpdateEvents()
        }

        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 1) { memberServiceAdapter.updateMemberFields(any()) }
        verify(exactly = 1) { syncService.processExpenseUpdateEvents() }
    }

    @Test
    fun `should not process update events if employee origin is external`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni Test First Name\"",
            lastName = "\"Buchanan HHH Ahihi\"",
            birthDate = "null",
            gender = "\"FEMALE\"",
            maritalStatus = "\"DIVORCED\"",
            status = "\"INACTIVE\"",
            terminationDate = "null"
        )
        val contractId = 1L
        val mockContractIntegrationPair = getMockContractIntegration(contractId)

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_UPDATE,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.INTERNAL
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents.get(0)
        justRun { syncService.processExpenseUpdateEvents() }
        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 0) { memberServiceAdapter.updateMemberFields(any()) }
    }

    @Test
    fun `should process update compensation successfully`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val contractId = 1L
        val memberId = 2L
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockContract = getMockContract(contractId, memberId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 8000.0
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair
        val mockUpdatedPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 9000.0
        )
        val mockPerformanceReviewRequest = PerformanceReviewRequest(
            contractId = contractId,
            effectiveDate = LocalDate.of(2024, 1, 10),
            amount = 9000.0,
            additionalPays = null
        )

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_UPDATE,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { contractServiceAdapter.performanceReviewBulkCreateAsApproved(mockPerformanceReviewRequest) } returns listOf(
            1L
        )

        every { platformEmployeeDataRepository.save(any()) } returns mockUpdatedPlatformEmployeeData[0]
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents[0]
        every { featureFlagService.isSyncCompensationUpdate(any()) } returns true
        justRun { syncService.processExpenseUpdateEvents() }
        justRun { contractOffboardingInitialisationEventHandler.handleContractOffboardingInitialisation(any(), any()) }

        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 0) { memberServiceAdapter.updateMemberFields(any()) }
        verify(exactly = 1) { contractServiceAdapter.performanceReviewBulkCreateAsApproved(any()) }

        every { contractServiceAdapter.findContractByContractId(any()) } throws IntegrationDownstreamException("Test")
        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 2) { jpaReceivedEventRepository.save(any()) }
    }

    @Test
    fun `should process update null compensation`() {
        val mockReceivedEvents = getMockReceivedEventsNoCompensation(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val contractId = 1L
        val memberId = 2L
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockContract = getMockContract(contractId, memberId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 8000.0
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair
        val mockUpdatedPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 9000.0
        )
        val mockPerformanceReviewRequest = PerformanceReviewRequest(
            contractId = contractId,
            effectiveDate = LocalDate.of(2024, 1, 10),
            amount = 9000.0,
            additionalPays = null
        )

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_UPDATE,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { contractServiceAdapter.performanceReviewBulkCreateAsApproved(mockPerformanceReviewRequest) } returns listOf(
            1L
        )

        every { platformEmployeeDataRepository.save(any()) } returns mockUpdatedPlatformEmployeeData[0]
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents[0]
        justRun { syncService.processExpenseUpdateEvents() }
        justRun { contractOffboardingInitialisationEventHandler.handleContractOffboardingInitialisation(any(), any()) }

        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 0) { memberServiceAdapter.updateMemberFields(any()) }
        verify(exactly = 0) { contractServiceAdapter.performanceReviewBulkCreateAsApproved(any()) }
    }

    @Test
    fun `should not update compensation for no changes`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val contractId = 1L
        val memberId = 2L
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockContract = getMockContract(contractId, memberId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 9000.0
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_UPDATE,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData

        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents[0]
        justRun { syncService.processExpenseUpdateEvents() }

        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 0) { contractServiceAdapter.performanceReviewBulkCreateAsApproved(any()) }
    }

    @Test
    fun `should update compensation with additional pays`() {
        val mockReceivedEvents = getMockReceivedEvents(
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            gender = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            variable = "[{\"type\": \"Bonus\", \"amount\": 1000.0, \"planId\": \"Bonus\", \"endDate\": null, \"currency\": \"USD\", \"frequency\": \"MONTHLY\", \"payPeriod\": \"MONTHLY\", \"startDate\": \"2024-02-27T00:00:00Z\", \"percentage\": null}]"
        )
        val contractId = 1L
        val memberId = 2L
        val mockContractIntegrationPair = getMockContractIntegration(contractId)
        val mockContract = getMockContract(contractId, memberId)
        val mockPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 9000.0
        )
        val (mockCompanyIntegration, _) = mockContractIntegrationPair
        val mockUpdatedPlatformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null",
            compensationAmount = 9000.0,
            variable = "[{\"type\": \"Bonus\", \"amount\": 1000.0, \"planId\": \"Bonus\", \"endDate\": null, \"currency\": \"USD\", \"frequency\": \"MONTHLY\", \"payPeriod\": \"MONTHLY\", \"startDate\": \"2024-02-27T00:00:00Z\", \"percentage\": null}]"
        )
        val mockPerformanceReviewRequest = PerformanceReviewRequest(
            contractId = contractId,
            effectiveDate = null,
            amount = null,
            additionalPays = listOf(
                AdditionalPay(
                    type = "Bonus",
                    amount = 1000.0,
                    frequency = Frequency.MONTHLY
                )
            )
        )

        every {
            jpaReceivedEventRepository.findByEventTypeAndSyncDataTypeAndConfirmedByUserAndProcessed(
                eventType = EventType.RECORD_UPDATE,
                any(),
                true,
                false
            )
        } returns mockReceivedEvents
        every {
            customerIntegrationService.findPlatformContractIntegrationFromEvent(
                any(),
                any()
            )
        } returns mockContractIntegrationPair
        every { employeeService.getEmployeeDataOrigin(contractId) } returns Constants.EmployeeOrigin.EXTERNAL
        every { contractServiceAdapter.findContractByContractId(contractId) } returns mockContract
        every {
            platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(
                mockCompanyIntegration.id!!,
                "3277135280330507201"
            )
        } returns mockPlatformEmployeeData
        every { contractServiceAdapter.performanceReviewBulkCreateAsApproved(mockPerformanceReviewRequest) } returns listOf(
            1L
        )

        every { platformEmployeeDataRepository.save(any()) } returns mockUpdatedPlatformEmployeeData[0]
        every { jpaReceivedEventRepository.save(any()) } returns mockReceivedEvents[0]
        every { featureFlagService.isSyncCompensationUpdate(any()) } returns true
        justRun { syncService.processExpenseUpdateEvents() }
        justRun { contractOffboardingInitialisationEventHandler.handleContractOffboardingInitialisation(any(), any()) }

        testScheduler.fetchAndProcessUpdateEvents()
        verify(exactly = 1) { contractServiceAdapter.performanceReviewBulkCreateAsApproved(any()) }
    }

    @Test
    fun `checkIfCompensationChanged should return true and update base pay and additional pays`() {
        val contractId = 1L
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "ACTIVE",
            terminationDate = null,
            compensation = Compensation(
                fixed = listOf(
                    CompensationItemData(
                        type = CompensationType.SALARY.name,
                        amount = 9000.0,
                        currency = null,
                        frequency = null,
                        planId = null,
                        payPeriod = null,
                        startDate = "2024-03-05T00:00:00Z",
                        percentage = null,
                        endDate = null
                    )
                ),
                variable = listOf(
                    CompensationItemData(
                        type = CompensationType.BONUS.name,
                        amount = 1000.0,
                        currency = null,
                        frequency = Frequency.MONTHLY,
                        planId = null,
                        payPeriod = null,
                        startDate = null,
                        percentage = null,
                        endDate = null
                    )
                ),
                stock = null
            )
        )
        val mockInternalEmployeeData = getMockKnitEmployeeData(
            firstName = "Britanni",
            lastName = "Buchanan",
            compensationData = CompensationData(
                fixed = listOf(
                    CompensationItem(
                        type = CompensationType.SALARY.name,
                        amount = "1000.0",
                        currency = null,
                        payPeriod = null,
                        frequency = null,
                        startDate = null,
                        endDate = null,
                        percentage = null,
                        planId = null
                    ),
                ),
                variable = null
            )
        )

        val response = testScheduler.checkIfCompensationChanged(
            employeeData = mockInternalEmployeeData,
            eventData = mockEventData, contractId = contractId
        )
        Assertions.assertEquals(true, response.isUpdated)
        Assertions.assertEquals(9000.0, response.compensationUpdateRequest?.amount)
        Assertions.assertEquals(1000.0, response.compensationUpdateRequest?.additionalPays?.get(0)?.amount)
    }

    @Test
    fun `checkIfCompensationChanged should return false`() {
        val contractId = 1L
        val mockEventData = getMockEventData(
            firstName = "Britanni",
            lastName = "Buchanan",
            status = "ACTIVE",
            terminationDate = null,
        )
        val mockInternalEmployeeData = getMockKnitEmployeeData(
            firstName = "Britanni",
            lastName = "Buchanan",
        )

        val response = testScheduler.checkIfCompensationChanged(
            employeeData = mockInternalEmployeeData,
            eventData = mockEventData, contractId = contractId
        )
        Assertions.assertEquals(false, response.isUpdated)
    }
}