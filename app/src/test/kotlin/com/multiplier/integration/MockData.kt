package com.multiplier.integration

import com.github.javafaker.Faker
import com.google.type.Date
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventType
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventType.*
import com.multiplier.core.schema.company.CompanyOuterClass
import com.multiplier.core.schema.company.CompanyOuterClass.Company
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.*
import com.multiplier.contract.schema.currency.Currency.CurrencyCode
import com.multiplier.integration.adapter.api.resources.knit.CreateEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.GetPositionDetailResponse
import com.multiplier.integration.adapter.model.*
import com.multiplier.integration.adapter.api.resources.knit.Data
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.UpdateEmployeeDetailsResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.BusinessUnitData
import com.multiplier.integration.adapter.api.resources.knit.successfactors.GetSAPBusinessUnitsResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.GetSAPWorkLocationsResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPBusinessUnitsNestedResult
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPBusinessUnitsResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPBusinessUnitsResponseBody
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPBusinessUnitsResponseData
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPWorkLocationNestedResult
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPWorkLocationResponse
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPWorkLocationResponseBody
import com.multiplier.integration.adapter.api.resources.knit.successfactors.SAPWorkLocationResponseData
import com.multiplier.integration.adapter.api.resources.knit.successfactors.WorkLocationData
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaEventLog
import com.multiplier.integration.repository.model.JpaPlatform
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.type.EventStatus
import com.multiplier.integration.repository.type.EventType
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.Member.MaritalStatus
import com.multiplier.member.schema.Member.MemberStatus
import com.multiplier.member.schema.MemberUpdateMessage
import dev.merge.client.hris.models.RemoteResponse
import dev.merge.client.hris.models.ResponseTypeEnum
import io.ktor.http.*
import java.time.LocalDate
import java.time.LocalDateTime

fun mockKafkaContractEventMessage(
    eventType: ContractEventType? = CONTRACT_STATUS_UPDATE,
    contractId: Long? = (50000L..60000L).random(),
): ContractEventMessage =
    ContractEventMessage.newBuilder()
        .setEventType(eventType ?: CONTRACT_STATUS_UPDATE)
        .setEvent(
            ContractEventMessageOuterClass.ContractEvent.newBuilder()
                .setContractId(contractId ?: 50001)
                .build()
        )
        .build()

fun mockGrpcMemberEventMessage(
    memberId: Long? = (50000L..60000L).random(),
    type: MemberUpdateMessage.MemberUpdateEventType? =
        MemberUpdateMessage.MemberUpdateEventType.BASIC_DETAIL_UPDATE,
): MemberUpdateMessage.EventMessage =
    MemberUpdateMessage.EventMessage.newBuilder()
        .setMemberId(memberId ?: 50001L)
        .setType(type)
        .build()

fun mockGrpcMember(
    country: CountryCode? = CountryCode.SGP,
    email: String? = Faker().internet().emailAddress(),
    firstName: String? = Faker().name().firstName(),
    lastName: String? = Faker().name().lastName(),
    fullLegalName: String? = Faker().name().fullName(),
    status: MemberStatus? = MemberStatus.ACTIVE,
    memberId: Long? = (50000L..60000L).random(),
    userId: Long? = (50000L..60000L).random(),
    phone: String? = "+69 **********",
): Member =
    Member.newBuilder()
        .setId(memberId ?: 50001L)
        .addAddresses(com.multiplier.member.schema.Address.newBuilder().setCountry(country))
        .addEmails(
            com.multiplier.member.schema.EmailAddress.newBuilder()
                .setType("primary")
                .setEmail(email ?: "<EMAIL>")
        )
        .setFirstName(firstName ?: "Nick")
        .setLastName(lastName ?: "Fury")
        .setFullLegalName(fullLegalName ?: firstName?.let { it + lastName } ?: "Nick Fury")
        .setStatus(status ?: MemberStatus.ACTIVE)
        .setUserId(userId?.toString() ?: "50001")
        .addPhoneNos(
            com.multiplier.member.schema.PhoneNumer.newBuilder()
                .setType("primary")
                .setPhoneNo(phone ?: "+69 **********")
        )
        .build()

fun mockGrpcContract(
    contractId: Long? = (50000L..60000L).random(),
    companyId: Long? = (50000L..60000L).random(),
    memberId: Long? = (50000L..60000L).random(),
    country: CountryCode? = CountryCode.SGP,
    state: String? = "",
    currency: CurrencyCode? = CurrencyCode.SGD,
    workEmail: String? = Faker().internet().emailAddress(),
    position: String? = Faker().job().position(),
    status: ContractStatus? = ContractStatus.ACTIVE,
    type: ContractType? = ContractType.EMPLOYEE,
    term: ContractTerm? = ContractTerm.FIXED,
    workStatus: CountryWorkStatus? = CountryWorkStatus.RESIDENT,
    startDate: LocalDate? = LocalDate.now(),
): Contract =
    Contract.newBuilder()
        .setId(contractId ?: 50001L)
        .setCountry(country.toString())
        .setCompanyId(companyId ?: 50001L)
        .setMemberId(memberId ?: 50001L)
        .setCountryStateCode(state ?: "")
        .setWorkEmail(workEmail)
        .setPosition(position ?: "Software Engineer")
        .setStatus(status ?: ContractStatus.ACTIVE)
        .setType(type ?: ContractType.EMPLOYEE)
        .setTerm(term ?: ContractTerm.FIXED)
        .setWorkStatus(workStatus ?: CountryWorkStatus.RESIDENT)
        .setCurrency(currency ?: CurrencyCode.SGD)
        .apply {
            if (startDate != null)
                this.startOn = Date.newBuilder()
                    .setYear(startDate.year)
                    .setMonth(startDate.monthValue)
                    .setDay(startDate.dayOfMonth)
                    .build()
        }
        .build()

fun mockCompensationData(
    amount: Double? = Faker().number().randomDouble(2, 1000, 5000),
    currency: CurrencyCode? = CurrencyCode.USD,
    frequency: CompensationOuterClass.RateFrequency? = CompensationOuterClass.RateFrequency.MONTHLY,
): CompensationData =
    CompensationData(
        amount = amount ?: 5000.00,
        currency = currency ?: CurrencyCode.USD,
        frequency = frequency ?: CompensationOuterClass.RateFrequency.MONTHLY,
    )

fun mockGrpcAddress(
    city: String? = Faker().address().city(),
    country: CountryCode? = CountryCode.SGP,
    line1: String? = Faker().address().streetAddress(),
    line2: String? = Faker().address().secondaryAddress(),
    zipCode: String? = Faker().address().zipCode(),
    postalCode: String? = Faker().address().zipCode(),
    province: String? = Faker().address().state(),
    state: String? = Faker().address().state(),
): com.multiplier.member.schema.Address =
    com.multiplier.member.schema.Address.newBuilder()
        .setCity(city)
        .setCountry(country)
        .setLine1(line1)
        .setLine2(line2)
        .setKey("PRIMARY")
        .setPostalCode(postalCode)
        .setProvince(province)
        .setState(state)
        .setStreet(line1)
        .setZipcode(zipCode)
        .build()

fun mockBankData(
    bankName: String? = Faker().leagueOfLegends().champion(),
    accountNumber: String? = Faker().finance().creditCard(),
): BankData =
    BankData(
        bankName = bankName ?: "Bank Of America",
        accountNumber = accountNumber ?: "**************",
        accountType = "PRIMARY",
    )

fun mockEmployeeData(
    id: Long? = (50000L..60000L).random(),
    firstName: String? = Faker().name().firstName(),
    lastName: String? = Faker().name().lastName(),
    fullLegalName: String? = Faker().name().fullName(),
    username: String? = Faker().internet().emailAddress(),
    email: String? = Faker().internet().emailAddress(),
    phone: String? = "+69 **********",
    gender: Gender? = Gender.MALE,
    maritalStatus: MaritalStatus? = MaritalStatus.SINGLE,
    dob: LocalDateTime? = LocalDateTime.now().minusYears(20),
    startDate: LocalDateTime? = LocalDateTime.now(),
    endDate: LocalDateTime? = null,
    employmentStatus: Boolean? = true,
): EmployeeData =
    EmployeeData(
        firstName = firstName ?: "Nick",
        lastName = lastName ?: "Fury",
        fullName = fullLegalName ?: "Nick Fury",
        username = username ?: "nickfury",
        personalEmail = email ?: "<EMAIL>",
        workEmail = email ?: "<EMAIL>",
        id = id?.toString() ?: "50001",
        phoneNumber = phone ?: "+69 **********",
        gender = gender ?: Gender.MALE,
        maritalStatus = maritalStatus ?: MaritalStatus.SINGLE,
        dateOfBirth = dob ?: LocalDateTime.now().minusYears(20),
        startDate = startDate ?: LocalDateTime.now(),
        endDate = endDate,
        employmentActive = employmentStatus ?: true,
        position = "",
        contactDetails = null
    )

fun mockBasicDetails(
    firstName: String? = Faker().name().firstName(),
    lastName: String? = Faker().name().lastName(),
    fullLegalName: String? = Faker().name().fullName(),
    gender: Gender? = Gender.MALE,
): BasicDetails =
    BasicDetails(
        firstName = firstName ?: "Nick",
        lastName = lastName ?: "Fury",
        fullLegalName = fullLegalName ?: "Nick Fury",
        gender = gender ?: Gender.MALE,
    )

fun mockContactDetails(
    phone: String? = Faker().phoneNumber().phoneNumber(),
    line1: String? = Faker().address().streetAddress(false),
    line2: String? = Faker().address().secondaryAddress(),
    city: String? = Faker().address().city(),
    state: String? = Faker().address().state(),
    zipCode: String? = Faker().address().zipCode(),
    countryCode: CountryCode? = CountryCode.SGP,
    country: String? = Faker().address().country(),
): ContactDetails =
    ContactDetails(
        phoneNumber = phone,
        addressLine1 = line1,
        addressLine2 = line2,
        city = city,
        state = state,
        zipCode = zipCode,
        countryCode = countryCode,
        countryName = country,
    )

fun mockGrpcCompany(
    companyId: Long? = (50000L..60000L).random(),
    companyLogoId: Long? = (50000L..60000L).random(),
    displayName: String? = Faker().company().name(),
    financialYear: Int? = 0,
    city: String? = Faker().address().city(),
    country: String? = Faker().address().countryCode(),
    line1: String? = Faker().address().streetAddress(false),
    line2: String? = Faker().address().secondaryAddress(),
): Company =
    Company.newBuilder()
        .setId(companyId ?: 50001)
        .setCompanyLogoId(companyLogoId ?: 50001)
        .setDisplayName(displayName ?: "Multiplier")
        .setFinancialYear(financialYear ?: 0)
        .setPrimaryEntity(
            CompanyOuterClass.LegalEntity.newBuilder()
                .setAddress(
                    CompanyOuterClass.Address.newBuilder()
                        .setCity(city)
                        .setCountry(country)
                        .setLine1(line1)
                        .setLine2(line2)
                )
        )
        .build()

fun mockCompanyIntegration(
    companyId: Long = (50000L..60000L).random(),
    platform: JpaPlatform? = mockPlatform(),
    connected: Boolean? = true,
) =
    JpaCompanyIntegration(
        id = 1,
        companyId = companyId,
        provider = JpaProvider(2, ProviderName.KNIT),
        platform = platform ?: mockPlatform(),
        accountToken = "token",
        enabled = connected ?: true,
        outgoingSyncEnabled = false,
        incomingSyncEnabled = false,
        lastIncomingSyncTime = LocalDateTime.now(),
        lastOutgoingSyncTime = LocalDateTime.now(),
        lastOutgoingSyncTimeToggleOnTime = null,
        lastOutgoingSyncTimeToggleOffTime = null
    )

fun mockPlatform(
    id: Long? = 1,
    platformName: String? = "BambooHR",
) = JpaPlatform(id, PlatformCategory.HRIS, platformName ?: "BambooHR", true)

fun mockRemoteResponse(
    method: HttpMethod? = HttpMethod.Post,
    path: String? = Faker().internet().url(),
    status: HttpStatusCode? = HttpStatusCode.allStatusCodes.random(),
    response: Any? = null,
    responseHeaders: Map<String, Any>? = null,
    responseType: ResponseTypeEnum? = ResponseTypeEnum.BASE64_GZIP,
    headers: Map<String, Any>? = null,
) =
    RemoteResponse(
        method = method?.toString() ?: "POST",
        path = path ?: "/",
        status = status?.value ?: 200,
        response = response,
        responseHeaders = responseHeaders,
        responseType = responseType,
        headers = headers,
    )

fun mockEventLog(
    id: Long? = null,
    eventId: String = "MockEventId${(Math.random() * 1000).toInt()}",
    eventType: EventType = EventType.values().random(),
    eventPayload: String = "{}",
    status: EventStatus = EventStatus.values().random(),
    retriesDone: Int = 0,
    retriesLeft: Int = 3,
    errorMessage: String? = null,
    lastAttempt: LocalDateTime? = null,
    nextAttempt: LocalDateTime? = null,
): JpaEventLog = JpaEventLog(
    id = id,
    eventId = eventId,
    eventType = eventType,
    eventPayload = eventPayload,
    status = status,
    retriesDone = retriesDone,
    retriesLeft = retriesLeft,
    errorMessage = errorMessage,
    lastAttempt = lastAttempt,
    nextAttempt = nextAttempt
)

fun mockBambooHRFields(): List<BambooHRField> =
    listOf(
        BambooHRField("1", "Bank Name"),
        BambooHRField("2", "Bank Account Number"),
        BambooHRField("3", "Account Type"),
        BambooHRField("4", "SWIFT code"),
    )

fun mockPositionDetails(
    positionId: String? = "Software Engineer",
    designation: String? = "Engineer",
    department: String? = "Tech",
): GetPositionDetailResponse =
    GetPositionDetailResponse(
        success = true,
        data = Data(
            positions = listOf(
                Position(positionId, designation, department)
            ),
            workShifts = listOf()
        )
    )

fun mockSuccessFactorsLocations(): GetSAPWorkLocationsResponse =
    GetSAPWorkLocationsResponse(
        success = true,
        data = SAPWorkLocationResponse(
            response = SAPWorkLocationResponseBody(
                body = SAPWorkLocationNestedResult(
                    d = SAPWorkLocationResponseData(
                        results = listOf(
                            WorkLocationData("Vietnam", "VN")
                        )
                    )
                )
            )

        )
    )

fun mockSuccessFactorsBusinessUnits(): GetSAPBusinessUnitsResponse =
    GetSAPBusinessUnitsResponse(
        success = true,
        data = SAPBusinessUnitsResponse(
            response = SAPBusinessUnitsResponseBody(
                body = SAPBusinessUnitsNestedResult(
                    d = SAPBusinessUnitsResponseData(
                        results = listOf(
                            BusinessUnitData("Engineering", "ENG")
                        )
                    )
                )
            )

        )
    )

fun mockProvider(id: Long? = 1, name: ProviderName): JpaProvider =
    JpaProvider(
        id,
        name
    )

fun mockCreateEmployeeRecord(employeeId: String): CreateEmployeeResponse =
    CreateEmployeeResponse(
        success = true,
        data = com.multiplier.integration.adapter.api.resources.knit.EmployeeData(
            employeeId
        )
    )

fun mockPlatformContractIntegration(
    id: Long = (50000L..60000L).random(),
    contractId: Long = (50000L..60000L).random(),
    providerId: Long = (50000L..60000L).random(),
    platformId: Long = (50000L..60000L).random(),
    platformEmployeeId: String,
    integrationId: Long? = null,
): JpaPlatformContractIntegration =
    JpaPlatformContractIntegration(
        id = id,
        contractId = contractId,
        providerId = providerId,
        platformId = platformId,
        platformEmployeeId = platformEmployeeId,
        remoteId = "remoteId",
        platform = mockPlatform(),
        provider = mockProvider(name = ProviderName.KNIT),
        integrationId = integrationId
    )

fun mockUpdateEmployeeRecord(): UpdateEmployeeDetailsResponse =
    UpdateEmployeeDetailsResponse(
        success = true,
    )