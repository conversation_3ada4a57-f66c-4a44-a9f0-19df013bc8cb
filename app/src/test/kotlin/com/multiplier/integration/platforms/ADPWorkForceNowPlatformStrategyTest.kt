package com.multiplier.integration.platforms

import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.service.InternalDocument
import io.mockk.impl.annotations.InjectMockKs
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class ADPWorkForceNowPlatformStrategyTest {


    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @InjectMockKs
    lateinit var adpWorkForceNowPlatformStrategy: ADPWorkForceNowPlatformStrategy

    @Test
    fun `should throw UnsupportedOperationException when updating employee compensation`() = testScope.runTest {
        val compensationDetails: CompensationData = getCompensationData()

        assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateEmployeeCompensation(1L, 1L, compensationDetails)
        }.also {
            assertThat(it.message).isEqualTo("ADP WorkForceNow platform does not support updating employee compensation")
        }
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee basic details`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateEmployeeBasicDetails(1L, 1L, getBasicDetails())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating employee basic details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee contact details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateEmployeeContactDetails(1L, 1L, getContactDetails())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating employee contact details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating bank details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateBankDetails(1L, 1L, getBankData())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating employee bank details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating onboarding kit document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateOnboardingKitDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating onboarding kit document")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating factsheet document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateFactsheetDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating factsheet document")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating contract document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateContractDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating contract document")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating salary review document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.updateSalaryReviewDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support updating salary review document")
    }

    @Test
    fun `should throw UnsupportedOperationException when uploading payslip document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            adpWorkForceNowPlatformStrategy.uploadPayslipDocument(
                1L,
                1L,
                InternalDocument(downloadUrl = "example.com")
            )
        }
        assertThat(exception.message).isEqualTo("ADP WorkForceNow platform does not support uploading payslip document")
    }
}