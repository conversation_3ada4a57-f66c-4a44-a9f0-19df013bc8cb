package com.multiplier.integration.platforms

import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Category
import com.multiplier.integration.adapter.api.resources.knit.bamboo.GetDocumentCategoriesResponse
import com.multiplier.integration.service.FeatureFlagService
import io.mockk.coEvery
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class BambooDocumentsServiceTest {

    @MockK
    private lateinit var knitAdapter: KnitAdapter

    @MockK
    private lateinit var featureFlagService: FeatureFlagService

    @InjectMockKs
    private lateinit var bambooDocumentsService: BambooDocumentsService

    @Test
    fun `should match the target folder configured in the feature flag`() {
        val externalCategory = MockCategory(id = "1", name = "payslips")
        mockCategories(externalCategory)
        mockTargetFolder(externalCategory.name)

        val result = bambooDocumentsService.getPayslipFolderId(123, 123, "123")

        assertThat(result).isEqualTo(externalCategory.id)
    }

    @Test
    fun `should return null when no matching folder is found`() {
        val externalCategory = MockCategory(id = "1", name = "unknown")
        mockCategories(externalCategory)
        mockTargetFolder("payslips")

        val result = bambooDocumentsService.getPayslipFolderId(123, 123, "123")

        assertThat(result).isNull()
    }

    data class MockCategory(val id: String, val name: String)

    private fun mockCategories(vararg categories: MockCategory) {
        coEvery {
            knitAdapter.getDocumentCategories(any(), any(), any())
        } returns
            GetDocumentCategoriesResponse(
                success = true,
                categories = categories.map { Category(id = it.id, name = it.name) }
            )
    }

    private fun mockTargetFolder(name: String) {
        every { featureFlagService.getStringValue(any(), any()) } returns name
    }
}