package com.multiplier.integration.platforms

import com.google.protobuf.Timestamp
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DocgenServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.LeaveCreateRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequest
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.bamboo.BambooApproveTimeOffResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Employee
import com.multiplier.integration.adapter.api.resources.knit.bamboo.GetEmployeeDirectoryResponse
import com.multiplier.integration.adapter.api.resources.knit.bamboo.ListData
import com.multiplier.integration.adapter.api.resources.knit.bamboo.OptionData
import com.multiplier.integration.adapter.api.resources.knit.bamboo.Options
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensation
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getCompensationPlanResponse
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.mock.getCreateDocumentResponse
import com.multiplier.integration.mock.getDocumentCategoriesResponse
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEmploeeRecord
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockEmployeeData
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.mock.getMockMemberWithoutEmail
import com.multiplier.integration.mock.getMockPlatformEmployeeData
import com.multiplier.integration.mock.getPositionDetail
import com.multiplier.integration.mock.getTerminateEmployeeResponse
import com.multiplier.integration.mock.getTerminationReasonResponse
import com.multiplier.integration.mock.getUpdateCompensationResponse
import com.multiplier.integration.mock.getUpdateEmployeeDetailsResponse
import com.multiplier.integration.mock.getWorkLocations
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaDocumentFolder
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.PositionsService
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.service.TimeoffService
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.LeaveTypeMappingDefinition
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import com.multiplier.timeoff.schema.GrpcTimeOff
import com.multiplier.timeoff.schema.GrpcTimeOffs
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import jakarta.persistence.EntityNotFoundException
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.net.URL
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.*

@ExtendWith(SpringExtension::class)
class BambooHRPlatformStrategyTest{

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var docgenServiceAdapter: DocgenServiceAdapter

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var featureFlagService: FeatureFlagService

    @MockK
    lateinit var positionsService: PositionsService

    @MockK
    private lateinit var documentFoldersRepository: DocumentFoldersRepository

    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @MockK
    lateinit var timeOffServiceAdapter: TimeoffServiceAdapter

    @MockK
    lateinit var timeOffSyncService: TimeOffSyncService

    @MockK
    lateinit var platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository

    @MockK
    lateinit var timeOffService: TimeoffService

    @InjectMockKs
    lateinit var testStrategy: BambooHRPlatformStrategy

    @Test
    fun `should throw exception when create new employee with no workEmail`() = testScope.runTest {
        val contract = getMockContract(workEmail = "")
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.createEmployee(1,getMockEmployeeData(),getMockMemberWithoutEmail(1),
                contract,null)
        }
    }

    @Test
    fun `should create employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1, workEmail = "<EMAIL>")
        val integration = getMockCompanyIntegration().get()

        every { featureFlagService.isOn(any(), any()) } returns false

        every { featureFlagService.isKnitDataModelV2(any()) } returns false

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(false, null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(false, listOf())
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(true,null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        var workLocation: ListData = ListData(
            alias = "test",
            options = Options(
                option = listOf(
                    OptionData(
                        name = "Test"
                    )
                )
            )
        )

        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(true, listOf(workLocation))
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        workLocation = ListData(
            alias = "location",
            options = Options(
                option = listOf(
                    OptionData(
                        name = "Test"
                    )
                )
            )
        )

        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(true, listOf(workLocation))
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        member = getMockMember(1)
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(false)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(true)
        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.empty()
        every { platformRepository.findFirstByCategoryAndName(any(), any()) } returns null
        coEvery { knitAdapter.getEmployeeDirectory(any(), any()) } returns GetEmployeeDirectoryResponse(
            success = true,
            employees = listOf(Employee(
                id = "1",
                workEmail = "workEmail"
            ))
        )
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformRepository.findFirstByCategoryAndName(any(), any())} returns integration.platform
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second

        val createEmployeePlatformResponse: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,
            getMockContract(1, 1, workEmail = "workEmail"),null)

        assert(createEmployeePlatformResponse.createdEmployeeId=="1")

        val createEmployeePlatformResponse2: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,
            getMockContract(1, 1, workEmail = "notBlank", contractType = ContractOuterClass.ContractType.CONTRACTOR),null)

        assert(createEmployeePlatformResponse2.createdEmployeeId=="1")

        val createEmployeePlatformResponse3: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,
            getMockContract(1, 1, workEmail = "notBlank", contractType = ContractOuterClass.ContractType.FREELANCER),null)

        assert(createEmployeePlatformResponse3.createdEmployeeId=="1")
    }

    @Test
    fun `should update employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(false, null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(false, listOf())
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(true,null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        var workLocation: ListData = ListData(
            alias = "test",
            options = Options(
                option = listOf(
                    OptionData(
                        name = "Test"
                    )
                )
            )
        )

        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(true, listOf(workLocation))
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        workLocation = ListData(
            alias = "location",
            options = Options(
                option = listOf(
                    OptionData(
                        name = "Test"
                    )
                )
            )
        )

        coEvery { knitAdapter.getBambooWorkLocations(1,1) } returns getWorkLocations(true, listOf(workLocation))
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        member = getMockMember(1)
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(false,"already")
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(false,"test")
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(true,"test")
        testStrategy.updateEmployee(companyId, employeeData, member, getMockContract(1,1, workEmail = "notBlank"), null)

        coVerify(exactly = 3){knitAdapter.updateEmployeeDetails(any(),any(),any(),any())}
    }

    @Test
    fun `should update employee compensation`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val compensationDetails: CompensationData = getCompensationData()
        val integration = getMockCompanyIntegration().get()

        integration.setPrivateField("outgoingSyncEnabled",false)
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        integration.setPrivateField("outgoingSyncEnabled",true)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        every { contractServiceAdapter.getCurrentCompensation(1) } returns getCompensation()
        coEvery { knitAdapter.getCompensationPlan(1,1) } returns getCompensationPlanResponse()

        coEvery { knitAdapter.updateCompensation(1,1,any()) } returns getUpdateCompensationResponse(false)
        assertThrows<Exception> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        coEvery { knitAdapter.updateCompensation(1,1,any()) } returns getUpdateCompensationResponse(true)
        testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)

        coVerify(exactly = 2){knitAdapter.updateCompensation(any(),any(),any())}
    }

    @Test
    fun `should update onboarding kit document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf()
        assertThrows<EntityNotFoundException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(1,1) } returns null
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(1,1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateOnboardingKitDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update factsheet document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateFactsheetDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update contract document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateContractDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update salary review document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://upload.wikimedia.org/wikipedia/commons/7/78/Image.jpg"))
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        document.setPrivateField("internalDownloadURL",URL("https://upload.wikimedia.org/wikipedia/commons/7/78/Image.jpg"))
        testStrategy.updateSalaryReviewDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should upload payslip document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document = InternalDocument(downloadUrl = "http://127.0.0.1:8080")
        val integration = getMockCompanyIntegration().get()
        val mockDocumentFolder = mockk<JpaDocumentFolder>(relaxed = true) {
            every { folderId } returns "test"
        }

        val targetFolderName = "Test"
        every { featureFlagService.getStringValue(any(), any()) } returns targetFolderName

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        every { documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!) } returns mockDocumentFolder
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.uploadPayslipDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should terminate employee`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val terminationReason = "Test"
        val integration = getMockCompanyIntegration().get()
        val platformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "BambooHR") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        val contractIntegration = getMockContractIntegration(1).second
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns contractIntegration
        every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integrationId = integration.id!!, contractIntegration.platformEmployeeId) } returns platformEmployeeData
        coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(false,"test")
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(true,"test")
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(true,"Terminate")
        coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(false)
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(true)
        every { platformEmployeeDataRepository.save(any()) } returns platformEmployeeData[0]
        testStrategy.terminateEmployee(companyId, contractId, terminationDate, terminationReason)

        coVerify(exactly = 3){knitAdapter.terminateEmployee(1,1,any())}
    }

    @Test
    fun `test download and encode base64 error`() = testScope.runTest {
        val result = testStrategy.downloadFileAndEncodeBase64("https://madeupurl.com/joke.jpg")
        assertNull(result)
    }

    @Test
    fun `test download and encode base64 successful`() = testScope.runTest {
        val result = testStrategy.downloadFileAndEncodeBase64("https://upload.wikimedia.org/wikipedia/commons/3/39/GodfreyKneller-IsaacNewton-1689.jpg")
        assertNotNull(result)
    }

    @Test
    fun `createEmployeeWithoutIntegrationData should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.createEmployeeWithoutIntegrationData(
                companyId = 1L,
                firstName = "John",
                lastName = "Doe",
                primaryEmail = "<EMAIL>",
                position = "Software Engineer",
                workEmail = "<EMAIL>"
            )
        }

        assertEquals("BambooHR platform does not support creating employee without integration data", exception.message)
    }

    @Test
    fun `updateEmployeeBasicDetails should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeBasicDetails(
                companyId = 1L,
                contractId = 1L,
                details = getBasicDetails()
            )
        }

        assertEquals("BambooHR platform does not support updating employee basic details", exception.message)
    }

    @Test
    fun `updateEmployeeContactDetails should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeContactDetails(
                companyId = 1L,
                contractId = 1L,
                contactDetails = getContactDetails()
            )
        }

        assertEquals("BambooHR platform does not support updating employee contact details", exception.message)
    }

    @Test
    fun `updateBankDetails should not throw exception`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(
                companyId = 1L,
                contractId = 1L,
                bankData = getBankData()
            )
        }

        assertEquals("BambooHR platform does not support updating employee bank details", exception.message)
    }


    @Test
    fun `processTimeOff should handle empty synced employees list`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        every { integration.id } returns 1L

        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns emptyList()

        testStrategy.processTimeOff(integration)

        coVerify { platformContractIntegrationRepository.findByIntegrationId(1L) }
        coVerify(exactly = 0) { timeOffServiceAdapter.getTimeOffsByContractIds(any()) }
    }

    @Test
    fun `processTimeOff should handle empty time off type mappings`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()

        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.startDate } returns getTimestamp("2021-09-01")
        every { timeOff.endDate } returns getTimestamp("2021-09-01")
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns emptyList()
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false

        testStrategy.processTimeOff(integration)

        coVerify { platformContractIntegrationRepository.findByIntegrationId(1L) }
        coVerify { timeOffServiceAdapter.getTimeOffsByContractIds(any()) }
        coVerify(exactly = 0) { knitAdapter.createLeaveRequest(any(), any(), any(), any()) }
        coVerify(exactly = 0) { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) }
    }

    private fun getTimestamp(dateString: String): Timestamp =
        LocalDate.parse(dateString).atStartOfDay(ZoneOffset.UTC).toEpochSecond().let { seconds ->
            Timestamp.newBuilder().setSeconds(seconds).build()
        }

    @Test
    fun `processTimeOff should handle empty time offs list`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()

        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().build()
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns emptyList()

        testStrategy.processTimeOff(integration)

        coVerify { platformContractIntegrationRepository.findByIntegrationId(1L) }
        coVerify { timeOffServiceAdapter.getTimeOffsByContractIds(any()) }
        coVerify(exactly = 0) { knitAdapter.createLeaveRequest(any(), any(), any(), any()) }
        coVerify(exactly = 0) { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `processTimeOff should handle draft time offs correctly`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"


        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L

        val timeOff1 = mockk<GrpcTimeOff>()
        every { timeOff1.id } returns 1
        every { timeOff1.status.name } returns "DRAFT"

        val timeOff2 = mockk<GrpcTimeOff>()
        every { timeOff2.id } returns 2
        every { timeOff2.status.name } returns "PENDING"

        val timeOffs = mockk<GrpcTimeOffs>()
        every { timeOffs.timeOffsList } returns listOf(timeOff1, timeOff2)

        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns timeOffs

        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(1) } returns true
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(2) } returns false

        val platformTimeoffData = mockk<JpaPlatformTimeoffIntegration>()
        every { platformTimeoffData.externalTimeoffId } returns "ext1"
        every { platformTimeoffData.employeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { platformTimeoffDataRepository.findByInternalTimeoffId(1) } returns platformTimeoffData

        val cancelResult = mockk<BambooApproveTimeOffResponse>()
        every { cancelResult.success } returns true
        coEvery {
            knitAdapter.approveBambooHrTimeOffRequest(
                companyId = 100L,
                platformId = 10L,
                requestId = "ext1",
                status = "canceled",
                note = any()
            )
        } returns cancelResult
        coEvery { companyIntegrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration)

        coEvery { platformTimeoffDataRepository.delete(any()) } just Runs

        testStrategy.processTimeOff(integration)

        coVerify(exactly = 1) {
            knitAdapter.approveBambooHrTimeOffRequest(
                companyId = 100L,
                platformId = 10L,
                requestId = "ext1",
                status = "canceled",
                note = any()
            )
        }
        coVerify(exactly = 1) { platformTimeoffDataRepository.delete(platformTimeoffData) }
        coVerify(exactly = 0) { knitAdapter.createLeaveRequest(any(), any(), any(), any()) }
    }
    @Test
    fun `processTimeOff should handle failed approve time off request`() = runBlocking {
        // Mock data
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<LeaveCreateRequestResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false
        coEvery { knitAdapter.createLeaveRequest(any(), any(), any(), any()) } returns addTimeOffResult
        every { addTimeOffResult.success } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()
        coEvery { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) } returns mockk {
            every { success } returns false
        }

        testStrategy.processTimeOff(integration)

        // Verify that addTimeoffDataToCache is not called
        coVerify(exactly = 0) { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `processTimeOff should handle failed add time off request`() = runBlocking {
        // Mock data
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<LeaveCreateRequestResponse>()
        val approveResult = mockk<BambooApproveTimeOffResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false
        every { addTimeOffResult.success } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) } returns approveResult
        every { approveResult.success } returns true
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()
        coEvery { knitAdapter.createLeaveRequest(any(), any(), any(), any()) } returns mockk {
            every { success } returns false
        }

        testStrategy.processTimeOff(integration)

        // Verify that approveBambooHrTimeOffRequest is not called
        coVerify(exactly = 0) { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `processTimeOff should handle missing time off type`() = runBlocking {
        // Mock data
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<LeaveCreateRequestResponse>()
        val approveResult = mockk<BambooApproveTimeOffResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "NOT_VACATION")
        )
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false
        coEvery { knitAdapter.createLeaveRequest(any(), any(), any(), any()) } returns addTimeOffResult
        every { addTimeOffResult.success } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) } returns approveResult
        every { approveResult.success } returns true
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()

        testStrategy.processTimeOff(integration)

        // Verify that addBambooHrTimeOffRequest is not called
        coVerify(exactly = 0) { knitAdapter.createLeaveRequest(any(), any(), any(), any()) }
        coVerify(exactly = 0) { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) }
    }

    @Test
    fun `processTimeOff should skip time offs that already exist`() = runBlocking {
        // Mock data
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<LeaveCreateRequestResponse>()
        val approveResult = mockk<BambooApproveTimeOffResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.integrationId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { knitAdapter.createLeaveRequest(any(), any(), any(), any()) } returns addTimeOffResult
        every { addTimeOffResult.success } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) } returns approveResult
        every { approveResult.success } returns true
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns true

        testStrategy.processTimeOff(integration)

        // Verify that addBambooHrTimeOffRequest is not called
        coVerify(exactly = 0) { knitAdapter.createLeaveRequest(any(), any(), any(), any()) }
    }


    @Test
    fun `processTimeOffs should process all time offs`() = runBlocking {
        // Mock data
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<LeaveCreateRequestResponse>()
        val approveResult = mockk<BambooApproveTimeOffResponse>()
        val getEmployeeLeaveRequestResponse = mockk<LeaveRequestResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "BambooHR"
        every { integration.accountToken } returns "TOKEN"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.integrationId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false
        coEvery { knitAdapter.createLeaveRequest(any(), any(), any(), any()) } returns addTimeOffResult
        every { addTimeOffResult.success } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { knitAdapter.approveBambooHrTimeOffRequest(any(), any(), any(), any(), any()) } returns approveResult
        every { approveResult.success } returns true
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()
        coEvery { knitAdapter.getEmployeeLeaveRequests(any(), any(), any()) } returns getEmployeeLeaveRequestResponse
        every { getEmployeeLeaveRequestResponse.data?.requests } returns listOf(
            LeaveRequest(
                "employee1",
                "ext-1",
                startDate = timeOff.startDate.toLocalDate().format(formatter),
                endDate = timeOff.endDate.toLocalDate().format(formatter),
                status = Status.valueOf("APPROVED"),
                requestedOn = null,
                note = null,
                unit = null,
                amount = null,
                isPaid = null
            )
        )
        every { approveResult.success } returns true

        // Execute
       testStrategy.processTimeOff(integration)

        // Verify
        coVerify { platformContractIntegrationRepository.findByIntegrationId(1L) }
        coVerify { timeOffServiceAdapter.getTimeOffsByContractIds(any()) }
        coVerify { timeOffSyncService.getLeaveTypeMappingDefinition(100L, 1L) }
        coVerify { platformTimeoffDataRepository.existsByInternalTimeoffId(1L) }
        coVerify { knitAdapter.createLeaveRequest(100L, 10L, "employee1", any()) }
        coVerify { knitAdapter.approveBambooHrTimeOffRequest(100L, 10L, any(), "approved", any()) }
        coVerify { timeOffService.addTimeoffDataToCache(1L, 1L, any(), 1L, any()) }
    }
}
