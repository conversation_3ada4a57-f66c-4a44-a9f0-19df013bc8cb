package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.*
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.PositionsService
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Member
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import jakarta.persistence.EntityNotFoundException
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*

@ExtendWith(SpringExtension::class)
class PaychexPlatformStrategyTest {

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var positionsService: PositionsService

    @InjectMockKs
    lateinit var testStrategy: PaychexPlatformStrategy

    @Test
    fun `should create employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        // Test platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Paychex") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test integration not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Paychex") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test position not found
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(false, null)
        assertThrows<Exception> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test position found but no details
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, null)
        assertThrows<Exception> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test missing primary email
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Setup for successful case
        member = getMockMember(1)

        // Test employee creation failure
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(false)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        assertThrows<IntegrationInternalServerException>("Employee creation failed: API Error") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test successful case
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(true)
        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second

        val response = testStrategy.createEmployee(companyId, employeeData, member,
            getMockContract(1,1, workEmail = "notBlank"), null)

        assert(response.createdEmployeeId == "1")
    }

    @Test
    fun `should update employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        // Test platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Paychex") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test contract integration not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Paychex") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<IntegrationInternalServerException>("ContractIntegration not found for contractId=1") {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test update failure
        member = getMockMember(1)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1, any()) } returns getUpdateEmployeeDetailsResponse(false, "API Error")
        assertThrows<IntegrationInternalServerException>("Employee update failed: API Error") {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test successful update
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1, any()) } returns getUpdateEmployeeDetailsResponse(true, "test")
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        testStrategy.updateEmployee(companyId, employeeData, member,
            getMockContract(1,1, workEmail = "notBlank"), null)

        coVerify(exactly = 1) { knitAdapter.updateEmployeeDetails(any(), any(), any(), any()) }
    }

    @Test
    fun `should update employee compensation`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val compensationDetails: CompensationData = getCompensationData()
        val integration = getMockCompanyIntegration().get()

        // Test platform not found
        integration.setPrivateField("outgoingSyncEnabled", false)
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Paychex") } returns null
        assertThrows<IntegrationIllegalStateException> { 
            testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails) 
        }

        // Test no enabled integration
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Paychex") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1, 1) } returns null
        assertThrows<EntityNotFoundException> { 
            testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails) 
        }

        // Test outgoing sync disabled
        every { companyIntegrationRepository.findEnabledIntegration(1, 1) } returns listOf(integration)
        assertThrows<IntegrationIllegalStateException> { 
            testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails)
        }

        // Test contract integration not found
        integration.setPrivateField("outgoingSyncEnabled", true)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { 
            testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails)
        }

        // Test compensation plan fetch fails
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        every { contractServiceAdapter.getCurrentCompensation(1) } returns getCompensation()
        coEvery { knitAdapter.getCompensationPlan(1, 1) } returns getCompensationPlanResponse()
        assertThrows<Exception> { 
            testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails)
        }

        // Test compensation update fails
        coEvery { knitAdapter.getCompensationPlan(1, 1) } returns getCompensationPlanResponse()
        coEvery { knitAdapter.updateCompensation(1, 1, any()) } returns getUpdateCompensationResponse(false)
        assertThrows<Exception> { 
            testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails)
        }

        // Test successful compensation update
        coEvery { knitAdapter.updateCompensation(1, 1, any()) } returns getUpdateCompensationResponse(true)
        testStrategy.updateEmployeeCompensation(companyId, contractId, compensationDetails)

        coVerify(exactly = 3) { knitAdapter.updateCompensation(any(), any(), any()) }
        coVerify(exactly = 3) { knitAdapter.getCompensationPlan(any(), any()) }
    }
} 