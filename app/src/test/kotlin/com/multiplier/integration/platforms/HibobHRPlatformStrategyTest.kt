package com.multiplier.integration.platforms

import com.google.protobuf.Timestamp
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequest
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobEmployee
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobEmployeeLookup
import com.multiplier.integration.adapter.api.resources.knit.hibob.HibobPassthroughResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.LookupEmployeeResponse
import com.multiplier.integration.adapter.api.resources.knit.hibob.WorkSite
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensation
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getCompensationHappy
import com.multiplier.integration.mock.getCompensationPlanHappyResponse
import com.multiplier.integration.mock.getCompensationPlanResponse
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.mock.getCreateDocumentResponse
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEmploeeRecord
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockEmployeeData
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.mock.getMockMemberWithoutEmail
import com.multiplier.integration.mock.getMockPlatformEmployeeData
import com.multiplier.integration.mock.getPositionDetail
import com.multiplier.integration.mock.getTerminateEmployeeResponse
import com.multiplier.integration.mock.getTerminationReasonResponse
import com.multiplier.integration.mock.getUpdateCompensationResponse
import com.multiplier.integration.mock.getUpdateEmployeeDetailsResponse
import com.multiplier.integration.mock.getWorksitesResponse
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaDocumentFolder
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaPlatformTimeoffIntegration
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.PositionsService
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.service.TimeoffService
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.LeaveTypeMappingDefinition
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.member.schema.Member
import com.multiplier.timeoff.schema.GrpcTimeOff
import com.multiplier.timeoff.schema.GrpcTimeOffs
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.just
import io.mockk.mockk
import jakarta.persistence.EntityNotFoundException
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.net.URL
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.*

@ExtendWith(SpringExtension::class)
class HibobHRPlatformStrategyTest{

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    private lateinit var documentFoldersRepository: DocumentFoldersRepository

    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @MockK
    lateinit var positionsService: PositionsService

    @InjectMockKs
    lateinit var testStrategy: HibobHRPlatformStrategy

    @MockK
    lateinit var timeOffServiceAdapter: TimeoffServiceAdapter

    @MockK
    lateinit var timeOffSyncService: TimeOffSyncService

    @MockK
    lateinit var platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository

    @MockK
    lateinit var timeOffService: TimeoffService

    @Test
    fun `should create employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(false, null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        coEvery { knitAdapter.getWorksitesResponse(1,1) } returns getWorksitesResponse(false,null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.getWorksitesResponse(1,1) } returns getWorksitesResponse(true,null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.getWorksitesResponse(1,1) } returns getWorksitesResponse(true,WorkSite(id = 1, value = "Test", archived = false))
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        member = getMockMember(1)
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(false)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.lookupHibobEmployeeByEmail(1,1, any()) } returns
                LookupEmployeeResponse(
                    true,
                    data = HibobEmployeeLookup(
                        employees = listOf(HibobEmployee(
                            id = "1",
                            email = "test",
                        ))
                    ),
                    responseCode = 200)
        val createEmployeePlatformResponseExisted: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,
            getMockContract(1,1, workEmail = "notBlank"),null)

        assert(createEmployeePlatformResponseExisted.createdEmployeeId=="1")

        coEvery { knitAdapter.lookupHibobEmployeeByEmail(1,1, any()) } returns LookupEmployeeResponse(false, responseCode = 400)
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(true)
        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.empty()
        every { platformRepository.findFirstByCategoryAndName(any(), any()) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformRepository.findFirstByCategoryAndName(any(), any())} returns integration.platform
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second

        val createEmployeePlatformResponse: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,
            getMockContract(1,1, workEmail = "notBlank"),null)

        assert(createEmployeePlatformResponse.createdEmployeeId=="1")
    }

    @Test
    fun `should update employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(false, null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        coEvery { knitAdapter.getWorksitesResponse(1,1) } returns getWorksitesResponse(false,null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { knitAdapter.getWorksitesResponse(1,1) } returns getWorksitesResponse(true,null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { knitAdapter.getWorksitesResponse(1,1) } returns getWorksitesResponse(true,WorkSite(id = 1, value = "Test", archived = false))
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        member = getMockMember(1)
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(false,"already")
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(false,"test")
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(true,"test")
        testStrategy.updateEmployee(companyId, employeeData, member, getMockContract(1,1, workEmail = "notBlank"), null)

        coVerify(exactly = 4){knitAdapter.updateEmployeeDetails(any(),any(),any(),any())}
    }

    @Test
    fun `should update employee compensation`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val compensationDetails: CompensationData = getCompensationData()
        val integration = getMockCompanyIntegration().get()

        integration.setPrivateField("outgoingSyncEnabled",false)
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        integration.setPrivateField("outgoingSyncEnabled",true)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        every { contractServiceAdapter.getCurrentCompensation(1) } returns getCompensation()
        coEvery { knitAdapter.getCompensationPlan(1,1) } returns getCompensationPlanResponse()

        coEvery { knitAdapter.updateCompensation(1,1,any()) } returns getUpdateCompensationResponse(false)
        assertThrows<Exception> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        coEvery { knitAdapter.updateCompensation(1,1,any()) } returns getUpdateCompensationResponse(true)
        testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)

        coVerify(exactly = 2){knitAdapter.updateCompensation(any(),any(),any())}

        coEvery { knitAdapter.getCompensationPlan(1,1) } returns getCompensationPlanHappyResponse()
        every { contractServiceAdapter.getCurrentCompensation(1) } returns getCompensationHappy()
        testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)

        coVerify(exactly = 3){knitAdapter.updateCompensation(any(),any(),any())}
    }

    @Test
    fun `should update onboarding kit document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf()
        assertThrows<EntityNotFoundException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        every { platformContractIntegrationRepository.findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(1,1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateOnboardingKitDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update factsheet document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateFactsheetDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update contract document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateContractDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update salary review document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateSalaryReviewDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should upload payslip document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document = InternalDocument(downloadUrl = "http://127.0.0.1:8080")
        val integration = getMockCompanyIntegration().get()
        val mockDocumentFolder = mockk<JpaDocumentFolder>(relaxed = true) {
            every { folderId } returns "test"
        }

        every { documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!) } returns mockDocumentFolder
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.uploadPayslipDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should terminate employee`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val terminationReason = "Test"
        val integration = getMockCompanyIntegration().get()
        val platformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Hibob") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        val contractIntegration = getMockContractIntegration(1).second
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns contractIntegration
        every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integrationId = integration.id!!, contractIntegration.platformEmployeeId) } returns platformEmployeeData
        coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(false,"test")
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(true,"test")
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(true,"Terminate")
        coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(false)
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason,
        )}

        coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(true)
        every { platformEmployeeDataRepository.save(any()) } returns platformEmployeeData[0]
        testStrategy.terminateEmployee(companyId, contractId, terminationDate, terminationReason)

        coVerify(exactly = 3){knitAdapter.terminateEmployee(1,1,any())}
    }

    @Test
    fun `processTimeOff should handle draft time offs correctly`() = runBlocking {
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "HibobHR"

        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"

        val timeOff1 = mockk<GrpcTimeOff>()
        every { timeOff1.id } returns 1
        every { timeOff1.status.name } returns "DRAFT"

        val timeOff2 = mockk<GrpcTimeOff>()
        every { timeOff2.id } returns 2
        every { timeOff2.status.name } returns "PENDING"

        val timeOffs = mockk<GrpcTimeOffs>()
        every { timeOffs.timeOffsList } returns listOf(timeOff1, timeOff2)

        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns timeOffs

        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(1) } returns true
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(2) } returns false

        val platformTimeoffData = mockk<JpaPlatformTimeoffIntegration>()
        every { platformTimeoffData.externalTimeoffId } returns "ext1"
        every { platformTimeoffData.employeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { platformTimeoffDataRepository.findByInternalTimeoffId(1) } returns platformTimeoffData

        val cancelResult = mockk<HibobPassthroughResponse>()
        every { cancelResult.succeeded } returns true
        coEvery {
            knitAdapter.cancelHibobLeaveRequest(
                companyId = 100L,
                platformId = 10L,
                requestId = "ext1",
                employeeId = "employee1"
            )
        } returns cancelResult
        coEvery { companyIntegrationRepository.findEORTimeoffEnabledIntegration() } returns listOf(integration)

        coEvery { platformTimeoffDataRepository.delete(any()) } just Runs

        testStrategy.processTimeOff(integration)

        coVerify(exactly = 1) {
            knitAdapter.cancelHibobLeaveRequest(
                companyId = 100L,
                platformId = 10L,
                requestId = "ext1",
                employeeId = "employee1"
            )
        }
        coVerify(exactly = 1) { platformTimeoffDataRepository.delete(platformTimeoffData) }
        coVerify(exactly = 0) { knitAdapter.addHibobLeaveRequest(any(), any(), any(), any()) }
    }

    @Test
    fun `processTimeOffs should process all time offs`() = runBlocking {
        // Mock data
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<HibobPassthroughResponse>()
        val getEmployeeLeaveRequestResponse = mockk<LeaveRequestResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "HibobHR"
        every { integration.accountToken } returns "TOKEN"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder().addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false
        coEvery { knitAdapter.addHibobLeaveRequest(any(), any(), any(), any()) } returns addTimeOffResult
        every { addTimeOffResult.succeeded } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()
        coEvery { knitAdapter.getEmployeeLeaveRequests(any(), any(), any()) } returns getEmployeeLeaveRequestResponse
        every { getEmployeeLeaveRequestResponse.data?.requests } returns listOf(
            LeaveRequest(
                "employee1",
                "ext-1",
                startDate = timeOff.startDate.toLocalDate().format(formatter),
                endDate = timeOff.endDate.toLocalDate().format(formatter),
                status = Status.valueOf("APPROVED"),
                requestedOn = null,
                note = null,
                unit = null,
                amount = null,
                isPaid = null
            )
        )
        // Execute
        testStrategy.processTimeOff(integration)
        // Verify
        coVerify { platformContractIntegrationRepository.findByIntegrationId(1L) }
        coVerify { timeOffServiceAdapter.getTimeOffsByContractIds(any()) }
        coVerify { timeOffSyncService.getLeaveTypeMappingDefinition(100L, 1L) }
        coVerify { platformTimeoffDataRepository.existsByInternalTimeoffId(1L) }
        coVerify { knitAdapter.addHibobLeaveRequest(100L, 10L, any(), any()) }
        coVerify { timeOffService.addTimeoffDataToCache(1L, 1L, any(), 1L, any()) }
    }

    @Test
    fun `updateEmployeeContactDetails should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeContactDetails(
                companyId = 1L,
                contractId = 1L,
                contactDetails = getContactDetails()
            )
        }

        assertEquals("HibobHR platform does not support updating employee contact details", exception.message)
    }

    @Test
    fun `updateBankDetails should not throw exception`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(
                companyId = 1L,
                contractId = 1L,
                bankData = getBankData()
            )
        }

        assertEquals("HibobHR platform does not support updating employee bank details", exception.message)
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee basic details`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeBasicDetails(1L, 1L, getBasicDetails())
        }
        assertThat(exception.message).isEqualTo("HibobHR platform does not support updating employee basic details")
    }
}
