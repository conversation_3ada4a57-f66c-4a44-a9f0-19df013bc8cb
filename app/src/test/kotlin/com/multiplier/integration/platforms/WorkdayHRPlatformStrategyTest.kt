package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategory
import com.multiplier.integration.adapter.model.BasicDetails
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.ContactDetails
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensation
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getCompensationPlanResponse
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.mock.getCreateDocumentResponse
import com.multiplier.integration.mock.getDocumentCategoriesResponse
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEmploeeRecord
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockEmployeeData
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.mock.getMockMemberWithoutEmail
import com.multiplier.integration.mock.getMockPlatformEmployeeData
import com.multiplier.integration.mock.getPositionDetail
import com.multiplier.integration.mock.getTerminateEmployeeResponse
import com.multiplier.integration.mock.getTerminationReasonResponse
import com.multiplier.integration.mock.getUpdateCompensationResponse
import com.multiplier.integration.mock.getUpdateEmployeeDetailsResponse
import com.multiplier.integration.mock.getUpdateEmployeeResponse
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaDocumentFolder
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.PositionsService
import com.multiplier.integration.service.exception.IntegrationIllegalArgumentException
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Member
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import jakarta.persistence.EntityNotFoundException
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.net.URL
import java.time.LocalDate
import java.util.*

@ExtendWith(SpringExtension::class)
class WorkdayHRPlatformStrategyTest{

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var positionsService: PositionsService

    @MockK
    private lateinit var documentFoldersRepository: DocumentFoldersRepository

    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @InjectMockKs
    lateinit var testStrategy: WorkdayHRPlatformStrategy

    @Test
    fun `should create employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns getPositionDetail(false, null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns getPositionDetail(true, null)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns getPositionDetail(true, Position("test","test","test"))
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        member = getMockMember(1)
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(false)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(true)
        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.empty()
        every { platformRepository.findFirstByCategoryAndName(any(), any()) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformRepository.findFirstByCategoryAndName(any(), any())} returns integration.platform
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second

        val createEmployeePlatformResponse: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,contract,null)

        assert(createEmployeePlatformResponse.createdEmployeeId=="1")
    }

    @Test
    fun `should update employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns getPositionDetail(false, null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns getPositionDetail(true, null)
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { positionsService.getPositions(1,1, ignoreCache = false) } returns getPositionDetail(true, Position("test","test","test"))
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        member = getMockMember(1)
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(false,"test")
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(true,"test")
        testStrategy.updateEmployee(companyId, employeeData, member, contract, null)

        coVerify(exactly = 2){knitAdapter.updateEmployeeDetails(any(),any(),any(),any())}
    }

    @Test
    fun `should update employee compensation`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val compensationDetails: CompensationData = getCompensationData()
        val integration = getMockCompanyIntegration().get()

        integration.setPrivateField("outgoingSyncEnabled",false)
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        integration.setPrivateField("outgoingSyncEnabled",true)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        every { contractServiceAdapter.getCurrentCompensation(1) } returns getCompensation()
        coEvery { knitAdapter.getCompensationPlan(1,1) } returns getCompensationPlanResponse()

        coEvery { knitAdapter.updateCompensation(1,1,any()) } returns getUpdateCompensationResponse(false)
        assertThrows<Exception> { testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)}

        coEvery { knitAdapter.updateCompensation(1,1,any()) } returns getUpdateCompensationResponse(true)
        testStrategy.updateEmployeeCompensation(companyId,contractId,compensationDetails)

        coVerify(exactly = 2){knitAdapter.updateCompensation(any(),any(),any())}
    }

    @Test
    fun `should update onboarding kit document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(1,1) } returns null
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdAndPlatformIdOrderByCreatedOnDesc(1,1) } returns getMockContractIntegration(1).second
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateOnboardingKitDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update factsheet document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateFactsheetDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update contract document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateContractDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update salary review document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(false)
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocumentCategories(1,1,any()) } returns getDocumentCategoriesResponse(true)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateSalaryReviewDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should upload payslip document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document = InternalDocument(downloadUrl = "http://127.0.0.1:8080")
        val integration = getMockCompanyIntegration().get()
        val mockDocumentFolder = mockk<JpaDocumentFolder>(relaxed = true) {
            every { folderId } returns "test"
        }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        every { documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!) } returns mockDocumentFolder
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)

        testStrategy.uploadPayslipDocument(companyId,contractId,document)

        coVerify(exactly = 1){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should terminate employee`() {
        testScope.runTest {
            val companyId: Long = 1
            val contractId: Long = 1
            val terminationDate: LocalDate = LocalDate.now()
            val terminationReason = "Test"
            val integration = getMockCompanyIntegration().get()
            val platformEmployeeData = getMockPlatformEmployeeData(
                gender = "null",
                firstName = "\"Britanni\"",
                lastName = "\"Buchanan\"",
                birthDate = "null",
                maritalStatus = "null",
                status = "null",
                terminationDate = "null"
            )
            val contractIntegration = getMockContractIntegration(1).second

            every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns null
            assertThrows<IntegrationIllegalStateException> { testStrategy.terminateEmployee(
                companyId,
                contractId,
                terminationDate,
                terminationReason,
            ) }

            every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Workday") } returns integration.platform
            every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
            assertThrows<EntityNotFoundException> { testStrategy.terminateEmployee(
                companyId,
                contractId,
                terminationDate,
                terminationReason,

                ) }

            every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
            every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
            assertThrows<Exception> { testStrategy.terminateEmployee(
                companyId,
                contractId,
                terminationDate,
                terminationReason,
            )}

            every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns contractIntegration
            every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integrationId = integration.id!!, contractIntegration.platformEmployeeId) } returns platformEmployeeData
            coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(false,"test")
            assertThrows<Exception> { testStrategy.terminateEmployee(
                companyId,
                contractId,
                terminationDate,
                terminationReason,
            )}

            coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(true,"test")
            assertThrows<Exception> { testStrategy.terminateEmployee(
                companyId,
                contractId,
                terminationDate,
                terminationReason,
            )}

            coEvery { knitAdapter.getTerminationReason(1, 1) } returns getTerminationReasonResponse(true,"Terminate")
            coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(false)
            assertThrows<Exception> { testStrategy.terminateEmployee(
                companyId,
                contractId,
                terminationDate,
                terminationReason,
            )}

            coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(true)
            every { platformEmployeeDataRepository.save(any()) } returns platformEmployeeData[0]

            testStrategy.terminateEmployee(companyId, contractId, terminationDate, terminationReason,)

            coVerify(exactly = 3){knitAdapter.terminateEmployee(1,1,any())}
        }
    }

    @Test
    fun `should update employee basic details`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val details: BasicDetails = getBasicDetails()
        every { platformContractIntegrationRepository.findByContractId(1) } returns listOf()
        assertThrows<IntegrationIllegalArgumentException> { testStrategy.updateEmployeeBasicDetails(companyId,contractId,details) }

        every { platformContractIntegrationRepository.findByContractId(1) } returns listOf(getMockContractIntegration(1).second)
        coEvery { knitAdapter.updateEmployeeRecord(1,any(),any(),any()) } returns getUpdateEmployeeResponse()

        testStrategy.updateEmployeeBasicDetails(companyId,contractId,details)
    }

    @Test
    fun `should update employee contact details`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val contactDetails: ContactDetails = getContactDetails()
        every { platformContractIntegrationRepository.findByContractId(1) } returns listOf()
        assertThrows< IntegrationIllegalArgumentException> { testStrategy.updateEmployeeContactDetails(companyId,contractId,contactDetails) }

        every { platformContractIntegrationRepository.findByContractId(1) } returns listOf(getMockContractIntegration(1).second)
        coEvery { knitAdapter.updateEmployeeContactDetails(1,any(),any(),any()) } returns getUpdateEmployeeResponse()

        testStrategy.updateEmployeeContactDetails(companyId,contractId,contactDetails)
    }

    @Test
    fun `createEmployeeWithoutIntegrationData should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.createEmployeeWithoutIntegrationData(
                companyId = 1L,
                firstName = "John",
                lastName = "Doe",
                primaryEmail = "<EMAIL>",
                position = "Software Engineer",
                workEmail = "<EMAIL>"
            )
        }

        assertEquals("Workday platform does not support creating employee without integration data", exception.message)
    }

    @Test
    fun `updateBankDetails should not throw exception`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(
                companyId = 1L,
                contractId = 1L,
                bankData = getBankData()
            )
        }

        assertEquals("Workday platform does not support updating employee bank details", exception.message)
    }
}
