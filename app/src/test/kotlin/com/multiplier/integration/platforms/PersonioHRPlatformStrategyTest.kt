package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.workday.DocumentCategory
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.mock.getCreateDocumentResponse
import com.multiplier.integration.mock.getDocumentCategoriesResponse
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getEmploeeRecord
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockEmployeeData
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.mock.getMockMemberWithoutEmail
import com.multiplier.integration.mock.getTerminateEmployeeResponse
import com.multiplier.integration.mock.getUpdateEmployeeDetailsResponse
import com.multiplier.integration.mock.setPrivateField
import com.multiplier.integration.platforms.actions.CreateEmployeePlatformResponse
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.DocumentFoldersRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaDocumentFolder
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.types.DocumentFolderType
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Member
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import jakarta.persistence.EntityNotFoundException
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.net.URL
import java.time.LocalDate
import java.util.*

@ExtendWith(SpringExtension::class)
class PersonioHRPlatformStrategyTest{

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var documentFoldersRepository: DocumentFoldersRepository

    @MockK
    private lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository

    @InjectMockKs
    lateinit var testStrategy: PersonioHRPlatformStrategy

    @Test
    fun `should create employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        member = getMockMember(1)
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(false)
        assertThrows<Exception> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(true)
        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.empty()
        every { platformRepository.findFirstByCategoryAndName(any(), any()) } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.createEmployee(companyId,employeeData,member,contract,null) }

        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformRepository.findFirstByCategoryAndName(any(), any())} returns integration.platform
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second

        val createEmployeePlatformResponse: CreateEmployeePlatformResponse = testStrategy.createEmployee(companyId,employeeData,member,contract,null)

        assert(createEmployeePlatformResponse.createdEmployeeId=="1")
    }

    @Test
    fun `should update employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateEmployee(
            companyId,
            employeeData,
            member,
            contract,
            null
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(false,"already")
        assertThrows<Exception> { testStrategy.updateEmployee(companyId, employeeData, member, contract, null) }

        coEvery { knitAdapter.updateEmployeeDetails(1,1,1 , any()) } returns getUpdateEmployeeDetailsResponse(true,"test")
        testStrategy.updateEmployee(companyId, employeeData, member, contract, null)

        coVerify(exactly = 2){knitAdapter.updateEmployeeDetails(any(),any(),any(),any())}
    }

    @Test
    fun `should update onboarding kit document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateOnboardingKitDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update factsheet document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateFactsheetDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateFactsheetDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateFactsheetDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update contract document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateContractDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateContractDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateContractDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should update salary review document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId,document)}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.updateSalaryReviewDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateSalaryReviewDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should upload payslip document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document = InternalDocument(downloadUrl = "http://127.0.0.1:8080")
        val integration = getMockCompanyIntegration().get()
        val mockDocumentFolder = mockk<JpaDocumentFolder>(relaxed = true) {
            every { folderId } returns "test"
        }

        every { documentFoldersRepository.findByFolderTypeAndIntegrationId(folderType = DocumentFolderType.PAYSLIP, integrationId = integration.id!!) } returns mockDocumentFolder
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.uploadPayslipDocument(companyId,contractId,document) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second

        val documentCatergory = DocumentCategory(
            id = "1",
            name = "Other documents"
        )
        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(false, documentCatergory)
        assertThrows<Exception> { testStrategy.updateOnboardingKitDocument(companyId,contractId, getDocumentResponse())}

        coEvery { knitAdapter.getDocCategories(1, 1) } returns getDocumentCategoriesResponse(true, documentCatergory)

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL",URL("http://127.0.0.1:8080"))
        assertThrows<Exception> { testStrategy.uploadPayslipDocument(companyId,contractId,document)}

        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.uploadPayslipDocument(companyId,contractId,document)

        coVerify(exactly = 2){knitAdapter.createDocument(any(),any(),any())}
    }

    @Test
    fun `should terminate employee`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val terminationReason = "Test"
        val integration = getMockCompanyIntegration().get()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns null
        assertThrows<IntegrationIllegalStateException> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        ) }

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Personio") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        ) }

        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(false)
        assertThrows<Exception> { testStrategy.terminateEmployee(
            companyId,
            contractId,
            terminationDate,
            terminationReason
        )}

        coEvery { knitAdapter.terminateEmployee(1,1,any()) } returns getTerminateEmployeeResponse(true)
        testStrategy.terminateEmployee(companyId, contractId, terminationDate, terminationReason)

        coVerify(exactly = 2){knitAdapter.terminateEmployee(1,1,any())}
    }


    @Test
    fun `createEmployeeWithoutIntegrationData should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.createEmployeeWithoutIntegrationData(
                companyId = 1L,
                firstName = "John",
                lastName = "Doe",
                primaryEmail = "<EMAIL>",
                position = "Software Engineer",
                workEmail = "<EMAIL>"
            )
        }

        assertEquals("Personio platform does not support creating employee without integration data", exception.message)
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee compensation`() = testScope.runTest {
        val compensationDetails: CompensationData = getCompensationData()

        assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeCompensation(1L, 1L, compensationDetails)
        }.also {
            assertThat(it.message).isEqualTo("Personio platform does not support updating employee compensation")
        }
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee basic details`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeBasicDetails(1L, 1L, getBasicDetails())
        }
        assertThat(exception.message).isEqualTo("Personio platform does not support updating employee basic details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee contact details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeContactDetails(1L, 1L, getContactDetails())
        }
        assertThat(exception.message).isEqualTo("Personio platform does not support updating employee contact details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating bank details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(1L, 1L, getBankData())
        }
        assertThat(exception.message).isEqualTo("Personio platform does not support updating employee bank details")
    }
}
