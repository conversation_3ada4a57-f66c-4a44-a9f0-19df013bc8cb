package com.multiplier.integration.platforms

import com.multiplier.integration.adapter.api.ContractServiceAdapter
import com.multiplier.integration.adapter.api.DefaultTriNetAPIAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.trinet.CreateEmployeeResponseModel
import com.multiplier.integration.adapter.api.resources.trinet.CreateEmployeeResponseModelData
import com.multiplier.integration.adapter.api.resources.trinet.GeneralTriNetResponse
import com.multiplier.integration.adapter.api.resources.trinet.GetLocationByNameResponse
import com.multiplier.integration.adapter.api.resources.trinet.Location
import com.multiplier.integration.adapter.api.resources.trinet.TerminateEmployeeRequest
import com.multiplier.integration.adapter.api.resources.trinet.TerminateEmployeeResponse
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.mock.getDocumentResponse
import com.multiplier.integration.mock.getMockCompanyIntegration
import com.multiplier.integration.mock.getMockContract
import com.multiplier.integration.mock.getMockContractIntegration
import com.multiplier.integration.mock.getMockMember
import com.multiplier.integration.mock.getMockPlatformEmployeeData
import com.multiplier.integration.mock.getMockPlatformEmployeeDataWithTriNetData
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.EventLogRepository
import com.multiplier.integration.repository.ManualSyncRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformEmployeeDataRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.repository.model.JpaProvider
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.InternalDocument
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.Member
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.kotlin.mock
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.LocalDate
import kotlin.test.assertEquals
import kotlin.test.assertFailsWith

@ExtendWith(SpringExtension::class)
class TriNetPlatformStrategyTest {

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository
    @MockK
    lateinit var providerRepository: ProviderRepository
    @MockK
    lateinit var knitAdapter: KnitAdapter
    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository
    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository
    @MockK
    lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK
    lateinit var triNetAPIAdapter: DefaultTriNetAPIAdapter
    @MockK
    lateinit var platformEmployeeDataRepository: PlatformEmployeeDataRepository
    @MockK
    lateinit var manualSyncRepository: ManualSyncRepository
    @MockK
    lateinit var eventLogRepository: EventLogRepository

    @InjectMockKs
    lateinit var testStrategy: TriNetPlatformStrategy

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `should sync terminated employee successfully`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val integration = getMockCompanyIntegration().get()
        val platformContractIntegration = getMockContractIntegration(1).second
        val platformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val externalEmployeeId = platformContractIntegration.platformEmployeeId

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(1, integration.provider.id, integration.platform.id) } returns platformContractIntegration
        every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integration.id!!, externalEmployeeId) } returns platformEmployeeData
        coEvery { triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, TerminateEmployeeRequest(
            terminationDate = terminationDate.toString()
        ))} returns TerminateEmployeeResponse(
            success = true,
            responseCode = 200
        )
        every { platformEmployeeDataRepository.save(any()) } returns platformEmployeeData[0]

        testStrategy.terminateEmployee(companyId, contractId, terminationDate, "",)

        coVerify(exactly = 1) { triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, any())}
    }

    @Test
    fun `should sync terminated employee failed`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val integration = getMockCompanyIntegration().get()
        val platformContractIntegration = getMockContractIntegration(1).second
        val externalEmployeeId = platformContractIntegration.platformEmployeeId
        val platformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(1, integration.provider.id, integration.platform.id) } returns platformContractIntegration
        every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integrationId = integration.id!!,
            externalEmployeeId
        ) } returns platformEmployeeData
        coEvery { triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, TerminateEmployeeRequest(
            terminationDate = terminationDate.toString()
        ))} returns TerminateEmployeeResponse(
            success = false,
            responseCode = 500,
            error = ErrorResponse(
                msg = "Test Exception"
            )
        )

        val exception = assertFailsWith<Exception> {
            testStrategy.terminateEmployee(companyId, contractId, terminationDate, "",)
        }

        coVerify(exactly = 1) { triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, any())}
        assertEquals("[TriNetPlatformStrategy] terminateEmployee failed: ErrorResponse(msg=Test Exception)", exception.message)
    }

    @Test
    fun `should sync terminated employee failed for not found contract integration`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val integration = getMockCompanyIntegration().get()
        val platformEmployeeId = "Test"

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(1, integration.provider.id, integration.platform.id) } returns null

        val exception = assertFailsWith<Exception> {
            testStrategy.terminateEmployee(companyId, contractId, terminationDate, "",)
        }

        coVerify(exactly = 0) { triNetAPIAdapter.terminateEmployee(integration, platformEmployeeId, any())}
        assertEquals("ContractIntegration not found with contractId=$contractId", exception.message)
    }

    @Test
    fun `should sync terminated employee failed for not found employee data`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val integration = getMockCompanyIntegration().get()
        val platformContractIntegration = getMockContractIntegration(1).second
        val platformEmployeeId = platformContractIntegration.platformEmployeeId

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(1, integration.provider.id, integration.platform.id) } returns platformContractIntegration
        every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integration.id!!,
            platformEmployeeId
        ) } returns emptyList()

        val exception = assertFailsWith<Exception> {
            testStrategy.terminateEmployee(companyId, contractId, terminationDate, "",)
        }

        coVerify(exactly = 0) { triNetAPIAdapter.terminateEmployee(integration, platformEmployeeId, any())}
        assertEquals("PlatformEmployeeData not found with contractId=$contractId", exception.message)
    }

    @Test
    fun `should sync terminated employee failed for unknown errors`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val terminationDate: LocalDate = LocalDate.now()
        val integration = getMockCompanyIntegration().get()
        val platformContractIntegration = getMockContractIntegration(1).second
        val platformEmployeeData = getMockPlatformEmployeeData(
            gender = "null",
            firstName = "\"Britanni\"",
            lastName = "\"Buchanan\"",
            birthDate = "null",
            maritalStatus = "null",
            status = "null",
            terminationDate = "null"
        )
        val externalEmployeeId = platformContractIntegration.platformEmployeeId

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(1, integration.provider.id, integration.platform.id) } returns platformContractIntegration
        every { platformEmployeeDataRepository.findByIntegrationIdAndEmployeeIdAndIsDeletedFalse(integrationId = integration.id!!, externalEmployeeId) } returns platformEmployeeData
        coEvery { triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, TerminateEmployeeRequest(
            terminationDate = terminationDate.toString()
        ))} returns TerminateEmployeeResponse(
            success = false,
            responseCode = 500,
        )

        val exception = assertFailsWith<Exception> {
            testStrategy.terminateEmployee(companyId, contractId, terminationDate, "",)
        }

        coVerify(exactly = 1) { triNetAPIAdapter.terminateEmployee(integration, externalEmployeeId, any())}
        assertEquals("[TriNetPlatformStrategy] terminateEmployee failed: Unknown error", exception.message)
    }

    @Test
    fun `should sync created employee successfully`() = testScope.runTest {
        val companyId: Long = 1
        val integration = getMockCompanyIntegration().get()
        val member = getMockMember(1L)
        val contract = getMockContract(1L, 1L)
        val employeeData = EmployeeData(
            firstName = "Test",
            lastName = "Test",
            fullName = "Test",
            username = "test123",
            personalEmail = "<EMAIL>",
            id = "test",
            phoneNumber = "test",
            gender = Gender.FEMALE,
            maritalStatus = Member.MaritalStatus.MARRIED,
            employmentActive = true,
            position = "Test",
            contactDetails = null,
            dateOfBirth = null,
            endDate = null,
            startDate = null,
            workEmail = null
        )

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(any(), any(), any()) } returns null
        coEvery { triNetAPIAdapter.getLocationByName(integration, any())} returns GetLocationByNameResponse(
            data = listOf(Location(locationId = "Test"))
        )
        coEvery { triNetAPIAdapter.getOrFetchDepartmentCode(integration) } returns "TEST"

        coEvery { triNetAPIAdapter.createEmployee(integration, 0L, any())} returns CreateEmployeeResponseModel(
            data = CreateEmployeeResponseModelData(employeeId = "Test")
        )
        every { providerRepository.findFirstByName(ProviderName.TRINET) } returns JpaProvider(
            id = 1L,
            name = ProviderName.TRINET
        )
        every { platformContractIntegrationRepository.save(any()) } returns mock< JpaPlatformContractIntegration>()

        val resp =testStrategy.createEmployee(companyId, employeeData, member, contract, null)

        assertEquals("Test", resp?.createdEmployeeId)
        assertEquals(companyId, resp?.companyIntegrationId)
        coVerify(exactly = 1) { triNetAPIAdapter.createEmployee(integration, 0L, any()) }
    }

    @Test
    fun `should sync updated employee successfully`() = testScope.runTest {
        val companyId: Long = 1
        val integration = getMockCompanyIntegration().get()
        val member = getMockMember(1L)
        val contract = getMockContract(1L, 1L)
        val mockPlatformContractIntegration = getMockContractIntegration(contract.id).second
        val mockPlatformEmployeeData = getMockPlatformEmployeeDataWithTriNetData()

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findByContractId(contractId = contract.id) } returns listOf(mockPlatformContractIntegration)
        every { platformEmployeeDataRepository.findByEmployeeId(mockPlatformContractIntegration.platformEmployeeId) } returns mockPlatformEmployeeData

        coEvery { triNetAPIAdapter.updateEmployeeName(integration, mockPlatformContractIntegration.platformEmployeeId, 0L, any())} returns GeneralTriNetResponse(
            _statusCode = "200",
            _statusMessage = "OK"
        )
        coEvery { triNetAPIAdapter.updateEmployeeAddress(integration, mockPlatformContractIntegration.platformEmployeeId, 0L, any())} returns GeneralTriNetResponse(
            _statusCode = "200",
            _statusMessage = "OK"
        )
        coEvery { triNetAPIAdapter.updateEmployeeTitle(integration, mockPlatformContractIntegration.platformEmployeeId, 0L, any())} returns GeneralTriNetResponse(
            _statusCode = "200",
            _statusMessage = "OK"
        )
        coEvery { triNetAPIAdapter.updateEmployeeContacts(integration, mockPlatformContractIntegration.platformEmployeeId, 0L, any())} returns GeneralTriNetResponse(
            _statusCode = "200",
            _statusMessage = "OK"
        )
        every { triNetAPIAdapter.updateInternalTriNetEmployeeData(mockPlatformEmployeeData[0], any(), mockPlatformContractIntegration.platformEmployeeId) } returns Unit

        testStrategy.updateEmployee(companyId, null, member, contract, null)

        coVerify(exactly = 1) { triNetAPIAdapter.updateEmployeeName(integration, mockPlatformContractIntegration.platformEmployeeId, 0L, any()) }
    }

    @Test
    fun `should sync updated employee with not existed contract in cache successfully`() = testScope.runTest {
        val companyId: Long = 1
        val integration = getMockCompanyIntegration().get()
        val member = getMockMember(1L)
        val contract = getMockContract(1L, 1L)

        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findByContractId(contractId = contract.id) } returns emptyList()
        every { platformEmployeeDataRepository.findByEmployeeId(null) } returns emptyList()
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "TriNet") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdAndProviderIdAndPlatformId(any(), any(), any()) } returns null

        coEvery { triNetAPIAdapter.getLocationByName(integration, any())} returns GetLocationByNameResponse(
            data = listOf(Location(locationId = "Test"))
        )
        coEvery { triNetAPIAdapter.getOrFetchDepartmentCode(integration) } returns "TEST"

        coEvery { triNetAPIAdapter.createEmployee(integration, 0L, any())} returns CreateEmployeeResponseModel(
            data = CreateEmployeeResponseModelData(employeeId = "Test")
        )
        every { providerRepository.findFirstByName(ProviderName.TRINET) } returns JpaProvider(
            id = 1L,
            name = ProviderName.TRINET
        )
        every { platformContractIntegrationRepository.save(any()) } returns mock< JpaPlatformContractIntegration>()

        testStrategy.updateEmployee(companyId, null, member, contract, null)

        coVerify(exactly = 0) { triNetAPIAdapter.updateEmployeeName(integration, any(), 0L, any()) }
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee compensation`() = testScope.runTest {
        val compensationDetails: CompensationData = getCompensationData()

        assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeCompensation(1L, 1L, compensationDetails)
        }.also {
            assertThat(it.message).isEqualTo("TriNet platform does not support updating employee compensation")
        }
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee basic details`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeBasicDetails(1L, 1L, getBasicDetails())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating employee basic details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee contact details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeContactDetails(1L, 1L, getContactDetails())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating employee contact details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating bank details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(1L, 1L, getBankData())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating bank details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating onboarding kit document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateOnboardingKitDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating onboarding kit document")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating factsheet document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateFactsheetDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating factsheet document")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating contract document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateContractDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating contract document")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating salary review document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateSalaryReviewDocument(1L, 1L, getDocumentResponse())
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support updating salary review document")
    }

    @Test
    fun `should throw UnsupportedOperationException when uploading payslip document`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.uploadPayslipDocument(
                1L,
                1L,
                InternalDocument(downloadUrl = "example.com")
            )
        }
        assertThat(exception.message).isEqualTo("TriNet platform does not support uploading payslip document")
    }
}