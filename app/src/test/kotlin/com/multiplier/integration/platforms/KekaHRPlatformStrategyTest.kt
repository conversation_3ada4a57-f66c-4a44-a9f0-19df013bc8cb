package com.multiplier.integration.platforms

import com.google.protobuf.Timestamp
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.TimeoffServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequest
import com.multiplier.integration.adapter.api.resources.knit.LeaveRequestResponse
import com.multiplier.integration.adapter.api.resources.knit.keka.KekaResponse
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.mock.getBankData
import com.multiplier.integration.mock.getBasicDetails
import com.multiplier.integration.mock.getCompensationData
import com.multiplier.integration.mock.getContactDetails
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformTimeoffIntegrationRepository
import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaPlatformContractIntegration
import com.multiplier.integration.service.FeatureFlag
import com.multiplier.integration.service.FeatureFlagService
import com.multiplier.integration.service.TimeOffSyncService
import com.multiplier.integration.service.TimeoffService
import com.multiplier.integration.sync.model.Status
import com.multiplier.integration.types.LeaveTypeMappingDefinition
import com.multiplier.integration.utils.toLocalDate
import com.multiplier.timeoff.schema.GrpcTimeOff
import com.multiplier.timeoff.schema.GrpcTimeOffs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.time.format.DateTimeFormatter

@ExtendWith(SpringExtension::class)
class KekaHRPlatformStrategyTest {

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @InjectMockKs
    lateinit var testStrategy: KekaHRPlatformStrategy

    @MockK
    lateinit var timeOffServiceAdapter: TimeoffServiceAdapter

    @MockK
    lateinit var timeOffSyncService: TimeOffSyncService

    @MockK
    lateinit var platformTimeoffDataRepository: PlatformTimeoffIntegrationRepository

    @MockK
    lateinit var timeOffService: TimeoffService

    @MockK
    lateinit var featureFlagService: FeatureFlagService

    @Test
    fun `processTimeOffs should process all time offs`() = runBlocking {
        // Mock data
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'00:00:00'Z'")
        val integration = mockk<JpaCompanyIntegration>()
        val syncedEmployee = mockk<JpaPlatformContractIntegration>()
        val timeOff = mockk<GrpcTimeOff>()
        val addTimeOffResult = mockk<KekaResponse>()
        val getEmployeeLeaveRequestResponse = mockk<LeaveRequestResponse>()

        // Setup mocks
        every { integration.id } returns 1L
        every { integration.companyId } returns 100L
        every { integration.platform.id } returns 10L
        every { integration.platform.name } returns "KekaHR"
        every { integration.accountToken } returns "TOKEN"
        coEvery { platformContractIntegrationRepository.findByIntegrationId(any()) } returns listOf(syncedEmployee)
        every { syncedEmployee.contractId } returns 1L
        every { syncedEmployee.platformEmployeeId } returns "employee1"
        every { syncedEmployee.integrationId } returns 1L
        coEvery { timeOffServiceAdapter.getTimeOffsByContractIds(any()) } returns GrpcTimeOffs.newBuilder()
            .addTimeOffs(timeOff).build()
        every { timeOff.status.name } returns "APPROVED"
        every { timeOff.id } returns 1L
        every { timeOff.timeOffType.key } returns "VACATION"
        every { timeOff.contractId } returns 1L
        every { timeOff.startDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.endDate } returns Timestamp.newBuilder().setSeconds(**********).build()
        every { timeOff.noOfDays } returns 1.5
        coEvery { timeOffSyncService.getLeaveTypeMappingDefinition(any(), any()) } returns listOf(
            LeaveTypeMappingDefinition(1, 1, "1", "VACATION")
        )
        coEvery { platformTimeoffDataRepository.existsByInternalTimeoffId(any()) } returns false
        coEvery { knitAdapter.addKekaLeaveRequest(any(), any(), any(), any()) } returns addTimeOffResult
        every { addTimeOffResult.succeeded } returns true
        every { addTimeOffResult.data } returns mockk(relaxed = true)
        coEvery { timeOffService.addTimeoffDataToCache(any(), any(), any(), any(), any()) } returns mockk()
        coEvery { knitAdapter.getEmployeeLeaveRequests(any(), any(), any()) } returns getEmployeeLeaveRequestResponse
        every { getEmployeeLeaveRequestResponse.data?.requests } returns listOf(
            LeaveRequest(
                "employee1",
                "ext-1",
                startDate = timeOff.startDate.toLocalDate().format(formatter),
                endDate = timeOff.endDate.toLocalDate().format(formatter),
                status = Status.valueOf("APPROVED"),
                requestedOn = null,
                note = null,
                unit = null,
                amount = null,
                isPaid = null
            )
        )
        // Execute
        testStrategy.processTimeOff(integration)

        // Verify
        coVerify { platformContractIntegrationRepository.findByIntegrationId(1L) }
        coVerify { timeOffServiceAdapter.getTimeOffsByContractIds(any()) }
        coVerify { timeOffSyncService.getLeaveTypeMappingDefinition(100L, 1L) }
        coVerify { platformTimeoffDataRepository.existsByInternalTimeoffId(1L) }
        coVerify { knitAdapter.addKekaLeaveRequest(100L, 10L, any(), any()) }
        coVerify { timeOffService.addTimeoffDataToCache(1L, 1L, any(), 1L, any()) }
    }
    @Test
    fun `should throw UnsupportedOperationException when updating employee basic details`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeBasicDetails(1L, 1L, getBasicDetails())
        }
        assertThat(exception.message).isEqualTo("KekaHR platform does not support updating employee basic details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee contact details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeContactDetails(1L, 1L, getContactDetails())
        }
        assertThat(exception.message).isEqualTo("KekaHR platform does not support updating employee contact details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating bank details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(1L, 1L, getBankData())
        }
        assertThat(exception.message).isEqualTo("KekaHR platform does not support updating employee bank details")
    }

    @Test
    fun `createEmployeeWithoutIntegrationData should throw UnsupportedOperationException`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.createEmployeeWithoutIntegrationData(
                companyId = 1L,
                firstName = "John",
                lastName = "Doe",
                primaryEmail = "<EMAIL>",
                position = "Software Engineer",
                workEmail = "<EMAIL>"
            )
        }

        assertEquals("KekaHR platform does not support creating employee without integration data", exception.message)
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee compensation`() = testScope.runTest {
        val compensationDetails: CompensationData = getCompensationData()

        assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeCompensation(1L, 1L, compensationDetails)
        }.also {
            assertThat(it.message).isEqualTo("KekaHR platform does not support updating employee compensation details")
        }
    }
}