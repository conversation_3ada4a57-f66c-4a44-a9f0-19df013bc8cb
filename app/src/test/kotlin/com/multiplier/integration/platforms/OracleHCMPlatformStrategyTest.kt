package com.multiplier.integration.platforms

import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.Position
import com.multiplier.integration.adapter.api.resources.knit.oracle.*
import kotlinx.serialization.json.Json
import com.multiplier.integration.adapter.model.EmployeeData
import com.multiplier.integration.mock.*
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.PlatformContractIntegrationRepository
import com.multiplier.integration.repository.PlatformRepository
import com.multiplier.integration.repository.ProviderRepository
import com.multiplier.integration.repository.type.ProviderName
import com.multiplier.integration.service.PositionsService
import com.multiplier.integration.service.exception.IntegrationIllegalStateException
import com.multiplier.integration.service.exception.IntegrationInternalServerException
import com.multiplier.integration.types.PlatformCategory
import com.multiplier.member.schema.Member
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import jakarta.persistence.EntityNotFoundException
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*
import com.multiplier.core.common.rest.client.docgen.model.DocumentResponse
import com.multiplier.integration.adapter.model.CompensationData
import com.multiplier.integration.service.InternalDocument
import org.assertj.core.api.Assertions.assertThat
import java.net.URL

@ExtendWith(SpringExtension::class)
class OracleHCMPlatformStrategyTest {

    private val dispatcher = StandardTestDispatcher()
    private val testScope = TestScope(dispatcher)

    @MockK
    lateinit var platformRepository: PlatformRepository

    @MockK
    lateinit var providerRepository: ProviderRepository

    @MockK
    lateinit var knitAdapter: KnitAdapter

    @MockK
    lateinit var platformContractIntegrationRepository: PlatformContractIntegrationRepository

    @MockK
    lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    lateinit var positionsService: PositionsService

    @InjectMockKs
    lateinit var testStrategy: OracleHCMPlatformStrategy

    @Test
    fun `should create employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        // Test platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test integration not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test position not found
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(false, null)
        assertThrows<Exception> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test position found but no details
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, null)
        assertThrows<Exception> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test missing primary email
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Setup for legal entities tests
        member = getMockMember(1)

        // Test unsuccessful legal entities response
        coEvery { knitAdapter.getLegalEntitiesOracleHCM(1,1) } returns GetLegalEntitiesResponse(
            success = false,
            error = ErrorResponse(msg = "API Error"),
            data = null,
            responseCode = 200
        )
        assertThrows<IntegrationInternalServerException>("Failed to fetch legal entities: API Error") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test null success flag in legal entities response
        coEvery { knitAdapter.getLegalEntitiesOracleHCM(1,1) } returns GetLegalEntitiesResponse(
            success = null,
            data = null,
            responseCode = 200
        )
        assertThrows<IntegrationInternalServerException>("Failed to fetch legal entities: No data received") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test empty legal entities data
        val emptyResponse = LegalEntityBodyWrapper(body = """{"items": []}""")
        coEvery { knitAdapter.getLegalEntitiesOracleHCM(1,1) } returns GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(response = emptyResponse),
            responseCode = 200
        )
        assertThrows<IntegrationInternalServerException>("Failed to fetch legal entities: No data received") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test null legal entities data
        coEvery { knitAdapter.getLegalEntitiesOracleHCM(1,1) } returns GetLegalEntitiesResponse(
            success = true,
            data = null,
            responseCode = 200
        )
        assertThrows<IntegrationInternalServerException>("Failed to fetch legal entities: No data received") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test legal entities with null OrganizationId
        val nullOrgIdResponse = LegalEntityBodyWrapper(
            body = """
            {
                "items": [
                    {
                        "Name": "Test Legal Entity",
                        "LegislationCode": "US"
                    }
                ]
            }
            """.trimIndent()
        )

        coEvery { knitAdapter.getLegalEntitiesOracleHCM(1,1) } returns GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(response = nullOrgIdResponse),
            responseCode = 200
        )
        assertThrows<IntegrationInternalServerException>("No legal entities available for companyId=1") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test employee creation failure
        val successResponse = LegalEntityBodyWrapper(
            body = """
            {
                "items": [
                    {
                        "OrganizationId": 300100037952498,
                        "Name": "Test Legal Entity",
                        "LegislationCode": "US"
                    }
                ]
            }
            """.trimIndent()
        )

        coEvery { knitAdapter.getLegalEntitiesOracleHCM(1,1) } returns GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(response = successResponse),
            responseCode = 200
        )
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(false)
        assertThrows<IntegrationInternalServerException>("Employee creation failed: API Error") {
            testStrategy.createEmployee(companyId, employeeData, member, contract, null)
        }

        // Test successful case
        coEvery { knitAdapter.createEmployeeRecord(1,1, any()) } returns getEmploeeRecord(true)
        every { providerRepository.findFirstByName(ProviderName.KNIT) } returns integration.provider
        every { platformRepository.findById(1)} returns Optional.of(integration.platform)
        every { platformContractIntegrationRepository.save(any()) } returns getMockContractIntegration(1).second

        val response = testStrategy.createEmployee(companyId, employeeData, member,
            getMockContract(1,1, workEmail = "notBlank"), null)

        assert(response.createdEmployeeId == "1")
    }

    @Test
    fun `should update employee`() = testScope.runTest {
        val companyId: Long = 1
        val employeeData: EmployeeData? = getMockEmployeeData()
        var member: Member = getMockMemberWithoutEmail(1)
        val contract: Contract = getMockContract(1,1)
        val integration = getMockCompanyIntegration().get()

        // Test platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test contract integration not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<IntegrationInternalServerException>("ContractIntegration not found for contractId=1") {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test missing primary email
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        assertThrows<IntegrationIllegalStateException>("Primary email not found for member.") {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test update failure
        member = getMockMember(1)
        coEvery { positionsService.getPositions(1,1, ignoreCache = true) } returns getPositionDetail(true, Position("test","test","test"))
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1, any()) } returns getUpdateEmployeeDetailsResponse(false, "API Error")
        assertThrows<IntegrationInternalServerException>("Employee update failed: API Error") {
            testStrategy.updateEmployee(companyId, employeeData, member, contract, null)
        }

        // Test successful update
        coEvery { knitAdapter.updateEmployeeDetails(1,1,1, any()) } returns getUpdateEmployeeDetailsResponse(true, "test")

        testStrategy.updateEmployee(companyId, employeeData, member,
            getMockContract(1,1, workEmail = "notBlank"), null)

        coVerify(exactly = 2) { knitAdapter.updateEmployeeDetails(any(), any(), any(), any()) }
    }

    @Test
    fun `should update onboarding kit document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        // Test when platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.updateOnboardingKitDocument(companyId, contractId, document)
        }

        // Test when integration not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns null
        assertThrows<EntityNotFoundException> {
            testStrategy.updateOnboardingKitDocument(companyId, contractId, document)
        }

        // Test when contract integration not found
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<IntegrationInternalServerException> {
            testStrategy.updateOnboardingKitDocument(companyId, contractId, document)
        }

        // Test when document upload fails
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        document.setPrivateField("internalDownloadURL", URL("http://127.0.0.1:8080"))
        assertThrows<IntegrationInternalServerException> {
            testStrategy.updateOnboardingKitDocument(companyId, contractId, document)
        }

        // Test successful document upload
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        testStrategy.updateOnboardingKitDocument(companyId, contractId, document)

        coVerify(exactly = 2) { knitAdapter.createDocument(any(), any(), any()) }
    }

    @Test
    fun `should update factsheet document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        // Test when platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.updateFactsheetDocument(companyId, contractId, document)
        }

        // Test successful document upload
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        document.setPrivateField("internalDownloadURL", URL("http://127.0.0.1:8080"))

        testStrategy.updateFactsheetDocument(companyId, contractId, document)

        coVerify(exactly = 1) { knitAdapter.createDocument(any(), any(), any()) }
    }

    @Test
    fun `should update contract document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        // Test when platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.updateContractDocument(companyId, contractId, document)
        }

        // Test successful document upload
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        document.setPrivateField("internalDownloadURL", URL("http://127.0.0.1:8080"))

        testStrategy.updateContractDocument(companyId, contractId, document)

        coVerify(exactly = 1) { knitAdapter.createDocument(any(), any(), any()) }
    }

    @Test
    fun `should update salary review document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document: DocumentResponse = getDocumentResponse()
        val integration = getMockCompanyIntegration().get()

        // Test when platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.updateSalaryReviewDocument(companyId, contractId, document)
        }

        // Test successful document upload
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)
        document.setPrivateField("internalDownloadURL", URL("http://127.0.0.1:8080"))

        testStrategy.updateSalaryReviewDocument(companyId, contractId, document)

        coVerify(exactly = 1) { knitAdapter.createDocument(any(), any(), any()) }
    }

    @Test
    fun `should upload payslip document`() = testScope.runTest {
        val companyId: Long = 1
        val contractId: Long = 1
        val document = InternalDocument(downloadUrl = "http://127.0.0.1:8080")
        val integration = getMockCompanyIntegration().get()

        // Test when platform not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns null
        assertThrows<IntegrationIllegalStateException> {
            testStrategy.uploadPayslipDocument(companyId, contractId, document)
        }

        // Test when contract integration not found
        every { platformRepository.findFirstByCategoryAndName(PlatformCategory.HRIS, "Oracle HCM") } returns integration.platform
        every { companyIntegrationRepository.findEnabledIntegration(1,1) } returns listOf(integration)
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns null
        assertThrows<IntegrationInternalServerException>("ContractIntegration not found for contractId=1") {
            testStrategy.uploadPayslipDocument(companyId, contractId, document)
        }

        // Test document upload failure
        every { platformContractIntegrationRepository.findFirstByContractIdOrderByCreatedOnDesc(1) } returns getMockContractIntegration(1).second
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(false)
        assertThrows<IntegrationInternalServerException>("Upload payslipDocument failed: API Error") {
            testStrategy.uploadPayslipDocument(companyId, contractId, document)
        }

        // Test successful document upload
        coEvery { knitAdapter.createDocument(1,1,any()) } returns getCreateDocumentResponse(true)

        testStrategy.uploadPayslipDocument(companyId, contractId, document)

        coVerify(exactly = 2) { knitAdapter.createDocument(any(), any(), any()) }
    }

    @Test
    fun `should validate GetLegalEntitiesModel data structures`() {
        // Test complete legal entities response with all fields
        val completeBodyJson = """
            {
                "items": [
                    {
                        "OrganizationId": 300100037952498,
                        "EffectiveStartDate": "2023-01-01",
                        "EffectiveEndDate": "2023-12-31",
                        "Name": "Test Legal Entity",
                        "LegislationCode": "US",
                        "PayrollStatutoryUnitId": *********,
                        "links": [
                            {
                                "rel": "self",
                                "href": "https://example.com/api/entity/1",
                                "name": "entity",
                                "kind": "item"
                            }
                        ]
                    }
                ],
                "count": 1,
                "hasMore": false,
                "limit": 100,
                "offset": 0,
                "links": [
                    {
                        "rel": "self",
                        "href": "https://example.com/api/entities",
                        "name": "entities",
                        "kind": "collection"
                    }
                ]
            }
        """.trimIndent()

        val completeResponse = GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(
                response = LegalEntityBodyWrapper(body = completeBodyJson)
            ),
            responseCode = 200
        )

        // Validate the getLegalEntities extension function
        val entities = completeResponse.getLegalEntities()
        assert(entities != null) { "Entities should not be null" }
        assert(entities?.size == 1) { "Should have 1 entity" }

        // Validate all fields in the entity
        val entity = entities?.get(0)
        assert(entity?.OrganizationId == 300100037952498) { "OrganizationId should match" }
        assert(entity?.EffectiveStartDate == "2023-01-01") { "EffectiveStartDate should match" }
        assert(entity?.EffectiveEndDate == "2023-12-31") { "EffectiveEndDate should match" }
        assert(entity?.Name == "Test Legal Entity") { "Name should match" }
        assert(entity?.LegislationCode == "US") { "LegislationCode should match" }
        assert(entity?.PayrollStatutoryUnitId == *********L) { "PayrollStatutoryUnitId should match" }
        assert(entity?.links?.size == 1) { "Should have 1 link" }
        assert(entity?.links?.get(0)?.rel == "self") { "Link rel should match" }
        assert(entity?.links?.get(0)?.href == "https://example.com/api/entity/1") { "Link href should match" }
        assert(entity?.links?.get(0)?.name == "entity") { "Link name should match" }
        assert(entity?.links?.get(0)?.kind == "item") { "Link kind should match" }

        // Test partial data (missing some fields)
        val partialBodyJson = """
            {
                "items": [
                    {
                        "OrganizationId": 300100037952499,
                        "Name": "Another Legal Entity",
                        "LegislationCode": "UK"
                    }
                ],
                "count": 1
            }
        """.trimIndent()

        val partialResponse = GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(
                response = LegalEntityBodyWrapper(body = partialBodyJson)
            ),
            responseCode = 200
        )

        val partialEntities = partialResponse.getLegalEntities()
        assert(partialEntities != null) { "Partial entities should not be null" }
        assert(partialEntities?.size == 1) { "Should have 1 entity" }

        // Validate partial entity fields
        val partialEntity = partialEntities?.get(0)
        assert(partialEntity?.OrganizationId == 300100037952499) { "OrganizationId should match" }
        assert(partialEntity?.Name == "Another Legal Entity") { "Name should match" }
        assert(partialEntity?.LegislationCode == "UK") { "LegislationCode should match" }
        assert(partialEntity?.EffectiveStartDate == null) { "EffectiveStartDate should be null" }
        assert(partialEntity?.EffectiveEndDate == null) { "EffectiveEndDate should be null" }
        assert(partialEntity?.PayrollStatutoryUnitId == null) { "PayrollStatutoryUnitId should be null" }
        assert(partialEntity?.links == null) { "Links should be null" }

        // Test empty items list
        val emptyBodyJson = """
            {
                "items": [],
                "count": 0,
                "hasMore": false
            }
        """.trimIndent()

        val emptyResponse = GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(
                response = LegalEntityBodyWrapper(body = emptyBodyJson)
            ),
            responseCode = 200
        )

        val emptyEntities = emptyResponse.getLegalEntities()
        assert(emptyEntities != null) { "Empty entities should not be null" }
        assert(emptyEntities?.isEmpty() == true) { "Entities list should be empty" }

        // Test null data scenarios
        val nullDataResponse = GetLegalEntitiesResponse(
            success = true,
            data = null,
            responseCode = 200
        )
        assert(nullDataResponse.getLegalEntities() == null) { "Entities should be null when data is null" }

        val nullResponseResponse = GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(response = null),
            responseCode = 200
        )
        assert(nullResponseResponse.getLegalEntities() == null) { "Entities should be null when response is null" }

        val nullBodyResponse = GetLegalEntitiesResponse(
            success = true,
            data = LegalEntityResponseWrapper(
                response = LegalEntityBodyWrapper(body = null)
            ),
            responseCode = 200
        )
        assert(nullBodyResponse.getLegalEntities() == null) { "Entities should be null when body is null" }

        // Test error response
        val errorResponse = GetLegalEntitiesResponse(
            success = false,
            error = ErrorResponse(msg = "API Error"),
            data = null,
            responseCode = 400
        )
        assert(errorResponse.getLegalEntities() == null) { "Entities should be null for error response" }

        // Test direct deserialization of LegalEntitiesBody
        val legalEntitiesBody = Json.decodeFromString<LegalEntitiesBody>(completeBodyJson)
        assert(legalEntitiesBody.items?.size == 1) { "Should have 1 item" }
        assert(legalEntitiesBody.count == 1) { "Count should be 1" }
        assert(legalEntitiesBody.hasMore == false) { "hasMore should be false" }
        assert(legalEntitiesBody.limit == 100) { "Limit should be 100" }
        assert(legalEntitiesBody.offset == 0) { "Offset should be 0" }
        assert(legalEntitiesBody.links?.size == 1) { "Should have 1 link" }
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee compensation`() = testScope.runTest {
        val compensationDetails: CompensationData = getCompensationData()

        assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeCompensation(1L, 1L, compensationDetails)
        }.also {
            assertThat(it.message).isEqualTo("Oracle HCM platform does not support updating employee compensation")
        }
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee basic details`() = testScope.runTest {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeBasicDetails(1L, 1L, getBasicDetails())
        }
        assertThat(exception.message).isEqualTo("Oracle HCM platform does not support updating employee basic details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating employee contact details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateEmployeeContactDetails(1L, 1L, getContactDetails())
        }
        assertThat(exception.message).isEqualTo("Oracle HCM platform does not support updating employee contact details")
    }

    @Test
    fun `should throw UnsupportedOperationException when updating bank details`() {
        val exception = assertThrows<UnsupportedOperationException> {
            testStrategy.updateBankDetails(1L, 1L, getBankData())
        }
        assertThat(exception.message).isEqualTo("Oracle HCM platform does not support updating employee bank details")
    }
}