package utils

import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import utils.StringUtils.Companion.maskEmail
import utils.StringUtils.Companion.replaceAllNonAlphanumericCharacters

class StringUtilsTest {

    @Test
    fun `should replace all non alphanumeric characters`() {
        Assertions.assertEquals("55006 2911", replaceAllNonAlphanumericCharacters("55006-2911", " "))
    }

    @Test
    fun `should mask email with standard format`() {
        Assertions.assertEquals("jo***<EMAIL>", maskEmail("<EMAIL>"))
    }

    @Test
    fun `should return original email when username is too short`() {
        Assertions.assertEquals("<EMAIL>", maskEmail("<EMAIL>"))
    }

    @Test
    fun `should return original email when format is invalid`() {
        Assertions.assertEquals("invalid-email", maskEmail("invalid-email"))
    }
}