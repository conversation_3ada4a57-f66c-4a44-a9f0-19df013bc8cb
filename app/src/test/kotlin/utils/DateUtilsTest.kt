package utils

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import utils.DateUtils.Companion.parseToFormattedDateString

class DateUtilsTest {

    @Test
    fun `should parse to correct formatted date string`() {
        assertEquals("2000-01-10", parseToFormattedDateString("2000-01-10T00:00:00+01:00", "yyyy-MM-dd"))
        assertEquals("2000-01-10", parseToFormattedDateString("2000-01-10T00:00:00Z", "yyyy-MM-dd"))
        assertEquals("2000-01-10", parseToFormattedDateString("2000-01-10T00:00Z", "yyyy-MM-dd"))
        assertEquals("2000-01-10", parseToFormattedDateString("2000-01-10", "yyyy-MM-dd"))
    }
}