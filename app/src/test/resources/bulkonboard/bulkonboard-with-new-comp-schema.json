{"inputs": [{"requestId": 1, "properties": {"employeeId": "grpc-test001", "position": "grpc-test001", "startOn": "2025-02-14", "firstName": "grpc-test001", "lastName": "grpc-test001", "email": "<EMAIL>", "gender": "MALE", "race": "grpc-test001", "phoneNumber": "+65 **********", "address.line1": "grpc-test001", "address.city": "grpc-test001", "address.country": "SGP", "address.postalCode": "1234345", "immigrationStatus": "Citizen", "nid": "grpctest001", "bank.accountHolderName": "grpctest", "bank.accountNumber": "**********", "bank.swiftCode": "AHTSDHCT", "bank.bankName": "grpc-test001", "bank.address.country": "SG"}, "group": "EMPLOYMENT_DATA"}, {"requestId": 2, "properties": {"employeeId": "grpc-test001", "COMPONENT_NAME": "Base Salary", "CURRENCY": "SGD", "BILLING_RATE_TYPE": "Value", "BILLING_RATE": "20000", "BILLING_FREQUENCY": "MONTHLY", "PAY_SCHEDULE_NAME": "Monthly-01", "IS_INSTALLMENT": "No", "START_DATE": "2025-02-14"}, "group": "COMPENSATION_DATA"}], "option": {"companyId": "866846", "context": "GLOBAL_PAYROLL", "contractType": "CONTRACT_TYPE_HR_MEMBER", "countryCode": "COUNTRY_CODE_SGP", "entityId": "********"}}