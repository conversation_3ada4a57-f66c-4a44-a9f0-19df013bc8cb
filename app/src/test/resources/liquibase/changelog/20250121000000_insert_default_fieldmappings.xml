<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <changeSet id="20250121000000-1" author="nick" dbms="postgresql">
        <validCheckSum>ANY</validCheckSum>
        <comment>inserts all the default field mappings (generated from stage db)</comment>
        <sql>
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (135, 'address.city', 'locations.presentAddress.city', 'DEFAULT', false, '2024-08-28 09:57:40.061497', '2024-08-28 09:57:40.061497', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (14, 'address.city', 'locations.presentAddress.city', 'DEFAULT', false, '2024-08-22 18:50:21.697513', '2024-08-22 18:50:21.697513', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (133, 'address.country', 'locations.presentAddress.country', 'DEFAULT', false, '2024-08-28 09:57:40.040442', '2024-08-28 09:57:40.040442', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (15, 'address.country', 'locations.presentAddress.country', 'DEFAULT', false, '2024-08-22 18:50:21.700168', '2024-08-22 18:50:21.700168', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (132, 'address.line1', 'locations.presentAddress.addressLine1', 'DEFAULT', false, '2024-08-28 09:57:40.016814', '2024-08-28 09:57:40.016814', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (16, 'address.line1', 'locations.presentAddress.addressLine1', 'DEFAULT', false, '2024-08-22 18:50:21.702965', '2024-08-22 18:50:21.702965', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (17, 'address.line2', 'locations.presentAddress.addressLine2', 'DEFAULT', false, '2024-08-22 18:50:21.706148', '2024-08-22 18:50:21.706148', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (18, 'address.postalCode', 'locations.presentAddress.zipCode', 'DEFAULT', false, '2024-08-22 18:50:21.708798', '2024-08-22 18:50:21.708798', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (136, 'address.postalCode', 'locations.presentAddress.zipCode', 'DEFAULT', false, '2024-08-28 09:57:40.068120', '2024-08-28 09:57:40.068120', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (19, 'address.state', 'locations.presentAddress.state', 'DEFAULT', false, '2024-08-22 18:50:21.711278', '2024-08-22 18:50:21.711278', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (134, 'address.state', 'locations.presentAddress.state', 'DEFAULT', false, '2024-08-28 09:57:40.056275', '2024-08-28 09:57:40.056275', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (20, 'address.zipcode', 'locations.presentAddress.zipCode', 'DEFAULT', false, '2024-08-22 18:50:21.713841', '2024-08-22 18:50:21.713841', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (21, 'albanianIdentityCard', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.716431', '2024-08-22 18:50:21.716431', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (138, 'bank.accountHolderName', 'customFields.fields.accountHolderName', 'DEFAULT', false, '2024-08-28 09:57:40.088580', '2024-08-28 09:57:40.088580', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (140, 'bank.accountNumber', 'bankAccounts[0].accountNumber', 'DEFAULT', false, '2024-08-28 09:57:40.102655', '2024-08-28 09:57:40.102655', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (150, 'bank.address.country', 'bankAccounts[0].routingInfo[type=COUNTRY].number', 'DEFAULT', false, '2024-08-28 09:57:40.132181', '2024-08-28 09:57:40.132181', null, null, '{"MEXICO": "MX"}', 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (142, 'bank.bankName', 'bankAccounts[0].bankName', 'DEFAULT', false, '2024-08-28 09:57:40.109512', '2024-08-28 09:57:40.109512', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (139, 'bank.localBankCode', 'bankAccounts[0].routingInfo[type=CLABE].number', 'DEFAULT', false, '2024-08-28 09:57:40.095644', '2024-08-28 09:57:40.095644', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (141, 'bank.swiftCode', 'bankAccounts[0].routingInfo[type=BANK_IDENTIFICATION_CODE].number', 'DEFAULT', false, '2024-08-28 09:57:40.105926', '2024-08-28 09:57:40.105926', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (151, 'basePay', 'compensation.fixed[type=SALARY].amount', 'DEFAULT', false, '2024-08-28 09:57:40.134379', '2024-08-28 09:57:40.134379', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (122, 'basePay', 'compensation.fixed[0].amount', 'DEFAULT', false, '2024-08-22 18:50:22.131095', '2024-08-22 18:50:22.131095', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (22, 'beneficiaryName', 'dependents[0].firstName', 'DEFAULT', false, '2024-08-22 18:50:21.719675', '2024-08-22 18:50:21.719675', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (152, 'beneficiaryName', 'customFields.fields.beneficiaryName', 'DEFAULT', false, '2024-08-28 09:57:40.135966', '2024-08-28 09:57:40.135966', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (24, 'beneficiaryNationalId', 'customFields.fields.beneficiaryNationalId', 'DEFAULT', false, '2024-08-22 18:50:21.787528', '2024-08-22 18:50:21.787528', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (23, 'beneficiaryRelationship', 'dependents[0].relation', 'DEFAULT', false, '2024-08-22 18:50:21.722423', '2024-08-22 18:50:21.722423', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (25, 'bpjsNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.790393', '2024-08-22 18:50:21.790393', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (26, 'burgerServiceNummer', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.792966', '2024-08-22 18:50:21.792966', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (27, 'citizenCard', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.795684', '2024-08-22 18:50:21.795684', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (28, 'cityOfWork', 'locations.workAddress.city', 'DEFAULT', false, '2024-08-22 18:50:21.798528', '2024-08-22 18:50:21.798528', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (29, 'commercialRegistrationNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.802715', '2024-08-22 18:50:21.802715', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (12, 'contract.status', 'profile.employmentStatus', 'DEFAULT', false, '2024-08-22 18:50:21.691478', '2024-08-22 18:50:21.691478', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (30, 'corCode', 'customFields.fields.corCode', 'DEFAULT', false, '2024-08-22 18:50:21.805714', '2024-08-22 18:50:21.805714', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (9, 'countryState', 'locations.workAddress.state', 'DEFAULT', false, '2024-08-22 18:50:21.618265', '2024-08-22 18:50:21.618265', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (143, 'dateOfBirth', 'profile.birthDate', 'DEFAULT', false, '2024-08-28 09:57:40.112670', '2024-08-28 09:57:40.112670', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (31, 'dateOfBirth', 'profile.birthDate', 'DEFAULT', false, '2024-08-22 18:50:21.808155', '2024-08-22 18:50:21.808155', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (32, 'disabilityDegree', 'customFields.fields.disabilityDegree', 'DEFAULT', false, '2024-08-22 18:50:21.811865', '2024-08-22 18:50:21.811865', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (33, 'eiNit', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.814150', '2024-08-22 18:50:21.814150', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (128, 'email', 'contactInfo.personalEmails[0]', 'DEFAULT', false, '2024-08-28 09:57:39.943708', '2024-08-28 09:57:39.943708', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (4, 'email', 'contactInfo.personalEmails[0]', 'DEFAULT', false, '2024-08-22 18:50:21.607346', '2024-08-22 18:50:21.607346', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (34, 'employeeCprNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.816373', '2024-08-22 18:50:21.816373', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (125, 'employeeId', 'profile.id', 'DEFAULT', false, '2024-08-28 09:57:39.844263', '2024-08-28 09:57:39.844263', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (1, 'employeeId', 'profile.id', 'DEFAULT', false, '2024-08-22 18:50:21.586743', '2024-08-22 18:50:21.586743', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (8, 'endOn', 'profile.terminationDate', 'DEFAULT', false, '2024-08-22 18:50:21.615297', '2024-08-22 18:50:21.615297', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (35, 'ethnicity', 'customFields.fields.ethnicity', 'DEFAULT', false, '2024-08-22 18:50:21.825000', '2024-08-22 18:50:21.825000', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (36, 'fatherHusbandName', 'dependents[relation=FATHER].firstName', 'DEFAULT', false, '2024-08-22 18:50:21.828213', '2024-08-22 18:50:21.828213', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (37, 'fathersName', 'dependents[relation=FATHER].firstName', 'DEFAULT', false, '2024-08-22 18:50:21.833133', '2024-08-22 18:50:21.833133', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (126, 'firstName', 'profile.firstName', 'DEFAULT', false, '2024-08-28 09:57:39.938619', '2024-08-28 09:57:39.938619', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (2, 'firstName', 'profile.firstName', 'DEFAULT', false, '2024-08-22 18:50:21.603549', '2024-08-22 18:50:21.603549', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (38, 'fiscalCode', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.836324', '2024-08-22 18:50:21.836324', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (129, 'gender', 'profile.gender', 'DEFAULT', false, '2024-08-28 09:57:39.945326', '2024-08-28 09:57:39.945326', null, null, '{"MALE": "MALE", "FEMALE": "FEMALE", "NON_BINARY": "OTHER", "NOT_SPECIFIED": "OTHER"}', 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (5, 'gender', 'profile.gender', 'DEFAULT', false, '2024-08-22 18:50:21.609097', '2024-08-22 18:50:21.609097', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (39, 'gibraltarIdentityCard', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.839444', '2024-08-22 18:50:21.839444', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (40, 'hdmfNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.842095', '2024-08-22 18:50:21.842095', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (41, 'healthInsuranceNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.844505', '2024-08-22 18:50:21.844505', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (42, 'idCardGeorgia', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.846709', '2024-08-22 18:50:21.846709', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (43, 'identificationNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.849022', '2024-08-22 18:50:21.849022', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (44, 'identificationNumberForForeigner', 'employeeIdentificationData[type=PASSPORT].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.851898', '2024-08-22 18:50:21.851898', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (45, 'identityProof', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.854173', '2024-08-22 18:50:21.854173', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (46, 'idIssueDate', 'customFields.fields.idIssueDate', 'DEFAULT', false, '2024-08-22 18:50:21.856194', '2024-08-22 18:50:21.856194', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (47, 'idIssuePlace', 'customFields.fields.idIssuePlace', 'DEFAULT', false, '2024-08-22 18:50:21.858343', '2024-08-22 18:50:21.858343', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (48, 'idIssuingBody', 'customFields.fields.idIssuingBody', 'DEFAULT', false, '2024-08-22 18:50:21.860832', '2024-08-22 18:50:21.860832', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (49, 'immigrationStatus', 'customFields.fields.immigrationStatus', 'DEFAULT', false, '2024-08-22 18:50:21.863551', '2024-08-22 18:50:21.863551', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (50, 'incomeTaxNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.866909', '2024-08-22 18:50:21.866909', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (51, 'individualIdentificationNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.876420', '2024-08-22 18:50:21.876420', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (52, 'inssNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.880100', '2024-08-22 18:50:21.880100', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (53, 'internalPassportOfRussia', 'employeeIdentificationData[type=PASSPORT].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.888129', '2024-08-22 18:50:21.888129', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (54, 'isssNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.890731', '2024-08-22 18:50:21.890731', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (55, 'khmerIdentityCard', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.893700', '2024-08-22 18:50:21.893700', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (127, 'lastName', 'profile.lastName', 'DEFAULT', false, '2024-08-28 09:57:39.940370', '2024-08-28 09:57:39.940370', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (3, 'lastName', 'profile.lastName', 'DEFAULT', false, '2024-08-22 18:50:21.605625', '2024-08-22 18:50:21.605625', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (56, 'longTermVisa', 'customFields.fields.longTermVisa', 'DEFAULT', false, '2024-08-22 18:50:21.896248', '2024-08-22 18:50:21.896248', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (144, 'maritalStatus', 'profile.maritalStatus', 'DEFAULT', false, '2024-08-28 09:57:40.118694', '2024-08-28 09:57:40.118694', null, null, '{"SINGLE": "SINGLE", "MARRIED": "MARRIED", "DIVORCED": "DIVORCED", "SEPARATED": "UNSPECIFIED", "NOT_SPECIFIED": "UNSPECIFIED"}', 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (57, 'maritalStatus', 'profile.maritalStatus', 'DEFAULT', false, '2024-08-22 18:50:21.899191', '2024-08-22 18:50:21.899191', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (58, 'nationalHealthFundBranch', 'customFields.fields.nationalHealthFundBranch', 'DEFAULT', false, '2024-08-22 18:50:21.901487', '2024-08-22 18:50:21.901487', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (59, 'nationalId', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.903913', '2024-08-22 18:50:21.903913', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (145, 'nationalId', 'employeeIdentificationData[type=NATIONAL_ID,subType=Personal ID Code (CURP)].identificationNumber', 'DEFAULT', false, '2024-08-28 09:57:40.120759', '2024-08-28 09:57:40.120759', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (60, 'nationalIdCard', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.906266', '2024-08-22 18:50:21.906266', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (61, 'nationalIdCardNo', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.908466', '2024-08-22 18:50:21.908466', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (62, 'nationalIdentificationNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.910567', '2024-08-22 18:50:21.910567', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (63, 'nationalIdentityCard', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.912643', '2024-08-22 18:50:21.912643', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (64, 'nationalIdIssuedDate', 'customFields.fields.nationalIdIssuedDate', 'DEFAULT', false, '2024-08-22 18:50:21.915022', '2024-08-22 18:50:21.915022', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (65, 'nationalIdNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.917234', '2024-08-22 18:50:21.917234', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (66, 'nationalInsuranceId', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.919396', '2024-08-22 18:50:21.919396', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (67, 'nationalInsuranceNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.921432', '2024-08-22 18:50:21.921432', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (146, 'nationality', 'customFields.fields.nationality', 'DEFAULT', false, '2024-08-28 09:57:40.122970', '2024-08-28 09:57:40.122970', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (68, 'nationality', 'locations.presentAddress.country', 'DEFAULT', false, '2024-08-22 18:50:21.923644', '2024-08-22 18:50:21.923644', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (69, 'NationalRegisterNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.926210', '2024-08-22 18:50:21.926210', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (70, 'nationalTaxRegistryNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.928434', '2024-08-22 18:50:21.928434', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (71, 'nid', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.930723', '2024-08-22 18:50:21.930723', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (72, 'nitNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.932794', '2024-08-22 18:50:21.932794', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (73, 'npwpCardNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.934896', '2024-08-22 18:50:21.934896', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (74, 'nssfNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.937034', '2024-08-22 18:50:21.937034', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (75, 'nuitNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.939259', '2024-08-22 18:50:21.939259', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (123, 'numberOfDependants', 'dependents', 'CALCULATED', false, '2024-08-22 18:51:43.265146', '2024-08-22 18:51:43.265146', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (124, 'numberOfDependents', 'dependents', 'CALCULATED', false, '2024-08-22 18:51:43.352876', '2024-08-22 18:51:43.352876', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (76, 'numericPersonal', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.941659', '2024-08-22 18:50:21.941659', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (77, 'numericPersonalIssueDate', 'customFields.fields.numericPersonalIssueDate', 'DEFAULT', false, '2024-08-22 18:50:21.943862', '2024-08-22 18:50:21.943862', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (78, 'nupNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.945925', '2024-08-22 18:50:21.945925', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (79, 'panNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.948018', '2024-08-22 18:50:21.948018', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (80, 'passportIssuedDate', 'customFields.fields.passportIssuedDate', 'DEFAULT', false, '2024-08-22 18:50:21.950338', '2024-08-22 18:50:21.950338', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (81, 'passportNo', 'employeeIdentificationData[type=PASSPORT].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.952469', '2024-08-22 18:50:21.952469', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (82, 'passportNoOrDrivingLicense', 'employeeIdentificationData[type=PASSPORT].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.956307', '2024-08-22 18:50:21.956307', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (83, 'passportNumber', 'employeeIdentificationData[type=PASSPORT].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.960114', '2024-08-22 18:50:21.960114', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (148, 'payrollFrequency', 'compensation.fixed[type=SALARY].frequency', 'DEFAULT', false, '2024-08-28 09:57:40.128615', '2024-08-28 10:51:38.657763', null, null, '{"DAILY": "DAILY", "ANNUAL": "ANNUALLY", "HOURLY": "HOURLY", "WEEKLY": "WEEKLY", "MONTHLY": "MONTHLY", "BI WEEKLY": "SEMIMONTHLY", "QUARTERLY": "QUARTERLY", "SEMIMONTHLY": "SEMIMONTHLY", "SEMI MONTHLY": "SEMIMONTHLY"}', 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (120, 'payrollFrequency', 'compensation.fixed[0].frequency', 'DEFAULT', false, '2024-08-22 18:50:22.125937', '2024-08-22 18:50:22.125937', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (84, 'pensionFundNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.987206', '2024-08-22 18:50:21.987206', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (85, 'pensionsAffiliateNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:21.996484', '2024-08-22 18:50:21.996484', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (86, 'performanceBonusMaxLimitPercentage', 'customFields.fields.performanceBonusMaxLimitPercentage', 'DEFAULT', false, '2024-08-22 18:50:21.999986', '2024-08-22 18:50:21.999986', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (87, 'personalCard', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.016000', '2024-08-22 18:50:22.016000', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (88, 'personalIdentificationNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.029018', '2024-08-22 18:50:22.029018', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (89, 'peselNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.040354', '2024-08-22 18:50:22.040354', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (90, 'philHealthNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.048682', '2024-08-22 18:50:22.048682', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (137, 'phoneNumber', 'contactInfo.phones[0].number', 'DEFAULT', false, '2024-08-28 09:57:40.074818', '2024-08-28 09:57:40.074818', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (10, 'phoneNumber', 'contactInfo.phones[0].number', 'DEFAULT', false, '2024-08-22 18:50:21.620757', '2024-08-22 18:50:21.620757', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (91, 'placeOfBirth', 'customFields.fields.placeOfBirth', 'DEFAULT', false, '2024-08-22 18:50:22.051251', '2024-08-22 18:50:22.051251', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (6, 'position', 'orgStructure.designation', 'DEFAULT', false, '2024-08-22 18:50:21.610677', '2024-08-22 18:50:21.610677', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (130, 'position', 'orgStructure.designation', 'DEFAULT', false, '2024-08-28 09:57:40.001557', '2024-08-28 09:57:40.001557', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (92, 'race', 'customFields.fields.race', 'DEFAULT', false, '2024-08-22 18:50:22.054529', '2024-08-22 18:50:22.054529', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (149, 'rateFrequency', 'compensation.fixed[type=SALARY].payPeriod', 'DEFAULT', false, '2024-08-28 09:57:40.130344', '2024-08-28 10:51:38.724531', null, null, '{"DAILY": "DAILY", "ANNUAL": "ANNUALLY", "HOURLY": "HOURLY", "WEEKLY": "WEEKLY", "MONTHLY": "MONTHLY", "BI WEEKLY": "SEMIMONTHLY", "QUARTERLY": "QUARTERLY", "SEMI MONTHLY": "SEMIMONTHLY"}', 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (121, 'rateFrequency', 'compensation.fixed[0].payPeriod', 'DEFAULT', false, '2024-08-22 18:50:22.128958', '2024-08-22 18:50:22.128958', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (93, 'religion', 'customFields.fields.religion', 'DEFAULT', false, '2024-08-22 18:50:22.057303', '2024-08-22 18:50:22.057303', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (94, 'residenceCard', 'customFields.fields.residenceCard', 'DEFAULT', false, '2024-08-22 18:50:22.060350', '2024-08-22 18:50:22.060350', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (95, 'residentPermit', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.063627', '2024-08-22 18:50:22.063627', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (96, 'residentPermitNo', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.066143', '2024-08-22 18:50:22.066143', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (97, 'residentRegistrationNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.068801', '2024-08-22 18:50:22.068801', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (98, 'socialInsuranceNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.071142', '2024-08-22 18:50:22.071142', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (99, 'socialSecurityNumber', 'employeeIdentificationData[type=NATIONAL_ID].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.073933', '2024-08-22 18:50:22.073933', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (131, 'startOn', 'profile.startDate', 'DEFAULT', false, '2024-08-28 09:57:40.009956', '2024-08-28 09:57:40.009956', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (7, 'startOn', 'profile.startDate', 'DEFAULT', false, '2024-08-22 18:50:21.612890', '2024-08-22 18:50:21.612890', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (100, 'taxCode', 'customFields.fields.taxCode', 'DEFAULT', false, '2024-08-22 18:50:22.076115', '2024-08-22 18:50:22.076115', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (101, 'taxIdentification', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', null, '2024-08-22 18:50:22.078252', '2024-08-22 18:50:22.078252', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (102, 'taxIdentificationNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.081132', '2024-08-22 18:50:22.081132', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (103, 'taxIdNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.083609', '2024-08-22 18:50:22.083609', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (104, 'taxOfficeBranch', 'customFields.fields.taxOfficeBranch', 'DEFAULT', false, '2024-08-22 18:50:22.085676', '2024-08-22 18:50:22.085676', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (147, 'taxpayerID', 'employeeIdentificationData[type=NATIONAL_ID,subType=Taxpayer (RFC) Number].identificationNumber', 'DEFAULT', false, '2024-08-28 09:57:40.126583', '2024-08-28 09:57:40.126583', null, null, null, 4) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (105, 'taxpayerID', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.088174', '2024-08-22 18:50:22.088174', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (106, 'taxPayerIdentificationNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.090305', '2024-08-22 18:50:22.090305', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (107, 'taxpayerIdNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.092314', '2024-08-22 18:50:22.092314', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (13, 'term', 'profile.terminationDate', 'DEFAULT', false, '2024-08-22 18:50:21.694140', '2024-08-22 18:50:21.694140', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (108, 'tinNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.094407', '2024-08-22 18:50:22.094407', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (109, 'uanNumberExists', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.096471', '2024-08-22 18:50:22.096471', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (110, 'uanNumberValue', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.098525', '2024-08-22 18:50:22.098525', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (111, 'uniformCivilNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.101138', '2024-08-22 18:50:22.101138', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (112, 'uniqueMasterCitizenNumber', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.104000', '2024-08-22 18:50:22.104000', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (11, 'workEmail', 'profile.workEmail', 'DEFAULT', false, '2024-08-22 18:50:21.688428', '2024-08-22 18:50:21.688428', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (113, 'workLocation', 'locations.workAddress.city', 'DEFAULT', false, '2024-08-22 18:50:22.106436', '2024-08-22 18:50:22.106436', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (114, 'workPassNumber', 'customFields.fields.workPassNumber', 'DEFAULT', false, '2024-08-22 18:50:22.110151', '2024-08-22 18:50:22.110151', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (115, 'workPassType', 'customFields.fields.workPassType', 'DEFAULT', false, '2024-08-22 18:50:22.112778', '2024-08-22 18:50:22.112778', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (116, 'workPermit', 'employeeIdentificationData[type=OTHERS].identificationNumber', 'DEFAULT', false, '2024-08-22 18:50:22.115751', '2024-08-22 18:50:22.115751', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (117, 'workPermitNo', 'customFields.fields.workPermitNo', 'DEFAULT', false, '2024-08-22 18:50:22.119186', '2024-08-22 18:50:22.119186', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (118, 'workVisa', 'customFields.fields.workVisa', 'DEFAULT', false, '2024-08-22 18:50:22.121693', '2024-08-22 18:50:22.121693', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (119, 'workVisaNo', 'customFields.fields.workVisaNo', 'DEFAULT', false, '2024-08-22 18:50:22.123793', '2024-08-22 18:50:22.123793', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (160, 'bank.accountNumber', 'bankAccounts[0].accountNumber', 'DEFAULT', false, '2025-02-21 17:04:38.413474', '2025-02-21 17:04:38.413474', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (161, 'bank.swiftCode', 'bankAccounts[0].routingInfo[type=SWIFT_CODE].number', 'DEFAULT', false, '2025-02-21 17:04:38.413474', '2025-02-21 17:04:38.413474', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (162, 'bank.localBankCode', 'bankAccounts[0].routingInfo[type=BANK_IDENTIFICATION_CODE].number', 'DEFAULT', false, '2025-02-21 17:04:38.413474', '2025-02-21 17:04:38.413474', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (163, 'bank.bankName', 'bankAccounts[0].bankName', 'DEFAULT', false, '2025-02-21 17:04:38.413474', '2025-02-21 17:04:38.413474', null, null, null, null) on conflict do nothing;
            INSERT INTO customer_integration.field_mapping_configuration (id, key, value, type, is_deleted, created_on, updated_on, created_by, updated_by, enum_mappings, platform_id) VALUES (164, 'bank.routingNumber', 'bankAccounts[0].routingInfo[type=ROUTING_NUMBER].number', 'DEFAULT', false, '2025-02-21 17:04:38.413474', '2025-02-21 17:04:38.413474', null, null, null, null) on conflict do nothing;
        </sql>
    </changeSet>
</databaseChangeLog>
