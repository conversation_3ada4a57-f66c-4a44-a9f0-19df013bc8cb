spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    type: com.zaxxer.hikari.HikariDataSource
    url: **************************************************************************
    username: integrationdb
    password: ${REPLACE_FOR_MANUAL_TEST}
  task:
    scheduling:
      enabled: false
  kafka:
    enabled: false
  liquibase:
    enabled: false

platform:
  kafka:
    auto-startup: false
  knit:
    api-key: ${REPLACE_FOR_MANUAL_TEST}
    api-url: https://api.getknit.dev/v1.0/

grpc:
  server:
    port: 9090
    security:
      enabled: false
      certificate-chain: classpath:certificates/server.local.crt
      private-key: classpath:certificates/server.local.key
  client:
    core-service:
      address: dns:///core-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    member-service:
      address: dns:///member-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    pigeon-service:
      address: dns:///pigeon-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    contract-service:
      address: dns:///contract-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    country-service:
      address: dns:///countrz-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    expense-service:
      address: dns:///expense-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    contract-offboarding-service:
      address: dns:///contract-offboarding-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    company-service:
      address: dns:///company-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    contract-onboarding-service:
      address: dns:///contract-onboarding-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    timeoff-service:
      address: dns:///timeoff-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    pay-se:
      address: dns:///pay-se-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    org-management-service:
      address: dns:///org-management-service.staging.usemultiplier.internal:9090
      negotiationType: TLS
    payable-service:
      address: dns:///payable-service.staging.usemultiplier.internal:9090
      negotiationType: TLS

integration:
  webhook:
    sftp:
      api-key: abc123
