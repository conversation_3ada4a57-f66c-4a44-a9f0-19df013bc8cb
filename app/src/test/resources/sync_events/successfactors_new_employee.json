{"eventId": "ev_RSFZLQDKidd5UNkji10jY8", "recordId": "200000", "syncType": "initial_sync", "eventData": {"w4Data": null, "company": null, "profile": {"id": "200000", "gender": "MALE", "lastName": "<PERSON>", "birthDate": "1972-04-06T00:00Z", "firstName": "<PERSON>", "startDate": "1996-01-01T00:00Z", "workEmail": "<PERSON>@bestrunsap.com", "maritalStatus": "MARRIED", "employeeNumber": "2501", "employmentType": "FULL_TIME", "terminationDate": null, "employmentStatus": "ACTIVE"}, "locations": {"workAddress": {"city": "Montreal", "state": "3913", "country": "CA", "zipCode": "H3C 2M1", "addressType": "WORK", "addressLine1": "111 Duke St", "addressLine2": "Suite 9000"}, "presentAddress": {"city": "Winnipeg", "state": "3916", "country": "CA", "zipCode": "R3B 1B9", "addressType": "HOME_PRESENT", "addressLine1": "510 Main Street", "addressLine2": null}, "permanentAddress": null}, "rawValues": {"profile": {"gender": "M", "maritalStatus": "Married", "employmentType": "Salaried staff", "employmentStatus": "Active"}}, "deductions": null, "dependents": null, "contactInfo": {"phones": [{"type": "WORK", "number": "555-8876"}], "personalEmails": null}, "employeeKYC": null, "bankAccounts": [{"bankName": "ROYAL BANK OF CANADA", "payTypes": ["ALL"], "accountType": null, "routingInfo": [{"type": "ROUTING_NUMBER", "number": "*********"}], "accountNumber": "*********"}], "compensation": {"fixed": [{"type": "Base Salary Canada", "amount": 17000, "planId": "BASESAL_CA", "endDate": null, "currency": "CAD", "frequency": "SEMI_MONTHLY", "payPeriod": null, "startDate": "2019-01-01T00:00Z", "percentage": null}], "stock": null, "variable": null}, "customFields": null, "orgStructure": {"manager": {"id": "890274", "workEmail": "<EMAIL>"}, "department": "LEADERSHIP TEAM CANADA", "designation": "President Best Run Canada"}, "leaveRequests": null, "employeeProfilePicture": null, "employeeIdentificationData": null}, "eventType": "record.new", "syncJobId": "sj_H22j13V3Jmv4Ttok5EJgsV", "syncRunId": "sr_2Vt5Pyq4qYtTbBddDG1hG6", "triggeredAt": "*************", "syncDataType": "employee"}