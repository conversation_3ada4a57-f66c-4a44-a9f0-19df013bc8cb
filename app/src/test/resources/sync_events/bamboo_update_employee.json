{"eventId": "ev_yRNT8JSDCl4se53m1vlLQD", "recordId": "200", "syncType": "delta_sync", "eventData": {"w4Data": null, "company": null, "profile": {"id": "200", "gender": "MALE", "lastName": "comp", "birthDate": "1996-12-08T00:00:00Z", "firstName": "Ind", "startDate": "2025-02-26T00:00:00Z", "workEmail": "<EMAIL>", "maritalStatus": "SINGLE", "employeeNumber": "Ad qui esse facere sit eum elit do est quidem et pariatur Ipsum consequat Qui", "employmentType": "NOT_SPECIFIED", "terminationDate": null, "employmentStatus": "ACTIVE"}, "locations": {"workAddress": {"city": null, "state": null, "country": null, "zipCode": null, "addressType": "WORK", "addressLine1": "Canada Custom", "addressLine2": null}, "presentAddress": {"city": "Voluptate", "state": "", "country": "CA", "zipCode": "93441", "addressType": "HOME_PRESENT", "addressLine1": "<PERSON><PERSON><PERSON>", "addressLine2": "as<PERSON>s"}, "permanentAddress": null}, "rawValues": {"profile": {"gender": "Male", "maritalStatus": "Single", "employmentType": "Permanent", "employmentStatus": "Active"}}, "deductions": null, "dependents": null, "contactInfo": {"phones": [{"type": "PERSONAL", "number": "+****************"}, {"type": "HOME", "number": "+****************"}, {"type": "WORK", "number": "+****************"}], "personalEmails": ["<EMAIL>"]}, "employeeKYC": null, "bankAccounts": null, "compensation": {"fixed": [{"type": "SALARY", "amount": 1000.0, "planId": null, "endDate": null, "currency": "USD", "frequency": "Monthly", "payPeriod": "DAILY", "startDate": null, "percentage": null}], "stock": null, "variable": null}, "customFields": {"fields": {"bankName": "SBI", "ethnicity": "Asian", "swiftCode": "HDFCINBBCHE", "bankCountry": "US", "transitNumber": "*********", "nationalIdNumber": "*************", "bankAccountNumber": "********", "immigrationStatus": "Citizen", "socialInsuranceNumber": "**********", "financialInstitutionNumber": "********"}}, "orgStructure": {"manager": {"id": null, "workEmail": null}, "department": null, "designation": "QA"}, "leaveRequests": null, "employeeProfilePicture": null, "employeeIdentificationData": null}, "eventType": "record.modified", "syncJobId": "sj_qUC8orVK94Kg7yVDQTDkV9", "syncRunId": "sr_IaMafm2b9Js83vMVr3OEPb", "triggeredAt": "*************", "syncDataType": "employee"}