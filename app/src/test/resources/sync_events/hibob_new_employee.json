{"eventId": "ev_HCavHneBu7dfaauH6OYzSi", "recordId": "357130925371084017511", "syncType": "initial_sync", "eventData": {"w4Data": null, "company": {"companyId": "India", "companyName": "India", "companyAddressZip": null, "companyAddressCity": null, "companyAddressLine1": null, "companyAddressLine2": null, "companyAddressState": null, "companyAddressCountry": null}, "profile": {"id": "357130925371084017511", "gender": "MALE", "lastName": "<PERSON><PERSON><PERSON>", "birthDate": "1988-03-19T00:00:00Z", "firstName": "<PERSON>", "startDate": "2025-02-04T00:00:00Z", "workEmail": "<EMAIL>", "maritalStatus": "NOT_SPECIFIED", "employeeNumber": null, "employmentType": "FULL_TIME", "terminationDate": null, "employmentStatus": "ACTIVE"}, "locations": {"workAddress": {"city": null, "state": null, "country": "IN", "zipCode": null, "addressType": "WORK", "addressLine1": null, "addressLine2": null}, "presentAddress": {"city": null, "state": null, "country": "IN", "zipCode": null, "addressType": "HOME_PRESENT", "addressLine1": null, "addressLine2": null}, "permanentAddress": null}, "rawValues": {"profile": {"gender": null, "maritalStatus": null, "employmentType": "Full time", "employmentStatus": "Active"}}, "deductions": null, "dependents": [{"relation": "FATHER", "firstName": "<PERSON><PERSON><PERSON><PERSON>"}], "contactInfo": {"phones": null, "personalEmails": ["<EMAIL>"]}, "employeeKYC": null, "bankAccounts": [{"bankName": "VCB", "payTypes": ["ALL"], "accountType": "NOT_SPECIFIED", "routingInfo": [{"type": "IFSC_CODE", "number": "KKBK0000191"}], "accountNumber": "***********"}], "compensation": {"fixed": [{"type": "SALARY", "amount": 100000.0, "planId": "SALARY", "endDate": null, "currency": "USD", "frequency": "MONTHLY", "payPeriod": "MONTHLY", "startDate": "2025-02-04T00:00:00Z", "percentage": null}], "stock": null, "variable": null}, "customFields": null, "orgStructure": {"manager": {"id": "3463352647749207012", "workEmail": "<EMAIL>"}, "department": "Engineering", "designation": "CEO"}, "leaveRequests": null, "employeeProfilePicture": null, "employeeIdentificationData": null}, "eventType": "record.new", "syncJobId": "sj_oxtWHYYHI9BLVJlf1lvVdD", "syncRunId": "sr_R7DelpkhBQcHPs3oS62d9f", "triggeredAt": "*************", "syncDataType": "employee"}