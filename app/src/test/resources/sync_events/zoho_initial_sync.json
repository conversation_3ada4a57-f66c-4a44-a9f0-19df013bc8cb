{"eventId": "ev_L6o3Qs20LvDFZgsSpbGo8D", "recordId": "66638000000202130", "syncType": "initial_sync", "eventData": {"w4Data": null, "company": null, "profile": {"id": "66638000000202130", "gender": "FEMALE", "lastName": "<PERSON><PERSON>", "birthDate": "1985-01-13T00:00:00Z", "firstName": "<PERSON><PERSON><PERSON>", "startDate": "2012-07-08T00:00:00Z", "workEmail": "<EMAIL>", "maritalStatus": "SINGLE", "employeeNumber": "S15", "employmentType": "CONTRACT", "terminationDate": null, "employmentStatus": "ACTIVE"}, "locations": {"workAddress": null, "presentAddress": {"city": "Miami", "state": "Florida", "country": "US", "zipCode": "33169", "addressType": "HOME_PRESENT", "addressLine1": "620 NW", "addressLine2": null}, "permanentAddress": null}, "rawValues": {"profile": {"gender": "Female", "maritalStatus": "Single", "employmentType": "On Contract", "employmentStatus": "Active"}}, "deductions": null, "dependents": null, "contactInfo": {"phones": [{"type": "PERSONAL", "number": "************"}, {"type": "WORK", "number": "************"}], "personalEmails": null}, "employeeKYC": null, "bankAccounts": null, "compensation": null, "customFields": null, "orgStructure": {"manager": {"id": "*****************", "workEmail": "<EMAIL>"}, "department": "IT", "designation": "Team Member"}, "leaveRequests": null, "employeeProfilePicture": {"pictureURL": "https://people.zoho.eu/api/viewEmployeePhoto?filename=5014268000000002008", "pictureName": null}, "employeeIdentificationData": null}, "eventType": "record.new", "syncJobId": "sj_K3JwquP0ghdCoMSpWNYAfH", "syncRunId": "sr_8YknAjbUPDrskzul925cx2", "triggeredAt": "*************", "syncDataType": "employee"}