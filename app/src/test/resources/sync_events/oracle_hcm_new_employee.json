{"eventId": "ev_HCavHneBu7dfaauH6OYzSi", "recordId": "300000294424339", "syncType": "initial_sync", "eventData": {"w4Data": null, "company": {"companyId": "US1", "companyName": "US1 Legal Entity", "companyAddressZip": "94011", "companyAddressCity": "<PERSON><PERSON><PERSON><PERSON>", "companyAddressLine1": "182 Ferry Road", "companyAddressLine2": null, "companyAddressState": "CA", "companyAddressCountry": "US"}, "profile": {"id": "300000294424339", "gender": "MALE", "lastName": "<PERSON><PERSON>", "birthDate": "1990-01-01T00:00:00Z", "firstName": "<PERSON>", "startDate": "2024-11-18T00:00:00Z", "workEmail": "<EMAIL>", "maritalStatus": "NOT_SPECIFIED", "employeeNumber": "10014", "employmentType": "FULL_TIME", "terminationDate": null, "employmentStatus": "ACTIVE"}, "locations": {"workAddress": {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "CA", "country": "US", "zipCode": "94011", "addressType": "WORK", "addressLine1": "182 Ferry Road", "addressLine2": null}, "presentAddress": {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "CA", "country": "US", "zipCode": "94011", "addressType": "HOME_PRESENT", "addressLine1": "182 Ferry Road", "addressLine2": null}, "permanentAddress": null}, "rawValues": {"profile": {"gender": null, "maritalStatus": null, "employmentType": "E", "employmentStatus": "Active"}}, "deductions": null, "dependents": [{"relation": "SPOUSE", "firstName": "<PERSON>"}], "contactInfo": {"phones": [{"type": "PERSONAL", "number": "+***********"}], "personalEmails": ["<EMAIL>"]}, "employeeKYC": null, "bankAccounts": [{"bankName": "Chase", "payTypes": ["ALL"], "accountType": "CHECKING", "routingInfo": [{"type": "ROUTING_NUMBER", "number": "*********"}], "accountNumber": "*********"}], "compensation": {"fixed": [{"type": "SALARY", "amount": 120000.0, "planId": "SALARY", "endDate": null, "currency": "USD", "frequency": "MONTHLY", "payPeriod": "ANNUAL", "startDate": "2024-11-18T00:00:00Z", "percentage": null}], "stock": null, "variable": null}, "customFields": null, "orgStructure": {"manager": {"id": "***************", "workEmail": "<EMAIL>"}, "department": "Engineering", "designation": "Software Engineer", "businessUnit": "***************", "legalEmployerName": "US1 Legal Entity"}, "leaveRequests": null, "employeeProfilePicture": null, "employeeIdentificationData": null}, "eventType": "record.new", "syncJobId": "sj_oxtWHYYHI9BLVJlf1lvVdD", "syncRunId": "sr_R7DelpkhBQcHPs3oS62d9f", "triggeredAt": "*************", "syncDataType": "employee"}