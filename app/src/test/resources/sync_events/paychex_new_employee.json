{"eventId": "ev_t1z3feqTUFdXtMKnJhXUnv", "recordId": "004WOHNWMAC15MICDXHR", "syncType": "initial_sync", "eventData": {"w4Data": {"filingStatus": "SINGLE_OR_MARRIED_FILING_SEPARATELY", "hasMultipleJobs": "FALSE", "deductionsAmount": null, "otherIncomeAmount": null, "withholdingStatus": "NOT-EXEMPT", "otherDependentsAmount": null, "totalDependentsAmount": null, "extraWithholdingAmount": null, "numberOfOtherDependents": null, "qualifiedDependentsAmount": null, "numberOfQualifiedDependents": null}, "company": null, "profile": {"id": "004WOHNWMAC15MICDXHR", "gender": "MALE", "lastName": "Multiplier", "birthDate": "2000-10-05T00:00:00Z", "firstName": "<PERSON><PERSON><PERSON>", "startDate": "2025-05-06T00:00:00Z", "workEmail": "<EMAIL>", "maritalStatus": "MARRIED", "employeeNumber": "58", "employmentType": "FULL_TIME", "terminationDate": null, "employmentStatus": "NOT_SPECIFIED"}, "locations": {"workAddress": null, "presentAddress": {"city": "kakkanad", "state": null, "country": "IN", "zipCode": "673010", "addressType": "HOME_PRESENT", "addressLine1": "address line 1", "addressLine2": "address line2"}, "permanentAddress": null}, "rawValues": {"profile": {"gender": "MALE", "maritalStatus": null, "employmentType": "FULL_TIME", "employmentStatus": "HIRED"}}, "deductions": null, "dependents": [{"id": "7c0b0836-33cc-4fc5-aa20-98b34491e7b2", "gender": null, "lastName": "LastNameWife", "relation": "SPOUSE", "birthDate": null, "firstName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "contactInfo": {"phones": [{"type": "WORK", "number": "1-987-6543210"}], "personalEmails": ["<EMAIL>"]}, "employeeKYC": null, "bankAccounts": null, "compensation": null, "customFields": null, "orgStructure": {"manager": {"id": "001GJICFM4RHVMJPWEG9", "workEmail": "<EMAIL>"}, "department": "11 ORG_11-3", "designation": "Billing Specialist X"}, "leaveRequests": null, "employeeProfilePicture": null, "employeeIdentificationData": [{"type": "NATIONAL_ID", "subType": "SSN", "identificationNumber": "*********"}]}, "eventType": "record.new", "syncJobId": "sj_DcKjtqjsm2pLPoxfJ2oS8h", "syncRunId": "sr_BFx7lpDR6gLwnXH29IeNG6", "triggeredAt": "*************", "syncDataType": "employee"}