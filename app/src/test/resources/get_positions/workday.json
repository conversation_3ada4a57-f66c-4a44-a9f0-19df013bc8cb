{"success": true, "data": {"positions": [{"positionId": "P-00046", "designation": "Sales Contracts Manager", "department": "Sales_Operations_supervisory"}, {"positionId": "P-00050", "designation": "Corporate Counsel", "department": "Legal_supervisory"}, {"positionId": "P-00121", "designation": "Senior Manager, Marketing Communications-1", "department": "Marketing_Communications_supervisory"}, {"positionId": "P-00002", "designation": "Chief Information Officer", "department": "Executive_Management_supervisory"}, {"positionId": "P-00191", "designation": "Senior Risk Analyst", "department": "Risk_Management_supervisory"}, {"positionId": "P-00065", "designation": "Senior Risk Analyst-2", "department": "Risk_Management_supervisory"}, {"positionId": "P-00076", "designation": "Executive VP, Sales & Marketing", "department": "Executive_Management_supervisory"}, {"positionId": "P-00086", "designation": "Product Developer", "department": "Product_Development_supervisory"}, {"positionId": "P-00090", "designation": "Sales Proposal Specialists", "department": "Sales_Operations_supervisory"}, {"positionId": "P-00193", "designation": "Senior Data Analyst-1", "department": "Information_Analysis_supervisory"}, {"positionId": "P-00106", "designation": "Senior Data Analyst", "department": "Information_Analysis_supervisory"}, {"positionId": "P-00111", "designation": "Vice President, Program Management", "department": "Operations_supervisory"}, {"positionId": "P-00190", "designation": "Senior Manager, Field Marketing", "department": "Field_Marketing_supervisory"}, {"positionId": "P-00150", "designation": "Senior Customer Services Representative - JAPAC-3", "department": "SUPERVISORY_ORGANIZATION-4-214"}, {"positionId": "P-00152", "designation": "Customer Service Representative", "department": "SUPERVISORY_ORGANIZATION-4-216"}, {"positionId": "P-00157", "designation": "Senior Customer Services Representative", "department": "Global_Support_Nordic_Group"}, {"positionId": "P-00167", "designation": "Senior Customer Services Representative", "department": "Global_Support_LATAM_supervisory"}, {"positionId": "P-00182", "designation": "Product Developer-3", "department": "Product_Development_supervisory"}, {"positionId": "P-00198", "designation": "Senior Customer Service Representative-2", "department": "Global_Support_Central_Southern_Europe_Group"}, {"positionId": "P-00205", "designation": "Director, Field Sales LATAM", "department": "Sales_supervisory"}, {"positionId": "P-00209", "designation": "Regional Sales Manager", "department": "Field_Sales_EMEA_supervisory"}, {"positionId": "P-00206", "designation": "Regional Sales Manager", "department": "Field_Sales_EMEA_supervisory"}, {"positionId": "P-00245", "designation": "Consultant", "department": "Consulting_Services_North_America_Group"}, {"positionId": "P-00252", "designation": "Consultant-3", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-00257", "designation": "Consultant", "department": "Consulting_Services_LATAM_Group"}, {"positionId": "P-00256", "designation": "Consultant-3", "department": "Consulting_Services_LATAM_Group"}, {"positionId": "P-00265", "designation": "Senior Consultant", "department": "Consulting_Services_LATAM_Group"}, {"positionId": "P-00269", "designation": "Customer Service Representative", "department": "SUPERVISORY_ORGANIZATION-4-215"}, {"positionId": "P-00279", "designation": "Senior Customer Services Representative", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00281", "designation": "Senior Customer Services Representative", "department": "Global_Support_USA_Group"}, {"positionId": "P-00292", "designation": "Customer Service Operations", "department": "Global_Support_Uk_Ireland_Group"}, {"positionId": "P-00294", "designation": "Customer Service Representative", "department": "Global_Support_LATAM_supervisory"}, {"positionId": "P-00296", "designation": "Administrative Assistant", "department": "Global_Support_Center_supervisory"}, {"positionId": "P-00297", "designation": "Operations Business Analyst", "department": "Global_Support_Center_supervisory"}, {"positionId": "P-00304", "designation": "Senior Consultant", "department": "Consulting_Services_North_America_Group"}, {"positionId": "P-00305", "designation": "Regional Sales Manager", "department": "Field_Sales_AP_supervisory"}, {"positionId": "P-00306", "designation": "Regional Sales Manager-2", "department": "Field_Sales_AP_supervisory"}, {"positionId": "P-00308", "designation": "Regional Sales Manager", "department": "Field_Sales_AP_supervisory"}, {"positionId": "P-00309", "designation": "Regional Sales Manager", "department": "Field_Sales_NA_supervisory"}, {"positionId": "P-00310", "designation": "Regional Sales Manager", "department": "Field_Sales_AP_supervisory"}, {"positionId": "P-00311", "designation": "Regional Sales Manager", "department": "Field_Sales_AP_supervisory"}, {"positionId": "P-00315", "designation": "Senior Site Engineer I", "department": "Property_Management_supervisory"}, {"positionId": "P-00316", "designation": "Manager, Global Support USA", "department": "Global_Support_NA_supervisory"}, {"positionId": "P-00320", "designation": "Customer Service Representative", "department": "Global_Support_USA_Group"}, {"positionId": "P-00322", "designation": "Customer Service Representative", "department": "Global_Support_Uk_Ireland_Group"}, {"positionId": "P-00323", "designation": "Customer Service Representative", "department": "Global_Support_Middle_East_Africa_Group"}, {"positionId": "P-00324", "designation": "Customer Service Representative", "department": "Global_Support_Uk_Ireland_Group"}, {"positionId": "P-00326", "designation": "Customer Service Representative", "department": "Global_Support_Nordic_Group"}, {"positionId": "P-00335", "designation": "Senior Customer Service Representative", "department": "Global_Support_Middle_East_Africa_Group"}, {"positionId": "P-00336", "designation": "Senior Customer Service Representative", "department": "Global_Support_Benelux_Group"}, {"positionId": "P-00337", "designation": "Senior Customer Service Representative", "department": "Global_Support_Uk_Ireland_Group"}, {"positionId": "P-00338", "designation": "Senior Customer Service Representative", "department": "Global_Support_Central_Southern_Europe_Group"}, {"positionId": "P-00346", "designation": "Product Manager", "department": "Product_Management_supervisory"}, {"positionId": "P-00350", "designation": "Product Developer", "department": "Product_Development_supervisory"}, {"positionId": "P-00353", "designation": "Customer Service Representative", "department": "Global_Support_Benelux_Group"}, {"positionId": "P-00360", "designation": "Administrative Assistant", "department": "Global_Support_NA_supervisory"}, {"positionId": "P-00374", "designation": "Customer Service Representative", "department": "SUPERVISORY_ORGANIZATION-4-217"}, {"positionId": "P-00375", "designation": "Senior Customer Services Representative", "department": "SUPERVISORY_ORGANIZATION-4-217"}, {"positionId": "P-00378", "designation": "Manager, Global Support Greater China", "department": "Global_Support_AP__supervisory"}, {"positionId": "P-00392", "designation": "Senior Customer Service Representative", "department": "Global_Support_USA_Group"}, {"positionId": "P-00404", "designation": "Shipping & Receiving Associate", "department": "SUPERVISORY_ORGANIZATION-4-219"}, {"positionId": "P-00408", "designation": "Senior Customer Services Representative", "department": "Consulting_Services_Asia/Pac_Group"}, {"positionId": "P-00413", "designation": "Administrative Assistant", "department": "Global_Support_Uk_Ireland_Group"}, {"positionId": "P-00426", "designation": "Customer Service Representative", "department": "SUPERVISORY_ORGANIZATION-10-222"}, {"positionId": "P-00451", "designation": "ZK Workday Time tracking Consultant-2", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00450", "designation": "ZK Workday Time tracking Consultant-3", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00449", "designation": "ZK Workday Time tracking Consultant-4", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00448", "designation": "ZK Workday Time tracking Consultant-5", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00447", "designation": "ZK Workday Time tracking Consultant-6", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00446", "designation": "ZK Workday Time tracking Consultant-7", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00445", "designation": "ZK Workday Time tracking Consultant-8", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00444", "designation": "ZK Workday Time tracking Consultant-9", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00443", "designation": "ZK Workday Time tracking Consultant-10", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00442", "designation": "ZK Workday Time tracking Consultant-11", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00441", "designation": "ZK Workday Time tracking Consultant-12", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00440", "designation": "ZK Workday Time tracking Consultant-13", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00439", "designation": "ZK Workday Time tracking Consultant-14", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00438", "designation": "ZK Workday Time tracking Consultant-15", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00437", "designation": "ZK Workday Time tracking Consultant-16", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00436", "designation": "ZK Workday Time tracking Consultant-17", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00435", "designation": "ZK Workday Time tracking Consultant-18", "department": "SUPERVISORY_ORGANIZATION-6-225"}, {"positionId": "P-00462", "designation": "IT System Admin-1", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00476", "designation": "Software Engineer-2", "department": "Global_Modern_Services_supervisory"}, {"positionId": "P-00474", "designation": "Software Engineer-4", "department": "Global_Modern_Services_supervisory"}, {"positionId": "P-00472", "designation": "Software Engineer-6", "department": "Global_Modern_Services_supervisory"}, {"positionId": "P-00523", "designation": "Project Lead-10", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00519", "designation": "Project Lead-14", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00517", "designation": "Project Lead-16", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00516", "designation": "Project Lead-17", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00542", "designation": "Sales Lead-1", "department": "Purchasing_supervisory"}, {"positionId": "P-00541", "designation": "Sales Lead-2", "department": "Purchasing_supervisory"}, {"positionId": "P-00540", "designation": "Sales Lead-3", "department": "Purchasing_supervisory"}, {"positionId": "P-00539", "designation": "Sales Lead-4", "department": "Purchasing_supervisory"}, {"positionId": "P-00538", "designation": "Sales Lead-5", "department": "Purchasing_supervisory"}, {"positionId": "P-00537", "designation": "Sales Lead-6", "department": "Purchasing_supervisory"}, {"positionId": "P-00536", "designation": "Sales Lead-7", "department": "Purchasing_supervisory"}, {"positionId": "P-00535", "designation": "Sales Lead-8", "department": "Purchasing_supervisory"}, {"positionId": "P-00534", "designation": "Sales Lead-9", "department": "Purchasing_supervisory"}, {"positionId": "P-00533", "designation": "Sales Lead-10", "department": "Purchasing_supervisory"}, {"positionId": "P-00555", "designation": "Sales-Business Development", "department": "SUPERVISORY_ORGANIZATION-6-228"}, {"positionId": "P-00556", "designation": "Sales Account Executive", "department": "SUPERVISORY_ORGANIZATION-6-228"}, {"positionId": "P-00558", "designation": "HR", "department": "SUPERVISORY_ORGANIZATION-6-230"}, {"positionId": "P-00559", "designation": "Development Manager", "department": "SUPERVISORY_ORGANIZATION-6-231"}, {"positionId": "P-00560", "designation": "Development Engineer", "department": "SUPERVISORY_ORGANIZATION-6-230"}, {"positionId": "P-00570", "designation": "Consultant-3", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00566", "designation": "Consultant-7", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00565", "designation": "Consultant-8", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00573", "designation": "CEO", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00574", "designation": "Sales-Business Development", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00576", "designation": "IMPLEMENTATION SPECIALIST", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00583", "designation": "HR", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00586", "designation": "Graphic Designer", "department": "SUPERVISORY_ORGANIZATION-6-231"}, {"positionId": "P-00588", "designation": "IMPLEMENTATION LEAD", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00590", "designation": "Account Executive", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00600", "designation": "Test Engineer", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00610", "designation": "software Architect", "department": "SUPERVISORY_ORGANIZATION-6-232"}, {"positionId": "P-00611", "designation": "Software Developer", "department": "SUPERVISORY_ORGANIZATION-6-230"}, {"positionId": "P-00612", "designation": "Software Tester", "department": "SUPERVISORY_ORGANIZATION-6-230"}, {"positionId": "P-00615", "designation": "Technical Support", "department": "SUPERVISORY_ORGANIZATION-6-231"}, {"positionId": "P-00619", "designation": "Sales Account Executive-2", "department": "SUPERVISORY_ORGANIZATION-6-228"}, {"positionId": "P-00618", "designation": "Sales Account Executive-3", "department": "SUPERVISORY_ORGANIZATION-6-228"}, {"positionId": "P-00624", "designation": "Software Developer-1", "department": "SUPERVISORY_ORGANIZATION-6-240"}, {"positionId": "P-00623", "designation": "Software Developer-2", "department": "SUPERVISORY_ORGANIZATION-6-240"}, {"positionId": "P-00630", "designation": "ZK Workday Support Engineer-1", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00629", "designation": "ZK Workday Support Engineer-2", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00627", "designation": "ZK Workday Support Engineer-4", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00626", "designation": "ZK Workday Support Engineer-5", "department": "SUPERVISORY_ORGANIZATION-6-224"}, {"positionId": "P-00694", "designation": "Mango Workday Consultant-2", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00693", "designation": "Mango Workday Consultant-3", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00692", "designation": "Mango Workday Consultant-4", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00691", "designation": "Mango Workday Consultant-5", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00690", "designation": "Mango Workday Consultant-6", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00689", "designation": "Mango Workday Consultant-7", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00688", "designation": "Mango Workday Consultant-8", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00687", "designation": "Mango Workday Consultant-9", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00686", "designation": "Mango Workday Consultant-10", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00685", "designation": "Mango Workday Consultant-11", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00684", "designation": "Mango Workday Consultant-12", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00683", "designation": "Mango Workday Consultant-13", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00682", "designation": "Mango Workday Consultant-14", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00681", "designation": "Mango Workday Consultant-15", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00680", "designation": "Mango Workday Consultant-16", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00679", "designation": "Mango Workday Consultant-17", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00678", "designation": "Mango Workday Consultant-18", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00677", "designation": "Mango Workday Consultant-19", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00676", "designation": "Mango Workday Consultant-20", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00675", "designation": "Mango Workday Consultant-21", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00674", "designation": "Mango Workday Consultant-22", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00673", "designation": "Mango Workday Consultant-23", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00672", "designation": "Mango Workday Consultant-24", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00671", "designation": "Mango Workday Consultant-25", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00670", "designation": "Mango Workday Consultant-26", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00669", "designation": "Mango Workday Consultant-27", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00668", "designation": "Mango Workday Consultant-28", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00667", "designation": "Mango Workday Consultant-29", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00666", "designation": "Mango Workday Consultant-30", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00665", "designation": "Mango Workday Consultant-31", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00664", "designation": "Mango Workday Consultant-32", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00663", "designation": "Mango Workday Consultant-33", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00662", "designation": "Mango Workday Consultant-34", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00661", "designation": "Mango Workday Consultant-35", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00660", "designation": "Mango Workday Consultant-36", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00659", "designation": "Mango Workday Consultant-37", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00658", "designation": "Mango Workday Consultant-38", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00657", "designation": "Mango Workday Consultant-39", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00656", "designation": "Mango Workday Consultant-40", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00655", "designation": "Mango Workday Consultant-41", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00654", "designation": "Mango Workday Consultant-42", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00653", "designation": "Mango Workday Consultant-43", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00652", "designation": "Mango Workday Consultant-44", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00651", "designation": "Mango Workday Consultant-45", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00650", "designation": "Mango Workday Consultant-46", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00649", "designation": "Mango Workday Consultant-47", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00648", "designation": "Mango Workday Consultant-48", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00647", "designation": "Mango Workday Consultant-49", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00646", "designation": "Mango Workday Consultant-50", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-00712", "designation": "Mango Workday project Manager-14", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-00709", "designation": "Mango Workday project Manager-17", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-00779", "designation": "Executive Assistant, technical Operations-1", "department": "Operations_supervisory"}, {"positionId": "P-00778", "designation": "Executive Assistant, technical Operations-2", "department": "Operations_supervisory"}, {"positionId": "P-00777", "designation": "Executive Assistant, technical Operations-3", "department": "Operations_supervisory"}, {"positionId": "P-00776", "designation": "Executive Assistant, technical Operations-4", "department": "Operations_supervisory"}, {"positionId": "P-00775", "designation": "Executive Assistant, technical Operations-5", "department": "Operations_supervisory"}, {"positionId": "P-00774", "designation": "Executive Assistant, technical Operations-6", "department": "Operations_supervisory"}, {"positionId": "P-00784", "designation": "Workday Consultant-3", "department": "SUPERVISORY_ORGANIZATION-6-227"}, {"positionId": "P-00783", "designation": "Workday Consultant-4", "department": "SUPERVISORY_ORGANIZATION-6-227"}, {"positionId": "P-00782", "designation": "Workday Consultant-5", "department": "SUPERVISORY_ORGANIZATION-6-227"}, {"positionId": "P-00791", "designation": "ZK Contractor-1", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00790", "designation": "ZK Contractor-2", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00789", "designation": "ZK Contractor-3", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00788", "designation": "ZK Contractor-4", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00787", "designation": "ZK Contractor-5", "department": "SUPERVISORY_ORGANIZATION-6-226"}, {"positionId": "P-00792", "designation": "Staff Payroll Specialist", "department": "IT_Services_supervisory"}, {"positionId": "P-00793", "designation": "Implementation Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-227"}, {"positionId": "P-00800", "designation": "Workday Consultant", "department": "Human_Resources_supervisory"}, {"positionId": "P-00809", "designation": "EVP-2", "department": "SUPERVISORY_ORGANIZATION-6-303"}, {"positionId": "P-00811", "designation": "Manager", "department": "SUPERVISORY_ORGANIZATION-6-303"}, {"positionId": "P-00815", "designation": "WLI HRIS Specialist-1", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-00814", "designation": "WLI HRIS Specialist-2", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-00817", "designation": "WLI HRIS Analyst-1", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-00816", "designation": "WLI HRIS Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-00818", "designation": "WLI Benefits Analyst 1", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-00849", "designation": "Workday Consultant", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-00909", "designation": "Development Engineer", "department": "HR_Services_supervisory"}, {"positionId": "P-00910", "designation": "Director, Research & Development", "department": "HR_Services_supervisory"}, {"positionId": "P-00912", "designation": "Netra-1", "department": "SUPERVISORY_ORGANIZATION-6-355"}, {"positionId": "P-00915", "designation": "zktk Test-1", "department": "SUPERVISORY_ORGANIZATION-6-355"}, {"positionId": "P-00914", "designation": "zktk Test-2", "department": "SUPERVISORY_ORGANIZATION-6-355"}, {"positionId": "P-00913", "designation": "zktk Test-3", "department": "SUPERVISORY_ORGANIZATION-6-355"}, {"positionId": "P-00923", "designation": "HRTeam-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-00948", "designation": "Developer Edited by <PERSON>", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-00998", "designation": "Development Engineer", "department": "Field_Sales_NA_supervisory"}, {"positionId": "P-01019", "designation": "MK SNOW CHIEF TECHNOLOGY OCIER", "department": "SUPERVISORY_ORGANIZATION-6-404"}, {"positionId": "P-01018", "designation": "MK SNOW CHIF HUMAN RESOURCE OFFOCIER-2", "department": "SUPERVISORY_ORGANIZATION-6-404"}, {"positionId": "P-01020", "designation": "MK SNOW CHIF FINANCE OFFICER", "department": "SUPERVISORY_ORGANIZATION-6-404"}, {"positionId": "P-01030", "designation": "MK SNOW APPLICATION DEVELOPMENT MANAGER", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01031", "designation": "Workday Integration Consultant - UPWORK", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01033", "designation": "Workday Consultant-5", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01043", "designation": "Workday Integration Consultant - UPWORK", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01042", "designation": "Workday Integration Consultant - UPWORK", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01041", "designation": "Workday Integration Consultant - UPWORK", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01040", "designation": "Workday Integration Consultant - UPWORK", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01039", "designation": "Workday Integration Consultant - UPWORK", "department": "SUPERVISORY_ORGANIZATION-6-407"}, {"positionId": "P-01056", "designation": "WLI Support Engineer-1", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01055", "designation": "WLI Support Engineer-2", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01054", "designation": "WLI Support Engineer-3", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01053", "designation": "WLI Support Engineer-4", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01052", "designation": "WLI Support Engineer-5", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01057", "designation": "WLI Team Lead", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01058", "designation": "WLI Manager", "department": "SUPERVISORY_ORGANIZATION-6-286"}, {"positionId": "P-01063", "designation": "WLI Support Engineer-1", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01062", "designation": "WLI Support Engineer-2", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01061", "designation": "WLI Support Engineer-3", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01060", "designation": "WLI Support Engineer-4", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01059", "designation": "WLI Support Engineer-5", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01064", "designation": "WLI Supervisor", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01065", "designation": "WLI", "department": "SUPERVISORY_ORGANIZATION-6-288"}, {"positionId": "P-01071", "designation": "Snow Developer", "department": "SUPERVISORY_ORGANIZATION-6-403"}, {"positionId": "P-01070", "designation": "Snow Developer", "department": "SUPERVISORY_ORGANIZATION-6-403"}, {"positionId": "P-01073", "designation": "Workday Business consultatant", "department": "SUPERVISORY_ORGANIZATION-6-403"}, {"positionId": "P-01074", "designation": "Corporate Strategy Manager/Sr Manager", "department": "SUPERVISORY_ORGANIZATION-6-403"}, {"positionId": "P-01148", "designation": "DevopsEnginner-27", "department": "Global_Modern_Services_supervisory"}, {"positionId": "P-01176", "designation": "NK Excutive Vice President", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01177", "designation": "NK Chief Excutive Officer", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01178", "designation": "NK Chief EAxcitive  officer", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01181", "designation": "NK Workday manager", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01182", "designation": "NK Workday Manager", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01190", "designation": "Test Job Posting", "department": "SUPERVISORY_ORGANIZATION-6-253"}, {"positionId": "P-01191", "designation": "NK Vice President-2", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01193", "designation": "NK Contractor", "department": "SUPERVISORY_ORGANIZATION-6-443"}, {"positionId": "P-01194", "designation": "NK Chief Executive Officer-2", "department": "SUPERVISORY_ORGANIZATION-6-444"}, {"positionId": "P-01207", "designation": "Test 1", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01304", "designation": "Mobileadmin-8", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-01312", "designation": "<PERSON>real Chief Executive Officer", "department": "SUPERVISORY_ORGANIZATION-6-473"}, {"positionId": "P-01313", "designation": "Loreal Executive Vice President", "department": "SUPERVISORY_ORGANIZATION-6-474"}, {"positionId": "P-01314", "designation": "Loreal Vice President", "department": "SUPERVISORY_ORGANIZATION-6-474"}, {"positionId": "P-01315", "designation": "Loreal Director", "department": "SUPERVISORY_ORGANIZATION-6-474"}, {"positionId": "P-01316", "designation": "Loreal Manager", "department": "SUPERVISORY_ORGANIZATION-6-474"}, {"positionId": "P-01368", "designation": "Developer Dummy-5", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01379", "designation": "Workday API Consultant-4", "department": "SUPERVISORY_ORGANIZATION-6-533"}, {"positionId": "P-01378", "designation": "Workday API Consultant-5", "department": "SUPERVISORY_ORGANIZATION-6-533"}, {"positionId": "P-01377", "designation": "Workday API Consultant-6", "department": "SUPERVISORY_ORGANIZATION-6-533"}, {"positionId": "P-01375", "designation": "Workday API Consultant-8", "department": "SUPERVISORY_ORGANIZATION-6-533"}, {"positionId": "P-01384", "designation": "Analyst-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01445", "designation": "Analyst-18", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01444", "designation": "Analyst-19", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01561", "designation": "Product Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01560", "designation": "Product Manager-3", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01559", "designation": "Product Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01558", "designation": "Product Manager-5", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01557", "designation": "Product Manager-6", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01556", "designation": "Product Manager-7", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01555", "designation": "Product Manager-8", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01554", "designation": "Product Manager-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01553", "designation": "Product Manager-10", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01552", "designation": "Product Manager-11", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01551", "designation": "Product Manager-12", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01550", "designation": "Product Manager-13", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01549", "designation": "Product Manager-14", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01548", "designation": "Product Manager-15", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01547", "designation": "Product Manager-16", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01546", "designation": "Product Manager-17", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01545", "designation": "Product Manager-18", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01544", "designation": "Product Manager-19", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01543", "designation": "Product Manager-20", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01542", "designation": "Product Manager-21", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01541", "designation": "Product Manager-22", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01540", "designation": "Product Manager-23", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01539", "designation": "Product Manager-24", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01538", "designation": "Product Manager-25", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01537", "designation": "Product Manager-26", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01536", "designation": "Product Manager-27", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01535", "designation": "Product Manager-28", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01534", "designation": "Product Manager-29", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01533", "designation": "Product Manager-30", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01532", "designation": "Product Manager-31", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01531", "designation": "Product Manager-32", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01530", "designation": "Product Manager-33", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01529", "designation": "Product Manager-34", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01528", "designation": "Product Manager-35", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01527", "designation": "Product Manager-36", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01526", "designation": "Product Manager-37", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01525", "designation": "Product Manager-38", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01524", "designation": "Product Manager-39", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01523", "designation": "Product Manager-40", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01522", "designation": "Product Manager-41", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01521", "designation": "Product Manager-42", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01520", "designation": "Product Manager-43", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01519", "designation": "Product Manager-44", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01518", "designation": "Product Manager-45", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01517", "designation": "Product Manager-46", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01516", "designation": "Product Manager-47", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01515", "designation": "Product Manager-48", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01514", "designation": "Product Manager-49", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01513", "designation": "Product Manager-50", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01512", "designation": "Product Manager-51", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01511", "designation": "Product Manager-52", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01510", "designation": "Product Manager-53", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01509", "designation": "Product Manager-54", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01508", "designation": "Product Manager-55", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01507", "designation": "Product Manager-56", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01506", "designation": "Product Manager-57", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01505", "designation": "Product Manager-58", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01504", "designation": "Product Manager-59", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01503", "designation": "Product Manager-60", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01502", "designation": "Product Manager-61", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01501", "designation": "Product Manager-62", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01500", "designation": "Product Manager-63", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01499", "designation": "Product Manager-64", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01498", "designation": "Product Manager-65", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01497", "designation": "Product Manager-66", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01496", "designation": "Product Manager-67", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01495", "designation": "Product Manager-68", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01494", "designation": "Product Manager-69", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01493", "designation": "Product Manager-70", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01492", "designation": "Product Manager-71", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01491", "designation": "Product Manager-72", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01490", "designation": "Product Manager-73", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01489", "designation": "Product Manager-74", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01488", "designation": "Product Manager-75", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01487", "designation": "Product Manager-76", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01486", "designation": "Product Manager-77", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01485", "designation": "Product Manager-78", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01484", "designation": "Product Manager-79", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01483", "designation": "Product Manager-80", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01482", "designation": "Product Manager-81", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01481", "designation": "Product Manager-82", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01480", "designation": "Product Manager-83", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01479", "designation": "Product Manager-84", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01478", "designation": "Product Manager-85", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01477", "designation": "Product Manager-86", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01476", "designation": "Product Manager-87", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01475", "designation": "Product Manager-88", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01474", "designation": "Product Manager-89", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01473", "designation": "Product Manager-90", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01472", "designation": "Product Manager-91", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01471", "designation": "Product Manager-92", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01470", "designation": "Product Manager-93", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01469", "designation": "Product Manager-94", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01468", "designation": "Product Manager-95", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01467", "designation": "Product Manager-96", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01466", "designation": "Product Manager-97", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01465", "designation": "Product Manager-98", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01464", "designation": "Product Manager-99", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01463", "designation": "Product Manager-100", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01572", "designation": "Customer Success Manager-1", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01571", "designation": "Customer Success Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01570", "designation": "Customer Success Manager-3", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01569", "designation": "Customer Success Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01568", "designation": "Customer Success Manager-5", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01567", "designation": "Customer Success Manager-6", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01566", "designation": "Customer Success Manager-7", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01565", "designation": "Customer Success Manager-8", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01564", "designation": "Customer Success Manager-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01563", "designation": "Customer Success Manager-10", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01612", "designation": "Assistant Product Manager-1", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01611", "designation": "Assistant Product Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01610", "designation": "Assistant Product Manager-3", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01609", "designation": "Assistant Product Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01608", "designation": "Assistant Product Manager-5", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01607", "designation": "Assistant Product Manager-6", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01606", "designation": "Assistant Product Manager-7", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01605", "designation": "Assistant Product Manager-8", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01604", "designation": "Assistant Product Manager-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01603", "designation": "Assistant Product Manager-10", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01602", "designation": "Assistant Product Manager-11", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01601", "designation": "Assistant Product Manager-12", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01600", "designation": "Assistant Product Manager-13", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01599", "designation": "Assistant Product Manager-14", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01598", "designation": "Assistant Product Manager-15", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01597", "designation": "Assistant Product Manager-16", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01596", "designation": "Assistant Product Manager-17", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01595", "designation": "Assistant Product Manager-18", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01594", "designation": "Assistant Product Manager-19", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01593", "designation": "Assistant Product Manager-20", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01592", "designation": "Assistant Product Manager-21", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01591", "designation": "Assistant Product Manager-22", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01590", "designation": "Assistant Product Manager-23", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01589", "designation": "Assistant Product Manager-24", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01588", "designation": "Assistant Product Manager-25", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01587", "designation": "Assistant Product Manager-26", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01586", "designation": "Assistant Product Manager-27", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01585", "designation": "Assistant Product Manager-28", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01584", "designation": "Assistant Product Manager-29", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01583", "designation": "Assistant Product Manager-30", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01582", "designation": "Assistant Product Manager-31", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01581", "designation": "Assistant Product Manager-32", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01580", "designation": "Assistant Product Manager-33", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01579", "designation": "Assistant Product Manager-34", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01578", "designation": "Assistant Product Manager-35", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01577", "designation": "Assistant Product Manager-36", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01576", "designation": "Assistant Product Manager-37", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01575", "designation": "Assistant Product Manager-38", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01574", "designation": "Assistant Product Manager-39", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01573", "designation": "Assistant Product Manager-40", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01614", "designation": "Consultant-1", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01634", "designation": "Senior Data Analyst-1", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01633", "designation": "Senior Data Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01631", "designation": "Senior Data Analyst-4", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01630", "designation": "Senior Data Analyst-5", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01629", "designation": "Senior Data Analyst-6", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01628", "designation": "Senior Data Analyst-7", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01627", "designation": "Senior Data Analyst-8", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01626", "designation": "Senior Data Analyst-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01625", "designation": "Senior Data Analyst-10", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01624", "designation": "Senior Data Analyst-11", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01623", "designation": "Senior Data Analyst-12", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01622", "designation": "Senior Data Analyst-13", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01621", "designation": "Senior Data Analyst-14", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01619", "designation": "Senior Data Analyst-16", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01618", "designation": "Senior Data Analyst-17", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01617", "designation": "Senior Data Analyst-18", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01616", "designation": "Senior Data Analyst-19", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01615", "designation": "Senior Data Analyst-20", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01654", "designation": "Data Scientist-1", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01653", "designation": "Data Scientist-2", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01652", "designation": "Data Scientist-3", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01651", "designation": "Data Scientist-4", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01650", "designation": "Data Scientist-5", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01649", "designation": "Data Scientist-6", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01648", "designation": "Data Scientist-7", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01647", "designation": "Data Scientist-8", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01646", "designation": "Data Scientist-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01645", "designation": "Data Scientist-10", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01644", "designation": "Data Scientist-11", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01643", "designation": "Data Scientist-12", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01642", "designation": "Data Scientist-13", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01641", "designation": "Data Scientist-14", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01640", "designation": "Data Scientist-15", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01639", "designation": "Data Scientist-16", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01638", "designation": "Data Scientist-17", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01637", "designation": "Data Scientist-18", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01636", "designation": "Data Scientist-19", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01635", "designation": "Data Scientist-20", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01663", "designation": "Scrum Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01662", "designation": "Scrum Manager-3", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01661", "designation": "Scrum Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01660", "designation": "Scrum Manager-5", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01659", "designation": "Scrum Manager-6", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01658", "designation": "Scrum Manager-7", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01657", "designation": "Scrum Manager-8", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01656", "designation": "Scrum Manager-9", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01687", "designation": "Techincian-3", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01686", "designation": "Techincian-4", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01685", "designation": "Techincian-5", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01684", "designation": "Techincian-6", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01683", "designation": "Techincian-7", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01682", "designation": "Techincian-8", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01681", "designation": "Techincian-9", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01679", "designation": "Techincian-11", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01678", "designation": "Techincian-12", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01677", "designation": "Techincian-13", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01675", "designation": "Techincian-15", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01674", "designation": "Techincian-16", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01673", "designation": "Techincian-17", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01672", "designation": "Techincian-18", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01670", "designation": "Techincian-20", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01667", "designation": "Techincian-23", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01666", "designation": "Techincian-24", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01690", "designation": "Product Analyst", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-01693", "designation": "Workday Solution Architect", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01713", "designation": "Workday Solution Artifact-1", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01712", "designation": "Workday Solution Artifact-2", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01711", "designation": "Workday Solution Artifact-3", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01710", "designation": "Workday Solution Artifact-4", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01709", "designation": "Workday Solution Artifact-5", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01708", "designation": "Workday Solution Artifact-6", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01707", "designation": "Workday Solution Artifact-7", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01706", "designation": "Workday Solution Artifact-8", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01705", "designation": "Workday Solution Artifact-9", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01704", "designation": "Workday Solution Artifact-10", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01719", "designation": "Junior Consultant", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-01718", "designation": "Junior Consultant", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-01717", "designation": "Junior Consultant", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-01716", "designation": "Junior Consultant", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-01720", "designation": "Administrative Assistant", "department": "HR_Services_supervisory"}, {"positionId": "P-01722", "designation": "Senior Customer Services Representative II", "department": "Global_Support_Nordic_Group"}, {"positionId": "P-01721", "designation": "Senior Customer Services Representative II", "department": "Global_Support_Nordic_Group"}, {"positionId": "P-01724", "designation": "Pre-Consultant", "department": "Consulting_Services_North_America_Group"}, {"positionId": "P-01723", "designation": "Pre-Consultant", "department": "Consulting_Services_North_America_Group"}, {"positionId": "P-01726", "designation": "Junior Web Content Manager", "department": "Marketing_Services_supervisory"}, {"positionId": "P-01725", "designation": "Junior Web Content Manager", "department": "Marketing_Services_supervisory"}, {"positionId": "P-01728", "designation": "Senior Program Manager", "department": "Program_Management_supervisory"}, {"positionId": "P-01733", "designation": "Junior Program Manager", "department": "Program_Management_supervisory"}, {"positionId": "P-01732", "designation": "Junior Program Manager", "department": "Program_Management_supervisory"}, {"positionId": "P-01731", "designation": "Junior Program Manager", "department": "Program_Management_supervisory"}, {"positionId": "P-01730", "designation": "Junior Program Manager", "department": "Program_Management_supervisory"}, {"positionId": "P-01729", "designation": "Junior Program Manager", "department": "Program_Management_supervisory"}, {"positionId": "P-01740", "designation": "Net2Apps IT Support-1", "department": "SUPERVISORY_ORGANIZATION-6-573"}, {"positionId": "P-01739", "designation": "Net2Apps IT Support-2", "department": "SUPERVISORY_ORGANIZATION-6-573"}, {"positionId": "P-01738", "designation": "Net2Apps IT Support-3", "department": "SUPERVISORY_ORGANIZATION-6-573"}, {"positionId": "P-01742", "designation": "Sr. Human Resource Technology Analyst", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-01743", "designation": "Senior Executive Vice President", "department": "SUPERVISORY_ORGANIZATION-6-577"}, {"positionId": "P-01750", "designation": "Power BI, BI Developer", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01751", "designation": "Mango Executive", "department": "SUPERVISORY_ORGANIZATION-6-333"}, {"positionId": "P-01753", "designation": "Analyst-1", "department": "SUPERVISORY_ORGANIZATION-6-635"}, {"positionId": "P-01752", "designation": "Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-635"}, {"positionId": "P-01762", "designation": "Integration Test Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01760", "designation": "Integration Test Analyst-4", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01759", "designation": "Integration Test Analyst-5", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01757", "designation": "Integration Test Analyst-7", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01756", "designation": "Integration Test Analyst-8", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01755", "designation": "Integration Test Analyst-9", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01782", "designation": "IT HelpDesk Specialist-2", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01779", "designation": "IT HelpDesk Specialist-5", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01778", "designation": "IT HelpDesk Specialist-6", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01777", "designation": "IT HelpDesk Specialist-7", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01776", "designation": "IT HelpDesk Specialist-8", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01775", "designation": "IT HelpDesk Specialist-9", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01774", "designation": "IT HelpDesk Specialist-10", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01773", "designation": "IT HelpDesk Specialist-11", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01772", "designation": "IT HelpDesk Specialist-12", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01771", "designation": "IT HelpDesk Specialist-13", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01770", "designation": "IT HelpDesk Specialist-14", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01769", "designation": "IT HelpDesk Specialist-15", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01768", "designation": "IT HelpDesk Specialist-16", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01767", "designation": "IT HelpDesk Specialist-17", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01766", "designation": "IT HelpDesk Specialist-18", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01765", "designation": "IT HelpDesk Specialist-19", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01764", "designation": "IT HelpDesk Specialist-20", "department": "IT_HelpDesk_supervisory"}, {"positionId": "P-01785", "designation": "HR Analyst-1", "department": "SUPERVISORY_ORGANIZATION-6-615"}, {"positionId": "P-01784", "designation": "HR Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-615"}, {"positionId": "P-01787", "designation": "HR Analyst - 28-1", "department": "SUPERVISORY_ORGANIZATION-6-626"}, {"positionId": "P-01786", "designation": "HR Analyst - 28-2", "department": "SUPERVISORY_ORGANIZATION-6-626"}, {"positionId": "P-01795", "designation": "Product Manager-3", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01794", "designation": "Product Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01793", "designation": "Product Manager-5", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01792", "designation": "Product Manager-6", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01791", "designation": "Product Manager-7", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01790", "designation": "Product Manager-8", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-01803", "designation": "Mango", "department": "SUPERVISORY_ORGANIZATION-6-339"}, {"positionId": "P-01805", "designation": "Modern Loop Workday API developer-1", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01806", "designation": "Test Analyst", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01808", "designation": "Mango Executive", "department": "SUPERVISORY_ORGANIZATION-6-337"}, {"positionId": "P-01813", "designation": "Service Advisor-1", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01812", "designation": "Service Advisor-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01811", "designation": "Service Advisor-3", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01810", "designation": "Service Advisor-4", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01816", "designation": "Technician-1", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01815", "designation": "Technician-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01814", "designation": "Technician-3", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01818", "designation": "Clock Technician-1", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01817", "designation": "Clock Technician-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01819", "designation": "Ultima Technician Level 2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01824", "designation": "[Test] MLoop Position 1-1", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01823", "designation": "[Test] MLoop Position 1-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01822", "designation": "[Test] MLoop Position 1-3", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01821", "designation": "[Test] MLoop Position 1-4", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-01825", "designation": "Mango Executive", "department": "SUPERVISORY_ORGANIZATION-6-336"}, {"positionId": "P-01835", "designation": "MLoop Test Q", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-01847", "designation": "Product Analyst", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-01848", "designation": "Product Analyst", "department": "SUPERVISORY_ORGANIZATION-6-337"}, {"positionId": "P-01853", "designation": "Modernloop  Sr. Technology Analyst-7", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-01852", "designation": "Modernloop  Sr. Technology Analyst-8", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-01851", "designation": "Modernloop  Sr. Technology Analyst-9", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-01850", "designation": "Modernloop  Sr. Technology Analyst-10", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02274", "designation": "Adex Tester", "department": "SUPERVISORY_ORGANIZATION-6-646"}, {"positionId": "P-02275", "designation": "<PERSON><PERSON>'s Best Friend", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02276", "designation": "Brittany's Assistant", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02278", "designation": "Sr. Associate Content Developer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02279", "designation": "test", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02280", "designation": "Test Job", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02281", "designation": "Test New", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02282", "designation": "<PERSON>", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02284", "designation": "Coordinator", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02283", "designation": "Coordinator", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02286", "designation": "VP of Product", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02287", "designation": "Test MyInterview", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02288", "designation": "SZ test requisition", "department": "SUPERVISORY_ORGANIZATION-6-543"}, {"positionId": "P-02293", "designation": "bdm Salaes Manager", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-02292", "designation": "bdm Salaes Manager", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-02291", "designation": "bdm Salaes Manager", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-02290", "designation": "bdm Salaes Manager", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-02289", "designation": "bdm Salaes Manager", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-02307", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02306", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02305", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02304", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02303", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02302", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02301", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02300", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02299", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02298", "designation": "Technical Integration Engineer", "department": "SUPERVISORY_ORGANIZATION-6-340"}, {"positionId": "P-02311", "designation": "Workday Integration Consultant-2", "department": "SUPERVISORY_ORGANIZATION-6-258"}, {"positionId": "P-02313", "designation": "Customer Success Manager", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02316", "designation": "Senior Developer Test", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02315", "designation": "Senior Developer Test", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02318", "designation": "<PERSON><PERSON> Finisher", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02317", "designation": "<PERSON><PERSON> Finisher", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02320", "designation": "Workday Admin", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02333", "designation": "Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02328", "designation": "Analyst-7", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02434", "designation": "Analyst-1", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02433", "designation": "Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02432", "designation": "Analyst-3", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02431", "designation": "Analyst-4", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02430", "designation": "Analyst-5", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02429", "designation": "Analyst-6", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02428", "designation": "Analyst-7", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02427", "designation": "Analyst-8", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02426", "designation": "Analyst-9", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02425", "designation": "Analyst-10", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02424", "designation": "Analyst-11", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02423", "designation": "Analyst-12", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02422", "designation": "Analyst-13", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02421", "designation": "Analyst-14", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02420", "designation": "Analyst-15", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02419", "designation": "Analyst-16", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02418", "designation": "Analyst-17", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02417", "designation": "Analyst-18", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02416", "designation": "Analyst-19", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02415", "designation": "Analyst-20", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02414", "designation": "Analyst-21", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02413", "designation": "Analyst-22", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02412", "designation": "Analyst-23", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02411", "designation": "Analyst-24", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02410", "designation": "Analyst-25", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02409", "designation": "Analyst-26", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02408", "designation": "Analyst-27", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02407", "designation": "Analyst-28", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02406", "designation": "Analyst-29", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02405", "designation": "Analyst-30", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02404", "designation": "Analyst-31", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02403", "designation": "Analyst-32", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02402", "designation": "Analyst-33", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02401", "designation": "Analyst-34", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02400", "designation": "Analyst-35", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02399", "designation": "Analyst-36", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02398", "designation": "Analyst-37", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02397", "designation": "Analyst-38", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02396", "designation": "Analyst-39", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02395", "designation": "Analyst-40", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02394", "designation": "Analyst-41", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02393", "designation": "Analyst-42", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02392", "designation": "Analyst-43", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02391", "designation": "Analyst-44", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02390", "designation": "Analyst-45", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02389", "designation": "Analyst-46", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02388", "designation": "Analyst-47", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02387", "designation": "Analyst-48", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02386", "designation": "Analyst-49", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02385", "designation": "Analyst-50", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02384", "designation": "Analyst-51", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02383", "designation": "Analyst-52", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02382", "designation": "Analyst-53", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02381", "designation": "Analyst-54", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02380", "designation": "Analyst-55", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02379", "designation": "Analyst-56", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02378", "designation": "Analyst-57", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02377", "designation": "Analyst-58", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02376", "designation": "Analyst-59", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02375", "designation": "Analyst-60", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02374", "designation": "Analyst-61", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02373", "designation": "Analyst-62", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02372", "designation": "Analyst-63", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02371", "designation": "Analyst-64", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02370", "designation": "Analyst-65", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02369", "designation": "Analyst-66", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02368", "designation": "Analyst-67", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02367", "designation": "Analyst-68", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02366", "designation": "Analyst-69", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02365", "designation": "Analyst-70", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02364", "designation": "Analyst-71", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02363", "designation": "Analyst-72", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02362", "designation": "Analyst-73", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02361", "designation": "Analyst-74", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02360", "designation": "Analyst-75", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02359", "designation": "Analyst-76", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02358", "designation": "Analyst-77", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02357", "designation": "Analyst-78", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02356", "designation": "Analyst-79", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02355", "designation": "Analyst-80", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02354", "designation": "Analyst-81", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02353", "designation": "Analyst-82", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02352", "designation": "Analyst-83", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02351", "designation": "Analyst-84", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02350", "designation": "Analyst-85", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02349", "designation": "Analyst-86", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02348", "designation": "Analyst-87", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02347", "designation": "Analyst-88", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02346", "designation": "Analyst-89", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02345", "designation": "Analyst-90", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02344", "designation": "Analyst-91", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02343", "designation": "Analyst-92", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02342", "designation": "Analyst-93", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02341", "designation": "Analyst-94", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02340", "designation": "Analyst-95", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02339", "designation": "Analyst-96", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02338", "designation": "Analyst-97", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02337", "designation": "Analyst-98", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02336", "designation": "Analyst-99", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02335", "designation": "Analyst-100", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02451", "designation": "Software Engineering Analyst - IT-1", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02450", "designation": "Software Engineering Analyst - IT-2", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02449", "designation": "Software Engineering Analyst - IT-3", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02448", "designation": "Software Engineering Analyst - IT-4", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02447", "designation": "Software Engineering Analyst - IT-5", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02446", "designation": "Software Engineering Analyst - IT-6", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02445", "designation": "Software Engineering Analyst - IT-7", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02444", "designation": "Software Engineering Analyst - IT-8", "department": "SUPERVISORY_ORGANIZATION-6-703"}, {"positionId": "P-02454", "designation": "Accounting Workday", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02453", "designation": "Accounting Workday", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02459", "designation": "Accountant", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02458", "designation": "Accountant", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02457", "designation": "Accountant", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02456", "designation": "Accountant", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02455", "designation": "Accountant", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02466", "designation": "Workable testing 1 (Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02465", "designation": "Workable testing 1 (Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02464", "designation": "Workable testing 1 (Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02463", "designation": "Workable testing 1 (Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02462", "designation": "Workable testing 1 (Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02471", "designation": "Workable testing 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02470", "designation": "Workable testing 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02469", "designation": "Workable testing 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02468", "designation": "Workable testing 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02467", "designation": "Workable testing 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02476", "designation": "Workable testing 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02475", "designation": "Workable testing 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02474", "designation": "Workable testing 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02473", "designation": "Workable testing 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02472", "designation": "Workable testing 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02478", "designation": "Workdays testing 4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02477", "designation": "Workdays testing 4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02483", "designation": "Workdays testing 5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02482", "designation": "Workdays testing 5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02481", "designation": "Workdays testing 5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02480", "designation": "Workdays testing 5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02479", "designation": "Workdays testing 5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02490", "designation": "Reservation Manager 2(Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02489", "designation": "Reservation Manager 2(Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02488", "designation": "Reservation Manager 2(Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02487", "designation": "Reservation Manager 2(Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02486", "designation": "Reservation Manager 2(Change name)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02495", "designation": "Reservation Supervisor", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02494", "designation": "Reservation Supervisor", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02493", "designation": "Reservation Supervisor", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02492", "designation": "Reservation Supervisor", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02491", "designation": "Reservation Supervisor", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02500", "designation": "Workdays testing 7", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02499", "designation": "Workdays testing 7", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02498", "designation": "Workdays testing 7", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02497", "designation": "Workdays testing 7", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02496", "designation": "Workdays testing 7", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02550", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02549", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02548", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02547", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02546", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02545", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02544", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02543", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02542", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02541", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02540", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02539", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02538", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02537", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02536", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02535", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02534", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02533", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02532", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02531", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02530", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02529", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02528", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02527", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02526", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02525", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02524", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02523", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02522", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02521", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02520", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02519", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02518", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02517", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02516", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02515", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02514", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02513", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02512", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02511", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02510", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02509", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02508", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02507", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02506", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02505", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02504", "designation": "Web Engineer", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02560", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02559", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02558", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02557", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02556", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02555", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02554", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02553", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02552", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02551", "designation": "Web Engineer II", "department": "Global_Support_Canada_Group"}, {"positionId": "P-02565", "designation": "Community Viewer (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02564", "designation": "Community Viewer (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02563", "designation": "Community Viewer (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02562", "designation": "Community Viewer (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02561", "designation": "Community Viewer (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02570", "designation": "Community viewer 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02569", "designation": "Community viewer 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02568", "designation": "Community viewer 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02567", "designation": "Community viewer 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02566", "designation": "Community viewer 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02591", "designation": "WD Workday API consultant", "department": "SUPERVISORY_ORGANIZATION-6-403"}, {"positionId": "P-02596", "designation": "Community viewer 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02595", "designation": "Community viewer 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02594", "designation": "Community viewer 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02593", "designation": "Community viewer 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02592", "designation": "Community viewer 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02606", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02605", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02604", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02603", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02602", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02601", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02600", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02599", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02598", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02597", "designation": "Test-on workday-rename 9", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02610", "designation": "<PERSON>. Software Engineer-7", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02609", "designation": "<PERSON>. Software Engineer-8", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02608", "designation": "<PERSON>. Software Engineer-9", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02607", "designation": "Sr. Software Engineer-10", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02623", "designation": "Workable testing- SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02622", "designation": "Workable testing- SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02621", "designation": "Workable testing- SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02620", "designation": "Workable testing- SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02619", "designation": "Workable testing- SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02630", "designation": "Workable testing- SONY 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02629", "designation": "Workable testing- SONY 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02628", "designation": "Workable testing- SONY 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02627", "designation": "Workable testing- SONY 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02626", "designation": "Workable testing- SONY 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02632", "designation": "Sony Updated by Dan", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02631", "designation": "Sony Updated by Dan", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02637", "designation": "Accountant-SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02636", "designation": "Accountant-SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02635", "designation": "Accountant-SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02634", "designation": "Accountant-SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02633", "designation": "Accountant-SONY", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02642", "designation": "Account Manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02641", "designation": "Account Manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02640", "designation": "Account Manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02639", "designation": "Account Manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02638", "designation": "Account Manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02647", "designation": "Account Manager 03", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02646", "designation": "Account Manager 03", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02645", "designation": "Account Manager 03", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02644", "designation": "Account Manager 03", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02643", "designation": "Account Manager 03", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02657", "designation": "Account Manager 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02656", "designation": "Account Manager 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02655", "designation": "Account Manager 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02654", "designation": "Account Manager 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02653", "designation": "Account Manager 2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02662", "designation": "Account Manager 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02661", "designation": "Account Manager 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02660", "designation": "Account Manager 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02659", "designation": "Account Manager 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02658", "designation": "Account Manager 04", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02677", "designation": "Account Manager 05-1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02676", "designation": "Test MyInterview", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02675", "designation": "Account Manager 05-3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02674", "designation": "Account Manager 05-4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02673", "designation": "New MyInterview test", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02682", "designation": "Account Manager 06 (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02681", "designation": "Account Manager 06 (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02680", "designation": "Account Manager 06 (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02679", "designation": "Account Manager 06 (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02678", "designation": "Account Manager 06 (Change)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02687", "designation": "Account Manager (<PERSON><PERSON> Cheese)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02686", "designation": "Account Manager (<PERSON><PERSON> Cheese)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02685", "designation": "Account Manager (<PERSON><PERSON> Cheese)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02684", "designation": "Account Manager (<PERSON><PERSON> Cheese)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02683", "designation": "Account Manager (<PERSON><PERSON> Cheese)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02693", "designation": "Account Manager 08", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02692", "designation": "Account Manager 08", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02691", "designation": "Account Manager 08", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02690", "designation": "Account Manager 08", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02689", "designation": "Account Manager 08", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02699", "designation": "Account Manager 09-1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02698", "designation": "Account Manager 09-2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02697", "designation": "Account Manager 09-3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02696", "designation": "Account Manager 09-4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02695", "designation": "Account Manager 09-5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02704", "designation": "Account Manager 10-1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02703", "designation": "Account Manager 10-2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02702", "designation": "Account Manager 10-3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02701", "designation": "Account Manager 10-4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02700", "designation": "Account Manager 10-5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02709", "designation": "Test 1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02708", "designation": "Test 1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02707", "designation": "Test 1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02706", "designation": "Test 1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02705", "designation": "Test 1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02714", "designation": "Test 2-1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02713", "designation": "Test 2-2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02712", "designation": "Test 2-3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02711", "designation": "Test 2-4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02710", "designation": "Test 2-5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02719", "designation": "Test 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02718", "designation": "Test 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02717", "designation": "Test 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02716", "designation": "Test 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02715", "designation": "Test 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02724", "designation": "Test 4-1", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02723", "designation": "Test 4-2", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02722", "designation": "Test 4-3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02721", "designation": "Test 4-4", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02720", "designation": "Test 4-5", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02745", "designation": "MANAGER TEST-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02744", "designation": "MANAGER TEST-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02743", "designation": "MANAGER TEST-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02742", "designation": "MANAGER TEST-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02741", "designation": "MANAGER TEST-5", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02739", "designation": "MANAGER TEST-7", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02738", "designation": "MANAGER TEST-8", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02737", "designation": "MANAGER TEST-9", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02750", "designation": "POSITION TEST-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02749", "designation": "POSITION TEST-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02748", "designation": "POSITION TEST-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02747", "designation": "POSITION TEST-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02759", "designation": "DevOps Engineer-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02758", "designation": "DevOps Engineer-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02757", "designation": "DevOps Engineer-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02756", "designation": "DevOps Engineer-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02755", "designation": "DevOps Engineer-5", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02754", "designation": "DevOps Engineer-6", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02753", "designation": "DevOps Engineer-7", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02752", "designation": "DevOps Engineer-8", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02751", "designation": "DevOps Engineer-9", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02763", "designation": "TEST POSITION -1-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02762", "designation": "TEST POSITION -1-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02761", "designation": "TEST POSITION -1-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02774", "designation": "PM Intern-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02773", "designation": "PM Intern-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02772", "designation": "PM Intern-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02771", "designation": "PM Intern-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02770", "designation": "PM Intern-5", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02769", "designation": "PM Intern-6", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02768", "designation": "PM Intern-7", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02767", "designation": "PM Intern-8", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02766", "designation": "PM Intern-9", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02765", "designation": "PM Intern-10", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02779", "designation": "Dev Interns-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02778", "designation": "Dev Interns-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02777", "designation": "Dev Interns-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02784", "designation": "Account Manager-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02783", "designation": "Account Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02781", "designation": "Account Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-02805", "designation": ".NET Developer", "department": "SUPERVISORY_ORGANIZATION-6-623"}, {"positionId": "P-02807", "designation": "Software Engineer (Dansoft) Edited", "department": "SUPERVISORY_ORGANIZATION-6-623"}, {"positionId": "P-02808", "designation": "Java Developer", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-02809", "designation": ".NET Developer", "department": "SUPERVISORY_ORGANIZATION-6-623"}, {"positionId": "P-02814", "designation": "Accounting manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02813", "designation": "Accounting manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02812", "designation": "Accounting manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02811", "designation": "Accounting manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02810", "designation": "Accounting manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02819", "designation": "Accounting manager (Edited)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02818", "designation": "Accounting manager (Edited)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02817", "designation": "Accounting manager (Edited)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02816", "designation": "Accounting manager (Edited)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02815", "designation": "Accounting manager (Edited)", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02824", "designation": "Account Manager 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02823", "designation": "Account Manager 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02822", "designation": "Account Manager 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02821", "designation": "Account Manager 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02820", "designation": "Account Manager 3", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02874", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02873", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02872", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02871", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02870", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02869", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02868", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02867", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02866", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02865", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02864", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02863", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02862", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02861", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02860", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02859", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02858", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02857", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02856", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02855", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02854", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02853", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02852", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02851", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02850", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02849", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02848", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02847", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02846", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02845", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02844", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02843", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02842", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02841", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02840", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02839", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02838", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02837", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02836", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02835", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02834", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02833", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02832", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02831", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02830", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02829", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02828", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02827", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02826", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02825", "designation": "Test QA Job 01", "department": "Consulting_Services_EMEA_Group"}, {"positionId": "P-02877", "designation": "Frontend Web Developer", "department": "SUPERVISORY_ORGANIZATION-6-617"}, {"positionId": "P-02878", "designation": "IT Consultant (Linux)", "department": "SUPERVISORY_ORGANIZATION-6-617"}, {"positionId": "P-02879", "designation": "Web Developer", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02882", "designation": "Chief of Cheese", "department": "SUPERVISORY_ORGANIZATION-6-625"}, {"positionId": "P-02892", "designation": "Workday Integration Developer-1", "department": "SUPERVISORY_ORGANIZATION-6-716"}, {"positionId": "P-02891", "designation": "Workday Integration Developer-2", "department": "SUPERVISORY_ORGANIZATION-6-716"}, {"positionId": "P-02890", "designation": "Workday Integration Developer-3", "department": "SUPERVISORY_ORGANIZATION-6-716"}, {"positionId": "P-02889", "designation": "Workday Integration Developer-4", "department": "SUPERVISORY_ORGANIZATION-6-716"}, {"positionId": "P-02888", "designation": "Workday Integration Developer-5", "department": "SUPERVISORY_ORGANIZATION-6-716"}, {"positionId": "P-02887", "designation": "Workday Integration Developer-6", "department": "SUPERVISORY_ORGANIZATION-6-716"}, {"positionId": "P-02895", "designation": "Assistant Manager", "department": "SUPERVISORY_ORGANIZATION-6-629"}, {"positionId": "P-02896", "designation": "Senior Product Manager", "department": "SUPERVISORY_ORGANIZATION-6-645"}, {"positionId": "P-02901", "designation": "WLI Workday Supporting New", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-02900", "designation": "WLI Workday Supporting New", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-02899", "designation": "WLI Workday Supporting New", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-02898", "designation": "WLI Workday Supporting New", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-02897", "designation": "WLI Workday Supporting New", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-02931", "designation": "SAPConsultant-1", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02916", "designation": "SAPConsultant-16", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02911", "designation": "SAPConsultant-21", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02910", "designation": "SAPConsultant-22", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02907", "designation": "SAPConsultant-25", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02906", "designation": "SAPConsultant-26", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02904", "designation": "SAPConsultant-28", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02903", "designation": "SAPConsultant-29", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-02936", "designation": "Software Engineer (SSE)", "department": "SUPERVISORY_ORGANIZATION-6-283"}, {"positionId": "P-02935", "designation": "Software Engineer (SSE)", "department": "SUPERVISORY_ORGANIZATION-6-283"}, {"positionId": "P-02934", "designation": "Software Engineer (SSE)", "department": "SUPERVISORY_ORGANIZATION-6-283"}, {"positionId": "P-02933", "designation": "Software Engineer (SSE)", "department": "SUPERVISORY_ORGANIZATION-6-283"}, {"positionId": "P-02932", "designation": "Software Engineer (SSE)", "department": "SUPERVISORY_ORGANIZATION-6-283"}, {"positionId": "P-02937", "designation": "Software Engineer II", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-03037", "designation": "Software Developer Hiring 2025-1", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03036", "designation": "Software Developer Hiring 2025-2", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03035", "designation": "Software Developer Hiring 2025-3", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03034", "designation": "Software Developer Hiring 2025-4", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03033", "designation": "Software Developer Hiring 2025-5", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03032", "designation": "Software Developer Hiring 2025-6", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03031", "designation": "Software Developer Hiring 2025-7", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03030", "designation": "Software Developer Hiring 2025-8", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03029", "designation": "Software Developer Hiring 2025-9", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03028", "designation": "Software Developer Hiring 2025-10", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03027", "designation": "Software Developer Hiring 2025-11", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03026", "designation": "Software Developer Hiring 2025-12", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03025", "designation": "Software Developer Hiring 2025-13", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03024", "designation": "Software Developer Hiring 2025-14", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03023", "designation": "Software Developer Hiring 2025-15", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03022", "designation": "Software Developer Hiring 2025-16", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03021", "designation": "Software Developer Hiring 2025-17", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03020", "designation": "Software Developer Hiring 2025-18", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03019", "designation": "Software Developer Hiring 2025-19", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03018", "designation": "Software Developer Hiring 2025-20", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03017", "designation": "Software Developer Hiring 2025-21", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03016", "designation": "Software Developer Hiring 2025-22", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03015", "designation": "Software Developer Hiring 2025-23", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03014", "designation": "Software Developer Hiring 2025-24", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03013", "designation": "Software Developer Hiring 2025-25", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03012", "designation": "Software Developer Hiring 2025-26", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03011", "designation": "Software Developer Hiring 2025-27", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03010", "designation": "Software Developer Hiring 2025-28", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03009", "designation": "Software Developer Hiring 2025-29", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03008", "designation": "Software Developer Hiring 2025-30", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03007", "designation": "Software Developer Hiring 2025-31", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03006", "designation": "Software Developer Hiring 2025-32", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03005", "designation": "Software Developer Hiring 2025-33", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03004", "designation": "Software Developer Hiring 2025-34", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03003", "designation": "Software Developer Hiring 2025-35", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03002", "designation": "Software Developer Hiring 2025-36", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03001", "designation": "Software Developer Hiring 2025-37", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03000", "designation": "Software Developer Hiring 2025-38", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02999", "designation": "Software Developer Hiring 2025-39", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02998", "designation": "Software Developer Hiring 2025-40", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02997", "designation": "Software Developer Hiring 2025-41", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02996", "designation": "Software Developer Hiring 2025-42", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02995", "designation": "Software Developer Hiring 2025-43", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02994", "designation": "Software Developer Hiring 2025-44", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02993", "designation": "Software Developer Hiring 2025-45", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02992", "designation": "Software Developer Hiring 2025-46", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02991", "designation": "Software Developer Hiring 2025-47", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02990", "designation": "Software Developer Hiring 2025-48", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02989", "designation": "Software Developer Hiring 2025-49", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02988", "designation": "Software Developer Hiring 2025-50", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02987", "designation": "Software Developer Hiring 2025-51", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02986", "designation": "Software Developer Hiring 2025-52", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02985", "designation": "Software Developer Hiring 2025-53", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02984", "designation": "Software Developer Hiring 2025-54", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02983", "designation": "Software Developer Hiring 2025-55", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02982", "designation": "Software Developer Hiring 2025-56", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02981", "designation": "Software Developer Hiring 2025-57", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02980", "designation": "Software Developer Hiring 2025-58", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02979", "designation": "Software Developer Hiring 2025-59", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02978", "designation": "Software Developer Hiring 2025-60", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02977", "designation": "Software Developer Hiring 2025-61", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02976", "designation": "Software Developer Hiring 2025-62", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02975", "designation": "Software Developer Hiring 2025-63", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02974", "designation": "Software Developer Hiring 2025-64", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02973", "designation": "Software Developer Hiring 2025-65", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02972", "designation": "Software Developer Hiring 2025-66", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02971", "designation": "Software Developer Hiring 2025-67", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02970", "designation": "Software Developer Hiring 2025-68", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02969", "designation": "Software Developer Hiring 2025-69", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02968", "designation": "Software Developer Hiring 2025-70", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02967", "designation": "Software Developer Hiring 2025-71", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02966", "designation": "Software Developer Hiring 2025-72", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02965", "designation": "Software Developer Hiring 2025-73", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02964", "designation": "Software Developer Hiring 2025-74", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02963", "designation": "Software Developer Hiring 2025-75", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02962", "designation": "Software Developer Hiring 2025-76", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02961", "designation": "Software Developer Hiring 2025-77", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02960", "designation": "Software Developer Hiring 2025-78", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02959", "designation": "Software Developer Hiring 2025-79", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02958", "designation": "Software Developer Hiring 2025-80", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02957", "designation": "Software Developer Hiring 2025-81", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02956", "designation": "Software Developer Hiring 2025-82", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02955", "designation": "Software Developer Hiring 2025-83", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02954", "designation": "Software Developer Hiring 2025-84", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02953", "designation": "Software Developer Hiring 2025-85", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02952", "designation": "Software Developer Hiring 2025-86", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02951", "designation": "Software Developer Hiring 2025-87", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02950", "designation": "Software Developer Hiring 2025-88", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02949", "designation": "Software Developer Hiring 2025-89", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02948", "designation": "Software Developer Hiring 2025-90", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02947", "designation": "Software Developer Hiring 2025-91", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02946", "designation": "Software Developer Hiring 2025-92", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02945", "designation": "Software Developer Hiring 2025-93", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02944", "designation": "Software Developer Hiring 2025-94", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02943", "designation": "Software Developer Hiring 2025-95", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02942", "designation": "Software Developer Hiring 2025-96", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02941", "designation": "Software Developer Hiring 2025-97", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02940", "designation": "Software Developer Hiring 2025-98", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02939", "designation": "Software Developer Hiring 2025-99", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-02938", "designation": "Software Developer Hiring 2025-100", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03237", "designation": "Software Developer Hiring 2025-101", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03236", "designation": "Software Developer Hiring 2025-102", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03235", "designation": "Software Developer Hiring 2025-103", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03234", "designation": "Software Developer Hiring 2025-104", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03233", "designation": "Software Developer Hiring 2025-105", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03232", "designation": "Software Developer Hiring 2025-106", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03231", "designation": "Software Developer Hiring 2025-107", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03230", "designation": "Software Developer Hiring 2025-108", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03229", "designation": "Software Developer Hiring 2025-109", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03228", "designation": "Software Developer Hiring 2025-110", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03227", "designation": "Software Developer Hiring 2025-111", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03226", "designation": "Software Developer Hiring 2025-112", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03225", "designation": "Software Developer Hiring 2025-113", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03224", "designation": "Software Developer Hiring 2025-114", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03223", "designation": "Software Developer Hiring 2025-115", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03222", "designation": "Software Developer Hiring 2025-116", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03221", "designation": "Software Developer Hiring 2025-117", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03220", "designation": "Software Developer Hiring 2025-118", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03219", "designation": "Software Developer Hiring 2025-119", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03218", "designation": "Software Developer Hiring 2025-120", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03217", "designation": "Software Developer Hiring 2025-121", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03216", "designation": "Software Developer Hiring 2025-122", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03215", "designation": "Software Developer Hiring 2025-123", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03214", "designation": "Software Developer Hiring 2025-124", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03213", "designation": "Software Developer Hiring 2025-125", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03212", "designation": "Software Developer Hiring 2025-126", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03211", "designation": "Software Developer Hiring 2025-127", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03210", "designation": "Software Developer Hiring 2025-128", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03209", "designation": "Software Developer Hiring 2025-129", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03208", "designation": "Software Developer Hiring 2025-130", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03207", "designation": "Software Developer Hiring 2025-131", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03206", "designation": "Software Developer Hiring 2025-132", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03205", "designation": "Software Developer Hiring 2025-133", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03204", "designation": "Software Developer Hiring 2025-134", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03203", "designation": "Software Developer Hiring 2025-135", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03202", "designation": "Software Developer Hiring 2025-136", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03201", "designation": "Software Developer Hiring 2025-137", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03200", "designation": "Software Developer Hiring 2025-138", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03199", "designation": "Software Developer Hiring 2025-139", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03198", "designation": "Software Developer Hiring 2025-140", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03197", "designation": "Software Developer Hiring 2025-141", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03196", "designation": "Software Developer Hiring 2025-142", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03195", "designation": "Software Developer Hiring 2025-143", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03194", "designation": "Software Developer Hiring 2025-144", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03193", "designation": "Software Developer Hiring 2025-145", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03192", "designation": "Software Developer Hiring 2025-146", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03191", "designation": "Software Developer Hiring 2025-147", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03190", "designation": "Software Developer Hiring 2025-148", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03189", "designation": "Software Developer Hiring 2025-149", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03188", "designation": "Software Developer Hiring 2025-150", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03187", "designation": "Software Developer Hiring 2025-151", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03186", "designation": "Software Developer Hiring 2025-152", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03185", "designation": "Software Developer Hiring 2025-153", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03184", "designation": "Software Developer Hiring 2025-154", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03183", "designation": "Software Developer Hiring 2025-155", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03182", "designation": "Software Developer Hiring 2025-156", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03181", "designation": "Software Developer Hiring 2025-157", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03180", "designation": "Software Developer Hiring 2025-158", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03179", "designation": "Software Developer Hiring 2025-159", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03178", "designation": "Software Developer Hiring 2025-160", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03177", "designation": "Software Developer Hiring 2025-161", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03176", "designation": "Software Developer Hiring 2025-162", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03175", "designation": "Software Developer Hiring 2025-163", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03174", "designation": "Software Developer Hiring 2025-164", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03173", "designation": "Software Developer Hiring 2025-165", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03172", "designation": "Software Developer Hiring 2025-166", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03171", "designation": "Software Developer Hiring 2025-167", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03170", "designation": "Software Developer Hiring 2025-168", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03169", "designation": "Software Developer Hiring 2025-169", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03168", "designation": "Software Developer Hiring 2025-170", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03167", "designation": "Software Developer Hiring 2025-171", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03166", "designation": "Software Developer Hiring 2025-172", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03165", "designation": "Software Developer Hiring 2025-173", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03164", "designation": "Software Developer Hiring 2025-174", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03163", "designation": "Software Developer Hiring 2025-175", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03162", "designation": "Software Developer Hiring 2025-176", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03161", "designation": "Software Developer Hiring 2025-177", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03160", "designation": "Software Developer Hiring 2025-178", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03159", "designation": "Software Developer Hiring 2025-179", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03158", "designation": "Software Developer Hiring 2025-180", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03157", "designation": "Software Developer Hiring 2025-181", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03156", "designation": "Software Developer Hiring 2025-182", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03155", "designation": "Software Developer Hiring 2025-183", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03154", "designation": "Software Developer Hiring 2025-184", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03153", "designation": "Software Developer Hiring 2025-185", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03152", "designation": "Software Developer Hiring 2025-186", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03151", "designation": "Software Developer Hiring 2025-187", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03150", "designation": "Software Developer Hiring 2025-188", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03149", "designation": "Software Developer Hiring 2025-189", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03148", "designation": "Software Developer Hiring 2025-190", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03147", "designation": "Software Developer Hiring 2025-191", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03146", "designation": "Software Developer Hiring 2025-192", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03145", "designation": "Software Developer Hiring 2025-193", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03144", "designation": "Software Developer Hiring 2025-194", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03143", "designation": "Software Developer Hiring 2025-195", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03142", "designation": "Software Developer Hiring 2025-196", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03141", "designation": "Software Developer Hiring 2025-197", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03140", "designation": "Software Developer Hiring 2025-198", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03139", "designation": "Software Developer Hiring 2025-199", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03138", "designation": "Software Developer Hiring 2025-200", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03337", "designation": "Software Developer Hiring 2025-201", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03336", "designation": "Software Developer Hiring 2025-202", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03335", "designation": "Software Developer Hiring 2025-203", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03334", "designation": "Software Developer Hiring 2025-204", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03333", "designation": "Software Developer Hiring 2025-205", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03332", "designation": "Software Developer Hiring 2025-206", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03331", "designation": "Software Developer Hiring 2025-207", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03330", "designation": "Software Developer Hiring 2025-208", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03329", "designation": "Software Developer Hiring 2025-209", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03328", "designation": "Software Developer Hiring 2025-210", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03327", "designation": "Software Developer Hiring 2025-211", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03326", "designation": "Software Developer Hiring 2025-212", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03325", "designation": "Software Developer Hiring 2025-213", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03324", "designation": "Software Developer Hiring 2025-214", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03323", "designation": "Software Developer Hiring 2025-215", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03322", "designation": "Software Developer Hiring 2025-216", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03321", "designation": "Software Developer Hiring 2025-217", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03320", "designation": "Software Developer Hiring 2025-218", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03319", "designation": "Software Developer Hiring 2025-219", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03318", "designation": "Software Developer Hiring 2025-220", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03317", "designation": "Software Developer Hiring 2025-221", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03316", "designation": "Software Developer Hiring 2025-222", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03315", "designation": "Software Developer Hiring 2025-223", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03314", "designation": "Software Developer Hiring 2025-224", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03313", "designation": "Software Developer Hiring 2025-225", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03312", "designation": "Software Developer Hiring 2025-226", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03311", "designation": "Software Developer Hiring 2025-227", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03310", "designation": "Software Developer Hiring 2025-228", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03309", "designation": "Software Developer Hiring 2025-229", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03308", "designation": "Software Developer Hiring 2025-230", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03307", "designation": "Software Developer Hiring 2025-231", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03306", "designation": "Software Developer Hiring 2025-232", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03305", "designation": "Software Developer Hiring 2025-233", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03304", "designation": "Software Developer Hiring 2025-234", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03303", "designation": "Software Developer Hiring 2025-235", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03302", "designation": "Software Developer Hiring 2025-236", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03301", "designation": "Software Developer Hiring 2025-237", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03300", "designation": "Software Developer Hiring 2025-238", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03299", "designation": "Software Developer Hiring 2025-239", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03298", "designation": "Software Developer Hiring 2025-240", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03297", "designation": "Software Developer Hiring 2025-241", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03296", "designation": "Software Developer Hiring 2025-242", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03295", "designation": "Software Developer Hiring 2025-243", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03294", "designation": "Software Developer Hiring 2025-244", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03293", "designation": "Software Developer Hiring 2025-245", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03292", "designation": "Software Developer Hiring 2025-246", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03291", "designation": "Software Developer Hiring 2025-247", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03290", "designation": "Software Developer Hiring 2025-248", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03289", "designation": "Software Developer Hiring 2025-249", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03288", "designation": "Software Developer Hiring 2025-250", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03287", "designation": "Software Developer Hiring 2025-251", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03286", "designation": "Software Developer Hiring 2025-252", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03285", "designation": "Software Developer Hiring 2025-253", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03284", "designation": "Software Developer Hiring 2025-254", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03283", "designation": "Software Developer Hiring 2025-255", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03282", "designation": "Software Developer Hiring 2025-256", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03281", "designation": "Software Developer Hiring 2025-257", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03280", "designation": "Software Developer Hiring 2025-258", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03279", "designation": "Software Developer Hiring 2025-259", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03278", "designation": "Software Developer Hiring 2025-260", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03277", "designation": "Software Developer Hiring 2025-261", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03276", "designation": "Software Developer Hiring 2025-262", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03275", "designation": "Software Developer Hiring 2025-263", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03274", "designation": "Software Developer Hiring 2025-264", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03273", "designation": "Software Developer Hiring 2025-265", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03272", "designation": "Software Developer Hiring 2025-266", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03271", "designation": "Software Developer Hiring 2025-267", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03270", "designation": "Software Developer Hiring 2025-268", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03269", "designation": "Software Developer Hiring 2025-269", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03268", "designation": "Software Developer Hiring 2025-270", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03267", "designation": "Software Developer Hiring 2025-271", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03266", "designation": "Software Developer Hiring 2025-272", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03265", "designation": "Software Developer Hiring 2025-273", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03264", "designation": "Software Developer Hiring 2025-274", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03263", "designation": "Software Developer Hiring 2025-275", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03262", "designation": "Software Developer Hiring 2025-276", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03261", "designation": "Software Developer Hiring 2025-277", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03260", "designation": "Software Developer Hiring 2025-278", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03259", "designation": "Software Developer Hiring 2025-279", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03258", "designation": "Software Developer Hiring 2025-280", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03257", "designation": "Software Developer Hiring 2025-281", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03256", "designation": "Software Developer Hiring 2025-282", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03255", "designation": "Software Developer Hiring 2025-283", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03254", "designation": "Software Developer Hiring 2025-284", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03253", "designation": "Software Developer Hiring 2025-285", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03252", "designation": "Software Developer Hiring 2025-286", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03251", "designation": "Software Developer Hiring 2025-287", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03250", "designation": "Software Developer Hiring 2025-288", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03249", "designation": "Software Developer Hiring 2025-289", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03248", "designation": "Software Developer Hiring 2025-290", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03247", "designation": "Software Developer Hiring 2025-291", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03246", "designation": "Software Developer Hiring 2025-292", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03245", "designation": "Software Developer Hiring 2025-293", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03244", "designation": "Software Developer Hiring 2025-294", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03243", "designation": "Software Developer Hiring 2025-295", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03242", "designation": "Software Developer Hiring 2025-296", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03241", "designation": "Software Developer Hiring 2025-297", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03240", "designation": "Software Developer Hiring 2025-298", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03239", "designation": "Software Developer Hiring 2025-299", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03238", "designation": "Software Developer Hiring 2025-300", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03437", "designation": "Software Developer Hiring 2025-301", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03436", "designation": "Software Developer Hiring 2025-302", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03435", "designation": "Software Developer Hiring 2025-303", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03434", "designation": "Software Developer Hiring 2025-304", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03433", "designation": "Software Developer Hiring 2025-305", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03432", "designation": "Software Developer Hiring 2025-306", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03431", "designation": "Software Developer Hiring 2025-307", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03430", "designation": "Software Developer Hiring 2025-308", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03429", "designation": "Software Developer Hiring 2025-309", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03428", "designation": "Software Developer Hiring 2025-310", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03427", "designation": "Software Developer Hiring 2025-311", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03426", "designation": "Software Developer Hiring 2025-312", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03425", "designation": "Software Developer Hiring 2025-313", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03424", "designation": "Software Developer Hiring 2025-314", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03423", "designation": "Software Developer Hiring 2025-315", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03422", "designation": "Software Developer Hiring 2025-316", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03421", "designation": "Software Developer Hiring 2025-317", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03420", "designation": "Software Developer Hiring 2025-318", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03419", "designation": "Software Developer Hiring 2025-319", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03418", "designation": "Software Developer Hiring 2025-320", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03417", "designation": "Software Developer Hiring 2025-321", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03416", "designation": "Software Developer Hiring 2025-322", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03415", "designation": "Software Developer Hiring 2025-323", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03414", "designation": "Software Developer Hiring 2025-324", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03413", "designation": "Software Developer Hiring 2025-325", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03412", "designation": "Software Developer Hiring 2025-326", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03411", "designation": "Software Developer Hiring 2025-327", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03410", "designation": "Software Developer Hiring 2025-328", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03409", "designation": "Software Developer Hiring 2025-329", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03408", "designation": "Software Developer Hiring 2025-330", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03407", "designation": "Software Developer Hiring 2025-331", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03406", "designation": "Software Developer Hiring 2025-332", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03405", "designation": "Software Developer Hiring 2025-333", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03404", "designation": "Software Developer Hiring 2025-334", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03403", "designation": "Software Developer Hiring 2025-335", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03402", "designation": "Software Developer Hiring 2025-336", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03401", "designation": "Software Developer Hiring 2025-337", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03400", "designation": "Software Developer Hiring 2025-338", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03399", "designation": "Software Developer Hiring 2025-339", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03398", "designation": "Software Developer Hiring 2025-340", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03397", "designation": "Software Developer Hiring 2025-341", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03396", "designation": "Software Developer Hiring 2025-342", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03395", "designation": "Software Developer Hiring 2025-343", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03394", "designation": "Software Developer Hiring 2025-344", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03393", "designation": "Software Developer Hiring 2025-345", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03392", "designation": "Software Developer Hiring 2025-346", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03391", "designation": "Software Developer Hiring 2025-347", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03390", "designation": "Software Developer Hiring 2025-348", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03389", "designation": "Software Developer Hiring 2025-349", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03388", "designation": "Software Developer Hiring 2025-350", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03387", "designation": "Software Developer Hiring 2025-351", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03386", "designation": "Software Developer Hiring 2025-352", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03385", "designation": "Software Developer Hiring 2025-353", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03384", "designation": "Software Developer Hiring 2025-354", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03383", "designation": "Software Developer Hiring 2025-355", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03382", "designation": "Software Developer Hiring 2025-356", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03381", "designation": "Software Developer Hiring 2025-357", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03380", "designation": "Software Developer Hiring 2025-358", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03379", "designation": "Software Developer Hiring 2025-359", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03378", "designation": "Software Developer Hiring 2025-360", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03377", "designation": "Software Developer Hiring 2025-361", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03376", "designation": "Software Developer Hiring 2025-362", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03375", "designation": "Software Developer Hiring 2025-363", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03374", "designation": "Software Developer Hiring 2025-364", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03373", "designation": "Software Developer Hiring 2025-365", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03372", "designation": "Software Developer Hiring 2025-366", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03371", "designation": "Software Developer Hiring 2025-367", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03370", "designation": "Software Developer Hiring 2025-368", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03369", "designation": "Software Developer Hiring 2025-369", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03368", "designation": "Software Developer Hiring 2025-370", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03367", "designation": "Software Developer Hiring 2025-371", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03366", "designation": "Software Developer Hiring 2025-372", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03365", "designation": "Software Developer Hiring 2025-373", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03364", "designation": "Software Developer Hiring 2025-374", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03363", "designation": "Software Developer Hiring 2025-375", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03362", "designation": "Software Developer Hiring 2025-376", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03361", "designation": "Software Developer Hiring 2025-377", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03360", "designation": "Software Developer Hiring 2025-378", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03359", "designation": "Software Developer Hiring 2025-379", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03358", "designation": "Software Developer Hiring 2025-380", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03357", "designation": "Software Developer Hiring 2025-381", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03356", "designation": "Software Developer Hiring 2025-382", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03355", "designation": "Software Developer Hiring 2025-383", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03354", "designation": "Software Developer Hiring 2025-384", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03353", "designation": "Software Developer Hiring 2025-385", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03352", "designation": "Software Developer Hiring 2025-386", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03351", "designation": "Software Developer Hiring 2025-387", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03350", "designation": "Software Developer Hiring 2025-388", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03349", "designation": "Software Developer Hiring 2025-389", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03348", "designation": "Software Developer Hiring 2025-390", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03347", "designation": "Software Developer Hiring 2025-391", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03346", "designation": "Software Developer Hiring 2025-392", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03345", "designation": "Software Developer Hiring 2025-393", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03344", "designation": "Software Developer Hiring 2025-394", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03343", "designation": "Software Developer Hiring 2025-395", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03342", "designation": "Software Developer Hiring 2025-396", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03341", "designation": "Software Developer Hiring 2025-397", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03340", "designation": "Software Developer Hiring 2025-398", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03339", "designation": "Software Developer Hiring 2025-399", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03338", "designation": "Software Developer Hiring 2025-400", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03537", "designation": "Software Developer Hiring 2025-401", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03536", "designation": "Software Developer Hiring 2025-402", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03535", "designation": "Software Developer Hiring 2025-403", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03534", "designation": "Software Developer Hiring 2025-404", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03533", "designation": "Software Developer Hiring 2025-405", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03532", "designation": "Software Developer Hiring 2025-406", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03531", "designation": "Software Developer Hiring 2025-407", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03530", "designation": "Software Developer Hiring 2025-408", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03529", "designation": "Software Developer Hiring 2025-409", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03528", "designation": "Software Developer Hiring 2025-410", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03527", "designation": "Software Developer Hiring 2025-411", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03526", "designation": "Software Developer Hiring 2025-412", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03525", "designation": "Software Developer Hiring 2025-413", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03524", "designation": "Software Developer Hiring 2025-414", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03523", "designation": "Software Developer Hiring 2025-415", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03522", "designation": "Software Developer Hiring 2025-416", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03521", "designation": "Software Developer Hiring 2025-417", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03520", "designation": "Software Developer Hiring 2025-418", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03519", "designation": "Software Developer Hiring 2025-419", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03518", "designation": "Software Developer Hiring 2025-420", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03517", "designation": "Software Developer Hiring 2025-421", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03516", "designation": "Software Developer Hiring 2025-422", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03515", "designation": "Software Developer Hiring 2025-423", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03514", "designation": "Software Developer Hiring 2025-424", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03513", "designation": "Software Developer Hiring 2025-425", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03512", "designation": "Software Developer Hiring 2025-426", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03511", "designation": "Software Developer Hiring 2025-427", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03510", "designation": "Software Developer Hiring 2025-428", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03509", "designation": "Software Developer Hiring 2025-429", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03508", "designation": "Software Developer Hiring 2025-430", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03507", "designation": "Software Developer Hiring 2025-431", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03506", "designation": "Software Developer Hiring 2025-432", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03505", "designation": "Software Developer Hiring 2025-433", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03504", "designation": "Software Developer Hiring 2025-434", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03503", "designation": "Software Developer Hiring 2025-435", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03502", "designation": "Software Developer Hiring 2025-436", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03501", "designation": "Software Developer Hiring 2025-437", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03500", "designation": "Software Developer Hiring 2025-438", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03499", "designation": "Software Developer Hiring 2025-439", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03498", "designation": "Software Developer Hiring 2025-440", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03497", "designation": "Software Developer Hiring 2025-441", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03496", "designation": "Software Developer Hiring 2025-442", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03495", "designation": "Software Developer Hiring 2025-443", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03494", "designation": "Software Developer Hiring 2025-444", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03493", "designation": "Software Developer Hiring 2025-445", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03492", "designation": "Software Developer Hiring 2025-446", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03491", "designation": "Software Developer Hiring 2025-447", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03490", "designation": "Software Developer Hiring 2025-448", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03489", "designation": "Software Developer Hiring 2025-449", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03488", "designation": "Software Developer Hiring 2025-450", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03487", "designation": "Software Developer Hiring 2025-451", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03486", "designation": "Software Developer Hiring 2025-452", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03485", "designation": "Software Developer Hiring 2025-453", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03484", "designation": "Software Developer Hiring 2025-454", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03483", "designation": "Software Developer Hiring 2025-455", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03482", "designation": "Software Developer Hiring 2025-456", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03481", "designation": "Software Developer Hiring 2025-457", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03480", "designation": "Software Developer Hiring 2025-458", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03479", "designation": "Software Developer Hiring 2025-459", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03478", "designation": "Software Developer Hiring 2025-460", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03477", "designation": "Software Developer Hiring 2025-461", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03476", "designation": "Software Developer Hiring 2025-462", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03475", "designation": "Software Developer Hiring 2025-463", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03474", "designation": "Software Developer Hiring 2025-464", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03473", "designation": "Software Developer Hiring 2025-465", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03472", "designation": "Software Developer Hiring 2025-466", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03471", "designation": "Software Developer Hiring 2025-467", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03470", "designation": "Software Developer Hiring 2025-468", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03469", "designation": "Software Developer Hiring 2025-469", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03468", "designation": "Software Developer Hiring 2025-470", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03467", "designation": "Software Developer Hiring 2025-471", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03466", "designation": "Software Developer Hiring 2025-472", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03465", "designation": "Software Developer Hiring 2025-473", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03464", "designation": "Software Developer Hiring 2025-474", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03463", "designation": "Software Developer Hiring 2025-475", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03462", "designation": "Software Developer Hiring 2025-476", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03461", "designation": "Software Developer Hiring 2025-477", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03460", "designation": "Software Developer Hiring 2025-478", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03459", "designation": "Software Developer Hiring 2025-479", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03458", "designation": "Software Developer Hiring 2025-480", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03457", "designation": "Software Developer Hiring 2025-481", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03456", "designation": "Software Developer Hiring 2025-482", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03455", "designation": "Software Developer Hiring 2025-483", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03454", "designation": "Software Developer Hiring 2025-484", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03453", "designation": "Software Developer Hiring 2025-485", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03452", "designation": "Software Developer Hiring 2025-486", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03451", "designation": "Software Developer Hiring 2025-487", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03450", "designation": "Software Developer Hiring 2025-488", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03449", "designation": "Software Developer Hiring 2025-489", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03448", "designation": "Software Developer Hiring 2025-490", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03447", "designation": "Software Developer Hiring 2025-491", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03446", "designation": "Software Developer Hiring 2025-492", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03445", "designation": "Software Developer Hiring 2025-493", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03444", "designation": "Software Developer Hiring 2025-494", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03443", "designation": "Software Developer Hiring 2025-495", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03442", "designation": "Software Developer Hiring 2025-496", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03441", "designation": "Software Developer Hiring 2025-497", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03440", "designation": "Software Developer Hiring 2025-498", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03439", "designation": "Software Developer Hiring 2025-499", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03438", "designation": "Software Developer Hiring 2025-500", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03538", "designation": "Quality Analysis", "department": "SUPERVISORY_ORGANIZATION-6-323"}, {"positionId": "P-03548", "designation": "IMPL specialist-1", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03547", "designation": "IMPL specialist-2", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03546", "designation": "IMPL specialist-3", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03545", "designation": "IMPL specialist-4", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03544", "designation": "IMPL specialist-5", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03543", "designation": "IMPL specialist-6", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03542", "designation": "IMPL specialist-7", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03541", "designation": "IMPL specialist-8", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03540", "designation": "IMPL specialist-9", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03539", "designation": "IMPL specialist-10", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03549", "designation": "SDE IV", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03649", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03648", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03647", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03646", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03645", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03644", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03643", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03642", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03641", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03640", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03639", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03638", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03637", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03636", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03635", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03634", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03633", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03632", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03631", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03630", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03629", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03628", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03627", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03626", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03625", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03624", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03623", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03622", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03621", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03620", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03619", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03618", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03617", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03616", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03615", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03614", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03613", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03612", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03611", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03610", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03609", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03608", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03607", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03606", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03605", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03604", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03603", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03602", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03601", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03600", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03599", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03598", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03597", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03596", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03595", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03594", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03593", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03592", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03591", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03590", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03589", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03588", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03587", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03586", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03585", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03584", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03583", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03582", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03581", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03580", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03579", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03578", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03577", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03576", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03575", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03574", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03573", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03572", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03571", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03570", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03569", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03568", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03567", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03566", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03565", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03564", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03563", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03562", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03561", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03560", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03559", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03558", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03557", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03556", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03555", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03554", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03553", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03552", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03551", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03550", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03759", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03758", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03757", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03756", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03755", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03754", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03753", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03752", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03749", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03748", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03747", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03746", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03745", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03744", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03743", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03742", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03741", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03740", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03739", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03738", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03737", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03736", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03735", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03734", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03733", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03732", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03731", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03730", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03729", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03728", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03727", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03726", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03725", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03724", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03723", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03722", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03721", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03720", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03719", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03718", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03717", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03716", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03715", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03714", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03713", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03712", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03711", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03710", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03709", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03708", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03707", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03706", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03705", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03704", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03703", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03702", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03701", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03700", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03699", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03698", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03697", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03696", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03695", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03694", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03693", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03692", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03691", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03690", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03689", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03688", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03687", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03686", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03685", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03684", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03683", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03682", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03681", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03680", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03679", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03678", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03677", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03676", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03675", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03674", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03673", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03672", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03671", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03670", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03669", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03668", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03667", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03666", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03665", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03664", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03663", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03662", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03661", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03660", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03659", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03658", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03657", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03656", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03655", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03654", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03653", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03652", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03651", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03650", "designation": "SR. Recruiting Consultant", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03751", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03750", "designation": "SQA", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03764", "designation": "Product Engineer-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03763", "designation": "Product Engineer-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03762", "designation": "Product Engineer-3", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03761", "designation": "Product Engineer-4", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03760", "designation": "Product Engineer-5", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03769", "designation": "Product Engineer 1-1", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03768", "designation": "Product Engineer 1-2", "department": "SUPERVISORY_ORGANIZATION-6-683"}, {"positionId": "P-03789", "designation": "Product Manager-1", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03788", "designation": "Product Manager-2", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03787", "designation": "Product Manager-3", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03786", "designation": "Product Manager-4", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03785", "designation": "Product Manager-5", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03784", "designation": "Product Manager-6", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03783", "designation": "Product Manager-7", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03782", "designation": "Product Manager-8", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03781", "designation": "Product Manager-9", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03780", "designation": "Product Manager-10", "department": "SUPERVISORY_ORGANIZATION-6-252"}, {"positionId": "P-03889", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03888", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03887", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03886", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03885", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03884", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03883", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03882", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03881", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03880", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03879", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03878", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03877", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03876", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03875", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03874", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03873", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03872", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03871", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03870", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03869", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03868", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03867", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03866", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03865", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03864", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03863", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03862", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03861", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03860", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03859", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03858", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03857", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03856", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03855", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03854", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03853", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03852", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03851", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03850", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03849", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03848", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03847", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03846", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03845", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03844", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03843", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03842", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03841", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03840", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03839", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03838", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03837", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03836", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03835", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03834", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03833", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03832", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03831", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03830", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03829", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03828", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03827", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03826", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03825", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03824", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03823", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03822", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03821", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03820", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03819", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03818", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03817", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03816", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03815", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03814", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03813", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03812", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03811", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03810", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03809", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03808", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03807", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03806", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03805", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03804", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03803", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03802", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03801", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03800", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03799", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03798", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03797", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03796", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03795", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03794", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03793", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03792", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03791", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03790", "designation": "QA Position", "department": "SUPERVISORY_ORGANIZATION-6-693"}, {"positionId": "P-03898", "designation": "Senior Analyst-2", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03897", "designation": "Senior Analyst-3", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03896", "designation": "Senior Analyst-4", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03895", "designation": "Senior Analyst-5", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03894", "designation": "Senior Analyst-6", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03893", "designation": "Senior Analyst-7", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03892", "designation": "Senior Analyst-8", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03891", "designation": "Senior Analyst-9", "department": "SUPERVISORY_ORGANIZATION-6-513"}, {"positionId": "P-03890", "designation": "Senior Analyst-10", "department": "SUPERVISORY_ORGANIZATION-6-513"}], "workShifts": [{"workShiftId": "Floating_Holiday_Hours", "workShiftName": "Floating Holiday (Hours)"}, {"workShiftId": "Sick_Hours", "workShiftName": "Sick (Hours)"}, {"workShiftId": "Vacation_Hours", "workShiftName": "Vacation (Hours)"}, {"workShiftId": "Paid_Time_Off_Days", "workShiftName": "Paid Time Off (Days)"}, {"workShiftId": "Holiday_Days", "workShiftName": "Holiday (Days)"}, {"workShiftId": "India_Earned_Leave_Hours", "workShiftName": "India Earned Leave (Hours)"}, {"workShiftId": "Sick_Days", "workShiftName": "Sick (Days)"}, {"workShiftId": "US_Intermittent_FMLA_Hours", "workShiftName": "U.S. Intermittent FMLA (Hours)"}, {"workShiftId": "Annual_Leave_Days", "workShiftName": "Annual Leave (Days)"}, {"workShiftId": "Childcare_Leave_Days", "workShiftName": "Childcare Leave (Days)"}, {"workShiftId": "COMPENSATORY_TIMEOFF", "workShiftName": "Compensatory Time Off"}, {"workShiftId": "FRA_Paternity_Leave_Birth", "workShiftName": "FRA Paternity Leave - Birth"}, {"workShiftId": "Paid_Time_Off_Adjustment_Hours", "workShiftName": "Paid Time Off Adjustment (Hours)"}, {"workShiftId": "Paid_Time_Off_Adjustment_Days", "workShiftName": "Paid Time Off Adjustment (Days)"}, {"workShiftId": "Anniversary Time (Hours)", "workShiftName": "Anniversary Time (Hours)"}, {"workShiftId": "FRA_Paternity_Leave_Adoption", "workShiftName": "FRA Paternity Leave - Adoption"}, {"workShiftId": "Vacation (Days)", "workShiftName": "Vacation (Days)"}, {"workShiftId": "VAB Sickness (Days)", "workShiftName": "VAB Sickness (Days)"}, {"workShiftId": "NLD Statutory Holiday", "workShiftName": "NLD Statutory Holiday"}, {"workShiftId": "FRA_RTT_Time_Off_Days", "workShiftName": "FRA RTT Time Off (Days)"}, {"workShiftId": "Vocation_Summer_Time", "workShiftName": "Vocation summer Time(Hours)"}, {"workShiftId": "Vacation_Time_off_type", "workShiftName": "1 - Vacation Time off type"}, {"workShiftId": "Sick_Days_(ZK)", "workShiftName": "Sick Days (ZK)"}, {"workShiftId": "SICK_TO_GMS_1", "workShiftName": "USA Sick Time Off GMS"}, {"workShiftId": "zk annual leave", "workShiftName": "zk annual leave"}, {"workShiftId": "1 - vacation", "workShiftName": "1 - vacation"}, {"workShiftId": "PTO time off type", "workShiftName": "PTO"}, {"workShiftId": "Mango_Vacation_Type", "workShiftName": "Mango Vacation Type Day"}, {"workShiftId": "Mango Hourly Time off type", "workShiftName": "Mango Hourly"}, {"workShiftId": "TEST", "workShiftName": "RAMESH-TEST"}, {"workShiftId": "CORONA", "workShiftName": "CORONA (Hours)"}, {"workShiftId": "ZKTeco_TimeOff_Day", "workShiftName": "ZKTeco TimeOff Day"}, {"workShiftId": "Mango_Time_Off", "workShiftName": "Mango Time Off"}, {"workShiftId": "Vacation", "workShiftName": "Holiday"}, {"workShiftId": "SL", "workShiftName": "Sick Day Time Off"}, {"workShiftId": "EL", "workShiftName": "Emergency Day Time Off"}, {"workShiftId": "NK Floating Holiday", "workShiftName": "NK Floating Holiday"}, {"workShiftId": "ZK Test 01", "workShiftName": "ZK Test 01"}, {"workShiftId": "Mango_Vacation_Type_hourly", "workShiftName": "Mango Vacation Type Hourly"}, {"workShiftId": "ZTK Tenant Vacation Days", "workShiftName": "ZTK Tenant Vacation Days"}, {"workShiftId": "Basic Time Off", "workShiftName": "Basic Time Off"}, {"workShiftId": "Basic New", "workShiftName": "Basic New"}, {"workShiftId": "Mango_Bereavement", "workShiftName": "Mango Bereavement Leave"}]}}