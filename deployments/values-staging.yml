environments:
  GRPC_CLIENT_CORE-SERVICE_NEGOTIATIONTYPE: TLS
  GRPC_SERVER_PORT: '9090'
  GRPC_SERVER_SECURITY_ENABLED: 'true'
  JAVA_HEAP_MAX_MEM: -Xmx1g
  SENTRY_DSN: https://<EMAIL>/4503968957333504
  SENTRY_ENVIRONMENT: staging
  SENTRY_TRACES_SAMPLE_RATE: '1.0'
  SERVER_PORT: '8080'
  SPRING_PROFILES_ACTIVE: stage
  awslogs-group: /stg/app/tech/customerIntegrationService/cloudwatchLogGroup
  awslogs-stream-prefix: ecs
kind: v2
name: customerIntegrationService
resources:
  cpu: 512
  memory: 2048
secrets:
  APM_SERVER_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/apmServer/param
  APM_TOKEN: arn:aws:secretsmanager:ap-southeast-1:133139256227:secret:/mgt/monitoring/devops/elasticsearch/apmtoken/secret-qmnKQx
  AWS_REGION: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/apse1/shared/aws/region
  AWS_S3_BUCKET: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/apse1/shared/aws/sftp-s3bucket
  FEIGN_CLIENT_CONFIG_DOCGENSERVICE_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platform/docgenService/param
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/growthbook/env/key/param
  GRPC_CLIENT_BULK_UPLOAD_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/apse1/shared/url/grpc/bulk-upload-service
  GRPC_CLIENT_FIELD_MAPPING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/apse1/shared/url/grpc/field-mapping-service
  GRPC_CLIENT_COMPANYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_COMPANY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_CONTRACTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/contractService/param
  GRPC_CLIENT_CONTRACT_OFFBOARDING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/contractOffboardingService/param
  GRPC_CLIENT_CONTRACT_ONBOARDING_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/contractOnboardingService/param
  GRPC_CLIENT_CORESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/coreService/param
  GRPC_CLIENT_COUNTRYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/countryService/param
  GRPC_CLIENT_EXPENSESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/expenseService/param
  GRPC_CLIENT_MEMBERSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/memberService/param
  GRPC_CLIENT_PAYROLLSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/payrollService/param
  GRPC_CLIENT_PAYSE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/paySeService/param
  GRPC_CLIENT_PIGEONSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/pigeonService/param
  GRPC_CLIENT_TIMEOFFSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/timeoffService/param
  GRPC_CLIENT_AUTHORITY_SERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/orgManagementService/param
  GRPC_CLIENT_PAYABLESERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/payableService/param
  INTEGRATION_WEBHOOK_SFTP_API_KEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/customerIntegrationService/webhook/sftp/api-key
  JWT_PUBLIC_KEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/employee/jwt/publicKey/param
  PIGEON_CLIENT_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_CUSTOMER_INTEGRATION_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platform/frontend/param
  PLATFORM_DOCGEN_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platfrom/docgenService/private/param
  PLATFORM_DOCGEN_PUBLICBASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platfrom/docgenService/public/param
  PLATFORM_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_KNIT_API_KEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/services/google/apikey/param
  PLATFORM_MERGE-DEV_API-KEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/mergedev/api/key/param
  PLATFORM_PIGEON_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  SPRING_DATASOURCE_PASSWORD: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/customerIntegrationService/db/user/password/param
  SPRING_DATASOURCE_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/customerIntegrationService/db/url/param
  SPRING_DATASOURCE_USERNAME: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/customerIntegrationService/db/user/param
